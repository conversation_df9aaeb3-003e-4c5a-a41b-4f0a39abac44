import { Button, Dropdown, Menu } from '@/components/basic';
import I18N from '@/utils/I18N';
import s from './index.less';

const MoreOptions = ({ options, className = '' }) => {
  if (!options?.length) {
    return null;
  }

  const overlayBasic = () => {
    return (
      <Menu>
        {options?.map((option, index) => {
          return (
            <Menu.Item className={s.option__menu} key={index}>
              {option}
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };
  return (
    <Dropdown
      style={{ flexShrink: 0 }}
      title="Click me"
      overlay={overlayBasic()}
      primary
      trigger="click"
    >
      <Button className={`${s.more__trigger} ${className}`} size="small">
        {I18N.auto.more}
      </Button>
    </Dropdown>
  );
};

export default MoreOptions;
