import { ChangeParams } from '@bedrock/components/lib/Select';
import classNames from 'classnames';

import { Icon, Select } from '@/components/basic';
import SelectedItem from '@/components/basic/select/selected-item';
import { IPriorityField, PriorityOptions, SelectType } from '@/utils/fields';

import s from './index.less';

interface TabsProps {
  value: any;
  onChange?: (value: any, params?: Partial<ChangeParams>) => void;
  type: SelectType;
}

const renderLabel = (option: IPriorityField) => {
  return (
    <div className={option.className}>
      <Icon name={option.iconName} className={classNames('com-level-icon', 'mr-7')}></Icon>
      {option.name}
    </div>
  );
};

const FieldSelect: React.FC<TabsProps> = (props) => {
  const { value, onChange } = props;
  return (
    <Select
      dropdownClassName={s.fieldSelect}
      multipleCheckedType={'check-icon'}
      multiple
      value={value}
      onChange={onChange}
      options={PriorityOptions}
      renderLabel={(option) => {
        return renderLabel(option);
      }}
      renderSelectedItem={(option, { index, onClose }) => {
        return {
          content: (
            <div className={classNames(option.className, 'com-level-bg', s.tag)}>
              <SelectedItem
                onClose={(e) => {
                  onClose(e, option);
                }}
              >
                <Icon
                  name={option.iconName}
                  className={classNames('com-level-icon', 'mr-2')}
                ></Icon>
                {option.name}
              </SelectedItem>
            </div>
          ),
          isRenderInTag: false,
        };
      }}
      zIndex={999999}
      //@ts-ignore
      // getPopupContainer={(dom) => dom.parentNode}
    />
  );
};
export default FieldSelect;
