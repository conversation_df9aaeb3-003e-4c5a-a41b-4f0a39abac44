import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { useEffect, useMemo, useRef } from 'react';
import { history, useSearchParams } from 'umi';

import { apiTaskDeleteTaskDelete, apiTodoCancelGet } from '@/api';
import { Icon, IconBtn, Popconfirm } from '@/components/basic';
import { DetailTodoInfo, TaskPermission } from '@/types';
import { getSessionInfo } from '@/utils';
import { ServerSourceTypeMapKey } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import { Calendar, Share } from '../operation';
import Follow from '../operation/follow';
import TaskStatus from '../operation/task-status';
import TransferCreator from '../operation/transfer-creator';
import s from './index.less';
import TransferIndependent from '../operation/TransferIndependent';
import ParentTaskDelContent from '@/hooks/useDelTaskConfirm/ParentTaskDelContent';

export interface Props {
  containerElement: HTMLElement;
  className?: string;
  onFollowChange?: (isFollower: boolean) => void;
  onRemove?: () => void;
  taskInfo?: DetailTodoInfo;
  permissions?: TaskPermission[];
}

const DetailHeaderWeb: React.FC<Props> = (props) => {
  const { onFollowChange, taskInfo, containerElement, permissions } = props;
  const { title, taskId, isFollower, calendarSyncStatus, assigneeUids, assigner } = taskInfo || {};
  const { assignerUid } = assigner || {};

  const extraParamsRef = useRef({ cancelType: 0 });

  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get('sessionId');
  const sessionType = searchParams.get('sessionType');
  const onClose = () => {
    pp.close();
  };

  const hasBack = useMemo(() => {
    const from = searchParams.get('from');
    if (from === 'pc-list') {
      return true;
    }
    return false;
  }, [searchParams]);

  useEffect(() => {
    if (sessionId && sessionType) {
      getSessionInfo({
        source: sessionType as unknown as ServerSourceTypeMapKey,
        sourceId: sessionId,
      });
    }
  }, [sessionType, sessionId]);

  const memoPermissions = useMemo(() => {
    const [
      CAN_COMPLETE,
      CAN_EDIT,
      CAN_DELETE,
      CAN_TRANSFER,
      CAN_SHARE,
      CAN_SYNC_TO_CALENDAR,
      CAN_NOT_OBSERVER,
      CAN_CANCEL_OBSERVER,
    ] = validatesPermission({
      permissions: permissions,
      key: [
        TaskPermissionEnum.CAN_COMPLETE,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_DELETE,
        TaskPermissionEnum.CAN_TRANSFER,
        TaskPermissionEnum.CAN_SHARE,
        TaskPermissionEnum.CAN_SYNC_TO_CALENDAR,
        TaskPermissionEnum.CAN_NOT_OBSERVER,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[];
    return {
      CAN_EDIT,
      CAN_COMPLETE,
      CAN_DELETE,
      CAN_SHARE,
      CAN_TRANSFER,
      CAN_SYNC_TO_CALENDAR,
      CAN_NOT_OBSERVER,
      CAN_CANCEL_OBSERVER,
    };
  }, [permissions]);

  const renderTransfer = () => {
    if (memoPermissions.CAN_TRANSFER) {
      if (taskInfo?.parentId) {
        return <TransferIndependent taskInfo={taskInfo}></TransferIndependent>;
      }
    }
    if (memoPermissions.CAN_TRANSFER) {
      return (
        <TransferCreator
          taskId={taskId!}
          assignerUid={assignerUid}
          iconBtnClass={s.icon}
          assigneeUids={assigneeUids}
          containerElement={containerElement}
          onTransferAndExit={() => {
            if (hasBack) {
              history.go(-1);
            } else {
              history.push(`/pc/list?sessionId=${sessionId}&sessionType=${sessionType}`);
            }
          }}
        ></TransferCreator>
      );
    }
    return null;
  };

  return (
    <div className={s.head}>
      <div className={`${s.clientHead} ${hasBack ? '' : s.hidden}`}>
        <div
          className={s.back}
          onClick={() => {
            if (hasBack) {
              history.go(-1);
            } else {
              history.push(`/pc/list?sessionId=${sessionId}&sessionType=${sessionType}`);
            }
          }}
        >
          <Icon name="icon-sys_open" className={s.backIcon}></Icon>
          {I18N.auto.return}
        </div>
      </div>
      <div
        className={s.todoHead}
        style={{
          paddingRight: hasBack ? '0.24rem' : '0.42rem',
          paddingTop: hasBack ? 0 : '0.02rem',
        }}
      >
        <div className={s.headLeft}>
          <TaskStatus taskInfo={taskInfo} disabled={!memoPermissions.CAN_COMPLETE} />
        </div>
        <div className="flex flex-align-center">
          {memoPermissions.CAN_SHARE ? (
            <Share taskId={taskId!} iconBtnClass={s.icon}></Share>
          ) : null}
          {memoPermissions.CAN_SYNC_TO_CALENDAR ? (
            <Calendar
              taskId={taskId!}
              iconBtnClass={s.icon}
              calendarSyncStatus={calendarSyncStatus}
            ></Calendar>
          ) : null}
          {renderTransfer()}
          {/* {memoPermissions.CAN_TRANSFER ? (
            <TransferCreator
              taskId={taskId!}
              assignerUid={assignerUid}
              iconBtnClass={s.icon}
              assigneeUids={assigneeUids}
              containerElement={containerElement}
              onTransferAndExit={() => {
                if (hasBack) {
                  history.go(-1);
                } else {
                  history.push(`/pc/list?sessionId=${sessionId}&sessionType=${sessionType}`);
                }
              }}
            ></TransferCreator>
          ) : null} */}

          {memoPermissions.CAN_DELETE ? (
            <Popconfirm
              title={
                <div>
                  <div
                    style={{
                      color: 'var(--text-color-1)',
                      fontWeight: 'var(--font-weight-lg)',
                      fontSize: 'var(--font-size-lg)',
                      lineHeight: '0.24rem',
                    }}
                    className="mb-10"
                  >
                    {I18N.auto.deleteTask}
                  </div>
                  {taskInfo?.isParent ? (
                    <ParentTaskDelContent
                      title={taskInfo?.title}
                      onChange={(v) => (extraParamsRef.current.cancelType = v)}
                    />
                  ) : (
                    <div className="line-ellipsis-3">{title}</div>
                  )}
                </div>
              }
              danger
              hasIcon={false}
              placement="bottomRight"
              cancelText={I18N.auto.cancel}
              okText={I18N.auto.delete}
              builtinPlacements={{
                bottomRight: {
                  points: ['tr', 'br'],
                  offset: [15, 0],
                },
              }}
              trigger="click"
              onOk={() => {
                const request =
                  taskInfo?.isParent || taskInfo?.parentId
                    ? apiTaskDeleteTaskDelete
                    : apiTodoCancelGet;
                request({
                  taskId: String(taskId),
                  todoId: taskId,
                  parentTaskId: taskInfo?.parentId,
                  ...extraParamsRef.current,
                }).finally(() => {
                  onClose?.();
                });
              }}
            >
              <IconBtn
                title={I18N.auto.delete}
                className={classNames(s.icon, 'ml-10')}
                iconName="icon-details_nav_delete1"
              ></IconBtn>
            </Popconfirm>
          ) : null}
          {!memoPermissions.CAN_NOT_OBSERVER ? (
            <Follow
              isFollower={isFollower}
              onFollow={(v) => {
                if (!v) {
                  if (memoPermissions.CAN_CANCEL_OBSERVER) {
                    onFollowChange?.(v);
                  }
                } else {
                  onFollowChange?.(v);
                }
              }}
              containerElement={containerElement}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default DetailHeaderWeb;
