import classNames from 'classnames';
import React, { useState } from 'react';

import { ComFieldText } from '@/components/basic-project';
import { TodoInfo } from '@/types';
import { CustomField, FieldTypeEnum } from '@/types/custom-field';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  value?: string;
  taskInfo: TodoInfo;
  filedInfo: CustomField;
  onChange?: (v?: string) => void;
  disabled?: boolean;
}

const RenderCustomFieldText: React.FC<Props> = (props) => {
  const { taskInfo, filedInfo, disabled, value = '', onChange } = props;
  const [editding, setEditding] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const changeVisible = (v: boolean) => {
    setVisible(v);
  };

  const change = (v: string) => {
    if (v !== value) {
      onChange?.(v);
    }
    changeVisible(false);
    setEditding(false);
  };
  return (
    <AddRowBox
      tipTitle=""
      placeholder={value || editding ? ' ' : '-'}
      focused={false}
      disabled={disabled}
      taskId={taskInfo.taskId}
      onClick={(e) => {
        changeVisible(!visible);
      }}
      className={`${s.box} box`}
    >
      <div className={s.customFieldText}>
        <ComFieldText
          className={classNames(s.listTitleEditor, { [s.fullWidth]: editding })}
          focusedClassName={classNames({ [s.focused]: editding })}
          contentClassName={s.listTitleContent}
          value={value}
          onChange={change}
          editding={editding}
          onChangeEditding={(v) => {
            setEditding(v);
          }}
          onBlur={(e) => {
            setEditding(false);
          }}
          blurChangeValue
        ></ComFieldText>
      </div>
    </AddRowBox>
  );
};

export default RenderCustomFieldText;
