import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'umi';

import { Dispatch, RootState } from '@/models/store';
import Head from '@/pages/pc/list/head';
import TableList from '@/pages/pc/list/table-list';
import { ServerSourceTypeMapKey } from '@/utils/const';

import s from './index.less';

const HomePage: React.FC = () => {
  const dispatch = useDispatch<Dispatch>();
  const [searchParams] = useSearchParams();
  const { sessionId } = useSelector((state: RootState) => ({
    sessionId: state.imTodoList.sessionId,
  }));
  useEffect(() => {
    let sessionId = searchParams.get('sessionId');
    let sessionType = searchParams.get('sessionType');
    dispatch.imTodoList.setSessionId(String(sessionId));
    dispatch.imTodoList.setSessionType(Number(sessionType) as ServerSourceTypeMapKey);
    // 请求数据
  }, []);

  return (
    <div className={s.page}>
      <Head></Head>
      {sessionId ? (
        <div className={s.body}>
          <TableList className={s.table}></TableList>
        </div>
      ) : null}
    </div>
  );
};
export default HomePage;
