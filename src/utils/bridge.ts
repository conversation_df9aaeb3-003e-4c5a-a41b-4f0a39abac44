import { GetRecentContactsListParams, pp } from '@popo-bridge/web';

import { UserInfo } from '@/types';

import { EnumSessionType } from './const';
import { validatesVersion } from './validate-version';

/**
 * 获取最近联系人
 */
export const bridgeGetRecentContactsList = async (v: GetRecentContactsListParams) => {
  const { maxCount = 30, sessionTypes } = v;
  try {
    let _list: UserInfo[] = [];
    if (validatesVersion('getRecentContactsList')) {
      const { data = [] } = await pp.getRecentContactsList({
        maxCount: maxCount,
        sessionTypes: sessionTypes,
      });
      if (data?.length) {
        const list = data
          .filter(
            (item) =>
              !item.uid?.includes('@cus.robot.popo.com') &&
              !item.uid?.includes('@app.robot.popo.com')
          )
          .map((item) => ({
            name: item.name || item.sessionName,
            uid: item.uid || item.sessionId,
            avatarUrl: item.headPic,
            // 最近联系人群sessionType是2,选人组件群sessionType是3  服务端接收的群sessionType是3
            sessionType:
              item.sessionType === EnumSessionType.p2p ? EnumSessionType.p2p : EnumSessionType.team,
          }));
        _list = list;
      }
    }
    return _list;
  } catch (error) {
    console.log('getRecentContactsList error');
    return [];
  }
};
