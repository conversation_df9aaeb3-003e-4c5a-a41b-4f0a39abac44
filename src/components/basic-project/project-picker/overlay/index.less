.overlay {
  width: 222px;
  padding: 0px;
  background-color: var(--bgTop);
  border-radius: 8px;
  box-shadow: var(--ComBoxShadow);
  border: 1px solid var(--aBlack10);
}

.input {
  height: 40px;
}

.list {
  position: relative;
  padding: 4px;
  border-top: 1px solid var(--aBlack10);
  max-height: 260px;
  min-height: 200px;
  overflow-y: auto;
  .item {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 8px;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background-color: var(--aBlack6);
    }
  }
}
.tip {
  display: flex;
  height: 48px;
  padding: 8px 12px;
  border-top: 1px solid var(--aBlack10);
  .icon {
    flex-shrink: 0;
    font-size: 16px;
    color: var(--Brand600);
  }
  .desc {
    flex: 1;
    margin-left: 5px;
    font-size: 12px;
    line-height: 16px;
    color: var(--Brand600);
    letter-spacing: 1px;
  }
}
