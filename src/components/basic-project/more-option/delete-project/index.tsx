import React, { PropsWithChildren } from 'react';

import { apiProjectDeleteDelete, apiProjectGroupDeleteDelete } from '@/api';
import Modal from '@/components/basic/modal';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
  onDeleteSuccess?: () => void;
  text?: {
    title: string;
    content: string;
    okText: string;
  };
}

const DeleteProject: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    children,
    projectInfo,
    onDeleteSuccess,
    text = {
      title: I18N.auto.confirmDisbandItem,
      content: I18N.auto.afterDisbandingTheProject,
      okText: I18N.auto.dissolution,
    },
  } = props;
  const disband = () => {
    if (projectInfo?.projectId) {
      if (projectInfo?.isGroup) {
        return apiProjectGroupDeleteDelete({
          groupId: String(projectInfo?.projectId),
        }).then(() => {
          //各场景不一致，返回具体场景处理
          onDeleteSuccess?.();
          // dispatch.project.getPingProjectList();
        });
      }
      return apiProjectDeleteDelete({
        projectId: String(projectInfo?.projectId),
      }).then(() => {
        //各场景不一致，返回具体场景处理
        onDeleteSuccess?.();
        // dispatch.project.getPingProjectList();
      });
    }
  };
  return React.cloneElement(children, {
    onClick: () => {
      Modal.confirm(
        {
          rootClassName: s.confirm,
          title: I18N.template(text.title, { val1: projectInfo?.name }),
          width: 400,
          scrolled: false,
          centered: true,
          okText: text.okText,
          content: <div>{text.content}</div>,

          onOk: disband,
        },
        'warning'
      );
    },
  });
};

export default DeleteProject;
