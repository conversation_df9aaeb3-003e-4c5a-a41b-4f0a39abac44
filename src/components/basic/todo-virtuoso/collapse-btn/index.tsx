import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  text?: string;
  className?: string;
  taskIds?: number[];
  value?: boolean;
  onChange?: (fold: boolean) => void;
};
const CollapseBtn: React.FC<PropsWithChildren<Props>> = (props) => {
  const { text, className, value, onChange } = props;

  return (
    <div className={classNames(s.groupHead, className)}>
      <div className={s.collapseItem}>
        <IconBtn
          title={value ? I18N.auto.expandTheDayOfWaiting : I18N.auto.putAwayTheDayAndWait}
          className={classNames(s.collapseIcon, { [s.collapse]: value })}
          fontSize={12}
          iconName="icon-triangleLineRight_1"
          onClick={() => {
            onChange?.(!value);
          }}
        ></IconBtn>
        <span className={s.headDesc}>{text}</span>
      </div>
    </div>
  );
};

export default CollapseBtn;
