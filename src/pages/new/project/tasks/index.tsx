import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'umi';

import { Guide } from '@/components/basic';
import { FiledTypeList } from '@/components/field-edit-popover/const';
import useGuide, { TaskGuide } from '@/components/guide/useGuide';
import LoadingPage from '@/components/loading-page';
import PageErrorBoundary from '@/components/page-error-boundary';
import { RootState } from '@/models/store';
import { GuideType } from '@/utils/const';
import I18N from '@/utils/I18N';

import PageTasks from '../../components/page-tasks';
import Head from './head';
import s from './index.less';

const Tasks: React.FC = () => {
  const params = useParams();
  const { id } = params as { id: string };
  const { loading, currentViewTab, viewTabListLoading } = useSelector((state: RootState) => {
    return {
      loading: state.project.loading,
      currentViewTab: state.viewSetting.currentViewTab,
      viewTabListLoading: state.viewSetting.viewTabListLoading,
    };
  });
  const [delay, setDelay] = useState(true);
  const [nextText, setNextText] = useState(I18N.auto.next);
  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.project,
  });

  const steps = useMemo(() => {
    return [
      {
        title: I18N.auto.addCustom,
        content: (
          <div>
            <div>{I18N.auto.projectManager}</div>
            <div className={s.customFieldbg}>
              {FiledTypeList.map((item, index) => {
                return (
                  <div key={index} className={s.filedItem}>
                    {item.icon
                      ? React.cloneElement(item.icon, {
                          className: `${item.icon.props.className || ''} ${s.customFieldbgIcon}`,
                        })
                      : null}
                    <div className={s.customFieldbgName}>{item.name}</div>
                  </div>
                );
              })}
            </div>
          </div>
        ),

        selector: '.task-fields-setting',
        placement: 'bottom',
      },
      {
        title: I18N.auto.personalizedTasks,
        content: <span>{I18N.auto.screenBasedOnDemands}</span>,

        selector: '.task-fields-select',
        placement: 'bottom',
      },
      {
        title: I18N.auto.quickProjectDesign,
        content: <span>{I18N.auto.projectManager_2}</span>,
        selector: '.task-project-head',
        placement: 'bottom',
      },
    ];
  }, []);

  // console.log('currentViewTab', currentViewTab, loading);

  useEffect(() => {
    setDelay(true);
    if (currentViewTab.viewId) {
      setDelay(false);
    }
  }, [id, currentViewTab]);

  useEffect(() => {
    TaskGuide.addListener(GuideType.project, () => {
      if (document.querySelector('.task-fields-setting')) {
        getGuideData().then((ret) => {
          //接口判定不显示引导
          if (!ret) {
            TaskGuide.removeGuide(GuideType.project);
          }
        });
      } else {
        //首个引导元素不存在 等待
        TaskGuide.addPending(GuideType.project);
      }
    });
    setNextText(I18N.auto.next);
  }, []);

  const show = useMemo(() => {
    return !loading && !viewTabListLoading && !delay;
  }, [loading, viewTabListLoading, delay]);

  return (
    <PageErrorBoundary
      errorId="Page-Project-Task-List-Render-Error"
      //@i18n-ignore
      errorMsg="项目详情任务列表渲染异常"
    >
      <Guide
        visible={!delay && showGuide}
        scrollType="fixed"
        padding={2}
        beforeText={I18N.auto.thePrevious}
        nextText={nextText}
        steps={steps}
        afterStepChange={(step: any, index: number) => {
          if (index >= 3) {
            document.body.style.overflow = '';
            handleHideGuide();
          }
        }}
        onClose={() => {}}
        beforeStepChange={(step: any, index: number) => {
          document.body.style.overflow = 'hidden';
          if (index >= 2) {
            setNextText(I18N.auto.iGotIt);
          } else {
            setNextText(I18N.auto.next);
          }
        }}
      />

      <div className={s.page}>
        {show ? (
          <>
            <Head></Head>
            <PageTasks></PageTasks>
          </>
        ) : (
          <LoadingPage className={classNames(s.loading)}></LoadingPage>
        )}
      </div>
    </PageErrorBoundary>
  );
};
export default Tasks;
