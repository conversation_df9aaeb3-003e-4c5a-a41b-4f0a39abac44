diff --git a/es/scenegraph/component/table-component.js b/es/scenegraph/component/table-component.js
index ff33f09441d7ede9c34e2a352fc2ac227b4e98a7..eb05be1b4161a0c2ec20a2b773bf57c6f6c9326b 100644
--- a/es/scenegraph/component/table-component.js
+++ b/es/scenegraph/component/table-component.js
@@ -234,12 +234,21 @@ export class TableComponent {
         var _a, _b, _c, _d, _e, _f, _g;
         const oldHorizontalBarPos = this.table.stateManager.scroll.horizontalBarPos, oldVerticalBarPos = this.table.stateManager.scroll.verticalBarPos, theme = this.table.theme, width = null === (_a = theme.scrollStyle) || void 0 === _a ? void 0 : _a.width, visible1 = null === (_b = theme.scrollStyle) || void 0 === _b ? void 0 : _b.visible, horizontalVisible = null !== (_d = null === (_c = theme.scrollStyle) || void 0 === _c ? void 0 : _c.horizontalVisible) && void 0 !== _d ? _d : visible1, verticalVisible = null !== (_f = null === (_e = theme.scrollStyle) || void 0 === _e ? void 0 : _e.verticalVisible) && void 0 !== _f ? _f : visible1, tableWidth = Math.ceil(this.table.scenegraph.tableGroup.attribute.width), tableHeight = Math.ceil(this.table.scenegraph.tableGroup.attribute.height), totalHeight = this.table.getAllRowsHeight(), totalWidth = this.table.getAllColsWidth(), frozenRowsHeight = this.table.getFrozenRowsHeight(), frozenColsWidth = this.table.getFrozenColsWidth(), bottomFrozenRowsHeight = this.table.getBottomFrozenRowsHeight(), rightFrozenColsWidth = this.table.getRightFrozenColsWidth(), sizeTolerance = (null === (_g = this.table.options.customConfig) || void 0 === _g ? void 0 : _g._disableColumnAndRowSizeRound) ? 1 : 0;
         if (totalWidth > tableWidth + sizeTolerance) {
+
+            // POPO: 空态高度撑满
+            if (this.hScrollBar.parent && this.hScrollBar.parent.parent && this.hScrollBar.parent.parent && this.hScrollBar.parent.parent.attribute && this.hScrollBar.parent.parent.attribute.clip) {
+                this.hScrollBar.parent.parent.setAttributes({
+                    clip: false
+                })
+            }
+
             const y = Math.min(tableHeight, totalHeight), rangeEnd = Math.max(.05, (tableWidth - frozenColsWidth) / (totalWidth - frozenColsWidth)), hoverOn = this.table.theme.scrollStyle.hoverOn;
             let attrY = 0;
             attrY = this.table.theme.scrollStyle.barToSide ? this.table.tableNoFrameHeight - (hoverOn ? width : -this.table.scenegraph.tableGroup.attribute.y) + this.table.tableY : y - (hoverOn ? width : -this.table.scenegraph.tableGroup.attribute.y), 
             this.hScrollBar.setAttributes({
                 x: frozenColsWidth + (hoverOn ? 0 : this.table.scenegraph.tableGroup.attribute.x),
-                y: attrY,
+                // POPO: 空态高度撑满
+                y: this.table.tableNoFrameHeight - 8, //attrY,
                 width: tableWidth - frozenColsWidth - rightFrozenColsWidth,
                 range: [ 0, rangeEnd ],
                 visible: "always" === horizontalVisible
@@ -247,7 +256,8 @@ export class TableComponent {
             const bounds = this.hScrollBar.AABBBounds && this.hScrollBar.globalAABBBounds;
             this.hScrollBar._viewPosition = {
                 x: bounds.x1,
-                y: bounds.y1
+                // POPO: 空态高度撑满
+                y: this.table.tableNoFrameHeight - 8
             }, "always" === horizontalVisible && this.hScrollBar.showAll();
         } else this.hScrollBar.setAttributes({
             x: 2 * -this.table.tableNoFrameWidth,
