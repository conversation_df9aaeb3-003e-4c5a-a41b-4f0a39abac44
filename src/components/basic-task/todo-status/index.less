.statusBox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  &:hover {
    :global {
      .iconfont {
        color: var(--IconSecondary);
      }
      .icon-taskstate_down {
        color: rgba(var(--G600-t), 0.95);
      }
    }
  }
  &:active {
    transform: scale(0.8);
    transition: transform 0.1s;
  }
  :global {
    .icon-taskstate_down {
      color: rgba(var(--G600-t), 0.8);
    }
  }
}
.statusIcon {
  position: relative;
  //left: 14px;
  color: var(--IconSecondary);
  cursor: pointer;
  // width: 28px;
  // height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  // 设计要求hover上去的时候改变icon 变成有对勾的icon
  &:not(.disabled):hover {
    :global {
      .icon-taskstate_nodown:before {
        content: '\e80c' !important;
      }
    }
  }
  &.disabled {
    cursor: auto;
    :global {
      .icon {
        color: var(--IconQuartus);
        border-radius: 14px;
      }
      .icon-taskstate_nodown {
        color: var(--IconQuartus);
        background-color: var(--aBlack6);
      }
    }
  }
}
.active {
  display: inline-block;
  position: absolute;
  left: 0;
  width: 28px;
  height: 28px;
  background-repeat: no-repeat;
  animation: bear 0.6s steps(30);
  animation-fill-mode: forwards;
  pointer-events: none;
  background-image: url('~@/assets/images/done.png');
  background-size: 840px 28px;
}

.dark {
  background-image: url('~@/assets/images/done-dark.png');
}

@keyframes bear {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -840px, 0;
  }
}
