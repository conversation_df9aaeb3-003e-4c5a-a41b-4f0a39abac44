import { useEffect, useRef } from 'react';
const useOutsideClick = <T>(callback: (event?: MouseEvent) => void) => {
  const ref = useRef<T>();
  const callbackRef = useRef<() => void>();
  callbackRef.current = callback;
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target)) {
        if (callbackRef?.current) {
          callbackRef?.current(event);
        }
      }
    };

    document.addEventListener('click', handleClick, false);

    return () => {
      document.removeEventListener('click', handleClick, false);
    };
  }, [ref]);

  return ref;
};

export default useOutsideClick;
