import { useEffect } from 'react';

export const useRegisterMouseEventEnableBridge = () => {
  useEffect(() => {
    function setMouseEventEnable(data) {
      console.log('setMouseEventEnable', data);
      if (typeof data === 'number') {
        document.body.style.pointerEvents = !data ? 'none' : '';
        const drawerEl = document.querySelector('.rock-drawer-content-wrapper') as HTMLDivElement;
        if (drawerEl) {
          drawerEl.style.pointerEvents = !data ? 'none' : '';
        }
      }
    }

    const deviceEventEmitter = window.popo_bridge_init_data?.DeviceEventEmitter;
    if (deviceEventEmitter) {
      deviceEventEmitter.addListener('mouseEventEnable', setMouseEventEnable);
    }

    console.log('deviceEventEmitter', deviceEventEmitter);

    return () => {
      deviceEventEmitter.removeListener('mouseEventEnable', setMouseEventEnable);
    };
  }, []);
};
