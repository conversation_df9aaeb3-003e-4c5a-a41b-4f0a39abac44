import { IGanttTaskGroupInfo } from '@/components/gantt/utils';
import { Gantt } from '@visactor/vtable-gantt';

class ExpandNodeSetManager {
  projectId: number = 0;
  viewId: number = 0;
  expandNodeSets: Record<number, Set<string | number>> = {};
  ganttInstanceRef: React.MutableRefObject<Gantt | undefined | null> | null = null;
  isAllExpand: boolean = false;
  queryGroupBys?: any[] = [];

  setGanttInstanceRef(ref: React.MutableRefObject<Gantt | undefined | null>) {
    this.ganttInstanceRef = ref;
  }

  setup({ viewId, queryGroupBys }: { viewId: number; queryGroupBys?: any[] }) {
    this.viewId = viewId;
    this.queryGroupBys = queryGroupBys;
  }

  toggleAllHierarchyState() {
    if (!this.queryGroupBys?.length) {
      return;
    }

    const groupByDepth = this.queryGroupBys.length;

    const state = this.isAllExpand ? 'collapse' : 'expand';
    const groups = this.ganttInstanceRef?.current?.records || [];
    let curExpandNodeSet = this.expandNodeSets[this.viewId];
    if (!curExpandNodeSet) {
      curExpandNodeSet = new Set<string | number>();
      this.expandNodeSets[this.viewId] = curExpandNodeSet;
    }

    const dfs = (groups: IGanttTaskGroupInfo[] | undefined, depth: number) => {
      if (!groups) {
        return;
      }
      groups?.forEach((g) => {
        if (state === 'collapse') {
          if (groupByDepth > 1 && g.groupLevel < 1) {
            g.hierarchyState = 'expand';
            curExpandNodeSet.add(g.groupId);
          } else {
            g.hierarchyState = state;
            curExpandNodeSet.delete(g.groupId);
          }
        } else {
          g.hierarchyState = state;
        }
        const nextDepth = depth + 1;
        if (nextDepth < groupByDepth) {
          const subs = g.children?.length ? g.children : g.subtask;
          dfs(subs as IGanttTaskGroupInfo[], nextDepth);
        }
      });
    };
    dfs(groups, 0);

    if (state === 'collapse') {
      curExpandNodeSet.clear();
    }

    this.ganttInstanceRef?.current?.setRecords([...groups]);
    this.isAllExpand = !this.isAllExpand;
  }

  getExpandNodeSet(viewId: number) {
    const curExpandNodeSet = this.expandNodeSets[viewId];
    if (!curExpandNodeSet) {
      this.expandNodeSets[viewId] = new Set<string | number>();
    }
    return this.expandNodeSets[viewId];
  }

  // isAllExpand() {
  //   let ret = true;
  //   const expandNodeSet = this.expandNodeSets[this.viewId];

  //   const dfs = (groups: IGanttTaskGroupInfo[] | undefined) => {
  //     if (!groups || !ret) {
  //       return;
  //     }

  //     groups?.forEach((g) => {
  //       /**
  //        * 这里必须要用 expandNodeSet 来判断，g.hierarchyState 不准确
  //        */
  //       if (g.isGroup && !expandNodeSet.has(g.taskId)) {
  //         ret = false;
  //         return;
  //       }

  //       dfs(g.children as IGanttTaskGroupInfo[]);
  //     });
  //   };

  //   dfs(this.ganttInstanceRef?.current?.records);

  //   return ret;
  // }
}
const expandNodeSetMap = new ExpandNodeSetManager();

// 保持window.expandNodeSetMap的引用不变
window.expandNodeSetMap = expandNodeSetMap;

export { expandNodeSetMap };
