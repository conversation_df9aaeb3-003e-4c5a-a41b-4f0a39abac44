import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode } from 'react';

import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  className?: string;
  leftRender: ReactNode;
  allowClear?: boolean;
  onClear?: () => any;
  disabled?: boolean;
};

const RenderItem: React.FC<PropsWithChildren<Props>> = (props) => {
  const { leftRender, children, className, allowClear = false, onClear, disabled = false } = props;
  return (
    <div className={classNames(s.item, className)}>
      {!!leftRender && (
        <div
          className={classNames(s.labelIcon, 'todo-detail-label-icon', {
            [s.height0]: !leftRender,
          })}
        >
          {leftRender}
        </div>
      )}
      <div
        className={classNames(s.infoItem, 'todo-detail-infoItem', { [s.padding0]: !leftRender })}
      >
        {children}
      </div>
      <div className={s.clear}>
        {!!allowClear && !disabled && (
          <IconBtn
            fontSize={16}
            title={I18N.auto.delete}
            iconName="icon-sys_close"
            onClick={onClear}
            iconClassName={s.clearIcon}
          ></IconBtn>
        )}
      </div>
    </div>
  );
};

export default RenderItem;
