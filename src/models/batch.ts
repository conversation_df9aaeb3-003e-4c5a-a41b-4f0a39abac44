import { createModel } from '@rematch/core';

import { TodoInfo } from '@/types';
import { TaskTableRowTypeAdd } from '@/utils/const';

import type { RootModel } from '.';

export const batch = createModel<RootModel>()({
  state: {
    isBatchMode: false, //是否是批量操作模式
    batchList: [] as TodoInfo[],
    preSelectItem: {} as TodoInfo,
  },
  reducers: {
    setIsBatchMode(state, payload: boolean) {
      return { ...state, isBatchMode: payload };
    },
    setPreSelectItem(state, payload: TodoInfo) {
      return { ...state, preSelectItem: payload };
    },
    setBatchList(state, payload: TodoInfo[]) {
      return {
        ...state,
        batchList: payload.filter((item) => !String(item.taskId).includes(TaskTableRowTypeAdd)),
      };
    },
  },
  effects: (dispatch) => ({
    /**
     * 设置是否是批量操作模式
     * @param state
     * @param payload
     * @returns
     */
    setBatchMode(isBatch: boolean) {
      dispatch.batch.setIsBatchMode(isBatch);
      if (!isBatch) {
        //清空已选数据
        dispatch.batch.setBatchList([]);
      }
    },
    exitBatchMode() {
      dispatch.batch.setIsBatchMode(false);
      //TODO 是否缓存数据
      dispatch.batch.setBatchList([]);
    },
    onShift(item: TodoInfo, state) {
      dispatch.batch.setBatchMode(true);
      // dispatch.batch.setPreSelectItem(item); 在shift中暂时不加入
      const { batchList, preSelectItem } = state.batch;
      const { dataList } = state.task;
      // 如果当前列表无值 从待办列表中第一位开始选取到当前item
      let filterList = [];
      if (batchList.length === 0) {
        dispatch.batch.setPreSelectItem(item);
        for (let index = 0; index < dataList.length; index++) {
          const element = dataList[index];
          if (element.taskId !== item.taskId) {
            filterList.push(element);
          } else {
            filterList.push(element);
            break;
          }
        }
      } else {
        let startShift = false;
        let endShift = false;
        // 如果当前列表有值 把上次一item到当前item数据全选 其他的选中的也保留
        let end: 'pre' | 'cur' | undefined = undefined;
        for (let index = 0; index < dataList.length; index++) {
          const element = dataList[index];
          if (element.taskId === item.taskId || element.taskId === preSelectItem.taskId) {
            if (!startShift) {
              startShift = true;
              if (element.taskId === item.taskId) {
                end = 'pre'; //先到当前元素  结尾是上一次数据
              } else {
                end = 'cur'; // 先到上一次元素  结尾是当前元素
              }
            }
          }
          // if (!startShift && !endShift) {
          //   console.log(1);
          //   //保留之前的以及保留之后的
          //   if (batchList.find((v) => v.taskId === element.taskId)) {
          //     filterList.push(element);
          //   }
          // }
          if (startShift && !endShift) {
            //全选Shift
            filterList.push(element);
          }
          if (end === 'pre') {
            if (element.taskId === preSelectItem.taskId && startShift) {
              endShift = true;
            }
          } else if (end === 'cur') {
            if (element.taskId === item.taskId && startShift) {
              endShift = true;
            }
          }
        }
      }
      dispatch.batch.setBatchList(filterList);
    },
    onMetaOrCtrl(item: TodoInfo, state) {
      const { isBatchMode } = state.batch;
      dispatch.batch.setPreSelectItem(item);
      const { batchList } = state.batch;
      const { dataList } = state.task;
      // 如果当前已被选中 从选中数据中丢弃
      let flag = false;
      let filterList = [];
      if (isBatchMode) {
        filterList = batchList.filter((v) => {
          if (v.taskId !== item.taskId) {
            return true;
          } else {
            flag = true;
            return false;
          }
        });
      } else {
        filterList = batchList;
      }

      // flag 为true 表示过滤的时候发现了已有数据 直接抛弃被排除的数据
      if (!flag) {
        // 如果当前未选中,需要按照原本待办列表顺序,加入选中列表
        filterList = dataList.filter(
          (value) =>
            batchList.find((_v) => _v.taskId === value.taskId) || value.taskId === item.taskId
        );
      }

      dispatch.batch.setBatchList(filterList);
      if (filterList.length === 0) {
        dispatch.batch.setBatchMode(false);
      } else {
        dispatch.batch.setBatchMode(true);
      }
    },
    filterList(opt: any, state) {
      const { batchList } = state.batch;
      const { dataList } = state.task;
      const list = dataList.filter(
        (item) => batchList.findIndex((v) => v.taskId === item.taskId) > -1
      );
      dispatch.batch.setBatchList(list);
    },
    allSelectList(opt: any, state) {
      const { dataList } = state.task;
      dispatch.batch.setBatchList(dataList);
    },
  }),
});
