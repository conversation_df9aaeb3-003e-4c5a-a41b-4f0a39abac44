import classNames from 'classnames';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { IconBtn } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import s from './index.less';

export enum FromType {
  calendar,
  unplanned,
}

interface Props {
  from: FromType;
}

const ToggleUnplanned: React.FC<Props> = (props) => {
  const { from } = props;
  const { showUnplanned } = useSelector((state: RootState) => ({
    showUnplanned: state.user.showUnplanned,
  }));
  const fromCalendar = useMemo(() => {
    return from === FromType.calendar;
  }, [from]);
  const dispatch = useDispatch<Dispatch>();
  const toggleUnplanned = (v: boolean) => {
    dispatch.user.setData({
      showUnplanned: v,
    });
  };
  return fromCalendar ? (
    <IconBtn
      placement="topRight"
      title={I18N.auto.unplannedDeployment}
      iconName="icon-pc_view_expandlist"
      onClick={() => {
        toggleUnplanned(true);
      }}
      className={classNames(s.btn, { [s.hidden]: showUnplanned })}
    ></IconBtn>
  ) : (
    <IconBtn
      placement="topRight"
      title={I18N.auto.collapseUnplanned}
      iconName="icon-pc_view_collapselist"
      onClick={() => {
        toggleUnplanned(false);
      }}
      className={classNames(s.btn, { [s.hidden]: !showUnplanned })}
    ></IconBtn>
  );
};
export default ToggleUnplanned;
