import classNames from 'classnames';
import React from 'react';

import { TextArea } from '@/components/basic';
import { Variant } from '@/components/basic/input';
import { TITLE_MAX_LENGTH } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  className?: string;
  value?: string;
  onBlur?: () => void;
  onFocus?: () => void;
  onChange?: (v: string) => void;
  onEnter?: () => void;
};
const TitleInput: React.FC<Props> = (props) => {
  const { value, onChange, onEnter, onBlur, onFocus, className } = props;

  return (
    <div className={classNames(s.textAreaBox, className)}>
      <TextArea
        className={s.textArea}
        value={value}
        onChange={(e) => {
          onChange?.(e.target.value);
        }}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={I18N.auto.addTitleEnter}
        autoFocus
        rows={1}
        allowResize={false}
        autoSize={true}
        border={false}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            onEnter?.();
          }
        }}
        maxLength={TITLE_MAX_LENGTH}
        variant={Variant.borderless}
      ></TextArea>
    </div>
  );
};

export default TitleInput;
