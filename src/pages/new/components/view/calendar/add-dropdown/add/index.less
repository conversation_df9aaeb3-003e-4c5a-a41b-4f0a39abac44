.add {
  display: flex;
  flex-direction: column;
  height: 100%;
  :global {
    .todo-base-label {
      color: var(--TextPrimary);
    }
    .com-placeholder {
      font-size: 14px !important;
      color: var(--TextTertiary);
      height: 30px;
      &:hover {
        background-color: var(--aBlack6);
      }
    }
  }
}
.head {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  height: 40px;
  padding-right: 20px;
  flex-shrink: 0;
}

.leftTip {
  display: flex;
  align-items: center;

  .icon {
    color: var(--IconPrimary);
  }
  .text {
    color: var(--TextTertiary);
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    margin-left: 6px;
  }
}

.body {
  padding: 0 24px 12px 16px;
  flex: 1;
  .addTitle {
    min-height: 24px;
    margin-left: 4px;
    padding-bottom: 8px;
    box-sizing: content-box;
    border-bottom: 2px solid var(--Brand500);
    margin-bottom: 16px;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  margin: 0 20px 20px;
  height: 32px;
}

.btn {
  width: 100%;
}

//TODO 提取Button组件
.cancel {
  background-color: transparent;
  border-color: var(--aBlack10);
  color: var(--TextPrimary);
  &:hover {
    background-color: var(--aBlack6);
    color: var(--TextPrimary);
  }
}

.confirm {
  background-color: var(--Brand500);
  border-color: transparent;
  color: var(--absWhite);
  &:hover {
    background-color: var(--Brand600);
    color: var(--absWhite);
  }
}

.successMessage {
  :global {
    .rock-icon.rock-message-icon {
      display: none !important;
    }
  }
}

.info {
  display: flex;
  align-items: center;
  font-size: 14px;
  .infoIcon {
    margin-right: 6px;
    color: var(--absaWhite80);
  }
  .divider {
    height: 24px;
    border-left-color: var(--absaWhite12);
    border-left-width: 1px;
  }

  .view {
    color: var(--Brand500);
    font-size: 14px;
    cursor: pointer;
  }
}
