.attachmentSelecWrap {
  width: 100%;
  height: 100%;
  min-height: 32px;
  font-size: 0;
  cursor: pointer;
  .count {
    height: 24px;
    margin-top: 3px;
    margin-bottom: 4px;
    line-height: 24px;
    font-size: 13px;
    font-weight: 400;
    color: var(--TextPrimary-strong);
  }

  .itemWrapper {
    margin-left: 0px;
    margin-right: 0px;
  }

  :global {
    .rock-dropdown-trigger-default {
      width: 100%;
      height: 100%;
      justify-content: flex-start;
      background-color: transparent !important;
      &:hover {
        background-color: unset;
      }
      & > span {
        display: flex;
        width: 100%;
        height: 100%;
      }
    }
  }
  .add {
    display: flex;
    align-items: center;
    height: 32px !important;
    font-size: 12px;
    font-weight: 400;
    color: var(--TextTertiary);
    // cursor: pointer;
    .height20 {
      display: flex;
      align-items: center;
      height: 20px;
    }
    .plus {
      margin-right: 4px;
      //margin-top: 2px;
    }
    &:hover {
      background-color: var(--aBlack6);
      border-radius: 6px;
    }
  }
  .hasListAdd {
    &:hover {
      background-color: transparent;
    }
  }
}

.panel {
  width: 128px;
  padding: 4px;

  .panelItem {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 0 12px;
    border-radius: 4px;
    color: var(--TextPrimary);
    font-size: 13px;
    cursor: pointer;
    &:hover,
    .active {
      background-color: var(--aBlack4);
    }
  }
}

.itemWrapper {
  margin: 2px 4px;
  height: 32px;
}

.attachmentDisabledWrapper {
  cursor: default;
  .add {
    &:hover {
      background-color: transparent;
    }
  }

  .hasListDisabledAdd {
    height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
  }

  :global {
    .com-placeholder {
      min-width: 0 !important;
      font-size: 14px !important;
    }
  }
}
