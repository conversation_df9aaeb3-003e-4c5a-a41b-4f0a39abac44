.notify__menu {
  padding: 4px !important;
  border: 1px solid var(--aBlack12) !important;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.12);
  min-width: 160px !important;

  .notify__cat {
    padding-left: 8px;
    font-size: 12px;
    line-height: 18px;
    color: var(--TextTertiary);
    padding-top: 6px;
    padding-bottom: 2px;
  }

  :global {
    .rock-dropdown-menu-submenu-title,
    .rock-dropdown-menu-item {
      padding: 8px;
      margin: 0;
      line-height: 20px;
      color: var(--TextPrimary);
      font-size: 13px;
      line-height: 20px;

      &.rock-dropdown-menu-submenu-active {
        background-color: var(--aBlack6);
      }
    }
  }
}

.submenu__popover {
  :global {
    .rock-dropdown-menu {
      .notify__menu;
      // padding: 4px !important;
      // border: 1px solid var(--aBlack12) !important;
      // box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.12);
      // min-width: 160px !important;

      // .rock-dropdown-menu-submenu-title,
      // .rock-dropdown-menu-item {
      //   padding: 8px;
      //   margin: 0;
      //   line-height: 20px;
      //   color: var(--TextPrimary);
      //   font-size: 13px;
      //   line-height: 20px;

      //   &.rock-dropdown-menu-submenu-active {
      //     background-color: var(--aBlack6);
      //   }
      // }
    }
  }
}
