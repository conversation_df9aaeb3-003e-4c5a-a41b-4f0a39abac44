function listenForOutsideClicks(
  listening: boolean,
  setListening: (v: boolean) => void,
  menuRef: any,
  setIsOpen: (v: boolean) => void
) {
  return () => {
    if (listening) return;
    if (!menuRef.current) return;
    setListening(true);
    //`touchstart`
    [`click`].forEach(() => {
      document.addEventListener(`click`, (evt) => {
        if (menuRef.current?.contains(evt.target)) return;
        setIsOpen(false);
      });
    });
  };
}

export { listenForOutsideClicks };
