import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import s from './index.less';

interface RenderItemProps {
  className?: string;
  leftClassName?: string;
  leftNode: React.ReactNode;
}

const RenderItem: React.FC<PropsWithChildren<RenderItemProps>> = (props) => {
  const { className, leftClassName, leftNode, children } = props;
  return (
    <div className={classNames(s.item, 'render__item', className)}>
      {leftNode && (
        <div className={classNames(s.left, 'render__item--left', leftClassName)}>{leftNode}</div>
      )}
      <div className={s.right}>{children}</div>
    </div>
  );
};

export default RenderItem;
