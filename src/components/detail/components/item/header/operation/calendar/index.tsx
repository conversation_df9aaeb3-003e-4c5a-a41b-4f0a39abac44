import { pp } from '@popo-bridge/web';
import React from 'react';
import { useDispatch } from 'react-redux';

import { apiTodoCalendarSyncPost } from '@/api';
import { IconBtn } from '@/components/basic';
import { Dispatch } from '@/models/store';
import { CalendarSyncStatus } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './web.less';
export interface Props {
  iconBtnClass?: string;
  taskId: number;
  calendarSyncStatus?: CalendarSyncStatus;
}

const Calendar: React.FC<Props> = (props) => {
  const { iconBtnClass, taskId, calendarSyncStatus } = props;
  const dispatch = useDispatch<Dispatch>();
  const syncCalendar = (v: CalendarSyncStatus) => {
    apiTodoCalendarSyncPost({
      taskId: taskId,
      calendarSyncStatus: v,
    }).then(() => {
      dispatch.detail.updateDetail({
        calendarSyncStatus: v,
      });
      if (v === CalendarSyncStatus.sync) {
        pp.showToast({
          title: I18N.auto.successfullyAdded,
        });
      } else {
        pp.showToast({
          title: I18N.auto.cancelledAdding,
        });
      }
    });
  };
  return (
    <span className="ml-10">
      {calendarSyncStatus === CalendarSyncStatus.unsync ? (
        <IconBtn
          title={I18N.auto.addToCalendar_2}
          className={iconBtnClass}
          iconName="icon-pc_details_updatecal"
          onClick={() => {
            syncCalendar(CalendarSyncStatus.sync);
          }}
        ></IconBtn>
      ) : (
        <IconBtn
          title={I18N.auto.cancelAddingTo}
          className={iconBtnClass}
          iconName="icon-pc_details_unaddcalendar"
          onClick={() => {
            syncCalendar(CalendarSyncStatus.unsync);
          }}
        ></IconBtn>
      )}
    </span>
  );
};

export default Calendar;
