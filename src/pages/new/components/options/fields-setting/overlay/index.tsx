import { OperateAdd } from '@babylon/popo-icons';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Icon } from '@/components/basic';
import DnDList from '@/components/dndkit/dnd-list';
import FieldEditPopover from '@/components/field-edit-popover';
import { useGetShownFields } from '@/hooks';
import { Dispatch, RootState } from '@/models/store';
import { EnumFieldVisible, Order } from '@/utils/const';
import { DisplayField } from '@/utils/fields';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import { EnumTrackeKey } from '@/utils/skyline';

import Container from '../../overlay-container';
import FieldsItem from './fields-item';
import s from './index.less';

export type Props = {
  className?: string;
  onClose?: () => any;
};

export interface Sorted {
  sortOrder: Order;
  key: string;
}
function isProjectId(str: any) {
  return /^\d+$/.test(str + '');
}

const GroupOverlay: React.FC<Props> = (props) => {
  const { className, onClose } = props;
  const { navigatorId, customFields, perms } = useSelector((state: RootState) => {
    return {
      customFields: state.viewSetting.customFields,
      navigatorId: state.viewSetting.navigatorId,
      perms: state.viewSetting.permissions,
    };
  });

  const list = useGetShownFields();

  const dispatch = useDispatch<Dispatch>();

  const onChange = (id: any, v: boolean) => {
    let newList = [...list];
    let field = newList.find((f) => f.id == id);
    if (field) {
      field.visible = v ? EnumFieldVisible.show : EnumFieldVisible.hidden;
    }
    dispatch.viewSetting.changeDisplayFields({
      displays: newList,
    });
    dispatch.user.trackingByView({
      key: EnumTrackeKey.FieldConfig,
      fieldName: field?.customFieldId || field?.fieldName,
      visible: v,
    });
  };

  return (
    <Container
      title={I18N.auto.fieldConfiguration}
      className={className}
      footer={
        isProjectId(navigatorId) &&
        validatesPermission({
          key: ProjectPermissionEnum.CAN_MANAGE_FIELD,
          permissions: perms,
        }) ? (
          <FieldEditPopover placement="rightBottom" onSuccess={onClose}>
            <div className={s.newField}>
              <OperateAdd className={s.plus}></OperateAdd>
              <span className={s.text}>{I18N.auto.newField}</span>
              <Icon name="icon-sys_open" className={s.arrow}></Icon>
            </div>
          </FieldEditPopover>
        ) : null
      }
    >
      <DnDList<DisplayField>
        wholeRowHandle={false}
        list={list}
        onDragEnd={(item, list: DisplayField[]) => {
          const { over, active } = item;
          if (!over?.id || !active.id) {
            return;
          }
          dispatch.viewSetting.changeDisplayFields({
            displays: list,
          });
          //埋点: 字段拖拽
          dispatch.user.tracking({ key: EnumTrackeKey.FieldConfigDrag });
        }}
        renderItem={(item: DisplayField) => {
          return (
            <FieldsItem
              hasHandle={true}
              item={item}
              customFields={customFields}
              onOpenModal={onClose}
              onChange={(v) => {
                onChange(item.id, v);
              }}
            ></FieldsItem>
          );
        }}
        renderOverlayItem={(item: DisplayField) => {
          return <FieldsItem item={item} customFields={customFields}></FieldsItem>;
        }}
      ></DnDList>
    </Container>
  );
};

export default GroupOverlay;
