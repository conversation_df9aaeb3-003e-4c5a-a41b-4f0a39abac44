import './index.less'

import { ListTable } from '@visactor/vtable'
import { EditContext, IEditor } from '@visactor/vtable-editors'
import ReactDOM from 'react-dom/client'

import { TextArea } from '@/components/basic'
import I18N from '@/utils/I18N'

import { getTreeIndent } from '../../utils'
import { AddMenuId } from '@/utils/const'

export class TaskNameEditor implements IEditor<string, ListTable> {
  root: ReactDOM.Root | null
  element: HTMLDivElement | null
  container: HTMLElement | null
  record: any
  currentValue: any
  editorContext: EditContext<string, ListTable> | null
  indent: number
  constructor() {
    this.root = null
    this.element = null
    this.container = null
    this.record = null
    this.indent = 0
  }

  // onStart: (context: EditContext<V, T>) => void;
  // onEnd: () => void;
  // isEditorElement?: (target: HTMLElement) => boolean;
  // validateValue?: (
  //   newValue?: any,
  //   oldValue?: any,
  //   position?: CellAddress,
  //   table?: any
  // ) => boolean | ValidateEnum | Promise<boolean | ValidateEnum>;
  // getValue: () => V;
  // beginEditing?: (container: HTMLElement, referencePosition: ReferencePosition, value: V) => void;
  // exit?: () => void;
  // targetIsOnEditor?: (target: HTMLElement) => boolean;
  // bindSuccessCallback?: (callback: () => void) => void;

  onStart(editorContext: EditContext<string, ListTable>) {
    const { container, referencePosition, value, row, col, table } = editorContext

    this.editorContext = editorContext
    const record = table.getRecordByCell(col, row)

    const indent = getTreeIndent(col, row, table)
    this.indent = indent
    this.record = record
    this.container = container
    this.createElement(value)
    value && this.setValue(value)
    setTimeout(() => {
      ;(null == referencePosition ? void 0 : referencePosition.rect) && this.adjustPosition(referencePosition.rect)
    }, 16)
    window.postMessage({ type: 'taskName-edit-start', col, row, value: this.currentValue }, '*')
  }

  createElement(defaultValue: string) {
    const div = document.createElement('div')
    div.classList.add('vtable__task-name-editor')
    this.container?.appendChild(div)
    this.root = ReactDOM.createRoot(div)
    const theme = document.body.getAttribute('data-theme')
    this.root.render(
      <>
        {this.record?.taskId === AddMenuId && (
          <img
            className="create__status--icon"
            src={`https://popo.gsf.netease.com/task-status/${theme}-normal-disabled.png`}
          />
        )}
        <TextArea
          autoFocus
          placeholder={I18N.auto.addTitle}
          className="table__textarea"
          rows={1}
          autoSize={true}
          onChange={this.handleChange}
          style={{ width: `calc(100% - ${this.indent + 40}px)` }}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              this.onEnd()
            }
            if (e.key === 'Escape') {
              e.preventDefault()
              this.currentValue = null
              this.onEnd()
            }
          }}
        />
      </>,
    )
    this.element = div
  }

  handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    this.setValue(e.target.value)
  }

  // beginEditing(container: HTMLElement, referencePosition, value) {
  //   this.createElement(value);
  // }

  getValue() {
    return this.currentValue
  }

  setValue(value) {
    if (this.editorContext) {
      this.currentValue = value
      const { table, col, row } = this.editorContext
      table.changeCellValue(col, row, this.currentValue)
    }
  }

  adjustPosition(rect: DOMRect): void {
    if (this.element) {
      this.element.style.top = rect.top + 1 + 'px'
      this.element.style.left = rect.left + 'px'
      this.element.style.width = rect.width + 'px'
      this.element.style.height = rect.height - 1 + 'px'
      this.element.style.opacity = '1'
    }
  }

  onEnd() {
    if (this.element && this.editorContext) {
      const { col, row } = this.editorContext
      window.postMessage({ type: 'taskName-edit-end', col, row, value: this.currentValue }, '*')
      this.container?.removeChild(this.element)
      this.element = null
      this.currentValue = null
      this.editorContext = null
    }
  }

  isEditorElement(target: HTMLElement): boolean {
    // cascader创建时时在cavas后追加一个dom，而popup append在body尾部。不论popup还是dom，都应该被认为是点击到了editor区域
    return this.element?.contains(target) || this.isClickPopUp(target)
  }

  isClickPopUp(target: HTMLElement): boolean {
    while (target) {
      if (target.classList && target.classList.contains('arco-select-vtable')) {
        return true
      }
      // 如果到达了DOM树的顶部，则停止搜索
      target = target.parentNode as HTMLElement | null
    }
    // 如果遍历结束也没有找到符合条件的父元素，则返回false
    return false
  }
}
