import { POPOBridgeEmitter } from '@popo-bridge/web';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { useCallback, useContext, useEffect, useRef } from 'react';

import { TableContext } from '@/components/table/context';
import { EnumEmitter } from '@/utils/const';

interface Props {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  [k: string]: any;
}

const CustomScrollbar = React.forwardRef((props: Props, ref) => {
  const { children, className, style, ...extraProps } = props;
  // Default scrollbar refSetter (for any component AND Virtuoso)
  const { theadRef } = useContext(TableContext);
  let osInstanceRef = useRef();
  const refSetter = useCallback(
    (scrollbarsRef: any) => {
      if (ref && scrollbarsRef) {
        const osInstance = scrollbarsRef.osInstance();
        osInstanceRef.current = osInstance;
        // For virtuoso, set viewport as ref and add our custom extension
        ref.current = osInstance.getElements().viewport;
        osInstance.addExt('virtuosoExtension', { extraProps });
      }
    },
    [ref, extraProps]
  );
  useEffect(() => {
    // 滚动到指定位置
    POPOBridgeEmitter.addListener(EnumEmitter.ListTableSoscrollToLeft, (x = 0) => {
      if (osInstanceRef?.current?.scroll) {
        //设置列表视图的横向滚动位置
        osInstanceRef.current?.scroll({ x: x });
      }
    });
    return () => {
      POPOBridgeEmitter.removeAllListeners(EnumEmitter.ListTableSoscrollToLeft);
    };
  }, []);
  return (
    <OverlayScrollbarsComponent
      options={{
        overflowBehavior: {
          x: 'scroll',
          y: 'scroll',
        },
        callbacks: {
          onScroll: (e) => {
            if (osInstanceRef.current && theadRef.current) {
              theadRef.current.scrollLeft = osInstanceRef.current.scroll().position.x;
            }
          },
        },
      }}
      ref={refSetter}
      className={className}
      style={style}
      {...extraProps}
    >
      {children}
    </OverlayScrollbarsComponent>
  );
});

export default CustomScrollbar;
