import { OperateClose } from '@babylon/popo-icons';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { PropsWithChildren, ReactNode, useEffect, useMemo, useState } from 'react';

import { Dropdown, IconBtn } from '@/components/basic';
import { Icon, Placeholder } from '@/components/basic';
import {
  getRepeatTypeByRuleStr,
  getRuleStrByRepeatType,
  RepeatOption,
  repeatOptions,
  RepeatType,
} from '@/components/basic/popo-date-picker/utils';

import s from './index.less';

export type Props = {
  value?: string;
  onChange?: (v?: string) => void;
  className?: string;
  placeholder?: string;
  renderLabel?: (label: string) => ReactNode;
  disabled?: boolean;
  time?: number;
};

const RrulePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, onChange, placeholder, renderLabel, disabled, time } = props;
  const [open, setOpen] = useState(false);
  const [selectList, setSelectList] = useState<RepeatOption[]>([]);
  const change = (item: any) => {
    const rrule =
      item.value === RepeatType.None ? '' : getRuleStrByRepeatType(item.value, dayjs(time));
    onChange?.(rrule);
    // setSelectList([item]);
    setOpen(false);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled) {
      return;
    }
    change({ value: RepeatType.None });
  };

  useEffect(() => {
    if (value) {
      const type = getRepeatTypeByRuleStr(value);
      const list = repeatOptions.filter((item) => item.value === type);
      setSelectList(list);
    } else {
      setSelectList([]);
    }
  }, [value]);
  const label = useMemo(() => {
    return selectList.map((item) => item.name).join('、');
  }, [selectList]);
  return (
    <Dropdown
      className={s.rrulePickerDropdown}
      title={
        <div className={s.value}>
          {value ? (
            renderLabel ? (
              renderLabel(label)
            ) : (
              label
            )
          ) : (
            <Placeholder text={placeholder}></Placeholder>
          )}
        </div>
        // <div>
        //   {/* <Icon
        //     name="icon-sys_Expand"
        //     className={classNames(s.arrow, 'picker-label-arrow', 'icon-sys_open', {
        //       [s.down]: showArrow,
        //     })}
        //   ></Icon> */}
        // </div>
        // <DropdownIcon showArrow={true} hasArrow={true}>
        // </DropdownIcon>
      }
      arrow={
        value ? (
          <OperateClose
            className={`${s.clear__icon} fs-16`}
            style={{ transition: 'unset' }}
            onClick={handleClear}
          />
        ) : (
          true
        )
      }
      overlayClassName={s.rrulePicker}
      trigger="click"
      disabled={disabled}
      defaultOpen={false}
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
      //@ts-ignore
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      getPopupContainer={(dom) => dom.parentNode}
      overlay={
        <div className={classNames('com-dropdown-select', s.panel)}>
          <div className={s.level}>
            {repeatOptions.map((item, index) => {
              return (
                <div
                  key={index}
                  className={classNames(s.item)}
                  onClick={() => {
                    change(item);
                  }}
                >
                  <div className={s.selectLabel}>{item.name}</div>
                  {!selectList?.length && item.value === RepeatType.None && (
                    <Icon className={s.activeIcon} name="icon-sys_check"></Icon>
                  )}
                  {selectList.findIndex((v) => v.value === item.value) > -1 ? (
                    <Icon className={s.activeIcon} name="icon-sys_check"></Icon>
                  ) : null}
                </div>
              );
            })}
          </div>
        </div>
      }
    />
  );
};

export default RrulePicker;
