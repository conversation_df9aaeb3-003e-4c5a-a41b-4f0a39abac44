import { useLatest } from 'ahooks';
import classNames from 'classnames';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Dropdown, Placeholder, RenderPeoples } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { mergeArrays } from '@/components/basic/people-picker/utils';
import { RootState } from '@/models/store';
import { UserInfo } from '@/types';
import { isControlled } from '@/utils';

import s from './index.less';
import Overlay from './overlay';

export type Props = {
  className?: string;
  value?: UserInfo[];
  onChange?: (users: UserInfo[]) => void;
  placeholder?: ReactNode;
  hasArrow?: boolean;
  children?: ReactNode;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  maxShowCount?: number;
  getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
};

export enum QuickType {
  deadline,
  remind,
}

export default function CustomFieldPeople(props: Props) {
  const {
    value,
    onChange,
    placeholder,
    hasArrow,
    children,
    className,
    disabled,
    visible: propVisible,
    onVisible,
    maxShowCount = 6,
    getPopupContainer,
  } = props;
  const valueRef = useLatest(value);

  const { recentContactsList, userInfo } = useSelector((state: RootState) => ({
    recentContactsList: state.user.recentContactsList,
    userInfo: state.user.userInfo,
  }));
  const [selectedPanelUsers, setSelectedPanelUsers] = useState<UserInfo[]>([]);
  const [visible, setVisible] = useState<boolean>();

  const memoVisible = useMemo(() => {
    return isControlled(propVisible) ? propVisible : visible;
  }, [propVisible, visible]);

  const changeVisible = (v: boolean) => {
    if (isControlled(propVisible)) {
      onVisible?.(v);
    } else {
      setVisible(v);
    }
  };

  const changeselectedPanelUsers = (opt: { recentContacts: UserInfo[] }) => {
    const { recentContacts } = opt;
    const _value = valueRef.current || [];
    let topUnSelect: UserInfo[] = [];
    let topSelect =
      [userInfo!]?.filter((item) => {
        if (_value.findIndex((v) => v.uid === item.uid) > -1) {
          return true;
        } else {
          return false;
        }
      }) || [];
    //已选人员value
    topSelect = topSelect.map((item) => ({
      ...item,
      selected: true,
    }));
    const selectValue = _value.map((item) => ({
      ...item,
      selected: true,
    }));
    const _selectedPanelUsers = disabled
      ? []
      : selectedPanelUsers.map((item) => ({
          ...item,
          selected: false,
        }));
    //我(选中) + 已选人人员 + 我(未选中) + 已选人员(取消选中的) + 最近联系人
    const list = [...topSelect, ...selectValue, ..._selectedPanelUsers];
    if (!list.length && !disabled) {
      list.push(...[...topUnSelect, ...recentContacts]);
    }
    // 针对list 去重

    const data = mergeArrays(list) as UserInfo[];
    setSelectedPanelUsers(data);
  };

  useEffect(() => {
    // 打开面板用户不存在时获取最近联系人
    if (memoVisible) {
      changeselectedPanelUsers({ recentContacts: disabled ? [] : recentContactsList || [] });
    }
  }, [memoVisible, disabled, value]);
  return (
    <Dropdown
      className={classNames(s.peoplePickerDropdown, className)}
      title={
        <DropdownIcon showArrow={memoVisible} hasArrow={hasArrow}>
          {children ? (
            children
          ) : value?.length ? (
            <RenderPeoples
              list={value}
              count={value?.length}
              maxShowCount={maxShowCount}
              avatarClassName="mr-4"
            ></RenderPeoples>
          ) : placeholder !== undefined ? (
            <Placeholder text={placeholder}></Placeholder>
          ) : null}
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      defaultOpen={false}
      open={memoVisible}
      onOpenChange={(open) => {
        changeVisible?.(open);
      }}
      disabled={disabled}
      //@ts-ignore
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlayClassName={classNames(s.picker)}
      overlay={
        <Overlay
          value={value}
          onChange={onChange}
          selectedPanelUsers={selectedPanelUsers}
          canSelect={!disabled}
        ></Overlay>
      }
    />
  );
}
