import { TaskInfo, UserInfo } from '@/types'

// 分组配置接口
export interface GroupConfig {
  title: string
  compareFunc?: (val?: any) => boolean
  defaultParams: Record<string, any>
  groupBy?: string
  groupId: string | number
  sortOrder?: number | string
  groupLevel: number
  users?: UserInfo[]
  totalCount?: number
  finishedCount?: number
  value?: any
  hierarchyState?: 'expand' | 'collapse'
}

// 分组配置映射
export interface GroupConfigMap {
  [groupId: string]: GroupConfig
}

// 分组项
export interface GroupedItem extends GroupConfig {
  subtask?: TaskInfo[]
  children?: GroupedItem[]
  totalCount?: number
  finishedCount?: number
  isGroup?: boolean
}

// 分组字段
export interface GroupByField {
  fieldName?: string
  customFieldId?: string
}

// 分组统计
export interface GroupStats {
  totalCount: number
  finishedCount: number
}

// 分组选项
export interface GroupOptions {
  groupMap: Map<string, TaskInfo[]>
  groupStats: Map<string, GroupStats>
  groupDuration: Map<string, any>
  fieldsName?: Record<string, string>
  keepFirstEmpty?: boolean
  keepSecondEmpty?: boolean
  canCreateTask?: boolean | boolean[]
}

// 甘特图任务信息接口
export interface IGanttTaskInfo extends TaskInfo {
  $startTime?: number
  $deadline?: number
  isParent?: boolean
  children?: IGanttTaskInfo[]
}

// 甘特图任务组信息接口
export interface IGanttTaskGroupInfo {
  subtask: IGanttTaskInfo[]
}
