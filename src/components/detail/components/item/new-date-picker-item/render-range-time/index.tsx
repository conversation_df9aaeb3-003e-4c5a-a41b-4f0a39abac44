import classNames from 'classnames';
import dayjs from 'dayjs';
import { useMemo } from 'react';

import { RenderAlarm, RenderRrule, RenderTimeStr } from '@/components/basic';
import {
  getDayEnd,
  getTimePickerFormat,
  PPTimeFormat,
} from '@/components/basic/popo-date-picker/utils';
import { TaskTime } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

interface PickerRenderRangeTimeProps {
  value: TaskTime;
  onChange: (v: TaskTime) => void;
  onClickOther: () => void;
  disabled?: boolean;
  isExpired?: boolean;
  isToday?: boolean;
}
export const PickerRenderRangeTime = (props: PickerRenderRangeTimeProps) => {
  const { onChange, value, onClickOther, disabled, isExpired, isToday } = props;
  const { startTime, deadline, rrule, timeFormat, alarm } = value;
  const onTime = (days: number) => {
    const date = dayjs().add(days, 'day');
    let time = getDayEnd(date).valueOf();
    onChange?.({
      deadline: time,
      timeFormat: PPTimeFormat.olayDay,
      rrule: rrule,
      startTime: 0,
    });
  };

  const memoStr = useMemo(() => {
    const hasHHmm = timeFormat === PPTimeFormat.dateAndTime;
    if (startTime) {
      return `${getTimePickerFormat({
        time: dayjs(startTime),
        hasHHmm,
      })}-${getTimePickerFormat({
        time: dayjs(deadline),
        hasHHmm,
      })}`;
    } else {
      return I18N.template(I18N.auto.end_2, {
        val1: getTimePickerFormat({
          time: dayjs(deadline),
          hasHHmm,
        }),
      });
    }
  }, [startTime, timeFormat, deadline]);

  const noDateItem = disabled ? (
    <span className="com-placeholder">{I18N.auto.nothing}</span>
  ) : (
    <div className={s.noDate}>
      <span className={s.noDateItem} onClick={onClickOther}>
        {I18N.auto.setStartDueDate}
      </span>
      <span
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onTime(0);
        }}
        className={s.noDateItem}
      >
        {I18N.auto.dueToday}
      </span>
      <span
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onTime(1);
        }}
        className={s.noDateItem}
      >
        {I18N.auto.dueTomorrow}
      </span>
    </div>
  );

  return (
    <div
      className={classNames(s.rangeTimePickerWrapper, {
        [s.rangeTimePickerWrapperNoDate]: !deadline,
        [s.disabled]: disabled,
      })}
    >
      {deadline || alarm?.time ? (
        <div className={s.timeStr} onClick={onClickOther}>
          <RenderTimeStr
            startTime={startTime}
            timeFormat={timeFormat}
            deadline={deadline}
            isExpired={isExpired}
            isToday={isToday}
            className={classNames('mr-6')}
          />
          <RenderRrule rrule={rrule} className="ml-6"></RenderRrule>
          <RenderAlarm alarm={alarm} className="mr-6"></RenderAlarm>
        </div>
      ) : (
        noDateItem
      )}
    </div>
  );
};
