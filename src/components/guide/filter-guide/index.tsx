import classNames from 'classnames';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@/components/basic';
import { RootState } from '@/models/store';
import I18N from '@/utils/I18N';
import { CorlorMode } from '@/utils/platform';

import s from './index.less';
interface KanBanGuideProps {
  onClick?: () => void;
  onExpClick?: () => void;
}

export const FilterGuide: React.FC<KanBanGuideProps> = ({ onClick, onExpClick }) => {
  const { corlorMode, language } = useSelector((state: RootState) => ({
    corlorMode: state.user?.corlorMode,
    language: state.user.language,
  }));
  const imgSrc = useMemo(() => {
    return `https://popo.res.netease.com/popo-assets/todo/guide-filter-${
      corlorMode === CorlorMode.light ? 'light' : 'dark'
    }-${language}.gif`;
  }, [corlorMode, language]);
  return (
    <div className={s.wrapper}>
      <div className={s.title}>{I18N.auto.useCustom}</div>
      <div className={s.des}>
        {I18N.templateNode(I18N.auto.itCanAlsoBeBasedOn, {
          val1: <span className={s.bold}>{I18N.auto.taskStatusCutoff}</span>,
        })}
      </div>
      <img className={s.img} src={imgSrc} alt={'img'} />
      <div className={s.btnWrapper}>
        <Button
          className="mr-10 fs-13"
          type="checked-neutral"
          onClick={(e) => {
            e.stopPropagation();
            onClick?.();
          }}
        >
          {I18N.auto.iGotIt}
        </Button>
        <Button
          className={classNames(s.btn, 'fs-13')}
          type="primary"
          onClick={(e) => {
            e.stopPropagation();
            onExpClick?.();
          }}
        >
          {I18N.auto.immediateMedicalExamination}
        </Button>
      </div>
    </div>
  );
};
