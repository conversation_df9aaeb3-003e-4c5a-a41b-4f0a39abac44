# 分组任务编辑行重复问题修复

## 问题描述

在进行用户分组时，同一条任务会出现在不同的人员分组下，对这些重复任务中某一条进行添加子任务操作时，其他任务也会出现编辑行。

## 问题根本原因

**真正的问题**：在分组时，同一个任务对象被添加到多个分组中，它们共享同一个对象引用，特别是共享同一个 `subtask` 数组。当在一个分组中的父任务下添加编辑行时，由于是同一个对象引用，所有分组中的相同父任务都会显示这个编辑行。

## 解决方案

修改 `src/hooks/useGetGroupedList/TaskGroupCollector.ts` 中的 `addTask` 方法，为每个分组创建任务的独立副本：

### 主要修改点：

#### 1. 添加深度复制函数

```typescript
// 深度复制任务数据，确保每个分组中的任务都是独立的副本
function deepCopyTask(task: TaskInfo, groupKey: string): TaskInfo {
  const copySubtasks = (subtasks: TaskInfo[] | undefined): TaskInfo[] => {
    if (!subtasks) return []
    return subtasks.map(subTask => ({
      ...subTask,
      subtask: copySubtasks(subTask.subtask),
      // 为每个子任务也添加分组标识
      _groupKey: groupKey,
    }))
  }

  return {
    ...task,
    subtask: copySubtasks(task.subtask),
    // 保留原始任务的引用，用于后续操作
    _originalTask: task,
    // 添加分组标识，用于区分不同分组中的相同任务
    _groupKey: groupKey,
  }
}
```

#### 2. 修改 addTask 方法

```typescript
addTask(groupKey: string, task: TaskInfo): void {
  // 添加任务到分组
  if (!this.groupMap.has(groupKey)) {
    this.groupMap.set(groupKey, [])
    this.groupStats.set(groupKey, { totalCount: 0, finishedCount: 0 })
  }

  // 为每个分组创建任务的独立副本，避免共享同一个对象引用
  // 这样可以防止在一个分组中添加编辑行时影响其他分组中的相同任务
  const taskCopy = deepCopyTask(task, groupKey)

  this.groupMap.get(groupKey)!.push(taskCopy)

  // 实时更新统计信息
  const stats = this.groupStats.get(groupKey)!
  stats.totalCount += 1
  if (task.finished === FinishedStatus.Finished) {
    stats.finishedCount += 1
  }
}
```

## 技术细节

1. **深度复制**：不仅复制任务对象本身，还递归复制所有子任务，确保每个分组中的任务都有独立的 `subtask` 数组
2. **分组标识**：为每个副本添加 `_groupKey` 标识，用于区分不同分组中的相同任务
3. **原始引用保留**：通过 `_originalTask` 保留对原始任务的引用，便于后续操作
4. **递归处理**：确保多层嵌套的子任务也被正确复制

## 预期效果

修复后，当用户在分组视图中对某个任务添加子任务时：

1. ✅ 只有被点击的那个任务实例会出现编辑行
2. ✅ 其他分组中的相同任务不会受到影响
3. ✅ 每个分组中的任务都有独立的 `subtask` 数组
4. ✅ 编辑行操作不会影响其他分组中的相同任务

## 相关文件

- **主要修改**：`src/hooks/useGetGroupedList/TaskGroupCollector.ts` - 分组任务收集器
- **分组逻辑**：`src/hooks/useGetGroupedList/groupedList.ts` - 分组数据处理
- **更新逻辑**：`src/models/utility/updateTaskInfo.ts` - 任务更新处理（需要考虑副本同步）
