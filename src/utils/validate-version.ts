import { pp } from '@popo-bridge/web';
import { compare } from 'compare-versions';

import I18N from './I18N';
import { Platform } from './platform';

const data = [
  {
    method: 'openTodoLocalDocPicker',
    platformMinVersionMap: {
      mac: '4.0.0 Beta 05',
      windows: '4.4.0',
      linux: undefined, //undefined 表示不支持 空字符串表示都支持
    },
  },
  {
    method: 'downloadTodoAttachment',
    platformMinVersionMap: {
      mac: '4.0.0 Beta 05',
      windows: '4.4.0',
      linux: undefined,
    },
  },
  {
    method: 'getRecentContactsList',
    platformMinVersionMap: {
      mac: '4.2.1',
      windows: '4.14.1',
      linux: undefined,
    },
  },
  {
    method: 'switchSeparateOfTodoPage',
    platformMinVersionMap: {
      mac: '4.2.1',
      windows: '4.14.1',
      linux: undefined,
    },
  },
  {
    method: 'getSeparateOfTodoPage',
    platformMinVersionMap: {
      mac: '4.10.0',
      windows: '4.22.0',
      linux: undefined,
    },
  },
  {
    method: 'chooseIMContacts',
    platformMinVersionMap: {
      mac: '4.0.0',
      windows: '4.0.0',
      linux: undefined,
    },
  },
  //新建日程 项目分享 和复制链接
  {
    method: 'openCalendarCreatePage',
    platformMinVersionMap: {
      mac: '4.17.0',
      windows: '4.29.0',
      linux: undefined,
    },
  },
];

export const validatesVersion = (methods: string) => {
  if (!methods) {
    return false;
  }
  const index = data.findIndex((item) => item.method === methods);
  if (index === -1) {
    return false;
  }
  const item = data[index]!;
  const OS = Platform.OS as 'mac' | 'windows' | 'linux';
  const version = item.platformMinVersionMap[OS];
  if (version !== undefined) {
    return compare(
      Platform.appVersion.replace('_Beta_', '-rc.'),
      version.replace(' Beta ', '-rc.'),
      '>='
    );
  } else {
    return false;
  }
};

export const validatesVersionAndTip = (opt: { methods: string; showTip?: boolean } | string) => {
  let _methods = '';
  let _showTip = true;
  if (typeof opt === 'string') {
    _methods = opt;
  } else {
    const { methods, showTip = true } = opt;
    _methods = methods;
    _showTip = showTip;
  }

  if (validatesVersion(_methods)) {
    return true;
  } else {
    if (_showTip) {
      pp.showToast({
        title: I18N.auto.theCurrentVersionDoesNot,
        iconType: 0,
      });
    }
    return false;
  }
};
