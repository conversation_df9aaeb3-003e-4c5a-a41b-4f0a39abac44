import './icon-blot';

import Quill from 'quill';
import Delta from 'quill-delta';

import { apiTodoCommentDocParsePost } from '@/api';
import { DocParse } from '@/types';
import { DocsRegex } from '../../utils';

export default class PopoDoc {
  quill: Quill;
  options: any;
  container: HTMLElement;
  isCopy: boolean;
  delta?: Delta;
  constructor(quill: Quill, options: any) {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this;
    this.quill = quill;
    this.options = options;
    this.container = document.querySelector(options.container);
    this.delta = undefined;
    quill.on('text-change', async function (delta, oldDelta, source) {
      if (source === 'user') {
        _this.delta = delta;
      }
    });
    quill.root.addEventListener('paste', async (e) => {
      _this.isCopy = true;
      if (_this.delta && _this.isCopy) {
        const delta = _this.delta;
        _this.isCopy = false;
        let ops = delta.ops;
        for (let i = 0; i < ops.length; i++) {
          if (ops[i].insert && typeof ops[i].insert === 'string') {
            let insertedText = ops[i].insert as string;
            //@ts-ignore
            const links = insertedText.match(DocsRegex);
            if (links) {
              const parseData = await apiTodoCommentDocParsePost({
                docUrls: links,
              });
              const DocMap = parseData.reduce((pre, cur) => {
                pre[cur.docUrl!] = cur;
                return pre;
              }, {} as Record<string, DocParse>);
              const textArray = insertedText.split(DocsRegex);
              // TODO 根据links获取需要替换的数据
              //根据textArray来替换文本数据

              let index = (delta.ops[0].retain || 0) as number; //
              if (index > -1) {
                textArray.forEach((text, k) => {
                  // 判断当前的text是不是识别出来的链接
                  if (links.length && links[0] === text && DocMap[text]) {
                    links.shift();
                    const item = DocMap[text] || {};
                    quill.insertEmbed(index, 'popoDoc', {
                      text: item.docName,
                      docUrl: item.docUrl,
                      docIcon: item.docIconUrl || 'https://popo.res.netease.com/emoji/gif/111.gif',
                    });
                    index += 1;
                    quill.deleteText(index, text.length);
                    //判断下个text开头是否含空格 如果不含 增加一个
                    if (k === textArray.length - 1 || !textArray[k + 1].match(/^\s/)) {
                      quill.insertText(index, ' ');
                      index += 1;
                    }
                  } else {
                    index += text.length;
                  }
                });
              }
            }
          }
        }
      }
    });
  }
}

Quill.register('modules/popoDoc', PopoDoc);
