import 'react-quill/dist/quill.snow.css';
import 'react-quill/dist/quill.core.css';
import './plugin/icon-link';
import './plugin/mention';
import './plugin/emoji';

import { InputElement } from '@bedrock/components/lib/Input/InputBase';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import Quill from 'quill';
import Delta from 'quill-delta';
import React, { ReactNode, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { Checkbox, Divider, IconBtn, Message, Scrollbar } from '@/components/basic';
import Emoji, { IEmoji } from '@/components/basic-task/emoji';
import { useOutsideClick } from '@/hooks';
import { DetailTodoInfo, UserInfo } from '@/types';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import { POPOEmojiProps } from './types';
import { dataDelta2POPOtag, getInitEmptyDelta, isEmptyDelta, MentionExtraData } from './utils';
export type Props = {
  className?: string;
  onSubmit?: (v: string, inviteJoinTaskWhenAt: boolean) => void;
  alwaysShowBtn?: boolean;
  reply?: ReactNode;
  placeholder?: string;
  onKeyDown?: React.KeyboardEventHandler<InputElement>;
  value?: Delta;
  onChange?: (v: Delta) => void;
  taskInfo: DetailTodoInfo;
  permissions?: { name: TaskPermissionEnum; value: boolean }[];
};

const InputEditor = React.forwardRef<
  {
    clear: () => void;
  },
  Props
>((props, refs) => {
  const { onSubmit, alwaysShowBtn, reply, placeholder, value, onChange, taskInfo, permissions } =
    props;
  const ref =
    useRef<(v: string, inviteJoinTaskWhenAt: boolean, atFollowers?: UserInfo[]) => void>();
  ref.current = onSubmit;

  const [atPersonList, setAtPersonList] = useState<UserInfo[]>([]);
  const [focus, setFocus] = useState<boolean>(false);
  const [inviteJoinTaskWhenAt, setInviteJoinTaskWhenAt] = useState<boolean>(false);
  const quillRef = useRef<any>(null);
  const quillRangeRef = useRef<any>({ index: 0, length: 0 });
  const atPersonListRef = useRef<UserInfo[]>([]);

  const setQuillFocus = (index: number) => {
    quillRangeRef.current = {
      index: index,
      length: 0,
    };
    quillRef.current.setSelection(quillRangeRef.current);
    quillRef.current?.focus?.();
    setFocus(true);
  };
  const onClickEmoji = (emoji: IEmoji) => {
    quillRef.current.insertEmbed(quillRangeRef.current.index, 'popoEmoji', {
      src: emoji.dynamicPath,
      text: emoji.name,
      key: emoji.key,
    } as POPOEmojiProps);
    setQuillFocus(quillRangeRef.current.index + 1);
  };

  const openMention = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    quillRef.current.insertText(quillRangeRef.current?.index || 0, '@', 'user');
    setQuillFocus(quillRangeRef.current.index + 1);
  };

  const clear = () => {
    quillRef.current.setContents(getInitEmptyDelta());
  };

  const submit = useMemoizedFn(() => {
    let delta = quillRef.current.getContents();
    //校验输入的字数是否超过上限
    const tfData = dataDelta2POPOtag(delta);
    if (tfData.length > 2000) {
      Message.text(I18N.auto.characterCountExceeds);
      return;
    }
    const text = tfData.text.trim();
    if (!text) {
      Message.text(I18N.auto.cannotSendEmpty);
      return;
    }

    quillRef.current?.blur?.();
    setFocus(false);
    // 需要去除文本前面的空格和换行等
    ref.current?.(text, !!inviteJoinTaskWhenAt, atFollowers);
  });

  const open = useMemo(() => {
    return focus || alwaysShowBtn || !isEmptyDelta(value);
  }, [focus, alwaysShowBtn, value]);

  const sendActive = useMemo(() => {
    return !isEmptyDelta(value);
  }, [value]);

  useEffect(() => {
    if (reply) {
      quillRef.current?.focus?.();
    }
  }, [reply]);
  const outsideRef = useOutsideClick<HTMLDivElement>(() => {
    if (quillRef.current.getText() === '\n') {
      setFocus(false);
    }
  });

  useImperativeHandle(refs, () => ({
    clear: clear,
  }));

  useEffect(() => {
    let quill = new Quill('#quillEditor', {
      placeholder: placeholder,
      debug: 'error',
      theme: 'snow',
      modules: {
        toolbar: false,
        popoDoc: {},
        popoEmoji: {},
        popoAt: {},
        keyboard: {
          bindings: {
            customBreak: {
              key: 'Enter', // Enter键的键码
              handler: function () {
                submit();
                return false;
              },
            },
            customKey: {
              key: 'Enter', // 13 代表 Enter 键的键码
              shortKey: true,
              handler: function (range, context) {
                quillRef.current.insertText(range.index || 0, '\n', 'user');
                setQuillFocus(range.index + 1);
              },
            },
          },
        },
        clipboard: {
          matchers: [
            [
              Node.ELEMENT_NODE,
              function (node, delta) {
                if (
                  node.tagName === 'IMG' &&
                  !(
                    node.classList.value.includes('ql-popo-emoji-img') ||
                    node.classList.value.includes('ql-popo-doc-img')
                  )
                ) {
                  // 忽略复制内容中的图片
                  return new Delta();
                }
                return delta;
              },
            ],
          ],
        },
      },
    });
    quill.root.addEventListener('copy', function (e: any) {
      e.preventDefault();
      let clipboardData = e.clipboardData;
      let htmlContent = window.getSelection()?.toString() || ''; // 获取当前选中的内容
      const text = htmlContent.replace(/\uFFFC/g, '').replace(/\n{2}/g, '\n');
      clipboardData.setData('text/html', text); // 设置 HTML 内容到剪贴板
      clipboardData.setData('text/plain', text);
    });
    quill.on('selection-change', function (range, oldRange, source) {
      if (!range) {
        quillRangeRef.current = oldRange;
      }
    });
    quill.on('text-change', function (delta, oldDelta, source) {
      const newDelta = oldDelta.compose(delta);
      onChange?.(newDelta);
      // 遍历Delta 找出@的人
      const atlist: UserInfo[] = [];
      newDelta.forEach((item: any) => {
        if (item.insert && item.insert.popoAt) {
          atlist.push({
            uid: item.insert.popoAt.uid,
            name: item.insert.popoAt.text,
          });
        }
      });
      if (atPersonListRef.current.sort().join() !== atlist.sort().join()) {
        setAtPersonList(atlist);
        atPersonListRef.current = atlist;
      }
    });
    quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node, delta) => {
      //@ts-ignore
      const text = delta.ops[0]?.insert?.popoAt?.text;
      if (text) {
        //@ts-ignore
        const uid = delta.ops[0]?.insert?.popoAt?.uid;
        return getInitEmptyDelta().insert(
          uid === MentionExtraData?.userInfo?.uid ? I18N.auto.me : text
        );
      }
      return delta;
    });
    quill.root.addEventListener('input', () => {
      if (
        quill.root.innerText == '\n' ||
        // quill.root.innerText == ' ' ||
        quill.root.innerText == ''
      ) {
        quill.root.classList.add('ql-blank');
      } else {
        if (quill.root.classList.contains('ql-blank')) {
          quill.root.classList.toggle('ql-blank', false);
        }
      }
    });
    quillRef.current = quill;
  }, []);

  const hasNoParticipant = useMemo(() => {
    return atPersonList
      .filter((v) => !!v.uid && v.uid !== taskInfo?.assigner?.assignerUid)
      .some((item) => taskInfo.participants?.findIndex((v) => v.uid === item.uid) === -1);
  }, [atPersonList, taskInfo.participants, taskInfo?.assigner?.assignerUid]);

  const atFollowers = useMemo(() => {
    return atPersonList
      .filter((v) => !!v.uid && v.uid !== taskInfo?.assigner?.assignerUid)
      .filter((item) => taskInfo?.followers?.findIndex((v) => v.uid === item.uid) !== -1);
  }, [atPersonList, taskInfo?.followers, taskInfo?.assigner?.assignerUid]);

  const memoPermissions = useMemo(() => {
    const [CAN_EDIT] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_EDIT],
    }) as boolean[];
    return {
      CAN_EDIT,
    };
  }, [permissions]);
  return (
    <div
      className={classNames(s.editorBox, open ? s.open : s.close)}
      onClick={(e) => {
        setFocus(true);
        quillRef.current?.focus?.();
      }}
      ref={outsideRef}
    >
      {reply}
      <div
        className={s.editor}
        onClick={() => {
          //setQuillFocus(quillRangeRef.current.index + 1);
        }}
        id="quillEditorBox"
      >
        <Scrollbar autoHeight autoHeightMax={210}>
          <div className={s.quill}>
            <div id="quillEditor" className={classNames(s.quillEditor, 'popo-qiull-editor ')}></div>
          </div>
        </Scrollbar>
      </div>
      <div className={classNames(s.btnBox)}>
        <div className={classNames(s.btnBoxLeft)}>
          <div
            className={classNames(s.otherCheckbox, {
              [s.show]: hasNoParticipant && memoPermissions.CAN_EDIT,
            })}
          >
            <Checkbox
              checked={inviteJoinTaskWhenAt}
              onChange={(e) => {
                setInviteJoinTaskWhenAt(e.target.checked);
              }}
            ></Checkbox>
            <div className={s.tip}>{I18N.auto.nonTaskResponsible}</div>
          </div>
        </div>
        <div className={s.options}>
          <Emoji onClickEmoji={onClickEmoji}>
            <IconBtn
              iconName="icon-pc_comment_emoji"
              title={I18N.auto.expression}
              className="mr-8"
              iconClassName={s.iconColor}
            ></IconBtn>
          </Emoji>
          <IconBtn
            onClick={openMention}
            iconName="icon-pc_comment_aite"
            title={I18N.auto.mention + `metion`}
            iconClassName={s.iconColor}
          ></IconBtn>
          <Divider type="vertical"></Divider>
          <IconBtn
            iconName="icon-input_solid"
            title={I18N.auto.send}
            iconClassName={classNames(s.iconSendColor, { [s.active]: sendActive })}
            onClick={() => {
              if (sendActive) {
                submit();
              }
            }}
          ></IconBtn>
        </div>
      </div>
    </div>
  );
});

export default InputEditor;
