import React, { PropsWithChildren, ReactNode } from 'react';

import { Dropdown, Placeholder } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { UserInfo } from '@/types';

import s from './index.less';
import Overlay from './overlay';

export type Props = {
  className?: string;
  value?: UserInfo;
  onChange?: (user: UserInfo) => void;
  hasArrow?: boolean;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  placeholder?: ReactNode;
  executorList?: UserInfo[]; //执行人列表
  fromCreate?: boolean;
  assignerUid?: string;
  initPanelType?: PanelType;
};

export const enum PanelType {
  init = 'init',
  search = 'searching',
}

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    children,
    value,
    onChange,
    hasArrow,
    disabled,
    visible = false,
    onVisible,
    placeholder,
    fromCreate,
    executorList,
    assignerUid,
    initPanelType = PanelType.init,
  } = props;
  return (
    <div className={s.peoplePickerWrap}>
      <Dropdown
        title={
          <DropdownIcon showArrow={visible} hasArrow={hasArrow}>
            {value?.uid ? (
              children
            ) : placeholder !== undefined ? (
              <Placeholder text={placeholder}></Placeholder>
            ) : null}
          </DropdownIcon>
        }
        trigger="click"
        arrow={false}
        defaultOpen={visible}
        disabled={disabled}
        open={visible}
        onOpenChange={(v) => {
          onVisible?.(v);
        }}
        //@ts-ignore
        destroyPopupOnHide
        minOverlayWidthMatchTrigger={false}
        overlay={
          <Overlay
            value={value}
            onChange={(user) => {
              onChange?.(user);
            }}
            fromCreate={fromCreate}
            executorList={executorList}
            assignerUid={assignerUid}
            initPanelType={initPanelType}
            visible={visible}
          ></Overlay>
        }
      />
    </div>
  );
};

export default PeoplePicker;
