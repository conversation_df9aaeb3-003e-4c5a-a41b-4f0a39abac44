import type { CollisionDetection, DropAnimation, UniqueIdentifier } from '@dnd-kit/core'
import {
  closestCenter,
  defaultDropAnimationSideEffects,
  DndContext,
  DragOverlay,
  getFirstCollision,
  MeasuringStrategy,
  MouseSensor,
  pointerWithin,
  rectIntersection,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { POPOBridgeEmitter, pp } from '@popo-bridge/web'
import classNames from 'classnames'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { unstable_batchedUpdates } from 'react-dom'
import { ErrorBoundary } from 'react-error-boundary'
import { useDispatch, useSelector } from 'react-redux'

import {
  apiTaskResortPost,
  apiTaskUpdateFieldPost,
  apiTodoParticipantUpdatePost,
  apiTodoUpdatePriorityPost,
} from '@/api'
import { taskUpdateDeadline } from '@/api-common'
import { Modal, Radio } from '@/components/basic'
import { LevelSelectOptionsMap } from '@/components/basic/level-picker/utils'
import { allTaskStatus } from '@/components/basic-task/task-status'
import {
  getTaskStatusFn,
  handleRebuildTask,
  isThatLastOneFn,
  selectOperationMethodFn,
  TaskCompleteStatus,
  TaskOperateStatus,
} from '@/components/basic-task/task-status/use-complete-task-status'
import useRefs from '@/hooks/useRefs'
import { Dispatch, RootState } from '@/models/store'
import { ILaneMapData, TaskGroupItem, TodoInfo } from '@/types'
import { FieldTypeEnum } from '@/types/custom-field'
import { EnumEmitter, OneAndMoreServerParamsType, OneAndMoreType, OrderCustom } from '@/utils/const'
import { EnumField, EnumGroupBy } from '@/utils/fields'
import I18N from '@/utils/I18N'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'
import scout from '@/utils/scout'
import { EnumTrackeKey } from '@/utils/skyline'

import { validateTask } from '../../list/utils'
import Card, { CardType } from './card'
import DroppableContainer from './droppable-lane'
import s from './index.less'
import MemoList from './memo-list'
import { init_page_size, isProjectId, isTaskMenuId } from '@/models/utils'
// import { coordinateGetter } from './multipleContainersKeyboardCoordinates';

export interface KanbanProps {}

const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
}

const findContainer = (items: Record<string, TodoInfo[]>, id: UniqueIdentifier) => {
  if (id in items) {
    return id
  }
  return Object.keys(items).find(key => items[key].findIndex(item => item?.id === id) > -1)
}

export const TRASH_ID = 'void'

const Kanban: React.FC<KanbanProps> = () => {
  const scrollbarRef = useRef()
  const recentlyMovedToNewContainer = useRef(false)
  const [getBtnRef, removeBtnRef] = useRefs<HTMLDivElement>()
  const checkRadioRef = useRef<TaskOperateStatus>()
  const moveRef = useRef<boolean>(false)
  const dispatch = useDispatch<Dispatch>()
  const { groupList, laneMapData, orderBy, queryGroupBy, userInfo, visibleDetail, permissions, navigatorId } =
    useSelector((state: RootState) => ({
      groupList: state.kanban.groupList,
      laneMapData: state.kanban.laneMapData,
      orderBy: state.viewSetting.currentViewTab?.querySort?.fieldName,
      queryGroupBy: state.viewSetting.currentViewTab.queryGroupBy,
      userInfo: state.user.userInfo,
      visibleDetail: state.detail.visibleDetail,
      permissions: state.viewSetting.permissions,
      navigatorId: state.viewSetting.navigatorId,
      customFields: state.viewSetting.customFields,
    }))
  const clonedDataRef = useRef<ILaneMapData>(laneMapData)
  const clonedGroupListRef = useRef<TaskGroupItem[]>(groupList)
  const [activeId, setActiveId] = useState<string | number | null>(null)
  const lastOverId = useRef<UniqueIdentifier | null>(null)

  const showAddBtn = useMemo(() => {
    return validatesPermission({
      permissions: permissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    })
  }, [permissions])

  const activeItem = useMemo(() => {
    let item = {}
    if (activeId) {
      Object.keys(laneMapData).forEach(container => {
        const items = laneMapData[container].filter(item => item?.id === activeId)
        if (items.length) {
          item = items[0]
        }
      })
    }
    return item
  }, [activeId, laneMapData])

  const loadMore = (id: string) => {
    dispatch.kanban.loadMoreTaskForGroupId({
      groupId: id,
    })
  }

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 4,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        distance: 4,
      },
    }),
  )

  useEffect(() => {
    POPOBridgeEmitter.addListener(EnumEmitter.AddKanbanAndScrollRight, x => {
      const element = scrollbarRef.current as unknown as HTMLElement
      if (element) {
        element.scrollLeft = x === undefined ? element.scrollWidth : x
      }
    })
    POPOBridgeEmitter.addListener(EnumEmitter.AddKanbanLaneAndScrollTop, (opt: { groupId: string; index: number }) => {
      const { groupId, index = 0 } = opt
      getBtnRef(groupId).current?.scrollToIndex({
        index: index,
        align: 'top',
        behavior: 'auto',
      })
    })
  }, [scrollbarRef])

  const collisionDetectionStrategy: CollisionDetection = useCallback(
    args => {
      const items = Object.keys(laneMapData)
      if (activeId && activeId in items) {
        return closestCenter({
          ...args,
          droppableContainers: args.droppableContainers.filter(container => container.id in items),
        })
      }

      // Start by finding any intersecting droppable
      const pointerIntersections = pointerWithin(args)
      const intersections =
        pointerIntersections.length > 0
          ? // If there are droppables intersecting with the pointer, return those
            pointerIntersections
          : rectIntersection(args)
      let overId = getFirstCollision(intersections, 'id')
      if (overId != null) {
        if (overId === TRASH_ID) {
          // If the intersecting droppable is the trash, return early
          // Remove this if you're not using trashable functionality in your app
          return intersections
        }

        if (overId in items) {
          const containerItems = laneMapData[overId]
          if (containerItems.length > 0) {
            overId = closestCenter({
              ...args,
              droppableContainers: args.droppableContainers.filter(
                container => container.id !== overId && containerItems.some(item => item.id === container.id),
              ),
            })[0]?.id
          }
        }

        lastOverId.current = overId

        return [{ id: overId }]
      }
      // When a draggable item moves to a new container, the layout may shift
      // and the `overId` may become `null`. We manually set the cached `lastOverId`
      // to the id of the draggable item that was moved to the new container, otherwise
      // the previous `overId` will be returned which can cause items to incorrectly shift positions
      if (recentlyMovedToNewContainer.current) {
        lastOverId.current = activeId
      }

      // If no droppable is matched, return the last match
      return lastOverId.current ? [{ id: lastOverId.current }] : []
    },
    [activeId, laneMapData],
  )

  const onCancel = () => {
    if (Object.keys(clonedDataRef.current).length) {
      // Reset items to their original state in case items have been
      // Dragged across containers
      dispatch.kanban.setLaneMapData(clonedDataRef.current)
      dispatch.kanban.setGroupList(clonedGroupListRef.current)
    }
    setActiveId(null)
    clonedDataRef.current = {}
    clonedGroupListRef.current = []
  }

  // useEffect(() => {
  //   requestAnimationFrame(() => {
  //     recentlyMovedToNewContainer.current = false;
  //   });
  // }, [laneMapData]);

  return (
    <ErrorBoundary
      fallback={<div></div>}
      onError={() => {
        scout.captureError({
          name: 'KANBAN-Render-Error',
          //@i18n-ignore
          message: '看板渲染异常',
          stack: '',
        })
        dispatch.user.trackingByView({ key: EnumTrackeKey.Error, msg: 'kanban-error' })
        //pp.showToast({ title: I18N.auto.reloadingInProgress });
        //window.location.reload();
      }}
    >
      <div className={s.kanban}>
        <DndContext
          sensors={sensors}
          collisionDetection={collisionDetectionStrategy}
          measuring={{
            droppable: {
              strategy: MeasuringStrategy.Always,
            },
          }}
          onDragStart={({ active }) => {
            moveRef.current = true
            setActiveId(active.id as string)
            clonedDataRef.current = laneMapData
            clonedGroupListRef.current = groupList
          }}
          onDragOver={({ active, over }) => {
            const overId = over?.id
            const activeId = active.id
            if (overId == null) {
              return
            }
            //获取2个元素的泳道  判断是否是不同泳道
            const activeContainerId = findContainer(laneMapData, activeId)
            const overContainerId = findContainer(laneMapData, overId)
            if (!activeContainerId || !overContainerId) {
              return
            }

            if (activeContainerId !== overContainerId) {
              //非同一个泳道操作
              let newIndex = 0
              const activeItems = laneMapData[activeContainerId]
              const overItems = laneMapData[overContainerId]
              const overIndex = overItems.findIndex(item => item.id === overId)
              const activeIndex = activeItems.findIndex(item => item.id === activeId)
              // 把当前的activeItem取出来
              if (overId === overContainerId) {
                //触发的是泳道 直接在当前泳道追加
                // 在overContainer泳道追加数据
                newIndex = overItems.length + 1
              } else {
                // 指定位置添加
                newIndex = overIndex
              }
              recentlyMovedToNewContainer.current = true
              const _data = {
                ...laneMapData,
                [activeContainerId]: laneMapData[activeContainerId].filter(item => item.id !== activeId),

                [overContainerId]: [
                  ...laneMapData[overContainerId].slice(0, newIndex),
                  laneMapData[activeContainerId][activeIndex],
                  ...laneMapData[overContainerId].slice(newIndex, laneMapData[overContainerId].length),
                ],
              }
              dispatch.kanban.setLaneMapData(_data)
            }
          }}
          onDragEnd={async ({ active, over }) => {
            const overId = over?.id
            const activeId = active.id
            const taskId = active.id as number

            if ((activeId === overId && !recentlyMovedToNewContainer.current) || overId == null) {
              onCancel()
              return
            }
            recentlyMovedToNewContainer.current = false

            //获取2个元素的泳道
            const activeContainerId = findContainer(clonedDataRef.current, activeId)
            const overContainerId = findContainer(laneMapData, overId)
            if (!activeContainerId || !overContainerId) {
              return
            }
            //用来刷新泳道数据
            let groupIds: string[] = []
            //不同泳道
            if (activeContainerId !== overContainerId) {
              groupIds = [activeContainerId as string, overContainerId as string]
              //非同一个泳道操作
              let newIndex = 0
              const activeItems = clonedDataRef.current[activeContainerId]
              const overItems = laneMapData[overContainerId]
              const overIndex = overItems.findIndex(item => item.id === overId)
              const activeIndex = activeItems.findIndex(item => item.id === activeId)

              // 把当前的activeItem取出来
              if (overId === overContainerId) {
                //触发的是泳道 直接在当前泳道追加
                // 在overContainer泳道追加数据
                newIndex = overItems.length + 1
              } else {
                // 指定位置添加
                newIndex = overIndex
              }
              const _data = {
                ...clonedDataRef.current,
                [activeContainerId]: clonedDataRef.current[activeContainerId].filter(item => item.id !== activeId),

                [overContainerId]: [
                  ...clonedDataRef.current[overContainerId].slice(0, newIndex),
                  clonedDataRef.current[activeContainerId][activeIndex],
                  ...clonedDataRef.current[overContainerId].slice(
                    newIndex,
                    clonedDataRef.current[overContainerId].length,
                  ),
                ],
              }
              let activeGroupItem: TaskGroupItem
              const _groupList = groupList.map(item => {
                if (item.groupId === activeContainerId) {
                  return {
                    ...item,
                    count: item.count - 1,
                  }
                } else if (item.groupId === overContainerId) {
                  activeGroupItem = item
                  return {
                    ...item,
                    count: item.count + 1,
                  }
                }
                return item
              })
              //assigneeUids 在groupBy = EnumGroupBy.ASSIGNEE才会有
              const { defaultParams, assigneeUids, priority, groupId } = activeGroupItem!
              if (!groupId) {
                return
              }
              // 先临时替换 之后在调用接口刷新
              unstable_batchedUpdates(() => {
                dispatch.kanban.setGroupList(_groupList)
                dispatch.kanban.setLaneMapData(_data)
              })
              if (queryGroupBy?.customFieldId) {
                try {
                  if (defaultParams?.customFieldValues?.values?.[queryGroupBy?.customFieldId]) {
                    await apiTaskUpdateFieldPost({
                      fieldValue: {
                        ...defaultParams.customFieldValues.values[queryGroupBy?.customFieldId],
                      },
                      taskId: taskId,
                    })
                  }
                } catch (error) {
                  console.log(error)
                }
              } else {
                if (queryGroupBy?.fieldName === EnumGroupBy.ASSIGNEE) {
                  try {
                    await apiTodoParticipantUpdatePost({
                      taskId: taskId,
                      uids: assigneeUids,
                    })
                  } catch (error) {
                    console.log(error)
                  }
                }

                if (queryGroupBy?.fieldName === EnumGroupBy.DEADLINE) {
                  try {
                    await taskUpdateDeadline({
                      startTime: defaultParams?.deadline ? 0 : undefined,
                      taskId: taskId,
                      ...defaultParams,
                    })
                  } catch (error) {
                    console.log(error)
                  }
                }
                if (queryGroupBy?.fieldName === EnumGroupBy.FINISHED) {
                  const _activeItem = activeItems[activeIndex]
                  const {
                    completeCondition,
                    participantCount,
                    finishedParticipantCount,
                    selfFinished,
                    finished,
                    isCoordinator,
                    isCreator,
                    assigneeUids,
                  } = _activeItem
                  const _oneAndMoreType =
                    completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one
                  const isThatLastOne = isThatLastOneFn({
                    oneAndMoreType: _oneAndMoreType,

                    participantCount: participantCount || 0,
                    finishedParticipantCount: finishedParticipantCount || 0,
                    selfFinished: selfFinished,
                    finished: finished,
                  })
                  const taskState = getTaskStatusFn({
                    oneAndMoreType: _oneAndMoreType,
                    isCoordinator,
                    isCreator,
                    selfFinished,
                    finished,
                    assigneeUids,
                    userInfo: userInfo!,
                    isThatLastOne,
                  })
                  if (
                    taskState === TaskCompleteStatus.InstantaneousFinished ||
                    taskState === TaskCompleteStatus.InstantaneousUnfinished
                  ) {
                    //TODO disabled 如果有完成权限
                    selectOperationMethodFn({
                      ..._activeItem,
                      visibleDetail,
                      taskId: _activeItem.taskId!,
                      taskState,
                      loadingRef: undefined,
                      dispatch,
                      userInfo: userInfo!,
                      isThatLastOne,
                      onCancel: () => {
                        onCancel()
                      },
                    })
                  } else if (taskState === TaskCompleteStatus.RebuildMeOrCompleteAll) {
                    //UX 要求此情况只重启个人任务即可
                    handleRebuildTask({
                      ..._activeItem,
                      isThatLastOne,
                      visibleDetail,
                      taskId: _activeItem.taskId!,
                      taskState,
                      loadingRef: undefined,
                      dispatch,
                      userInfo: userInfo!,
                    })
                  } else {
                    //TODO需要弹框
                    const options = allTaskStatus[taskState]?.options.map(item => ({
                      name: item.name,
                      value: item.key,
                    }))
                    checkRadioRef.current = options[0].value
                    await new Promise(resolve => {
                      Modal.info({
                        getContainer: () => document.body,
                        title: I18N.auto.chooseToCompleteOr,
                        width: 400,
                        className: 'statusModal',
                        content: (
                          <div>
                            <Radio.Group
                              options={options}
                              defaultValue={options[0].value}
                              onChange={e => {
                                checkRadioRef.current = e.target.value
                              }}
                              className={s.statusModalRadioGroup}
                            />
                          </div>
                        ),

                        centered: true,
                        closable: false,
                        onOk: () => {
                          selectOperationMethodFn({
                            ..._activeItem,
                            taskId: _activeItem.taskId!,
                            taskState,
                            loadingRef: undefined,
                            dispatch,
                            visibleDetail,
                            userInfo: userInfo!,
                            isThatLastOne,
                            key: checkRadioRef.current,
                          })
                          resolve(true)
                        },
                        onCancel: () => {
                          onCancel()
                          resolve(true)
                        },
                        zIndex: 10000,
                      })
                    })
                  }
                  //暂时先这样阻止, 完成和取消完成内部集成了刷新逻辑
                  return
                }
                if (queryGroupBy?.fieldName === EnumGroupBy.PRIORITY) {
                  try {
                    await apiTodoUpdatePriorityPost({
                      taskId: taskId,
                      priority: priority!,
                    })
                  } catch (error) {
                    console.log(error)
                  }
                }
              }

              //跨组自定义
              const _list = _data[overContainerId]
              if (orderBy === OrderCustom) {
                let preId = undefined
                let nextId = undefined
                for (let index = 0; index < _list.length; index++) {
                  const item = _list[index]
                  if (item.taskId === active.id) {
                    const pre = _list[index - 1]
                    const next = _list[index + 1]
                    if (pre?.taskId && validateTask(pre)) {
                      preId = pre.taskId
                    }
                    if (next?.taskId && validateTask(pre)) {
                      nextId = next.taskId
                    }
                  }
                }
                try {
                  if (navigatorId && isProjectId(navigatorId)) {
                    dispatch.viewSetting.updateProjectTaskOrder({
                      orderParam: {
                        preTaskId: preId,
                        tailTaskId: nextId,
                        currentTaskId: taskId,
                      },
                      hasViewChanged: true,
                    })
                    // 这里调用一次scroll-query只是为了获取更新后的任务数据
                    dispatch.kanban
                      .getTaskForGroupId({
                        groupId: groupId,
                        size: init_page_size,
                      })
                      .then(res => {
                        let dragItem = res?.list?.find?.(item => item.taskId === taskId)
                        if (dragItem) {
                          const __data = {
                            ...clonedDataRef.current,
                            [activeContainerId]: clonedDataRef.current[activeContainerId].filter(
                              item => item.id !== activeId,
                            ),

                            [overContainerId]: [
                              ...clonedDataRef.current[overContainerId].slice(0, newIndex),
                              dragItem,
                              ...clonedDataRef.current[overContainerId].slice(
                                newIndex,
                                clonedDataRef.current[overContainerId].length,
                              ),
                            ],
                          }
                          dispatch.kanban.setLaneMapData(__data)
                        }
                      })
                  } else if (isTaskMenuId(navigatorId)) {
                    await apiTaskResortPost({
                      currentTaskId: taskId,
                      preTaskId: preId,
                      tailTaskId: nextId,
                    })
                  }
                } catch (error) {
                  console.log(error)
                }
              }
            } else {
              groupIds = [activeContainerId as string]
              //同泳道 自定义排序就交换位置
              if (orderBy === OrderCustom) {
                const activeItems = laneMapData[activeContainerId]
                const overIndex = activeItems.findIndex(item => item.id === overId)
                const activeIndex = activeItems.findIndex(item => item.id === activeId)
                const _list = arrayMove(activeItems, activeIndex, overIndex)
                const _data = {
                  ...laneMapData,
                  [activeContainerId]: _list,
                }
                dispatch.kanban.setLaneMapData(_data)
                let preId = undefined
                let nextId = undefined
                for (let index = 0; index < _list.length; index++) {
                  const item = _list[index]
                  if (item.taskId === active.id) {
                    const pre = _list[index - 1]
                    const next = _list[index + 1]
                    if (pre?.taskId && validateTask(pre)) {
                      preId = pre.taskId
                    }
                    if (next?.taskId && validateTask(pre)) {
                      nextId = next.taskId
                    }
                  }
                }
                try {
                  if (navigatorId && isProjectId(navigatorId)) {
                    dispatch.viewSetting.updateProjectTaskOrder({
                      orderParam: {
                        preTaskId: preId,
                        tailTaskId: nextId,
                        currentTaskId: taskId,
                      },
                      hasViewChanged: true,
                    })
                  } else if (isTaskMenuId(navigatorId)) {
                    await apiTaskResortPost({
                      currentTaskId: taskId,
                      preTaskId: preId,
                      tailTaskId: nextId,
                    })
                  }
                } catch (error) {
                  console.log(error)
                }
              }
            }
            // 更新看板分组数量,以及当前加载的泳道数据
            // 只有当非自定义排序及项目下任务的时候才会刷新
            if (!(isProjectId(navigatorId) && orderBy === OrderCustom)) {
              dispatch.kanban.refreshListByGroups({
                groupIds: groupIds,
                isCurrentSize: true,
              })
            }
            setTimeout(() => {
              moveRef.current = false
            }, 0)
          }}
          onDragCancel={onCancel}
        >
          <div className={`${s.lane} line__wrapper`} ref={scrollbarRef}>
            {groupList?.map(item => {
              const sortableItems = laneMapData[item.id!] || []
              // 追加每一行的新增按钮
              return (
                <DroppableContainer id={item.id!} items={groupList} key={item.groupId}>
                  <SortableContext items={sortableItems} strategy={verticalListSortingStrategy}>
                    <div
                      className={classNames('kanban-lane-no-data', s.laneBody, {
                        [s.noDataLaneBody]: !sortableItems.length,
                      })}
                    >
                      <MemoList
                        list={sortableItems}
                        getBtnRef={getBtnRef}
                        showAddBtn={showAddBtn}
                        groupInfo={item}
                        loadMore={loadMore}
                        moving={moveRef.current}
                        navigatorId={navigatorId!}
                      />
                    </div>
                  </SortableContext>
                </DroppableContainer>
              )
            })}
          </div>

          <DragOverlay adjustScale={false} dropAnimation={dropAnimation}>
            {activeId ? <Card type={CardType.onlyShow} data={activeItem} className={s.dragCard} /> : null}
          </DragOverlay>
        </DndContext>
      </div>
    </ErrorBoundary>
  )
}

export default Kanban
