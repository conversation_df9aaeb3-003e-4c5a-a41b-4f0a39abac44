import { SystemInfo } from '@babylon/popo-icons'
import { Form, Input } from '@bedrock/components'
import { SelectValue } from '@bedrock/components/lib/Select'
import classNames from 'classnames'
import React, { PropsWithChildren, ReactElement, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { apiProjectViewCreatePost } from '@/api'
import { getBatchViewCheckParams } from '@/api-common'
import { Message } from '@/components/basic'
import Icon from '@/components/basic/Icon'
import Modal from '@/components/basic/modal'
import Select from '@/components/basic/select'
import Tooltip from '@/components/basic/tooltip'
import { Dispatch, RootState } from '@/models/store'
import { ViewTab } from '@/types'
import { parseViewConfig } from '@/utils'
import { UNKNOW_GROUP_BY, ViewType } from '@/utils/const'
import I18N from '@/utils/I18N'
import { ProjectPermissionEnum, validatesPermission, ViewTypeEnum } from '@/utils/permission'

import s from './index.less'
import { useParams } from 'umi'
import { getIsInSession, isProjectId } from '@/models/utils'

interface Props {
  className?: string
}

export const SaveAsView: React.FC<PropsWithChildren<Props>> = props => {
  const { children } = props
  const [visible, setVisible] = useState<boolean>(false)
  const { projectInfo, permissions, viewTabList, currentViewTab } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo!,
    permissions: state.viewSetting.permissions,
    viewTabList: state.viewSetting.viewTabList,
    currentViewTab: state.viewSetting.currentViewTab,
  }))

  const { id: projectId } = useParams()

  const inputRef = useRef<HTMLInputElement>()
  const CAN_CREATE_COMMON_VIEW = validatesPermission({
    permissions: permissions,
    key: ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
  })

  const dispatch = useDispatch<Dispatch>()
  const [inputValue, setInputValue] = useState(currentViewTab.name)

  const [canCreateCommonView, canCreatePersonView] = validatesPermission({
    permissions,
    key: [ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW, ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW],
  }) as boolean[]

  const [form] = Form.useForm()
  const _param = dispatch.viewSetting.getComParams({})
  const { navigatorId } = _param

  const [commonViewExceed, personalViewExceed] = useMemo(() => {
    const commonViewCount = viewTabList.filter(view => view.viewDistribution !== ViewTypeEnum.Personal).length
    const personalViewCount = viewTabList.length - commonViewCount
    return [commonViewCount >= 20, personalViewCount >= 20]
  }, [viewTabList])

  const [canCreateCommon, canCreatePerson] = useMemo(() => {
    return [canCreateCommonView && !commonViewExceed, canCreatePersonView && !personalViewExceed]
  }, [canCreateCommonView, canCreatePersonView, commonViewExceed, personalViewExceed])

  const addDisabledTip = useMemo(() => {
    if (canCreateCommonView && canCreatePersonView) {
      if (commonViewExceed && personalViewExceed) {
        return I18N.auto.publicView_3
      }
    } else if (canCreatePersonView) {
      if (personalViewExceed) {
        return I18N.auto.numberOfPersonalViews
      }
    }
    return ''
  }, [commonViewExceed, personalViewExceed, canCreateCommonView, canCreatePersonView])

  const options = [
    {
      name: I18N.auto.personalView,
      value: ViewTypeEnum.Personal,
      subText: I18N.auto.onlyYouCanDoItYourself,
      disabled: personalViewExceed,
    },
    {
      name: I18N.auto.publicView_2,
      value: ViewTypeEnum.Common,
      subText: I18N.auto.allMembersAre,
      disabled: commonViewExceed,
    },
  ]

  const activeSelectOption = useMemo(() => {
    return currentViewTab.viewDistribution === ViewTypeEnum.Personal
      ? canCreatePerson
        ? ViewTypeEnum.Personal
        : ViewTypeEnum.Common
      : canCreateCommon
      ? ViewTypeEnum.Common
      : ViewTypeEnum.Personal
  }, [currentViewTab.viewDistribution, canCreatePerson, canCreateCommon])

  useEffect(() => {
    setInputValue(currentViewTab.name)
  }, [currentViewTab])

  const onSubmit = () => {
    const payload = {
      projectId: projectInfo.projectId! || projectId,
      viewType: currentViewTab.viewType!,
      viewDistribution: form.getFieldValue('viewDistribution'),
      name: inputValue,
      navigatorId: String(navigatorId),
      queryGroupBy: currentViewTab.queryGroupBy,
      querySort: currentViewTab.querySort,
      conditions: currentViewTab.conditions,
      displays: currentViewTab.displays,
      showWeekend: currentViewTab.showWeekend,
      viewConfig: currentViewTab.viewConfig,
      viewSort: {},
    }

    if (currentViewTab.viewType === ViewType.timeline) {
      //@ts-ignore
      payload.queryGroupBys = currentViewTab.queryGroupBys
    }

    if (navigatorId && isProjectId(navigatorId) && (currentViewTab?.querySort as any)?.fieldName === 'customize') {
      payload.viewSort = {
        viewId: _param.viewId,
        ops: currentViewTab.projectTaskResortParamsList,
      }
    }

    // 更新接口
    apiProjectViewCreatePost(payload)
      .then(async res => {
        parseViewConfig(res)
        // 将item塞入viewTabList中，之后调用batchCheck接口获取当前用户对于这个item的编辑删除权限数据
        let currentViewList = [{ ...res, type: res.viewType }]
        const viewPermissions = await getBatchViewCheckParams(currentViewList)
        currentViewList.forEach(item => {
          // @ts-ignore
          item.permissions = viewPermissions[item.viewId!].permissions
          //@ts-ignore
          if (!item.queryGroupBy?.fieldName || item.queryGroupBy?.fieldName === UNKNOW_GROUP_BY) {
            item.queryGroupBy = {}
          }
        })
        dispatch.viewSetting.setViewTabList([...(viewTabList as ViewTab[])!, ...(currentViewList as ViewTab[])])

        dispatch.viewSetting.toggeleView(currentViewList[0] as ViewTab)
      })
      .catch(() => {
        Message.success(I18N.auto.saveAsNewVision)
      })
      .finally(() => {
        setVisible(false)
      })
  }

  useEffect(() => {
    return () => {
      form.resetFields()
      inputRef.current?.focus()
    }
  }, [form, visible])

  return (
    <>
      <Tooltip title={addDisabledTip}>
        {React.cloneElement(children as unknown as ReactElement, {
          onClick: () => {
            if (canCreateCommon || canCreatePerson) {
              setVisible(!visible)
            }
          },
          className: !canCreateCommon && !canCreatePerson ? s.disabled : children?.props?.className,
        })}
      </Tooltip>

      <Modal
        visible={visible}
        width={400}
        title={I18N.auto.saveAsNewVision_2}
        okText={I18N.auto.determine}
        closable={false}
        mask={!getIsInSession()}
        centered
        onCancel={() => {
          setVisible(false)
        }}
        okButtonProps={{ disabled: !inputValue }}
        onOk={onSubmit}
        scrolled={false}
        wrapClassName={classNames(s.modal, s.saveAsModal)}
      >
        <Form form={form}>
          <Form.Item label={I18N.auto.viewName}>
            <Input
              ref={inputRef}
              placeholder={I18N.auto.enterViewName}
              autoFocus
              value={inputValue}
              onChange={e => {
                setInputValue(e.target.value)
              }}
            />
          </Form.Item>

          <Form.Item label={I18N.auto.viewRange} name="viewDistribution" initialValue={activeSelectOption}>
            {CAN_CREATE_COMMON_VIEW ? (
              <Select
                options={options}
                dropdownClassName={s.viewModeSelect}
                renderLabel={option => {
                  const isCommon = option.value === ViewTypeEnum.Common
                  const disabledTitle =
                    (isCommon && commonViewExceed) || (!isCommon && personalViewExceed)
                      ? I18N.auto.thisTypeHasReached
                      : ''
                  return (
                    <Tooltip title={disabledTitle}>
                      <div
                        className={classNames(s.option, {
                          [s.disabledOption]: disabledTitle,
                        })}
                      >
                        <div className={s.text}>{option.name}</div>
                        <div className={s.subText}>{option.subText}</div>
                      </div>
                    </Tooltip>
                  )
                }}
              />
            ) : (
              <div className={s.personalViewTip}>
                <span className={s.tip}>{I18N.auto.personalView}</span>
                <Tooltip title={I18N.auto.thisViewIsFor}>
                  <SystemInfo className="fs-16" />
                </Tooltip>
              </div>
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
