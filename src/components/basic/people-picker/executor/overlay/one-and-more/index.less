.oneAndMore {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  .btn {
    display: inline-flex;
    align-items: center;
    height: 28px;
    padding: 4px 4px;
    line-height: 20px;
    border-radius: 4px;
    color: var(--TextPrimary);
    cursor: pointer;
    font-size: 13px;
    &:hover {
      background-color: var(--aBlack6);
    }
    .arrow {
      transform: rotate(-90deg);
      margin-left: 4px;
      color: var(--IconTertiary);
      transition: all 0.3s;
    }
    .openArrow {
      transform: rotate(90deg);
    }
  }
  .readonlyBtn {
    height: 36px;
    padding: 8px;
    font-size: 13px;
    color: var(--TextPrimary);
  }
}
.oneAndMoreDropdownSelect {
  border: 1px solid var(--aBlack12);
}
