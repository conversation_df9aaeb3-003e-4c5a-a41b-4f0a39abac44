import { Divider, Icon } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  onOpenDetail?: () => void;
}

const AddTaskMessage: React.FC<Props> = (props) => {
  const { onOpenDetail } = props;
  return (
    <div className={s.info}>
      <Icon name="icon-taskstate_nodownhover" fontSize={20} className={s.infoIcon}></Icon>
      {I18N.auto.newSuccessfully}
      <Divider type="vertical" className={s.divider}></Divider>
      <span
        className={s.view}
        onClick={(e) => {
          e.stopPropagation();
          onOpenDetail?.();

          const target = e.target as HTMLElement;
          const noticeEl = target.closest('.rock-message-notice');

          if (noticeEl) {
            noticeEl.remove();
          }
        }}
      >
        {I18N.auto.viewDetails}
      </span>
    </div>
  );
};

export default AddTaskMessage;
