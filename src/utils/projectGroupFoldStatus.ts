import { rIC } from './requestIdleCallback';
const PROJECT_GROUP_FOLD_STATUS_KEY = 'PROJECT_GROUP_FOLD_STATUS_KEY';

type IdsType = (string | number)[];

/**
 * 项目分组折叠状态管理类
 */
class ProjectGroupFoldStatus {
  foldStatusIds: IdsType = [];
  initialized = false;

  /**
   * 获取所有折叠状态
   * @returns 折叠状态映射
   */
  getFoldStatus(): IdsType {
    try {
      const str = localStorage.getItem(PROJECT_GROUP_FOLD_STATUS_KEY);
      if (str) {
        this.foldStatusIds = JSON.parse(str).map((item: any) => Number(item));
      }
    } catch (error) {
      console.error('解析项目分组折叠状态失败', error);
    }
    return this.foldStatusIds;
  }

  /**
   * 获取指定ID的折叠状态
   * @param id 分组ID
   * @returns 是否折叠，默认为false
   */
  getFoldStatusById(id: string | number): boolean {
    return !this.getFoldStatus()?.has(id);
  }

  isFold(id: string | number) {
    return this.getFoldStatusById(id);
  }

  /**
   * 设置折叠状态
   * @param id 分组ID
   * @param fold 是否折叠
   */
  setFoldStatus({ ids }: { ids?: IdsType }): void {
    rIC(() => {
      try {
        localStorage.setItem(PROJECT_GROUP_FOLD_STATUS_KEY, JSON.stringify(ids));
      } catch (error) {
        console.error('保存项目分组折叠状态失败', error);
      }
    });
  }
}

// 只创建一个实例并导出
export default new ProjectGroupFoldStatus();
