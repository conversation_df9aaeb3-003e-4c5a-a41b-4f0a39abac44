.view__selector {
  min-width: 113px;
  max-width: 202px;

  .selected__item {
    column-gap: 4px;
    color: var(--TextSecondary);
    font-size: 13px;
    min-width: 0;
  }

  :global {
    .rock-select-selector {
      background-color: transparent;
    }
  }
}

.view__dropdown {
  padding-left: 4px;
  padding-right: 4px;
  max-height: calc(100vh - 50px);
  // overflow: auto;

  :global {
    .rock-scrollbar-view {
      max-height: 160px !important;
    }

    .rock-select-item {
      border-radius: 4px;
    }
  }
}

.label__item {
  column-gap: 6px;
  color: var(--TextPrimary);

  &--icon {
    color: var(--TextSecondary);
  }
}
