import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/models/store';
import { combineDisplayFields, DisplayField } from '@/utils/fields';

/**
 * 获取字段配置中要展示的字段, 包括自定义字段、未开启展示的字段
 * @returns
 */
const useGetShownFields = () => {
  const {
    displays = [],
    navigatorId,
    customFields,
  } = useSelector((state: RootState) => {
    let view = state.viewSetting.currentViewTab;
    return {
      displays: view.displays,
      customFields: state.viewSetting.customFields,
      navigatorId: state.viewSetting.navigatorId,
    };
  });

  const list = useMemo(
    () => combineDisplayFields(displays as DisplayField[], customFields, navigatorId),
    [displays, customFields, navigatorId]
  );

  return list;
};

export default useGetShownFields;
