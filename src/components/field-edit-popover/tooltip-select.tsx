import React from 'react';

import { Select, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import { FiledTypeList } from './const';
import s from './index.less';

interface ToolTipSelectProps {
  isEdit: boolean;
  [k: string]: any;
}

const ToolTipSelect = (props: ToolTipSelectProps) => {
  const { isEdit, className = '', ...restProps } = props;
  return (
    <Tooltip title={isEdit ? I18N.auto.notSupportedForModification : ''}>
      <Select
        {...restProps}
        className={`${s.tooltip__select} ${className}`}
        options={FiledTypeList}
        disabled={isEdit}
        renderSelectedItem={(option) => (
          <>
            {option.icon
              ? React.cloneElement(option.icon, {
                  className: `${option.icon.props.className || ''} ${s.typeIcon} ${s.result} `,
                })
              : null}
            <span className={s.optionLabel}>{option.name}</span>
          </>
        )}
        renderLabel={(option) => {
          return (
            <>
              {option.icon
                ? React.cloneElement(option.icon, {
                    className: `${option.icon.props.className || ''} ${s.typeIcon} `,
                  })
                : null}
              <span className={s.optionLabel}>{option.name}</span>
            </>
          );
        }}
      />
    </Tooltip>
  );
};

export default ToolTipSelect;
