.inputNumber {
  &.noRadius {
    --border-radius-base: 0;
  }
  &.rightAlign {
    :global {
      //右对齐
      .rock-input {
        text-align: right;
        padding-right: 6px;
      }
    }
    &.noPlaceholder:not(:global(.rock-input-number-active)) {
      &:hover::after {
        left: auto;
        right: 8px;
      }
    }
  }
  // //hover下展示短横
  // &.noPlaceholder:not(:global(.rock-input-number-active)) {
  //   &:hover::after {
  //     content: '-';
  //     position: absolute;
  //     top: 6px;
  //     left: 8px;
  //     color: var(--TextPrimary);
  //   }
  // }
}
