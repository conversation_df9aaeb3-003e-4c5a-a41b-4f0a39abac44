.clear {
  color: var(--IconTertiary);
}

.optionClose {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.select {
  &.noRadius {
    --border-radius-base: 0;
  }

  &.disbaled {
    :global {
      .rock-select-arrow {
        display: none;
      }
    }
  }
  &:global(.rock-select-single .custom-select-label) {
    margin-left: -5px !important;
  }
  :global {
    .rock-select-selection-placeholder {
      left: 8px;
    }
    .rock-select-selector.rock-select-selector-disabled {
      // color: red;
      background-color: transparent !important;
      cursor: default !important;
    }
    .rock-select-selector.rock-select-selector-disabled .rock-select-arrow {
      display: none;
    }
  }

  // &:not(:global(.rock-select-has-value)) {
  //   :global {
  //     //空态展示短横
  //     .rock-select-selector {
  //       &:hover,
  //       &.rock-select-selector-active {
  //         .rock-select-selection-placeholder:empty::before {
  //           content: '-';
  //           color: var(--TextPrimary);
  //         }
  //       }
  //     }
  //   }
  // }

  &.noActiveBorder {
    :global {
      //去掉无边框时，active下的高亮蓝色边框
      .rock-select-selector.rock-select-selector-no-border:focus {
        border-color: transparent;
        background-color: var(--fill-1);
      }
      .rock-select-selector-active.rock-select-selector-no-border {
        background-color: var(--fill-1);
        &::before {
          display: none;
        }
      }
    }
  }
}

.label {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0px 8px;
  border-radius: 6px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;

  &.rest {
    background-color: var(--aBlack8);
  }
  &.withClose {
    padding-right: 26px;
    position: relative;
  }
}
.dropdownSelect {
  :global {
    .rock-select-item {
      padding: 0 8px;

      // 下拉框选中态
      &.rock-select-item-selected {
        background-color: transparent;
      }
    }
  }
}
.notFoundContent {
  text-align: left;
  padding-left: 6px;
  line-height: 24px;
  color: var(--TextTertiary);
  font-size: 13px;
}
