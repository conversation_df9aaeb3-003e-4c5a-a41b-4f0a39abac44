import { defineConfig } from 'umi';
import { CDN } from './publish/const';

const minimist = require('minimist');

const argv = minimist(process.argv.slice(2));

const versiontag = argv.version_tag;

export default defineConfig({
  publicPath: `${CDN.host}${CDN.projectName}/${versiontag}/dist/`,
  proxy: {
    '/api': {
      target: 'http://ncc.popo.netease.com:8106',
      changeOrigin: true,
    },
  },
  devtool: 'source-map',
});
