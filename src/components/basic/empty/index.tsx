import classNames from 'classnames';

import s from './index.less';

export type Props = {
  /**
   * 字符串或者数组 数组第一项为light, 第二项为dark
   */
  src?: string;
  showTitle?: boolean;
  title?: string;
  text?: string[];
  content?: string;
  className?: string;
  imgClassName?: string;
  textClassName?: string;
  descClassName?: string;
};
export default function Empty(props: Props) {
  const { title, text, className, imgClassName, textClassName, src } = props;
  return (
    <div className={classNames(s.empty, className)}>
      <img className={classNames(s.img, imgClassName)} src={src}></img>
      {title ? <div className={s.title}>{title}</div> : null}
      <div className={classNames(s.desc, textClassName)}>
        {(text || []).map((v, index) => (
          <div className={s.text} key={index}>
            {v}
          </div>
        ))}
      </div>
    </div>
  );
}
