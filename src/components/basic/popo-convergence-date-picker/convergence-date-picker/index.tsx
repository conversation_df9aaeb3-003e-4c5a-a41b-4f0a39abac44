import { Data16Timestar, Data16Timestop } from '@babylon/popo-icons';
import { Button } from '@bedrock/components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useEffect, useImperativeHandle, useMemo, useState } from 'react';

import { DatePicker, Icon } from '@/components/basic';
import { TaskTime, TimeValue, TimeValueAndTimeNum } from '@/types';
import { isUpdateTime } from '@/utils';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import Remind from '../remind-item';
import RRule from '../rrule-item';
import {
  getDayStart,
  getDayStartTimeValue,
  getTimePeriodMilliseconds,
  revisionTime,
} from '../utils';
import { PickerOperation } from './components/picker-footer';
import { PickerDateSelect } from './components/time-header';
import s from './index.less';

export enum TimeInputType {
  start,
  end,
}

export type Props = {
  className?: string;
  value: TaskTime;
  disabled?: boolean;
  loopInducedDisabled?: boolean;
  timeInputType?: TimeInputType;
  onChange?: (time: TaskTime) => void;
  onVisible?: (visible: boolean) => void;
  showCircle?: boolean;
};

const DataPickItem = React.forwardRef<
  {
    getData: () => TaskTime;
  },
  Props
>((props, refs) => {
  const { value, timeInputType = TimeInputType.end, onChange, onVisible, showCircle } = props;
  const [rrule, setRrule] = useState<string>('');
  const [deadline, setDeadline] = useState<TimeValueAndTimeNum>({});
  const [startTime, setStartTime] = useState<TimeValueAndTimeNum>({});
  const [remindTime, setRemindTime] = useState<TimeValue>({
    time: 0,
    rrule: '',
    selectedOption: 'NONE',
  });
  const [focusedDateType, setFocusedDateType] = useState<TimeInputType>(timeInputType);
  const [showTimePoint, setShowTimePoint] = useState<boolean>(false);

  useEffect(() => {
    const {
      startTime: _startTime,
      deadline: _deadline,
      rrule: _rrule,
      timeFormat: _timeFormat,
      alarm: _alarm,
    } = value;
    setRrule(_rrule || '');
    setDeadline({
      time: _deadline,
      timeFormat: _timeFormat,
      timeNum:
        _deadline && _timeFormat === PPTimeFormat.dateAndTime
          ? getTimePeriodMilliseconds(_deadline)
          : undefined,
    });
    setStartTime({
      time: _startTime,
      timeFormat: _timeFormat,
      timeNum:
        _startTime && _timeFormat === PPTimeFormat.dateAndTime
          ? getTimePeriodMilliseconds(_startTime)
          : undefined,
    });
    if (_timeFormat === PPTimeFormat.dateAndTime) {
      setShowTimePoint(true);
    }
    setRemindTime({
      time: _alarm?.time || 0,
      timeFormat: _alarm?.timeFormat,
      rrule: _alarm?.rrule || '',
      selectedOption: _alarm?.selectedOption,
    });
  }, [value]);

  useImperativeHandle(refs, () => ({
    getData: handleGetData,
  }));

  const handleGetData = () => {
    let _timeFormat = deadline.timeFormat;
    let _deadline = getDayStartTimeValue(deadline.time!).valueOf();
    let _deadlineTimeNum = deadline.timeNum;
    let _startTime = getDayStartTimeValue(startTime.time!).valueOf();
    let _startTimeTimeNum = startTime.timeNum;
    // 截止时间存在
    if (!_deadline) {
      _timeFormat = PPTimeFormat.noDate;
    } else {
      if (_startTime) {
        //具体时分都不存在
        if (_deadlineTimeNum === undefined && _startTimeTimeNum === undefined) {
          _timeFormat = PPTimeFormat.olayDay;
          _startTimeTimeNum = 0;
          _deadlineTimeNum = 0;
        } else if (_deadlineTimeNum && _startTimeTimeNum) {
          _timeFormat = PPTimeFormat.dateAndTime;
        } else if (_deadlineTimeNum && _startTimeTimeNum === undefined) {
          _timeFormat = PPTimeFormat.dateAndTime;
          _deadlineTimeNum = 0;
        } else if (_startTimeTimeNum && _deadlineTimeNum === undefined) {
          _timeFormat = PPTimeFormat.dateAndTime;
          _deadlineTimeNum = 86399999;
        }
      } else {
        _startTime = 0;
        _startTimeTimeNum = 0;
        if (_deadlineTimeNum === undefined) {
          _deadlineTimeNum === 0;
          _timeFormat = PPTimeFormat.olayDay;
        } else {
          _timeFormat = PPTimeFormat.dateAndTime;
        }
      }

      //截止具体时分存在 但是开始时间不存在 开始时分有无没什么关系
    }
    if (!_deadlineTimeNum) {
      _deadlineTimeNum = 0;
    }
    if (!_startTimeTimeNum) {
      _startTimeTimeNum = 0;
    }
    return {
      rrule: rrule,
      deadline: _deadline + _deadlineTimeNum,
      startTime: _startTime + _startTimeTimeNum,
      alarm: remindTime,
      timeFormat: _timeFormat,
    };
  };

  const changeStartTime = (v: TimeValueAndTimeNum) => {
    //是否开启时间 并且截止和开始的时分都有  开始校准时间 判断截止时间是否在开始时间之前
    if (v.time && deadline.time && (!showTimePoint || (showTimePoint && v.timeNum))) {
      const startTimeValue = revisionTime(v.time, v.timeNum)!;
      let deadlineTimeValue = revisionTime(deadline.time, deadline.timeNum)!;
      if (startTimeValue >= deadlineTimeValue) {
        deadlineTimeValue = startTimeValue;
        setDeadline({
          ...deadline,
          time: getDayStart(deadlineTimeValue).valueOf(),
          timeNum: getTimePeriodMilliseconds(deadlineTimeValue),
        });
      }
    }
    setStartTime(v);
  };

  const changeDeadline = (v: TimeValueAndTimeNum) => {
    //校准时间 判断截止时间是否在开始时间之前
    if (v.time && startTime.time && (!showTimePoint || (showTimePoint && v.timeNum))) {
      const deadlineTimeValue = revisionTime(v.time, v.timeNum)!;
      let startTimeValue = revisionTime(startTime.time, startTime.timeNum)!;
      if (startTimeValue >= deadlineTimeValue) {
        startTimeValue = deadlineTimeValue;
        setStartTime({
          ...startTime,
          time: getDayStart(startTimeValue).valueOf(),
          timeNum: getTimePeriodMilliseconds(startTimeValue),
        });
      }
    }

    setDeadline(v);

    if (v.time && (!showTimePoint || (showTimePoint && v.timeNum))) {
      setFocusedDateType(TimeInputType.start);
    }
  };

  const dateData = useMemo(() => {
    return handleGetData();
  }, [deadline, startTime, remindTime, rrule, showTimePoint]);

  const isUpdated = useMemo(() => {
    return isUpdateTime(dateData, value!);
  }, [dateData]);

  const handleConfirm = () => {
    isUpdated && onChange?.(dateData!);
  };

  const handleCancel = () => {
    onVisible?.(false);
  };

  return (
    <div className={classNames('com-dropdown-select', s.newDatePickerWrapper)}>
      <DatePicker.Panel
        picker="date"
        //defaultValue={dayjs()}
        value={
          focusedDateType === TimeInputType.end
            ? deadline.time
              ? dayjs(deadline.time)
              : undefined
            : startTime.time
            ? dayjs(startTime.time)
            : undefined
        }
        onSelect={(v) => {
          let timeFormat = PPTimeFormat.olayDay;
          if (deadline.timeNum) {
            timeFormat = PPTimeFormat.dateAndTime;
          }
          if (focusedDateType === TimeInputType.end) {
            changeDeadline({
              ...deadline,
              time: dayjs(v).valueOf(),
              timeFormat: timeFormat,
            });
            //校验并纠正开始时间
          } else {
            changeStartTime({
              ...startTime,
              time: dayjs(v).valueOf(),
              timeFormat: timeFormat,
            });
            // 设置开始时间 如果没有截止时间 默认设置截止时间
            if (!deadline.time) {
              changeDeadline({
                ...deadline,
                time: dayjs(v).valueOf(),
                timeFormat: timeFormat,
              });
            } else {
              //校验并纠正截止时间
            }
          }
        }}
        // disabledDate={(current) => {
        //   // 如果是截止时间  存在开始的时间的情况下  截止时间不能在开始时间之前
        //   if (
        //     focusedDateType === TimeInputType.end &&
        //     startTime.time &&
        //     deadline.time &&
        //     dayjs(current).isBefore(startTime.time)
        //   ) {
        //     return true;
        //   } else if (
        //     // 如果是开始时间  开始时间不能在截止时间之后 //截止时间一定是存在的???
        //     focusedDateType === TimeInputType.start &&
        //     deadline.time &&
        //     dayjs(current).isAfter(deadline.time)
        //   ) {
        //     return true;
        //   }
        //   return false;
        // }}
      />
      <div className={s.divider}></div>
      <div className={s.input__container}>
        <PickerDateSelect
          datePlaceholder={I18N.auto.startDate}
          timePlaceholder={I18N.auto.startTime}
          className="mb-8"
          value={startTime}
          showTime={showTimePoint}
          active={focusedDateType === TimeInputType.start}
          onChange={(v) => {
            changeStartTime(v);
          }}
          onClick={() => {
            setFocusedDateType(TimeInputType.start);
          }}
          prefix={<Data16Timestar />}
        ></PickerDateSelect>
        <PickerDateSelect
          datePlaceholder={I18N.auto.closingDate}
          timePlaceholder={I18N.auto.deadline_2}
          value={deadline}
          showTime={showTimePoint}
          active={focusedDateType === TimeInputType.end}
          onChange={changeDeadline}
          onClick={() => {
            setFocusedDateType(TimeInputType.end);
          }}
          prefix={<Data16Timestop />}
        ></PickerDateSelect>
        <PickerOperation
          hasTimePoint={showTimePoint}
          onHasTimePointChange={(v) => {
            console.log('onHasTimePointChange', v);

            let startTimeNum = undefined;
            let endTimeNum = undefined;
            if (v) {
              //默认时分
              if (startTime.time) {
                startTimeNum = 34200000;
              }
              if (deadline.time) {
                endTimeNum = 66600000;
              }
              setShowTimePoint(true);
            } else {
              setShowTimePoint(false);
            }
            changeDeadline({
              ...deadline,
              timeFormat: v ? PPTimeFormat.dateAndTime : PPTimeFormat.olayDay,
              timeNum: endTimeNum,
            });
            changeStartTime({
              ...startTime,
              timeFormat: v ? PPTimeFormat.dateAndTime : PPTimeFormat.olayDay,
              timeNum: startTimeNum,
            });
          }}
        />
      </div>
      <div className={s.divider}></div>

      <div className={s.setRemindRepeatSection}>
        {showCircle && (
          <RRule
            value={rrule}
            time={deadline.time}
            suffixIcon={<Icon fontSize={16} name="icon-quick_round"></Icon>}
            onChange={(v) => {
              if (!deadline.time) {
                changeDeadline({
                  ...deadline,
                  time: dayjs().valueOf(),
                  timeFormat: PPTimeFormat.olayDay,
                });
              }
              setRrule(v);
            }}
          ></RRule>
        )}
        <Remind
          suffixIcon={<Icon fontSize={16} name="icon-quick_remind"></Icon>}
          value={remindTime}
          deadlineTime={deadline}
          onChange={(v) => {
            setRemindTime(v);
          }}
        ></Remind>
      </div>
      <div className={s.footer}>
        <Button size="small" type="checked-neutral" onClick={handleCancel}>
          {I18N.auto.cancel}
        </Button>
        <Button
          className={`${s.confirm__btn} confirm__btn`}
          size="small"
          type="primary"
          disabled={!isUpdated}
          onClick={handleConfirm}
        >
          {I18N.auto.determine}
        </Button>
      </div>
    </div>
  );
});

export default DataPickItem;
