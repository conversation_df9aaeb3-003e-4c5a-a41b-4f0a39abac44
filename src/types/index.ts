import { ColumnsType, ColumnType } from '@bedrock/components/lib/Table'
import React from 'react'

import {
  ApiNavigatorViewListGetResponse,
  ApiProjectFlowQueryPostResponse,
  ApiRecordQueryGetResponse,
  ApiTaskCalendarViewUnplannedPostResponse,
  ApiTaskCreatePostRequest,
  ApiTaskGetGetResponse,
  ApiTaskKanbanViewGroupsPostResponse,
  ApiTaskListViewPostResponse,
  ApiTodoCommentDocParsePostResponse,
  ApiTodoParticipantCandidateSearchGetResponse,
  ApiTodoQueryLabelsGetResponse,
} from '@/api'
import {
  AtType,
  Distribution,
  EnumNotificationEvent,
  EnumProjectStatus,
  EnumRole,
  EnumSessionType,
  Order,
  PPTimeFormat,
  TableIndex,
  TaskTableRowType,
  ViewType,
} from '@/utils/const'
import { EnumGroupBy } from '@/utils/fields'
import { ProjectPermissionEnum, TaskPermissionEnum, ViewPermissionEnum } from '@/utils/permission'

import { FieldTypeEnum } from './custom-field'

export type ValueOf<T> = T[keyof T]

export type ArrayItem<T> = T extends (infer R)[] ? R : never

export type Pagination = {
  page?: number
  size?: number
  more?: boolean
  order?: Order
  orderBy?: string | undefined
  scrollId?: string
  searchId?: string
  total?: number
}

export interface UserInfo extends ArrayItem<ApiTodoParticipantCandidateSearchGetResponse> {
  finished?: 0 | 1 | undefined
  selected?: boolean
  desc?: string
  attype?: AtType
  sessionType?: EnumSessionType
}

export type Member = {
  uid: string
  name: string
  avatarUrl: string
  sessionType: EnumSessionType
  role: EnumRole
}

//视图信息
export type ViewInfo = ArrayItem<ApiNavigatorViewListGetResponse>

export type ExtraTaskInfo = {
  id?: number | string
  groupId?: string //新增按钮增加群组id, 暂时只是这里需要
  memoAlarm?: {
    time?: number
    timeFormat?: PPTimeFormat
    rrule?: string
    selectedOption?: string
    createTime?: number
    nextTime?: number
  }
  /**
   * 创建人
   */
  isCreator?: boolean
  /**
   * 关注人
   */
  isFollower?: boolean
  /**
   * 创建人 责任人 身份只少有一个
   */
  isParticipanter?: boolean
  /**
   *  责任人
   */
  isCoordinator?: boolean
  permissions?: TaskPermission[]
  project?: ProjectInfo
}

export interface TodoInfo
  extends ExtraTaskInfo,
    Omit<ArrayItem<ApiTaskListViewPostResponse['list']>, 'project' | 'taskId'> {
  _rowType?: TaskTableRowType
  children?: TodoInfo[]
  groupBy?: string
  finishedCount?: number
  totalCount?: number
  /** 分组ID，结构为：[[level1, level2], [level1, level2]]或者[[level1], [level1]]；
   * 同一条数据可能在多个分组下，在此保存同一条数据的不同分组信息；
   * 目的是在数据更新时，寻找到该条数据对应的其他的分组位置，方便数据修改
   */
  groupedIds?: Array<Array<string | number>>
  /**
   * 父级分组对应的数据
   */
  parentGroup?: TodoInfo
  /**
   * 原始任务的引用，用于分组任务的副本追踪
   */
  _originalTask?: TodoInfo
  /**
   * 分组标识，用于区分不同分组中的相同任务
   */
  _groupKey?: string
  taskId?: number | string
  rowId?: string | number // 由getRowId生成的唯一标识
  $startTime?: number
  $deadline?: number
}
export interface DetailTodoInfo extends ExtraTaskInfo, Omit<ApiTaskGetGetResponse, 'project'> {
  _rowType?: TaskTableRowType
}

export type TaskInfo = TodoInfo

export type AddTaskInfoItem = ApiTaskCreatePostRequest

export type RecordItem = ArrayItem<ApiRecordQueryGetResponse['list']>

export interface MenuLabelItem extends ArrayItem<ApiTodoQueryLabelsGetResponse> {
  count?: number
  keys?: string[]
}

export type FieldsItem = ArrayItem<NonNullable<ArrayItem<ApiNavigatorViewListGetResponse>['displays']>> & {
  fieldName?: string
  visible?: number
}

export type UnplannedTodoItem = ArrayItem<ApiTaskCalendarViewUnplannedPostResponse['list']>

export type DocParse = ArrayItem<ApiTodoCommentDocParsePostResponse>

//老的计划中分组
export type ListGroupItem = {
  name: string
  hidden: boolean
  count: number
  collapsed?: boolean
  todoList?: TodoInfo[]
  calendarSync?: boolean
}

// 新版本分组
export interface TaskGroupItem extends TaskKanbanViewGroup {
  id?: string
  count?: number
  title?: string
  defaultParams?: any
  dataList?: TodoInfo[]
  unfold?: boolean // 展开
  groupBy: EnumGroupBy
  value?: string | number | string[]
}

export type ColumnProps = {
  dataIndex: TableIndex
  title: string
  sortIndex?: string
  icon?: string
  className?: string
  hidden?: boolean
}

export interface ViewTab extends ArrayItem<ApiNavigatorViewListGetResponse> {
  id: any
  distribution: Distribution
  type: ViewType
  permissions: ViewPermission[]
  changeSearchParams: boolean
  // 初始viewTab的所有条件筛选
  initialSearchParams?: Record<string, any>
  /**
   * 视图是否发生改变
   * 判断依据是初始条件筛选和后续操作的条件是否一致
   */
  hasViewChanged?: boolean
  /* 视图配置, 由前端自定义,后端只负责存储
   * {
   * cardConfig: {
   *    //卡片侧边颜色 - 对应选项的值的颜色
   *    siderColor?: string;
   *    //卡片是否展示完成按钮
   *    showCompleteIcon?: boolean;
   *   };
   *   }
   */
  viewConfig: string
  // 解析后的视图配置, 与viewConfig对应
  parsedViewConfig?: {
    cardConfig?: {
      //卡片侧边颜色 - 对应选项的值的颜色
      siderColor?: string
      //卡片是否展示完成按钮
      showCompleteIcon?: boolean
    }
  }
  /**
   * 项目下任务自定义排序后的操作数组
   * 包含currentTaskId, preTaskId, tailTaskId
   */
  projectTaskResortParamsList?: ProjectTaskResortParams[]
}

export type OrderOption = {
  order?: Order
  orderBy?: string
}

export interface TaskTableColumnInfo {
  id: string
  name: string
  gender: string
  age: number
  email: string
  address: string
}

type TaskKanbanViewGroup = ArrayItem<ApiTaskKanbanViewGroupsPostResponse['groups']>

export interface TimeValue {
  time?: number //时间的毫秒时间戳
  timeFormat?: PPTimeFormat //0 - 无日期、1 - 日期、2 - 日期时间
  rrule?: string // rrule规则的字符串 https://github.com/jakubroztocil/rrule
  selectedOption?: string
}

export interface TimeValueAndTimeNum extends TimeValue {
  timeNum?: number
}

export interface TaskTime {
  deadline?: number
  rrule?: string
  timeFormat?: PPTimeFormat
  startTime?: number
  alarm?: TimeValue
  selectedOption?: string
}

export interface GroupItem {
  /**
   * 分组条件：deadline、assignee、priority、project、finished
   */
  groupBy?: string
  groupId?: string
  groupTaskCount?: number
  count?: number
  projectId?: number
  projectName?: string
  assignees?: {
    /**
     * 用户 uid
     */
    uid?: string
    /**
     * 用户显示名称，服务端处理多语言昵称
     */
    name?: string
    /**
     * 用户头像地址
     */
    avatarUrl?: string
  }[]
  assigneeUids?: string[]
  deadlineRange?: {
    begin?: number
    end?: number
  }
  deadlineGroup?: 'NONE' | 'EXPIRED' | 'TODAY' | 'TOMORROW' | 'FUTURE_7_DAYS' | 'FUTURE'
  priority?: number
  taskProgress?: number
  preTaskId?: number
  value?: any
  customFieldId?: string
  customFieldValue?: string | string[]
  fieldId?: string
  fieldVersion?: number
  fieldType?: FieldTypeEnum
}

export interface ILaneMapData {
  [k: string]: TodoInfo[]
}

export type MemberVo = {
  // memberId: string;
  // memberType: number;
  // pic: string;
  uid?: string
  name?: string
  avatarUrl?: string
  sessionType?: EnumSessionType
  role?: number
}

export type ServerProjectInfo = ArrayItem<ApiProjectFlowQueryPostResponse['list']>
//项目
export interface ProjectInfo extends ServerProjectInfo {
  id?: number
  members?: MemberVo[]
  /** 类型：项目分组/项目 */
  type?: string
  /** 项目分组是否是编辑中 */
  status?: ProjectItemStatus
  /** 项目分组是否是收起状态 */
  fold?: boolean
  index?: string
}

export interface CacheAddinfo {
  projectId?: number
  name?: string
  icon?: string
  iconColor?: string
  members?: Member[]
  createTeam?: boolean
  groupId?: number
  status?: ProjectItemStatus
  // 添加数据的来源
  source?: string
}

export interface ProjectStatus {
  name: string
  key: EnumProjectStatus
  className: string
  icon: string
}

export interface Permission {
  name: ProjectPermissionEnum
  value: boolean
}

export interface TaskPermission {
  name: TaskPermissionEnum
  value: boolean
}

export type PermissionsTasksMap = Record<string, { permissions: TaskPermission[] }>

export interface ViewPermission {
  name: ViewPermissionEnum
  value: boolean
}

export type QueryGroupBy = {
  /**
   * 字段名，如果是自定义字段，字段名为customField，具体自定义字段信息由其他字段描述
   */
  fieldName?: string
  /**
   * 自定义字段Id
   */
  customFieldId?: string
  /**
   * 自定义字段版本
   */
  customFieldVersion?: number
}

export interface TaskColumnType<T> extends ColumnType<T> {
  prefix?: React.ReactNode
  suffix?: React.ReactNode
  title?: React.ReactNode
  align?: 'left' | 'center' | 'right'
  /**tooltip 默认取title字段， 若要关闭tooltip，则设置tooltip=''即可 */
  tooltip?: React.ReactNode
}

export type TaskColumnsType<T> = TaskColumnType<T>[]

export type TableNoDataProp = {
  isNoData?: boolean
  bgUrl?: {
    dark: string
    light: string
  }
  descriptive?: React.ReactNode
}

export type NotificationEventItem = {
  eventId: EnumNotificationEvent
  roles: EnumRole[]
  isOpen: boolean
  name: string
}

export enum TodoDetailStatus {
  normal = 'normal',
  notExist = 'notExist',
  noPermission = 'noPermission',
  networkErr = 'networkErr',
}

export const enum ActionType {
  enter = 'enter',
  blur = 'blur',
}

export const enum ProjectItemStatus {
  normal = 'normal',
  editing = 'editing',
  project_creating = 'project_creating',
  placeholder = 'placeholder',
}

export type ProjectTaskResortParams = {
  currentTaskId: number
  preTaskId?: number
  tailTaskId?: number
}
