.modalWrapper {
  position: absolute;
  :global {
    .rock-modal-content {
      .rock-modal-body {
        & > .rock-scrollbar > .rock-scrollbar-view {
          padding: 1px;
          .rock-select-selector {
            background: var(--bgTop);
          }
          .rock-input-prefix {
            margin-right: 8px;
          }
        }
      }
      .rock-modal-footer {
        .rock-btn + .rock-btn {
          margin-left: 12px;
        }
      }
    }
  }
}

.recent {
  font-size: 13px;
  color: var(--TextTertiary);
  padding: 2px 6px;
}

.icon {
  animation: rotate 2s linear infinite;
  color: var(--Brand500);
}

.searchIcon {
  width: 16px;
  height: 16px;
  color: var(--IconSecondary);
  > svg {
    width: 16px;
    height: 16px;
  }
}

.ulWrapper {
  margin: 0;
  max-height: 240px;
  overflow-y: scroll;
  .loading,
  .noContent {
    margin: auto;
    pointer-events: none;
    > div {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  :global {
    .rock-dropdown-menu-item {
      padding-left: 6px;
      padding-right: 6px;
      margin-bottom: 2px;
      > span {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .rock-dropdown-menu-item-selected {
      background-color: var(--primary-5);
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
