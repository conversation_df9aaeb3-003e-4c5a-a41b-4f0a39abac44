import classNames from 'classnames';
import React, { useMemo } from 'react';

import Icon from '@/components/basic/Icon';
import { EnumProjectStatus } from '@/utils/const';

import s from './index.less';
import { projectStatusList } from './utils';

export type Props = {
  className?: string;
  status?: EnumProjectStatus;
};

const Overlay: React.FC<Props> = (props) => {
  const { className, status } = props;
  const memoItem = useMemo(() => {
    return (
      projectStatusList.find((item) => item.key === status) || {
        name: '',
        key: '',
        className: '',
        icon: '',
      }
    );
  }, [status]);
  return (
    <div
      className={classNames(
        s.tag,
        'com-project-status-bg',
        'com-project-status-label',
        className,
        memoItem?.className
      )}
    >
      <Icon name={memoItem.icon} fontSize={16} className="mr-4"></Icon>
      {memoItem?.name}
    </div>
  );
};

export default Overlay;
