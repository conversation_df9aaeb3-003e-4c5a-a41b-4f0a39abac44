body[data-theme='dark'] {
  --Brand50: #0e192e;
  --Brand100: #182a4a;
  --Brand200: #104582;
  --Brand300: #1a5daa;
  --Brand400: #1672da;
  --Brand500: #2d8eff;
  --Brand600: #4fa0ff;
  --Brand700: #7db9ff;
  --Brand800: #a1ccff;
  --Brand900: #c7e0ff;

  --N0: #111111;
  --N50: #191919;
  --N60: #282828;
  --N100: #202020;
  --N200: #2c2c2c;
  --N300: #3c3c3c;
  --N400: #585858;
  --N500: #828282;
  --N600: #a0a0a0;
  --N700: #b4b4b4;
  --N800: #c8c8c8;
  --N900: #dddddd;

  --aBlack: rgb(255, 255, 255);
  --aBlack90: rgba(255, 255, 255, 0.9);
  --aBlack80: rgba(255, 255, 255, 0.8);
  --aBlack60: rgba(255, 255, 255, 0.6);
  --aBlack50: rgba(255, 255, 255, 0.5);
  --aBlack40: rgba(255, 255, 255, 0.4);
  --aBlack24: rgba(255, 255, 255, 0.24);
  --aBlack20: rgba(255, 255, 255, 0.2);
  --aBlack16: rgba(255, 255, 255, 0.16);
  --aBlack12: rgba(255, 255, 255, 0.12);
  --aBlack10: rgba(255, 255, 255, 0.1);
  --aBlack8: rgba(255, 255, 255, 0.08);
  --aBlack6: rgba(255, 255, 255, 0.06);
  --aBlack4: rgba(255, 255, 255, 0.04);

  --aWhite90: rgba(0, 0, 0, 0.9);
  --aWhite80: rgba(0, 0, 0, 0.8);
  --aWhite60: rgba(0, 0, 0, 0.6);
  --aWhite50: rgba(0, 0, 0, 0.5);
  --aWhite40: rgba(0, 0, 0, 0.4);
  --aWhite24: rgba(0, 0, 0, 0.24);
  --aWhite20: rgba(0, 0, 0, 0.2);
  --aWhite16: rgba(0, 0, 0, 0.16);
  --aWhite12: rgba(0, 0, 0, 0.12);
  --aWhite10: rgba(0, 0, 0, 0.1);
  --aWhite8: rgba(0, 0, 0, 0.08);
  --aWhite6: rgba(0, 0, 0, 0.06);
  --aWhite4: rgba(0, 0, 0, 0.04);

  --aBrand90: rgba(45, 142, 255, 0.94);
  --aBrand80: rgba(45, 142, 255, 0.85);
  --aBrand60: rgba(45, 142, 255, 0.64);
  --aBrand50: rgba(45, 142, 255, 0.53);
  --aBrand40: rgba(45, 142, 255, 0.42);
  --aBrand24: rgba(45, 142, 255, 0.26);
  --aBrand20: rgba(45, 142, 255, 0.19);
  --aBrand16: rgba(45, 142, 255, 0.17);
  --aBrand12: rgba(45, 142, 255, 0.13);
  --aBrand10: rgba(45, 142, 255, 0.11);
  --aBrand8: rgba(45, 142, 255, 0.08);
  --aBrand6: rgba(45, 142, 255, 0.06);
  --aBrand4: rgba(45, 142, 255, 0.03);

  --R50: #52000f;
  --R100: #70061a;
  --R200: #8b0b24;
  --R300: #b31732;
  --R400: #da273d;
  --R500: #ff525d;
  --R600: #ff757c;
  --R700: #ff959b;
  --R800: #ffb9bb;

  --G50: #002d0b;
  --G100: #00360d;
  --G200: #015215;
  --G300: #007821;
  --G400: #019e33;
  --G500-t: 39, 185, 68;
  --G500: #27b944;
  --G600-t: 64, 201, 91;
  --G600: #40c95b;
  --G700: #67da73;
  --G800: #8ae593;

  --B50: #012c60;
  --B100: #003b89;
  --B200: #0151b0;
  --B300: #016ed5;
  --B400: #018df5;
  --B500: #15a9fb;
  --B600: #57bffc;
  --B700: #87d5fd;
  --B800: #bee9fd;

  --O50: #571a06;
  --O100: #6a220a;
  --O200: #a03100;
  --O300: #ba4e1f;
  --O400: #ff6f08;
  --O500: #ff842c;
  --O600: #ff9346;
  --O700: #ffbe8f;
  --O800: #ffe1c7;

  --M50: #69013e;
  --M100: #900250;
  --M200: #b70b65;
  --M300: #df1779;
  --M400: #f5278a;
  --M500: #fe50a1;
  --M600: #ff78b7;
  --M700: #ff9eca;
  --M800: #fec9e1;

  --Y50: #69410e;
  --Y100: #905e19;
  --Y200: #b88127;
  --Y300: #dea637;
  --Y400: #f5bb22;
  --Y500: #ffd44c;
  --Y600: #fee26c;
  --Y700: #ffee8a;
  --Y800: #fff4b8;

  --L50: #193004;
  --L100: #234008;
  --L200: #3a6611;
  --L300: #568c1c;
  --L400: #76b328;
  --L500: #95d62b;
  --L600: #abe548;
  --L700: #d5fc88;
  --L800: #e5ffad;

  --C50: #004752;
  --C100: #006970;
  --C200: #00858a;
  --C300: #00a3a3;
  --C400: #00bdbd;
  --C500: #00d1d1;
  --C600: #2de0e0;
  --C700: #71ebeb;
  --C800: #b0f5f5;

  --I50: #00016b;
  --I100: #00017a;
  --I200: #060a96;
  --I300: #1e14ba;
  --I400: #2d1adb;
  --I500: #473bf5;
  --I600: #6a6af7;
  --I700: #9c9fff;
  --I800: #ccceff;

  --P50: #24015b;
  --P100: #330a74;
  --P200: #4f1399;
  --P300: #6d22bf;
  --P400: #8e31e3;
  --P500: #ad5cf0;
  --P600: #c888fc;
  --P700: #d5a1fc;
  --P800: #e9cbfe;

  --V50: #5c015e;
  --V100: #850485;
  --V200: #b20ba9;
  --V300: #dc17ce;
  --V400: #f022e5;
  --V500: #fb50f3;
  --V600: #fc78f6;
  --V700: #fd96f8;
  --V800: #fecbfb;

  --bgApplication: #2b2b2b;
  --bgApplicationHover: rgba(255, 255, 255, 0.06);
  --bgApplicationFocus: rgba(255, 255, 255, 0.1);
  --bgAlert: #191919;
  --bgBottom: #1f1f1f;
  --bgMiddle: #202020;
  --bgMiddleLight: #222222;
  --bgTop: #2c2c2c;
  --bgMenu: rgba(255, 255, 255, 0.12);
  --bgonBubble: #222222;
  --bgcard-onbubble: #1f1f1f;

  --TextPrimary-strong: rgba(255, 255, 255, 0.96);
  --TextPrimary: rgba(255, 255, 255, 0.84);
  --TextSecondary: rgba(255, 255, 255, 0.7);
  --TextSecondary-ongrey: rgba(255, 255, 255, 0.56);
  --TextTertiary: rgba(255, 255, 255, 0.48);
  --TextQuartus: rgba(255, 255, 255, 0.32);

  --IconBlack: rgba(221, 221, 221, 0.8);
  --IconPrimary: rgba(221, 221, 221, 0.68);
  --IconSecondary: rgba(221, 221, 221, 0.54);
  --IconTertiary: rgba(221, 221, 221, 0.36);
  --IconQuartus: rgba(221, 221, 221, 0.24);

  --LinkNormal: #2d8eff;
  --LinkHover: #3d97ff;
  --LinkPressed: #1672da;

  --absWhite: #ffffff;
  --absaWhite90: rgba(255, 255, 255, 0.9);
  --absaWhite80: rgba(255, 255, 255, 0.8);
  --absaWhite60: rgba(255, 255, 255, 0.6);
  --absaWhite40: rgba(255, 255, 255, 0.4);
  --absaWhite24: rgba(255, 255, 255, 0.24);
  --absaWhite16: rgba(255, 255, 255, 0.16);
  --absaWhite12: rgba(255, 255, 255, 0.12);
  --absSecondary: rgba(255, 255, 255, 0.08);
  --absaBlack: #000000;
  --absaBlack80: rgba(0, 0, 0, 0.8);
  --absaBlack60: rgba(0, 0, 0, 0.6);
  --absaBlack40: rgba(0, 0, 0, 0.4);
  --absaBlack24: rgba(0, 0, 0, 0.24);
  --absaBlack16: rgba(0, 0, 0, 0.16);
  --absaBlack12: rgba(0, 0, 0, 0.12);
  --absaBlack8: rgba(0, 0, 0, 0.08);
  --absaBlack6: rgba(0, 0, 0, 0.06);
  --absaBlack4: rgba(0, 0, 0, 0.04);

  --admintopBarBgLight: #404040;
  --admintopBarBgStrong: #363636;
  --admintopBarSearchBg: #5a5a5a;
  --admintopBarSearchBgHover: #666666;
  --admintopBarSearchIcon: rgba(255, 255, 255, 0.6);
  --admintopBarSearchIconHover: rgba(255, 255, 255, 0.8);
  --admintopBarSearchText: rgba(255, 255, 255, 0.6);
  --admintopBarSearchTextInput: rgba(217, 217, 217, 0.96);
  --admintopBarIcon: #ffffff;
  --admintopBarIconBgHover: rgba(255, 255, 255, 0.2);
  --admintopBarIconBgPress: rgba(255, 255, 255, 0.25);
  --admintopLine: rgba(255, 255, 255, 0.24);
  --adminnavBg: #383737;
  --adminnavIcon: rgba(221, 221, 221, 0.54);
  --adminnavIconBgHover: rgba(0, 0, 0, 0.6);
  --adminnavText: rgba(221, 221, 221, 0.76);
  --admintoastBg: #3c3c3c;
  --adminmask: rgba(0, 0, 0, 0.7);
  --adminqrCode: #ffffff;
  --admindisableMask: rgba(25, 25, 25, 0.8);

  --imgrayBubble: #303132;
  --imbrandBubble: #27365c;
  --imunread: rgba(64, 201, 91, 0.6);
  --imemojiReply: rgba(0, 0, 0, 0.24);
  --imAi: #7676ff;
  --imAiBg: rgba(0, 0, 0, 0.24);

  --appLetscardFill: #2c2c2c;

  --cardB: #2f8fff;
  --cardBO16: rgba(47, 143, 255, 0.16);
  --cardGO16: rgba(39, 185, 68, 0.16);
  --cardOO16: rgba(255, 132, 44, 0.16);
  --cardOO8: rgba(255, 132, 44, 0.08);
  --cardRO16: rgba(255, 82, 93, 0.16);
  --cardNO16: rgba(130, 130, 130, 0.16);
  --cardbg: #1f1f1f;
  --cardborderMobile: rgba(255, 255, 255, 0.24);
  --cardborderDesktop: rgba(255, 255, 255, 0.12);
  --cardline: rgba(255, 255, 255, 0.12);
  --cardblue1: #1f86ff;
  --cardgreen1: #24c446;
  --cardorange1: #ff842c;
  --cardred1: #ff525d;
  --cardgray1: #828282;
  --cardblue2: #0e192e;
  --cardgreen2: rgba(36, 196, 70, 0.16);
  --cardorange2: rgba(255, 132, 44, 0.16);
  --cardred2: rgba(255, 82, 93, 0.16);
  --cardgray2: #191919;
  --cardblue3: rgba(31, 134, 255, 0.08);
  --cardgreen3: rgba(36, 196, 70, 0.08);
  --cardorange3: rgba(255, 132, 44, 0.08);
  --cardred3: rgba(255, 82, 93, 0.08);
  --cardgray3: rgba(130, 130, 130, 0.08);

  --caltimeConflict: rgba(255, 117, 124, 0.4);

  --TasksUrgentBg: #522e2a;
  --TasksHighBg: #5c3b23;
  --TasksMediumBg: #3c2e49;
  --TasksLowBg: #253d30;
  --TasksUrgentText: rgba(255, 255, 255, 0.8);
  --TasksHighText: rgba(255, 255, 255, 0.8);
  --TasksMediumText: rgba(255, 255, 255, 0.8);
  --TasksLowText: rgba(255, 255, 255, 0.8);
  --TasksUrgentIcon: #ce4944;
  --TasksHighIcon: #d87620;
  --TasksMediumIcon: #8d5bc1;
  --TasksLowIcon: #2d9964;
  --TasksDone: #019e33;
  --TasksTagbg: rgba(242, 244, 247, 0.05);
  --TasksBGIV: rgba(212, 76, 71, 0.5);
  --TasksBGIII: rgba(217, 115, 13, 0.5);
  --TasksBGII: rgba(144, 101, 176, 0.5);
  --TasksBGI: rgba(68, 131, 97, 0.5);
  --TasksBG0: #323232;
  --TasksTextIV: #edb9b7;
  --TasksTextIII: #e3c19f;
  --TasksTextII: #d6b9eb;
  --TasksTextI: #a4debf;
  --TasksText0: #d3dadb;

  --VCR: #e54e67;
  --VCG: #2fc26c;
  --VCO: #ffb257;
  --VCGIB: rgba(32, 32, 32, 0.8);
  --VCGIBH: rgba(88, 88, 88, 0.8);
  --VCAIB: rgba(44, 44, 44, 0.8);
  --VCCCtext: rgba(255, 255, 255, 0.96);
  --VCCCname: rgba(255, 255, 255, 0.76);
  --VCCCicon: rgba(255, 255, 255, 0.8);
  --VCCCbg: rgba(0, 0, 0, 0.7);

  --TasksProjectStatusI: rgba(125, 185, 255, 0.12);
  --TasksProjectStatusII: rgba(255, 190, 143, 0.12);
  --TasksProjectStatusIII: rgba(255, 149, 155, 0.12);
  --TaskGuideBg: #2c343d;
}
