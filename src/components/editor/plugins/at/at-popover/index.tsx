import React, { useContext } from 'react';
import { memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Popover, Scrollbar } from '@bedrock/components';
import classNames from 'classnames';
import s from './index.less';
import { AvatarPeople } from '@/components/basic';
import I18N from '@/utils/I18N';
import { pp } from '@popo-bridge/web';
import { apiTodoCommentAtSearchPost } from '@/api';
import { MentionExtraData } from '@/components/basic-task/comment/editor/utils';
import { UserInfo } from '@/types';
import { AtType } from '@/utils/const';
import { Platform } from '@/utils/platform';
import { useMemoizedFn } from 'ahooks';
export interface MentionPopoverProps {
  onChange?: (data: UserOption, isFlowUser?: boolean) => void;
  reference: Element;
  onHidden?: () => void;
  userId: string;
  requestId: string;
  [key: string]: any;
}

export interface MentionPopoverRef {
  hide: () => void;
  setReference: (reference: Element) => void;
  setKeyword: (k: string) => void;
}

export interface UserOption {
  code: any;
  label: string;
  department: string;
  email: string;
  avatar: string | null;
}

const AtPopover: React.ForwardRefRenderFunction<MentionPopoverRef, MentionPopoverProps> = (
  { reference: defaultReference, onChange, onHidden, requestId, userId, ...props },
  ref
) => {
  const [visible, setVisible] = useState(false);
  const methodRef = useRef<any>();
  const { participants, style } = props;
  const [list, setList] = useState<UserInfo[]>([]);
  const scrollRef = useRef<any>(null);
  const currentIndexRef = useRef(0);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [keyword, setKeyword] = useState<string>('');
  const [position, setPosition] = useState<any>([0, 5]);
  const [search, setSearch] = useState<boolean>(false);

  useEffect(() => {
    getPeopleList(keyword);
  }, [keyword]);

  const methods = useMemo(
    () => ({
      hide: () => {
        setVisible(false);
      },
      setReference: (reference: Element) => {
        // setReference(reference)
      },
      setKeyword: (keyword: string) => {
        setKeyword(keyword);
      },
      show: () => {
        setVisible(true);
      },
      setPosition: (position) => {
        setPosition(position)
      },
      injectFn: (fn: any) => {
        methodRef.current = fn;
      },
    }),
    []
  );

  useImperativeHandle(ref, () => methods, [methods]);

  const getRecentContacts = async () => {
    setSearch(false);
    const participants = [...(MentionExtraData.participants || [])].sort((item) =>
      item.uid === MentionExtraData.userInfo?.uid ? -1 : 0
    );
    if (participants?.length > 1) {
      //个人待办和1v1待办总人数肯定小于2
      if (participants?.length === 2) {
        return participants;
      }
      // 有未完成的人 且是指派人
      if (
        participants
          .filter((item) => item.uid !== MentionExtraData.assignor?.uid)
          .some((item) => !item.finished) &&
        MentionExtraData.assignor?.uid === MentionExtraData.userInfo?.uid
      ) {
        participants.unshift({
          name: I18N.auto.unfinishedToDoList,
          uid: '',
          avatarUrl: 'https://popo.res.netease.com/popo-assets/todo/avatar-unfinished.png',
          attype: AtType.unfinished,
        });
      }
      //如果只有2条以上数据 表示多人待办 加上所有人的操作
      participants.unshift({
        name: I18N.auto.everyone,
        uid: '',
        avatarUrl: 'https://popo.res.netease.com/popo-assets/todo/avatar-all.png',
        attype: AtType.all,
        desc: I18N.auto.mentionAllAchievements,
      });
      return participants;
    } else {
      if (!Platform.isPOPO) {
        return participants;
      }
      const { data = [] } = await pp.getRecentContactsList({ maxCount: 30 }).then((res) => {
        return {
          data:
            res.data?.filter(
              (item) =>
                !item.uid?.includes('@cus.robot.popo.com') &&
                !item.uid?.includes('@app.robot.popo.com')
            ) || [],
        };
      });
      let list: UserInfo[] = [];
      if (data?.length) {
        list = data.map((item) => ({
          name: item.name,
          uid: item.uid,
          avatarUrl: item.headPic,
        }));
      }
      return list;
    }
  };

  const getPeopleList = async (text: string) => {
    let list: UserInfo[] = [];
    if (text) {
      list = await apiTodoCommentAtSearchPost({ keyword: text });
      setSearch(true)
      if (!list.length) {
        // this.remove();
        return;
      }
    } else {
      list = await getRecentContacts();
    }
    setList(list);
  };

  const handleSelect = useMemoizedFn((o: UserInfo) => {
    // if (!o.uid) return;
    (onChange || methodRef.current?.change)?.(o);
    methods.hide();
  });
  const handleSelectRef = useRef(handleSelect);
  handleSelectRef.current = handleSelect;

  useEffect(() => {
    pp.setHotkeyFilterList({
      filters: [
        {
          modifiers: '',
          key: 'Key_Escape',
        },
      ],
    });

    const listener = (e: KeyboardEvent) => {
      if (!visible) return;
      if (e.key === 'ArrowUp') {
        selectIndex(-1);
      }
      if (e.key === 'ArrowDown') {
        selectIndex(1);
      }
      if (e.key === 'Escape') {
        // 隐藏popplist
        methods.hide();
      }
      if (e.key === 'Enter') {
        handleSelect(list[currentIndexRef.current]);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
      pp.setHotkeyFilterList({
        filters: [],
      });
    };
  }, [visible, methods, list]);

  const selectIndex = (d: number) => {
    if (d === -1 && currentIndexRef.current === 0) {
      currentIndexRef.current = list.length + d;
    } else {
      const newIndex = (currentIndexRef.current + d) % list.length;
      currentIndexRef.current = Math.abs(newIndex);
    }
    setCurrentIndex(currentIndexRef.current);

    // 计算滚动条滚动距离
    //向下正向
    if (d === 1) {
      //超出一屏幕开始滚动
      if (currentIndexRef.current >= 5) {
        //扣除最近联系人的高度34
        const top = 34 + 36 * (currentIndexRef.current - 5);
        scrollRef.current.scrollTop(top);
      }
      //循环到头的时候置顶
      if (currentIndexRef.current === 0) {
        scrollRef.current.scrollTop(0);
      }
    } else {
      // 超出一屏滚动
      if (currentIndexRef.current >= 5) {
        const top = 34 + 36 * currentIndexRef.current;
        scrollRef.current.scrollTop(top);
      } else {
        //一屏幕数据内 置顶
        scrollRef.current.scrollTop(0);
      }
    }
  };

  const renderContent = () => {
    return (
      <div
        style={{ left: 0, bottom: 0 }}
        className={s.mentionPanel}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className={s.box}>
          <Scrollbar ref={scrollRef}>
            <div className={s.list}>
              {list.map((item, index) => {
                let isParticipant = true;
                if (search && !participants.find((v) => v.uid === item.uid)) {
                  isParticipant = false;
                }
                return (
                  <div
                    className={classNames(s.item, { [s.active]: currentIndex === index })}
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentIndex(index);
                      currentIndexRef.current === index;
                      handleSelect(item);
                    }}
                  >
                    <div className={s.left}>
                      <AvatarPeople className={classNames(s.avatar)} avatarUrl={item.avatarUrl} />
                      <div className={s.name}>{item.name}</div>
                      {item.desc ? <div className={s.desc}>{item.desc}</div> : null}
                    </div>
                    {search && !isParticipant ? (
                      <div className={s.tag}>{I18N.auto.notJoined}</div>
                    ) : null}
                  </div>
                );
              })}
            </div>
          </Scrollbar>
        </div>
        <div className={s.tip}>
          <div>{I18N.auto.switch}</div>
          <div className={s.line}></div>
          <div>{I18N.auto.enter}</div>
          <div className={s.line}></div>
          <div>{I18N.auto.escCancel}</div>
        </div>
      </div>
    );
  };

  return (
    <Popover
      overlayClassName="mention-popover"
      visible={visible}
      {...props}
      placement="topLeft"
      showArrow={false}
      zIndex={2001}
      content={renderContent()}
      getPopupContainer={triggerNode => {
        return triggerNode.parentElement!;
      }}
      align={{ offset: position }}
    >
      {props.children}
    </Popover>
  );
};

// @ts-ignore
export default memo(React.forwardRef(AtPopover));
