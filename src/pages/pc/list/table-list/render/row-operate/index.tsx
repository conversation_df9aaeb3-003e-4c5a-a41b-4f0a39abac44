import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import s from './index.less';
export type Props = {
  className?: string;
};
const RowOperate: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, className } = props;
  return (
    <div className={classNames(s.rowOperate, className)}>
      <div className={classNames(s.warp)}>{children}</div>
    </div>
  );
};

export default RowOperate;
