.mentionPanel {
  position: absolute;
  // width: 270px;
  overflow: auto;
  border: 1px solid var(--aBlack12);
  border-radius: 8px;
  background-color: var(--bgTop);
  bottom: 150px;
  // right: 200px;
  min-width: 240px;
  z-index: 5555;
  cursor: pointer;
}
.box {
  padding: 4px;
  height: 222px;
}
.title {
  line-height: 22px;
  padding: 6px 8px;
  font-size: 12px;
  font-weight: 500;
  color: var(--TextSecondary);
}
.list {
  .item {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 8px;
    &.active,
    &:hover {
      background-color: var(--aBlack6);
      border-radius: 4px;
    }
  }
}

.left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 24px;
}

.name {
  margin-left: 8px;
  font-size: 13px;
  color: var(--TextPrimary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.desc {
  margin-left: 4px;
  font-size: 11px;
  color: var(--TextTertiary);
}

.tag {
  flex-shrink: 0;
  height: 20px;
  line-height: 20px;
  padding: 0 4px;
  border-radius: 2px;
  white-space: nowrap;
  background-color: var(--aBlack6);
  color: var(--TextTertiary);
  font-weight: 500;
  font-size: 11px;
}
.tip {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 8px 12px;
  line-height: 32px;
  background-color: var(--aBlack6);
  color: var(--TextSecondary);
  font-size: 11px;
  .line {
    width: 1px;
    height: 12px;
    margin: 0 10px;
    background-color: var(--aBlack8);
  }
}
