import { useDroppable } from '@dnd-kit/core';
import { rectSortingStrategy, SortableContext } from '@dnd-kit/sortable';
import { Virtuoso } from 'react-virtuoso';

import SortableItem, { SortableItemProps } from '../sortable-item';
import s from './index.less';

type DroppableProps = {
  items: SortableItemProps[];
  id: string;
  atBottomStateChange?: (atBottom: boolean) => void;
};

const Droppable = (props: DroppableProps) => {
  const { id, items, atBottomStateChange } = props;
  const { setNodeRef } = useDroppable({ id });
  // const atBottomStateChange = (atBottom: boolean) => {
  //   console.log(atBottom);
  // };

  return (
    <SortableContext id={id} items={items} strategy={rectSortingStrategy}>
      <div className={s.droppable} ref={setNodeRef}>
        <Virtuoso
          data={items}
          id={'scroll-content-group'}
          className={s.virtuoso}
          atBottomThreshold={150}
          atBottomStateChange={atBottomStateChange}
          // rangeChanged={rangeChanged}
          increaseViewportBy={{ top: 20, bottom: 20 }}
          itemContent={(index, item) => {
            return (
              <SortableItem key={item.taskId} taskId={item.taskId} title={item.title} data={item} />
            );
          }}
        ></Virtuoso>
      </div>
    </SortableContext>
  );
};

export default Droppable;
