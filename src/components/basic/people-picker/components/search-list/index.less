.text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding-top: 4px;
  color: var(--TextTertiary);
  font-size: 13px;
}

.search {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 4px 4px 0;
  margin-bottom: 28px;

  .divider {
    margin: 4px 0;
  }
}

.search__footer {
  position: absolute;
  bottom: 0;
  margin-left: -4px;
  margin-right: -4px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;

  display: flex;
  align-items: center;
  column-gap: 10px;
  width: calc(100% - 2px);
  font-size: 12px;
  color: var(--TextTertiary);

  background-color: var(--bgTop);
  border-top: 1px solid var(--aBlack6);
  padding: 2px 10px;
}
