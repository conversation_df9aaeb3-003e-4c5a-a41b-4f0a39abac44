import classNames from 'classnames';

import s from './index.less';

export interface IModeBtnProps<V = any> {
  value?: V;
  options: { label: string; value: V }[];
  onChange?(value: V, opt: { label: string; value: V }): any;
}

export function ModeBtn<V>(props: IModeBtnProps<V>) {
  const { value, options, onChange } = props;

  return (
    <div className={s.mode}>
      {options.map((opt) => {
        return (
          <div
            key={opt.label}
            className={classNames(s.modeBtn, {
              [s.modeBtnActive]: value === opt.value,
            })}
            onClick={() => {
              onChange?.(opt.value, opt);
            }}
          >
            {opt.label}
          </div>
        );
      })}
    </div>
  );
}
