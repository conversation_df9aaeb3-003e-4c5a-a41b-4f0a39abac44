.panel {
  padding: 4px;
  min-width: 160px;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 0 8px;
    border-radius: 4px;
    cursor: pointer;
    &:hover,
    .active {
      background-color: var(--aBlack4);
    }
    .title {
      font-size: 13px;
      font-weight: 400;
      color: var(--TextPrimary);
    }
    .desc {
      font-size: 12px;
      font-weight: 400;
      color: var(--TextTertiary);
      padding-left: 12px;
      white-space: nowrap;
    }
  }
  .divider {
    margin: 4px 8px;
  }
}
