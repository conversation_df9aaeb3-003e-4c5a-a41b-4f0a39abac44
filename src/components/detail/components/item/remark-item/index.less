.remark {
  position: relative;
  width: 100% !important;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: var(--TextPrimary-strong);
  outline: none;
  word-break: break-all;
  :global {
    .rock-input-textarea {
      font-size: 14px;
      line-height: 22px !important;
      color: var(--TextPrimary-strong);
      min-height: 22px !important;
      &::placeholder {
        font-size: 14px;
        color: var(--TextPrimary-strong);
      }
    }
  }
}
.disabledTip {
  color: var(--TextTertiary);
  font-size: 14px;
}

.remark__content {
  :global {
    .rock-editor,
    .rock-editor__toolbar {
      background-color: transparent;
    }
    .editor__wrapper {
      position: relative;
      top: 0px;
      left: -6px;
      width: calc(100% + 6px);
    }
  }
}
