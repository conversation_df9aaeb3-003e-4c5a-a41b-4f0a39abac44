.overlay {
  padding: 12px 20px 20px;
  background-color: var(--bgTop);
  border-radius: 8px;
  box-shadow: var(--ComBoxShadow);
}

.item {
  display: flex;
  align-items: center;
  color: var(--TextPrimary);
  font-weight: 400;
}
.divider {
  margin-top: 4px !important;
  margin-bottom: 4px;
}
.all {
  padding: 0;
}
.allBtn {
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  padding: 5px 12px;
  padding-right: 40px;
}
.delete {
  color: var(--R600);
}

.team__config {
  :global {
    .rock-modal-header {
      border-bottom: 1px solid var(--aBlack6);
    }

    .rock-modal-body {
      background-color: var(--N50);
      padding: 0 !important;
      .rock-scrollbar-view {
        padding: 16px 16px 0 !important;
      }
    }
  }
}
