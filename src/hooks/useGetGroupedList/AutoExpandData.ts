import { store } from '@/models/store'

class AutoExpandData {
  expandKeyMap: Map<string, Record<string, boolean>> = new Map()
  expandIndexMap: Map<string, Record<number, boolean>> = new Map()

  getViewId() {
    const viewId = store.getState().viewSetting.currentViewTab.viewId
    return viewId ? `${viewId}` : ''
  }

  setExpandData(type: 'key' | 'index', key: string | number, expand: boolean = true) {
    const viewId = this.getViewId()
    const data = type === 'key' ? this.expandKeyMap : this.expandIndexMap
    if (!data.has(viewId)) {
      data.set(viewId, {})
    }
    // @ts-ignore
    data.get(viewId)![key] = expand
  }

  setExpandKey(groupId: string, expand: boolean = true) {
    this.setExpandData('key', groupId, expand)
  }

  setExpandIndex(index: number, expand: boolean = true) {
    this.setExpandData('index', index, expand)
  }

  setExpandKeys(map: Record<string, boolean>) {
    const viewId = this.getViewId()
    this.expandKeyMap.set(viewId, map)
  }

  getExpandKeys(viewKey: string = this.getViewId()) {
    return this.expandKeyMap.get(viewKey) || {}
  }

  getExpandIndexes(viewKey: string = this.getViewId()) {
    return this.expandIndexMap.get(viewKey) || {}
  }

  clearExpandData() {
    this.expandKeyMap.clear()
    this.expandIndexMap.clear()
  }
}

/**
 * 分组后默认展开的数据，包括按key和index;
 * key给时间线用;
 * index给tanstack table用;
 */
const autoExpandData = new AutoExpandData()

export default autoExpandData
