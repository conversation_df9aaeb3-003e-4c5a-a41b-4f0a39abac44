.wrapper {
  width: 100%;

  .img__container {
    border: 1px solid var(--aBlack4);
    border-radius: 6px;
    overflow: hidden;
    line-height: 0;
  }

  img {
    width: 100%;
  }

  .title {
    font-size: 17px;
    font-weight: 600;
    line-height: 1;
    text-align: left;
    color: var(--Brand600);
  }

  .des {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    color: var(--TextPrimary);
    margin: 10px 0 10px;
  }

  .btnWrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 10px;

    .stepIndicator {
      color: var(--TextTertiary);
      font-size: 13px;
    }

    .btnGroup {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      column-gap: 8px;
    }

    .btn {
      cursor: pointer;
      background-color: var(--Brand500);
      color: var(--absWhite);
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 26px;
      font-size: 12px;
      font-weight: 500;
      line-height: 26px;
      border-radius: 6px;
    }
  }
}

.mask {
  position: fixed;
  z-index: 99;
  inset: 0;
}
