.box {
  :global {
    .todo-row-box-wrap {
      padding-left: 0;
    }
  }
}
.customFieldText {
  padding: 0;
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.listTitleEditor {
  height: 36px;
  width: 100%;
  // min-width: 0;

  &.fullWidth {
    width: 100%;
  }

  // &:hover {
  //   :global(#todo-title-input-editor) {
  //     background-color: var(--bgTop);
  //     border-radius: 4px;
  //     border: 1px solid var(--aBlack10) !important;
  //     &:hover {
  //       border-color: var(--aBlack24) !important;
  //     }
  //   }
  // }
  :global {
    .rock-input-wrapper {
      &::before {
        opacity: 0;
        transition: none;
      }
    }
    .contentEditableEditor-edit {
      padding: 7px !important;
      border-radius: 0;
    }
    .editor-content-editable-wrap {
      display: inline-flex;
      width: 100%;
      position: relative;
      height: fit-content;
    }
    .editor-content-editable-wrap-editding {
      z-index: 999 !important;
    }
  }
}
.listTitleContent {
  width: 100%;
  max-width: unset !important;
  padding: 2px 7px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: -1px !important;
  line-height: 20px;
  font-weight: 400;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  border: 1px solid transparent !important;
  border-radius: 0;
  color: var(--TextPrimary-strong);
  outline: none;
  user-select: none !important;
  -webkit-user-select: none !important;
  a[data-popo='tag'] {
    color: var(--LinkNormal);
  }
  &:hover {
    cursor: text;
  }

  :global {
    .rock-input-textarea {
      min-height: 20px !important;
      line-height: 20px !important;
      //color: var(--TextPrimary-strong) !important;
    }
  }
}

.focused {
  background-color: var(--bgTop) !important;
  border: 1px solid var(--Brand500) !important;
  border-radius: 4px;
  box-sizing: border-box !important;
  box-shadow: 0px 3px 4px 0px var(--absaBlack12) !important;
}
