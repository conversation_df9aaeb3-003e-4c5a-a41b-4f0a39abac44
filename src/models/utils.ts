import dayjs from 'dayjs'

import {
  GroupItem,
  PermissionsTasksMap,
  ProjectInfo,
  ProjectItemStatus,
  ServerProjectInfo,
  TaskGroupItem,
  TaskInfo,
  TimeValue,
  TodoInfo,
  UserInfo,
  ViewInfo,
} from '@/types'
import { CustomField, FieldTypeEnum } from '@/types/custom-field'
import {
  AddMenuId,
  EnumEmitter,
  EnumSessionType,
  NONE_GROUP_BY,
  OneAndMoreServerParamsType,
  PPTimeFormat,
  RelationType,
  TaskNavigatorMap,
  TaskNavigatorType,
  TaskTableRowType,
  ViewType,
} from '@/utils/const'
import {
  CustomFieldName,
  EnumDeadlineGroup,
  EnumField,
  EnumFieldNONE,
  EnumGroupBy,
  FinishedStatus,
  getFinishedGroupMap,
  getPriorityGroupMap,
  getTimeGroupMap,
  Priority,
} from '@/utils/fields'
import I18N from '@/utils/I18N'
import { store } from './store'
import { POPOBridgeEmitter } from '@popo-bridge/web'
import { isVerticalInViewport } from '@/utils'
import { RequestQueue } from '@/utils/requestQueue'
import { apiTaskCreateSubtaskPost } from '@/api'
import { apiCreateTask } from '@/api-common'
import { rIC } from '@/utils/requestIdleCallback'

export const getAddItem = (opt: {
  id?: number | string
  groupId?: string
  _rowType?: TaskTableRowType
  assigner?: UserInfo
  defaultParams?: TodoInfo
}) => {
  const { id, groupId, _rowType, assigner, defaultParams, ...rest } = opt
  const addItem = {
    _rowType: _rowType,
    id: id,
    taskId: id,
    groupId: groupId,
    assigner: {
      assignerUid: assigner?.uid,
      assignerName: assigner?.name,
      assignerAvatarUrl: assigner?.avatarUrl,
    },
    priority: Priority.Unset,
    ...defaultParams,
    deadlineEditable: true,
    isCreator: true,
    isCoordinator: true,
    isFollower: false,
    isParticipanter: true,
    completeCondition: OneAndMoreServerParamsType.all, // 用服务端数据格式
    ...rest,
  }
  return addItem
}

/**
 *
 * @param opt
 * @param opt.dataList 此处传入showDataList
 * @returns
 */
export const setNewAddItem = (opt: { dataList: TodoInfo[]; isHeader: boolean; item: TaskInfo }) => {
  const { dataList = [], isHeader, item } = opt
  const _data = [...dataList]
  if (isHeader) {
    _data.unshift(item)
  } else {
    const last = _data[_data.length - 1]
    if (last && last._rowType === TaskTableRowType.addBtn) {
      _data.splice(_data.length - 1, 0, item)
    } else {
      _data.push(item)
    }
  }
  return _data
}

type TfGroupsProps = {
  groups: GroupItem[]
  fieldName?: string //系统分组字段
  customFieldId?: string //当前分组的自定义字段
  customFields?: CustomField[]
  projectId?: TaskNavigatorType | string | number
}

/**
 * 策划、服务端和前端 协商之后的分组未设置的处理方式
 * 执行人和自定义人员 如果服务端未返回NONE 无的分组, 前端这边会补齐对应的分组和泳道
 * 另 系统字段
 * 截止时间 属于枚举,服务端一定会返回NONE
 * 完成状态 只有已完成和未完成
 * 优先级 枚举 无也是枚举 值时0 无NONE
 * 自定义字段
 * 时间 同截止时间
 * 单选 前端补齐选项值和无的分组, 服务端这边返回是有值才返回对应的组
 * 多选 如果服务端不返回NONE 前端补齐NONE 暂未支持多选分组
 * 人员 如果服务端不返回NONE 前端补齐NONE
 *
 * 此方法 还处理了新建自带的回填参数, 当然拖拽的时候也是可以用的
 * @param option
 * @param hadndleItem
 * @returns
 */
export const tfGroups = (option: TfGroupsProps, hadndleItem: (item: TaskGroupItem, index: number) => any) => {
  let { groups, fieldName, customFieldId, customFields, projectId, viewType } = option
  if (customFieldId) {
    const field = Object.assign(
      {},
      customFields?.find(item => item.fieldId === customFieldId),
    )

    const { type } = field || {}
    // 增加自定义字段数据
    if (type === FieldTypeEnum.option) {
      const obj = groups?.reduce((pre, cur) => {
        pre[cur.groupId!] = cur
        return pre
      }, {} as Record<string, any>)
      //@ts-ignore
      const options = [...(field?.options || [])] || []
      options.push({
        name: I18N.auto.notFilledIn,
        color: '',
        value: EnumFieldNONE.NONE,
      })

      // const optionsMap = options.reduce((pre, cur) => {
      //   pre[cur.value] = cur;
      //   return pre;
      // }, {} as Record<string, any>);

      // groups = options.map((item) => {
      //   //@ts-ignore
      //   const v = obj[item.value] || {};
      //   return {
      //     ...v,
      //     ...item,
      //     ...field,
      //     options: options,
      //     fieldId: field.fieldId,
      //     fieldVersion: field.fieldVersion,
      //     fieldType: field.type,
      //     value: item.value,
      //     customFieldValue: item.value,
      //     groupBy: field.fieldId,
      //     groupId: item.value,
      //     title: item.name,
      //   };
      // });

      // 游离的分组数量：即单选选项被删除后的分组数据；目的是将该数量合并到未填写的分组中
      let dissociatedCount = 0
      if (options.length) {
        groups = []
        for (let i = 0; i < options.length; i++) {
          const item = options[i]
          const v = obj[item.value] || {}
          // if (obj[item.value]) {
          groups.push({
            ...v,
            ...item,
            ...field,
            options: options,
            fieldId: field.fieldId,
            fieldVersion: field.fieldVersion,
            fieldType: field.type,
            value: item.value,
            customFieldValue: item.value,
            groupBy: field.fieldId,
            groupId: item.value,
            title: item.name,
          })
          // } else {
          // dissociatedCount = item.groupTaskCount || 0;
          // }

          delete obj[item.value]
        }
      }

      const restOptionKeys = Object.keys(obj)

      if (restOptionKeys.length) {
        restOptionKeys.forEach((k, i) => {
          const item = obj[k]
          dissociatedCount += item.groupTaskCount || 0
          // 看板下 需要将游离的分组数据也请求出来
          if (viewType === ViewType.kanban) {
            groups.push({
              ...item,
              ...field,
              options: options,
              fieldId: field.fieldId,
              fieldVersion: field.fieldVersion,
              fieldType: field.type,
              value: k,
              customFieldValue: k,
              groupBy: field.fieldId,
              groupId: k,
              title: item.name,
              isDissociated: true,
            })
          }
        })
      }

      if (dissociatedCount > 0) {
        for (let i = groups.length - 1; i >= 0; i--) {
          if (groups[i].groupId === EnumFieldNONE.NONE) {
            groups[i].groupTaskCount = (groups[i].groupTaskCount || 0) + dissociatedCount
            break
          }
        }
      }
    } else {
      if (type === FieldTypeEnum.user && !groups.find(item => item.groupId === NONE_GROUP_BY)) {
        groups.push({
          customFieldId: customFieldId,
          customFieldValue: [],
          groupBy: CustomFieldName,
          groupId: NONE_GROUP_BY,
          groupTaskCount: 0,
        })
      }
      groups = groups?.map(item => {
        return {
          ...field,
          ...item,
          fieldType: field.type,
        }
      })
    }
  } else if (fieldName === EnumGroupBy.ASSIGNEE && !groups.find(item => item.groupId === NONE_GROUP_BY)) {
    //如果是执行人分组 如果没有返回NONE 前端补齐
    groups.push({
      assigneeUids: [],
      groupBy: EnumGroupBy.ASSIGNEE,
      groupId: NONE_GROUP_BY,
      groupTaskCount: 0,
    })
  }
  const list = groups?.map((item, index) => {
    let title = item.title || ''
    let defaultParams = {}
    let groupId = item.groupId!
    if (item.groupBy === EnumGroupBy.DEADLINE) {
      const DeadlineGroupMap = getTimeGroupMap(dayjs())
      title = DeadlineGroupMap[groupId as EnumDeadlineGroup]?.title
      const timeParams = DeadlineGroupMap[groupId as EnumDeadlineGroup]?.defaultParams
      defaultParams = {
        deadline: timeParams.time,
        deadlineFormat: timeParams.format,
      }
    } else if (item.groupBy === EnumGroupBy.PRIORITY) {
      const PriorityGroupMap = getPriorityGroupMap()
      title = PriorityGroupMap[Number(groupId) as Priority]?.title
      defaultParams = PriorityGroupMap[Number(groupId) as Priority]?.defaultParams
    } else if (item.groupBy === EnumGroupBy.ASSIGNEE) {
      //assignees 返回不是全量 需要在assigneeUids解析一部分
      let moreUids: { uid: string }[] = []
      if (item.assigneeUids?.length) {
        moreUids = item.assigneeUids
          .filter(v => !item.assignees?.find(k => k.uid === v))
          .map(item => ({
            uid: item,
          }))
      }
      defaultParams = {
        assignees: item.assignees?.concat(moreUids),
      }
    } else if (item.groupBy === EnumGroupBy.FINISHED) {
      const FinishedGroupMap = getFinishedGroupMap()
      title = FinishedGroupMap[Number(groupId) as FinishedStatus]?.title
      defaultParams = FinishedGroupMap[Number(groupId) as FinishedStatus]?.defaultParams
    } else if (item.fieldId) {
      if (item.fieldType === FieldTypeEnum.datetime) {
        const DeadlineGroupMap = getTimeGroupMap(dayjs().endOf('D'))
        title = DeadlineGroupMap[groupId as EnumDeadlineGroup]?.title
        defaultParams = {
          customFieldValues: {
            projectId: projectId,
            values: {
              [item.fieldId]: {
                fieldId: item.fieldId,
                fieldVersion: item.fieldVersion,
                value: DeadlineGroupMap[groupId as EnumDeadlineGroup].defaultParams.time,
              },
            },
          },
        }
      } else {
        defaultParams = {
          customFieldValues: {
            projectId: projectId,
            values: {
              [item.fieldId]: {
                fieldId: item.fieldId,
                fieldVersion: item.fieldVersion,
                value:
                  item.customFieldValue !== EnumFieldNONE.NONE && item.customFieldValue !== ''
                    ? item.customFieldValue
                    : undefined,
              },
            },
          },
        }
      }
    }

    const itemData = {
      ...item,
      title: title,
      count: item.groupTaskCount || 0,
      groupTaskCount: item.groupTaskCount || 0, //默认增加的组可能没有这个字段,补齐
      id: item.groupId as string,
      defaultParams: defaultParams,
      assigneeCount: item.assigneeCount ?? item.assigneeUids?.length,
    }

    return hadndleItem ? hadndleItem(itemData, index) : itemData
  })
  return list
}

export const tfTasks = (
  opt: { dataList: TodoInfo[]; groups: TaskGroupItem[] },
  handleGroupSubRows = (group: TaskGroupItem, rows: TaskInfo[]) => rows,
) => {
  const { dataList, groups } = opt
  const groupsMap = groups.reduce((pre, cur) => {
    pre[cur.groupId!] = cur
    return pre
  }, {} as Record<string, any>)
  const mapList: Record<string, TodoInfo[]> = {}

  // combineParentSubTasks(dataList, {
  //   handleItem: (item) => {
  //     if (item.relationType !== RelationType.subTask) {
  //       let key = item.groupId!;
  //       if (key === '' || key === undefined || !groupsMap[key!]) {
  //         key = EnumFieldNONE.NONE;
  //       }
  //       if (mapList[key]) {
  //         mapList[key].push(item);
  //       } else {
  //         mapList[key] = [item];
  //       }
  //     }
  //     return item;
  //   },
  // });

  // 子任务跟随父任务，不做处理
  for (let i = 0; i < dataList.length; i++) {
    const item = dataList[i]
    let groupId = item.groupId!
    if (groupId === '' || groupId === undefined || !groupsMap[groupId!]) {
      groupId = EnumFieldNONE.NONE
    }
    if (mapList[groupId]) {
      mapList[groupId].push(item)
    } else {
      mapList[groupId] = [item]
    }
  }

  return groups.map(group => {
    const prevChildren = group.subtask || []

    const children = handleGroupSubRows(group, mapList[group.groupId!] || [])

    return {
      taskName: group.groupId,
      title: group.groupId,
      // !!!此处必须为string，有逻辑依赖taskId的类型校验
      taskId: group.groupId,
      ...group,
      subtask: prevChildren.concat(children),
    }
  })

  // let list: TodoInfo[] = [];
  // groups.forEach((item) => {
  //   if (mapList[item.groupId!]) {
  //     list = list.concat(mapList[item.groupId!]);
  //   }
  // });
  // return list;
}

export const groupArrayByIndices = (a: any[], b: number[]) => {
  let result = []
  let index = 0

  for (let groupSize of b) {
    if (index < a.length) {
      let group = a.slice(index, index + groupSize)
      result.push(group)
      index += groupSize
    } else {
      result.push([])
    }
  }
  return result
}

export const getUnfoldedArray = (dataList: TodoInfo[], groupList: TaskGroupItem[]) => {
  return groupList.reduce((result, item) => {
    if (item.unfold) {
      result.push(...dataList.slice(0, item.count))
      dataList = dataList.slice(item.count)
    } else {
      dataList = dataList.slice(item.count)
    }
    return result
  }, [] as TodoInfo[])
}

//<T extends TaskInfo>
export const handleSeverData = (
  taskInfo: TaskInfo,
  userInfo: UserInfo | undefined = store.getState().user.userInfo,
): TaskInfo => {
  const uid = userInfo?.uid
  const isCreator = taskInfo.assigner?.assignerUid === uid
  let isFollower = false
  if (taskInfo.followerUids) {
    isFollower = !!taskInfo.followerUids?.find(item => item === uid)
  } else if (taskInfo.followers) {
    isFollower = !!taskInfo.followers?.find(item => item.uid === uid)
  }
  let isCoordinator = false
  if (taskInfo.assigneeUids) {
    isCoordinator = !!taskInfo.assigneeUids?.find(item => item === uid)
  } else if (taskInfo.assignees) {
    isCoordinator = !!taskInfo.assignees?.find(item => item.uid === uid)
  }
  const isParticipanter = isCreator || isCoordinator
  return {
    ...taskInfo,
    id: taskInfo.taskId,
    memoAlarm: {
      time: taskInfo?.alarm?.alarmTimestamp,
      timeFormat: PPTimeFormat.dateAndTime, // 提醒时间默认有时分
      rrule: taskInfo?.alarm?.alarmRrule,
      selectedOption: taskInfo?.alarm?.selectedOption,
      createTime: taskInfo?.alarm?.alarmCreateTime,
      nextTime: taskInfo?.alarm?.nextAlarmTimestamp,
    },
    isCreator,
    isFollower,
    //执行人 & 创建人
    isParticipanter,
    //执行人
    isCoordinator,
    isExpired: taskInfo.isExpired && !(taskInfo.selfFinished || taskInfo.finished),
    isToday: taskInfo.isToday && !(taskInfo.selfFinished || taskInfo.finished),
  }
}

export const handleSeverList = ({
  list,
  userInfo,
  handleItem = handleSeverData,
  traverseFunc = 'map',
}: {
  list: TodoInfo[]
  userInfo?: UserInfo
  handleItem?: (item: TodoInfo) => TodoInfo
  traverseFunc?: 'map' | 'filter'
}) => {
  return list[traverseFunc](item => {
    handleItem(item)

    if (item.subtask) {
      item.subtask = handleSeverList({
        list: item.subtask as TodoInfo[],
        userInfo,
        handleItem,
        traverseFunc,
      })
    }
    return handleSeverData(item, userInfo)
  })
}

export const init_page_size = 30

export const isTaskMenuId = (id: TaskNavigatorType | number | string | undefined = '') => {
  return !!TaskNavigatorMap[id as TaskNavigatorType]
}

//[AddMenuId, ProjectListMenuId].includes(id)
export const isProjectId = (id: TaskNavigatorType | number | string | undefined = '') => {
  return Number(id) > 0
}

export const tfProjectItem = (item: ProjectInfo, extra: any) => {
  return {
    ...item,
    id: item.projectId!,
    members: item.memberVos?.map(v => ({
      uid: v.memberId!,
      name: v.name!,
      avatarUrl: v.pic!,
      sessionType: v.memberType as EnumSessionType, //1是人 3是群
    })),
    ...extra,
  }
}

export const tfProject = (projects: ServerProjectInfo[] | ServerProjectInfo, index?: string) => {
  if (Array.isArray(projects)) {
    const list: ProjectInfo[] = projects.map((item, i) => {
      const currIndex = typeof index === 'string' ? `${index}.${i}` : i + ''
      const getGroupProjects = () => {
        if (Array.isArray(item.groupProjects) && item.groupProjects.length) {
          return tfProject(item.groupProjects as ServerProjectInfo[], i + '')
        } else if (item.isGroup) {
          return [
            {
              status: ProjectItemStatus.placeholder,
              groupId: ProjectItemStatus.placeholder,
              projectId: item.projectId + ProjectItemStatus.placeholder,
              id: item.projectId + ProjectItemStatus.placeholder,
              name: I18N.auto.canDragToGaoup,
              index: currIndex + '.0',
            },
          ]
        }
        return []
      }

      const groups = getGroupProjects()
      return tfProjectItem(item, {
        index: currIndex,
        groupProjects: groups || [],
        children: groups || [],
      })
    })
    return list
  } else {
    return {
      ...projects,
      id: projects.projectId!,
      members: projects.memberVos?.map(v => ({
        uid: v.memberId!,
        name: v.name!,
        avatarUrl: v.pic!,
        sessionType: v.memberType as EnumSessionType, //1是人 3是群
      })),
      children: [],
    }
  }
}

/**
 * 格式化视图数据，兼容已删掉的自定义字段
 */
export function normalizeView<T extends ViewInfo>(view: T, fields: CustomField[]): T {
  const newView = { ...view }
  const { conditions = [], queryGroupBy, querySort, displays = [] } = newView
  let customFieldMap: Record<string, boolean> = {}
  fields.forEach(f => {
    customFieldMap[f.fieldId] = true
  })

  //删除了的自定义字段
  newView.conditions = conditions.filter(item => {
    return !item.customFieldId || customFieldMap[item.customFieldId!]
  })
  newView.displays = displays.filter(item => {
    return !item.customFieldId || customFieldMap[item.customFieldId!]
  })
  if (queryGroupBy?.customFieldId && !customFieldMap[queryGroupBy.customFieldId]) {
    newView.queryGroupBy = {
      fieldName: view.viewType === ViewType.kanban ? EnumGroupBy.DEADLINE : '',
    }
  }
  if (querySort?.customFieldId && !customFieldMap[querySort.customFieldId]) {
    newView.querySort = {
      fieldName: EnumField.createTime,
      order: 'desc',
    }
  }

  return newView
}
/**
 * 将子任务合并到父任务的subRows中
 * 要求：排序按照父任务顺序
 * @param list
 * @param parentTaskMap
 */
export function combineParentSubTasks(list: TaskInfo[], { handleItem }: { handleItem: (item: TaskInfo) => TaskInfo }) {
  const result: TaskInfo[] = []
  const parentSubRowsMap: Record<string, TaskInfo[]> = {}
  list.forEach(item => {
    handleItem(item)
    // 父任务
    if (item.relationType === RelationType.parentTask) {
      if (parentSubRowsMap[item.taskId!]) {
        item.subtask = parentSubRowsMap[item.taskId!]
      } else {
        item.subtask = item.subtask || []
      }
      parentSubRowsMap[item.taskId!] = item.subtask
      result.push(item)
    } else if (item.relationType === RelationType.subTask) {
      // 对应的父任务是否已经存在
      const parentSubRows = parentSubRowsMap[item.relationPid!]
      if (parentSubRows) {
        parentSubRows.push(item)
      } else {
        parentSubRowsMap[item.relationPid!] = [item]
      }
    } else {
      result.push(item)
    }
  })
  return result
}

interface IGetTaskByIndexesParams {
  indexes: number[] | string[]
  list: TodoInfo[]
  secondLast?: boolean
  id?: string
}
export const getTaskByIndexes = ({
  indexes,
  list,
  id,
  secondLast = false,
}: IGetTaskByIndexesParams): TodoInfo | null => {
  if (Array.isArray(indexes)) {
    let result: TodoInfo | null = null
    let currList = list
    indexes.some((item, index) => {
      if (index === indexes.length - (secondLast ? 2 : 1)) {
        let srcAddTarget = currList[item]
        // 更新引用
        result = srcAddTarget
        return true
      } else {
        currList = (currList[item]?.subtask as TodoInfo[]) || []
        return false
      }
    })
    return result
  }
  return null
}

export const setTaskByIndexes = (opt: {
  indexes: number[] | string[]
  list: TodoInfo[]
  id?: string
  task: TodoInfo
}) => {
  const { indexes, list, id, task } = opt
  if (Array.isArray(indexes)) {
    let currList = list
    let result: TodoInfo | null = null
    let stack = [...indexes]
    while (stack.length) {
      const item = stack.shift()
      if (stack.length === 0) {
        currList[item!] = task
        break
      }
      if (item !== undefined) {
        currList = (currList[item]?.subtask as TodoInfo[]) || []
      }
    }
  }
}
export const getCreateParams = (params: Partial<TodoInfo>, parentTaskId?: number) => {
  const parentId = parentTaskId || params.parentTaskId || params.parentId

  return {
    title: params.title,
    assignees: params.assignees?.map(v => v.uid!) || [],
    priority: params.priority || Priority.Unset,
    startTime: params.startTime,
    deadline: params.deadline,
    deadlineFormat: 'timeFormat' in params ? params.timeFormat : params.deadlineFormat,
    rrule: params.rrule,
    completeCondition: params.completeCondition,
    followers: params.followers?.map(v => v.uid!) || [],
    customFieldValues: params.customFieldValues,
    parentTaskId: parentId,
    parentId: parentId,
    alarm: {
      alarmCreateTime: params.alarm?.alarmCreateTime || 0,
      alarmTimestamp: (params.alarm as TimeValue)?.time || 0,
      alarmRrule: (params.alarm as TimeValue)?.rrule || '',
      selectedOption: params.alarm?.selectedOption,
    },
  }
}

interface IGetNoPermissionListParams {
  list: TodoInfo[]
  permissionsMap: Record<string, any>
  userInfo?: UserInfo
  widthSubtasks?: boolean
  limit?: number
}
/**
 *
 * @param {Object} param
 * @param {TodoInfo[]} param.list 要处理权限的列表
 * @param {Record<string, any>} param.permissionsMap 已经有的权限列表map
 * @param {UserInfo}  param.userInfo 用户信息
 * @returns
 *  - noPermissionsList: 没有权限的任务列表
 *  - noPermissionsTaskIds: 没有权限的任务id列表
 *  - myFollowedTaskIds: 我关注的任务id列表
 */
export const getNoPermissionList = ({
  list,
  permissionsMap,
  userInfo,
  widthSubtasks = true,
  limit = 50,
}: IGetNoPermissionListParams) => {
  const noPermissionsList = []
  const noPermissionsTaskIds = []
  const myFollowedTaskIds = []
  const stack = [...list]
  while (stack.length) {
    if (noPermissionsTaskIds.length >= limit) {
      break
    }
    const item = stack.shift()
    if (item) {
      const currPermissions = permissionsMap?.[item.taskId!]?.permissions
      if (!currPermissions) {
        noPermissionsList.push(item)
        noPermissionsTaskIds.push(item.taskId!)
        if (userInfo && item.followerUids?.includes(userInfo.uid!)) {
          myFollowedTaskIds.push(item.taskId!)
        }
      }
      if (widthSubtasks && item?.subtask?.length) {
        item.permissions = currPermissions
        stack.push(...(item.subtask as TodoInfo[]))
      }
    }
  }
  return { noPermissionsList, noPermissionsTaskIds, myFollowedTaskIds }
}

export const getExpandedIndexMap = (currViewId?: string) => {
  const viewId = currViewId || store.getState().viewSetting.currentViewTab?.viewId
  if (!viewId) {
    return {}
  }
  const expandedIndexMapStr = localStorage.getItem('expandedIndexMap')
  try {
    const expandedIndexMap: Record<string, Record<string, boolean>> = JSON.parse(expandedIndexMapStr || '{}')
    return expandedIndexMap[viewId] || {}
  } catch (error) {
    return {}
  }
}

export const setExpandedIndexMap = (newExpandedIndexMap?: Record<string, boolean>, currViewId?: string) => {
  rIC(() => {
    const viewId = currViewId || store.getState().viewSetting.currentViewTab?.viewId
    if (!viewId || !newExpandedIndexMap) {
      return
    }
    const expandedIndexMapStr = localStorage.getItem('expandedIndexMap')
    try {
      const expandedIndexMap: Record<string, Record<string, boolean>> = JSON.parse(expandedIndexMapStr || '{}')
      expandedIndexMap[viewId] = newExpandedIndexMap
      localStorage.setItem('expandedIndexMap', JSON.stringify(expandedIndexMap))
    } catch (error) {
      localStorage.setItem('expandedIndexMap', JSON.stringify({ [viewId]: newExpandedIndexMap }))
    }
  })
}

export const removeItemActive = () => {
  const preActiveEl = document.querySelector('.tanstack-table__tr.active')
  if (preActiveEl) {
    preActiveEl.classList.remove('active')
  }
}

export const addItemActive = (taskId: number) => {
  const activeItem = document.querySelector(`tr[id="${taskId}"]`)
  if (activeItem) {
    activeItem.querySelector('.status__wrapper')?.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
    activeItem.classList.add('active')
  }
}

export const getViewIsGroup = (): boolean => {
  const { queryGroupBy, queryGroupBys } = store.getState().viewSetting.currentViewTab || {}
  return !!(queryGroupBys?.length || queryGroupBy?.fieldName || queryGroupBy?.customFieldId)
}

export const scrollToIndex = ({
  taskId,
  subtaskLength = 0,
  callback,
  index,
}: {
  taskId?: string | number
  callback?: () => void
  subtaskLength?: number
  index?: number
}) => {
  setTimeout(() => {
    const { rows } = window.tanstackTable.getRowModel()

    let scrollIndex =
      typeof index === 'number' ? index : rows.findIndex((row: TodoInfo) => row?.original?.taskId === taskId)
    // scrollIndex += subtaskLength;
    const targetItem = rows[scrollIndex]
    if (targetItem) {
      const targetEl = document.querySelector(`tr[id="${targetItem.original.taskId}"]`) as HTMLDivElement
      // 判断targetEl是否在视口内
      const { isInViewport, dt, db } = isVerticalInViewport(
        targetEl!,
        document.querySelector('.tanstack-table__wrapper'),
      )

      callback?.()

      if (isInViewport && dt > 40 && db < 0) {
        return
      }
    }

    if (scrollIndex > -1) {
      POPOBridgeEmitter.emit(EnumEmitter.TanstackSoscrollToIndex, scrollIndex)
    }
  }, 160)
}

export const isTaskGotPermission = (taskId?: string | number, permissionsMap?: PermissionsTasksMap) => {
  if (typeof taskId !== 'number') {
    return false
  }

  if (!permissionsMap) {
    permissionsMap = store.getState().permissions.permissionsMap || {}
  }

  const gotted = permissionsMap?.[`${taskId}`]?.permissions

  return !!gotted
}

interface BatchRequestOptions<T, R> {
  // Maximum number of items to batch before sending request
  maxBatchSize?: number
  // Maximum time (ms) to wait before sending request regardless of batch size
  maxWaitTime?: number
  // Function to process the batched items
  processCallback: (items: T[]) => Promise<R>
  // Optional callback for results
  // onBatchProcessed?: (result: R, items: T[]) => void;
  // Optional error handler
  onError?: (error: any, items: T[]) => void
  // Optional request queue for controlling concurrency
  // requestQueue?: RequestQueue;
}
const maxConcurrentRequests = 1

export class BatchRequest<T, R> {
  batch: T[] = []
  timer: NodeJS.Timeout | null = null
  options: BatchRequestOptions<T, R>
  // gottedMap: Record<string, boolean> = {};
  requestQueue: RequestQueue

  constructor(options: BatchRequestOptions<T, R>) {
    this.options = {
      maxBatchSize: 30,
      maxWaitTime: 32,
      ...options,
    }
    this.requestQueue = new RequestQueue(maxConcurrentRequests)
  }

  add(item: T): void {
    // if (Array.isArray(item)) {
    //   this.batch.push(...item);
    // } else {
    //   this.batch.push(item);
    // }
    this.batch.push(item)
    // if (!this.gottedMap[item.taskId]) {
    //   this.gottedMap[item.taskId] = true;

    //   console.log('add', item.taskId, this.gottedMap);
    // }

    // Start timer if this is the first item
    if (this.batch.length === 1 && !this.timer) {
      this.timer = setTimeout(() => this.flush(), this.options.maxWaitTime)
    }

    // If we've reached max batch size, flush immediately
    if (this.batch.length >= this.options.maxBatchSize!) {
      this.flush()
    }
  }

  async flush(): Promise<void> {
    if (this.batch.length === 0) return

    // Clear any pending timer
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }

    const itemsToProcess = [...this.batch]
    this.batch = []

    try {
      const processRequest = () => this.options.processCallback(itemsToProcess)

      // Use request queue if provided, otherwise process directly
      const result = this.requestQueue ? await this.requestQueue.addRequest(processRequest) : await processRequest()

      // this.options.onBatchProcessed?.(result, itemsToProcess);
      // this.options.processCallback(itemsToProcess);
    } catch (error) {
      console.log('error', error)
      this.options.onError?.(error, itemsToProcess)
    }
  }

  // Manually flush remaining items
  async forceFlush(): Promise<void> {
    await this.flush()
  }
}

export const getPageIsProjectGroup = () => {
  return location.href.includes('project-group')
}
/**
 * 会话中的任务列表或关联的项目
 * @returns
 */
export const getIsInSession = () => {
  return location.href.includes('/session')
}
/**
 * 会话中的任务列表
 * @returns
 */
export const getIsSessionTasks = () => {
  return location.href.includes('/session/tasks')
}

export const updateTaskListByInfo = (opt: any) => {
  // targetTaskId 用于处理新建后替换掉新建的本地数据
  const { taskInfo, actionType, targetTaskId, dataList } = opt
  const { parentId, taskId } = taskInfo

  const tableList = dataList ? [...dataList] : []

  const newTargetId = targetTaskId || taskId
  // 子任务处理
  if (parentId) {
    const stack = [...tableList]
    let parentTask = null
    while (stack.length && !parentTask) {
      const item = stack.shift()
      if (item?.taskId === parentId) {
        parentTask = item
        break
      }
      if (item && item.subtask) {
        stack.push(...(item.subtask as TodoInfo[]))
      }
    }
    if (Array.isArray(parentTask?.subtask)) {
      const index = parentTask.subtask.findIndex(v => v.taskId === newTargetId)
      if (index !== -1) {
        if (actionType === 'delete') {
          parentTask.subtask.splice(index, 1)
        } else {
          const srcTask = parentTask.subtask[index]
          parentTask.subtask[index] = { ...srcTask, ...taskInfo }
        }
        return tableList
      }
    }
    return tableList
  } else {
    // 父任务或者普通任务处理
    function bfs(tableList: TodoInfo[]) {
      tableList.some((item, index) => {
        if (item.taskId === newTargetId) {
          if (actionType === 'delete') {
            tableList.splice(index, 1)
          } else {
            tableList[index] = { ...item, ...taskInfo }
          }

          return true
        }
        if (item.subtask) {
          bfs(item.subtask as TodoInfo[])
        }
      })
    }
    bfs(tableList)

    // const index = tableList.findIndex((v) => v.taskId === newTargetId);
    // if (index !== -1) {
    //   if (actionType === 'delete') {
    //     tableList.splice(index, 1);
    //   } else {
    //     tableList[index] = taskInfo;
    //   }
    // }

    return tableList
  }
}

export const createTask = async (opt: any) => {
  const state = store.getState()
  const { userInfo } = state.user
  const { navigatorId } = state.viewSetting

  let projectId
  if (!isTaskMenuId(navigatorId!)) {
    projectId = navigatorId
  }
  const params = {
    assigner: userInfo?.uid,
    ...opt,
    //@ts-ignore
    timezone: dayjs.tz.guess(),
    projectId,
  }

  if (getIsSessionTasks()) {
    const searchParams = new URLSearchParams(location.search)
    const sessionId = searchParams.get('sessionId')
    const sessionType = searchParams.get('sessionType')
    params.sourceId = sessionId
    params.sourceName = 'test'
    params.source = Number(sessionType)

    if (sessionType === '1') {
      // TODO: p2p 执行人逻辑处理
      params.sourceSubType = 10
      params.sendCardToSession = 0
      params.createFormStyle = 1
      params.taskShowInSession = 1
      // params.assignees = [sessionId];
    }
  }

  const requestFunc = params?.parentTaskId ? apiTaskCreateSubtaskPost : apiCreateTask
  return requestFunc(params)
}
