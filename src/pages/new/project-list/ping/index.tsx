import classNames from 'classnames';

import { apiProjectGroupPinPost, apiProjectPinPut } from '@/api';
import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import { EnumEmitter } from '@/utils/const';

interface Props {
  className?: string;
  isPin: boolean;
  projectId: number;
  groupId: number;
  isGroup?: boolean;
  onSuccess?: (v: { isPin: boolean; projectId: number }) => void;
}

const Ping: React.FC<Props> = (props) => {
  const { isPin, projectId, groupId, isGroup, onSuccess } = props;
  const togglePing = (v: boolean) => {
    if (isGroup) {
      apiProjectGroupPinPost({
        groupId: projectId,
        isPin: v,
      }).then(() => {
        POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, {
          projectId: projectId,
          groupId: projectId,
          isPin: v,
        });
        onSuccess?.({ isPin: v, projectId: projectId });
      });
      return;
    }

    apiProjectPinPut({
      projectId: projectId,
      // 服务端要求这样, 协商使用isPing未果
      isPin: v,
    }).then(() => {
      POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, {
        projectId: projectId,
        groupId,
        isPin: v,
      });
      onSuccess?.({ isPin: v, projectId: projectId });
    });
  };
  return isPin ? (
    <IconBtn
      title={I18N.auto.removeFromNavigation}
      className={classNames(s.pingBtn, s.ping)}
      iconClassName={s.ping}
      iconName="icon-sys_pin"
      onClick={(e) => {
        e.stopPropagation();
        togglePing(false);
      }}
    ></IconBtn>
  ) : (
    <IconBtn
      title={I18N.auto.addToNavigation}
      className={classNames(s.pingBtn)}
      iconName="icon-sys_unpin"
      onClick={(e) => {
        e.stopPropagation();
        togglePing(true);
      }}
    ></IconBtn>
  );
};
export default Ping;
