import './mention-blot';

import { pp } from '@popo-bridge/web';
import _ from 'lodash';
import Quill, { Sources } from 'quill';
import Delta from 'quill-delta';
import { createRoot, Root } from 'react-dom/client';

import { apiTodoCommentAtSearchPost } from '@/api';
import { UserInfo } from '@/types';
import { AtType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { Platform } from '@/utils/platform';
import { validatesVersion } from '@/utils/validate-version';

import { POPOAtProps } from '../../types';
import { MentionExtraData } from '../../utils';
import s from './index.less';
import MentionList from './mention-list';

type Options = {
  container: string;
  // participants: UserInfo[];
  // assignor: UserInfo;
};

export default class POPOMention {
  root: Root;
  quill: Quill;
  // container: HTMLElement;
  mentionContainer: HTMLElement;
  cursorPos: number;
  composition: boolean;
  flagIndex: number;
  show: boolean;
  start: number;
  end: number;
  noSearch: boolean;
  throttledFetch: (text: string) => void;
  constructor(quill: Quill, options: Options) {
    this.quill = quill;
    this.throttledFetch = _.throttle(this.getPeopleList, 1000);
    quill.on('text-change', this.textChange.bind(this));
    document.addEventListener('compositionstart', () => {
      this.composition = true;
    });
    document.addEventListener('compositionend', () => {
      this.composition = false;
      if (this.show) {
        this.getSearchText();
      }
    });
  }
  async getRecentContacts() {
    this.noSearch = true;
    const participants = [...(MentionExtraData.participants || [])].sort((item) =>
      item.uid === MentionExtraData.userInfo?.uid ? -1 : 0
    );
    if (participants?.length > 1) {
      //个人待办和1v1待办总人数肯定小于2
      if (participants?.length === 2) {
        return participants;
      }
      // 有未完成的人 且是指派人
      if (
        participants
          .filter((item) => item.uid !== MentionExtraData.assignor?.uid)
          .some((item) => !item.finished) &&
        MentionExtraData.assignor?.uid === MentionExtraData.userInfo?.uid
      ) {
        participants.unshift({
          name: I18N.auto.unfinishedToDoList,
          uid: '',
          avatarUrl: 'https://popo.res.netease.com/popo-assets/todo/avatar-unfinished.png',
          attype: AtType.unfinished,
        });
      }
      //如果只有2条以上数据 表示多人待办 加上所有人的操作
      participants.unshift({
        name: I18N.auto.everyone,
        uid: '',
        avatarUrl: 'https://popo.res.netease.com/popo-assets/todo/avatar-all.png',
        attype: AtType.all,
        desc: I18N.auto.mentionAllAchievements,
      });
      return participants;
    } else {
      if (!Platform.isPOPO) {
        return participants;
      }
      const { data = [] } = await pp.getRecentContactsList({ maxCount: 30 }).then((res) => {
        return {
          data:
            res.data?.filter(
              (item) =>
                !item.uid?.includes('@cus.robot.popo.com') &&
                !item.uid?.includes('@app.robot.popo.com')
            ) || [],
        };
      });
      let list: UserInfo[] = [];
      if (data?.length) {
        list = data.map((item) => ({
          name: item.name,
          uid: item.uid,
          avatarUrl: item.headPic,
        }));
      }
      return list;
    }
  }
  async getPeopleList(text: string) {
    let list: UserInfo[] = [];
    if (text) {
      list = await apiTodoCommentAtSearchPost({ keyword: text });
      this.noSearch = false;
      if (!list.length) {
        this.remove();
        return;
      }
    } else {
      list = await this.getRecentContacts();
    }
    try {
      this.root.render(
        <MentionList
          quill={this.quill}
          _this={this}
          remove={() => {
            this.remove();
          }}
          onSlected={(v) => {
            this.updateContent(v);
          }}
          list={list}
          participants={MentionExtraData.participants || []}
          search={!this.noSearch}
        />
      );
    } catch (error) {
      // console.log(error);
    }
  }
  async getSearchText() {
    const selection = this.quill.getSelection();
    const start = this.flagIndex + 1;
    this.start = this.flagIndex;
    let end = 0;
    if (selection?.length === 0) {
      end = selection.index;
    }
    if (end > 0) {
      this.end = end;
      const text = this.quill.getText(start, end - start);

      this.throttledFetch(text);
    }
  }
  remove() {
    if (this.root) {
      this.root.unmount();
    }
    this.mentionContainer.remove();
  }
  updateContent(user: UserInfo) {
    this.quill.deleteText(this.start, this.end - this.start);
    this.quill.insertEmbed(this.start, 'popoAt', {
      uid: user.uid,
      text: user.name,
      atType: user.attype,
    } as POPOAtProps);
    this.quill.insertText(this.start + 1, ' ');
    this.quill.setSelection({ index: this.start + 2, length: 0 });
  }
  async textChange(delta: Delta, oldDelta: Delta, source: Sources) {
    if (source === 'user') {
      let ops = delta.ops;
      const op = ops[ops.length - 1];
      //如果当前是个人待办 切不支持获取联系人桥 participants包含
      if (
        MentionExtraData.participants?.length === 1 &&
        !validatesVersion('getRecentContactsList')
      ) {
        return;
      }
      if (op.insert === '@') {
        if (ops.length === 1) {
          this.flagIndex = 0;
          this.start = 0;
        } else {
          this.flagIndex = ops[0].retain as number;
          this.start = ops[0].retain as number;
        }
        this.end = this.start + 1;
        if (this.mentionContainer) {
          this.remove();
        }
        this.mentionContainer = document.createElement('div');
        this.mentionContainer.className = s['ql-pp-mention-container'];
        document.querySelector('#quillEditorBox')?.appendChild(this.mentionContainer);
        const root = createRoot(this.mentionContainer);
        this.root = root;
        // 获取最近联系人
        const list = await this.getRecentContacts();
        root.render(
          <MentionList
            quill={this.quill}
            _this={this}
            remove={() => {
              this.remove();
            }}
            onSlected={(v) => {
              //英文模式会执行这个
              this.updateContent(v);
            }}
            list={list}
            search={!this.noSearch}
            participants={MentionExtraData.participants || []}
          />
        );

        this.show = true;
      } else if (this.show) {
        //判断当前删除了 @
        if (ops.length === 1 || ops[0].retain === this.flagIndex) {
          this.remove();
          this.show = false;
        } else {
          //
          if (!this.composition) {
            this.getSearchText();
          }
        }
      }
    }
  }
}

Quill.register('modules/popoAt', POPOMention);
