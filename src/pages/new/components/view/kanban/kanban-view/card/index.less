.card {
  position: relative;
  width: 240px;
  height: auto;
  padding: 6px;
  margin-top: 8px;
  border-radius: 6px;
  border: 1px solid var(--aBlack12);
  background-color: var(--bgBottom);
  box-shadow: 0px 1px 4px 0px var(--absaBlack8);
  transition: all ease 0.25s;

  &:hover {
    border-color: var(--Brand500);
  }

  &.cardFinished {
    background-color: var(--N50);
    border-color: var(--aBlack8);
    &:hover {
      border-color: var(--Brand500);
    }
    :global(#todo-title-input-editor) {
      color: var(--TextSecondary-ongrey) !important;
    }
  }

  &.cardAdd {
    background-color: var(--aBrand4);
    border-color: var(--Brand600);
    box-shadow: 0px 1px 6px 0px var(--absaBlack12);
  }

  &.cardDragging {
    border-color: var(--Brand600);
  }

  &.sideBorderColor {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
      border-radius: 4px 0 0 4px;
      background-color: var(--sideBorderColor);
      transition: background-color ease 0.25s;
    }
  }

  :global {
    // .todo-rendedr-peoples-avatar {
    //   width: 22px !important;
    //   height: 22px !important;
    // }
    .todo-content-editable {
      line-height: 22px;
    }

    .render__item {
      margin-top: 2px;

      .render__item--left {
        margin-right: 10px;
      }
    }
  }
}

.titleItem {
  align-items: flex-start !important;
  margin-top: 0 !important;
  height: auto !important;
  min-height: 30px !important;
  padding-top: 4px !important;
  :global {
    .rock-input-wrapper.rock-input-wrapper-inline .rock-input-textarea {
      line-height: 22px;
      min-height: 22px;
    }
    .todo-content-editable {
      font-weight: 500;
    }
  }
}

.titleItemLeft {
  position: relative;
  top: 3px;
}
.deadlineItem {
  height: auto !important;
  align-items: flex-start !important;
  padding-top: 4px !important;
}

.avatar {
  margin-right: 3px;
  height: 20px !important;
  width: 20px !important;
}
.customSelect {
  width: 100%;
  cursor: pointer !important;
  :global {
    .rock-select-selector {
      padding-left: 6px !important;
      padding-right: 0;
    }
    .custom-select-label {
      height: 20px;
      line-height: 20px;
      font-size: 12px;
    }
  }
}

.customNumber {
  border: none;
  :global {
    input {
      text-align: left !important;
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}

.line {
  margin: 4px 0;
}
.footer {
  display: flex;
  align-items: center;
}
.footerBtn {
  height: 24px;
  flex-shrink: 0;
  padding: 0 6px !important;
  display: flex;
  align-items: center;
  .icon {
    color: var(--IconTertiary);
    font-size: 12px;
  }
}
