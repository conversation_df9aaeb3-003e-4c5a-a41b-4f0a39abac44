import { pp } from '@popo-bridge/web';
import copy from 'copy-to-clipboard';

import { apiProjectGetShareLinkGet, apiProjectSharePost } from '@/api';
import { ProjectInfo } from '@/types';
import { BridgeUserType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { validatesVersionAndTip } from '@/utils/validate-version';

interface Props {
  projectInfo?: ProjectInfo;
}

const useProjectShare = (props: Props) => {
  const { projectInfo } = props;
  const onShare = () => {
    if (validatesVersionAndTip('chooseIMContacts')) {
      pp.chooseIMContacts({
        groupCollapse: false,
        title: I18N.auto.shareTo,
        checkTeamMsgForward: true,
      }).then((res) => {
        if (Array.isArray(res.data)) {
          const uids: string[] = [];
          const teamIds: string[] = [];
          res.data.forEach((item) => {
            if (item.type === BridgeUserType.p2p) {
              uids.push(item.id);
            } else if (item.type === BridgeUserType.team) {
              teamIds.push(item.id);
            }
          });
          if (uids.length || teamIds.length) {
            apiProjectSharePost({
              projectId: projectInfo?.projectId,
              uids: uids || [],
              teamIds: teamIds,
            })
              .then(() => {
                pp.showToast({
                  title: I18N.auto.sharingSuccess,
                  iconType: 1,
                });
              })
              .catch(() => {
                pp.showToast({
                  title: I18N.auto.sharingFailed,
                  iconType: 3,
                });
              });
          }
        }
      });
    }
  };
  const onCopy = async () => {
    // 从服务端获取复制的链接
    const url = await apiProjectGetShareLinkGet({ projectId: projectInfo?.projectId + '' });
    if (url) {
      copy(url);
      pp.setClipboardData({ data: url });
      pp.showToast({
        title: I18N.auto.copySuccessful,
      });
    }
  };
  return {
    onShare,
    onCopy,
  };
};

export default useProjectShare;
