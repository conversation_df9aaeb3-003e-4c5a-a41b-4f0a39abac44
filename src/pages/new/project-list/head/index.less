.head {
  display: flex;
  align-items: center;
  padding-left: 20px;
  padding-right: 16px;
  height: 64px;
  // border-bottom: 1px solid var(--aBlack6);
  flex-shrink: 0;
  .headLeft {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 16px;
    min-width: 10px;

    .title {
      line-height: 24px;
      font-size: 16px;
      font-weight: 600;
      color: var(--TextPrimary);
    }
  }
  .headRight {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }
}
.search {
  width: 200px !important;
  margin-right: 12px;
  :global {
    .rock-input {
      padding-top: 3px;
      padding-bottom: 3px;
    }
  }
}
.btn {
  display: flex;
  align-items: center;
  height: 28px;
  padding-left: 10px;
  padding-right: 12px;
  margin-right: 12px;
  &:global(.rock-btn-primary) {
    background-color: var(--Brand500);
    color: var(--absWhite);
    &:hover {
      background-color: var(--Brand600);
    }
  }
}
.icon {
  color: var(--IconPrimary);
}
