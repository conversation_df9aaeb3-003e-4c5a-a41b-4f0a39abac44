import classNames from 'classnames';
import { useState } from 'react';

import { Button, DropdownSelect, Icon } from '@/components/basic';
import { IField } from '@/utils/fields';
import I18N from '@/utils/I18N';

import s from './index.less';

interface TabsProps {
  onChange: (v: IField) => void;
  data: IField[];
}

const Add: React.FC<TabsProps> = (props) => {
  const { data, onChange } = props;
  const [visible, setVisible] = useState<boolean>(false);
  return (
    <DropdownSelect
      data={data}
      onChange={(value) => {
        const item = data?.find((item) => item.value === value);
        if (item) {
          onChange(item);
          setVisible(false);
        }
      }}
      menuClassName={s.menu}
      visible={visible}
      onVisibleChange={(v) => {
        setVisible(v);
      }}
    >
      <Button
        className={classNames(s.addBtn)}
        icon={<Icon name="icon-sys_add" fontSize={16} className="mr-4"></Icon>}
        type="checked-neutral"
      >
        {I18N.auto.addConditions}
      </Button>
    </DropdownSelect>
  );
};
export default Add;
