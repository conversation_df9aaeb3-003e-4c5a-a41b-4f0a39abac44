import { ConfigProvider } from '@bedrock/components';
import { PropsWithChildren, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { Dropdown } from '@/components/basic';
import { TaskGuide } from '@/components/guide/useGuide';
import { GuideType } from '@/utils/const';

import s from './index.less';
import GroupOverlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
}

const FieldsSetting: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled, className = '', ...rest } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const zIndexRef = useRef<number>(1000);

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext);

  //bedrock dropdown bug, 每次rerender zindex 会递增，无论visible是否发生变化

  zIndexRef.current = useMemo(() => {
    if (visible) {
      return getGlobalZIndex();
    }
    return zIndexRef.current;
  }, [visible]);

  useEffect(() => {
    TaskGuide.emitPending(GuideType.project);
  }, []);

  return (
    <Dropdown
      className={`${s.dropdown} ${className}`}
      title={children}
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      disabled={disabled}
      open={visible}
      onOpenChange={(v) => {
        setVisible(v);
      }}
      zIndex={zIndexRef.current}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={<GroupOverlay className={s.panl} onClose={() => setVisible(false)}></GroupOverlay>}
      {...rest}
    />
  );
};

export default FieldsSetting;
