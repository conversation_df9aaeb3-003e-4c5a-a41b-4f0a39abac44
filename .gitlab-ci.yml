image: ncr.nie.netease.com/popo/oscar-node16:20240914
stages:
  - build
  - publish-cdn
  - gray_publish
  - prod-publish

variables:
  PROJECT_NAME: popo-tobedone-fe
  AUTH_USER: $ACM_AUTH_USER
  AUTH_KEY: $ACM_AUTH_KEY
  DEPLOY_PROJECT: popo
  ACM_CONFIG_NAME: popo-tobedone-fe.conf
  ACM_GROUP: popo_open_fe_sym3
  ACM_NAMESPACE: test
  ACM_HOST: int-acm.nie.netease.com

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^test|staging|production|production-canary$/
      when: always

build:
  stage: build
  script:
    - VERSION=$(date +%Y%m%d%H%M%S)_$CI_COMMIT_SHORT_SHA
    - pnpm -v
    - pnpm i
    - npm run "build:$CI_COMMIT_REF_NAME" -- --version_tag="$VERSION"
    - echo $VERSION > version_tag
  cache:
    key: '$CI_COMMIT_REF_SLUG'
    paths:
      - node_modules/
  only:
    - production
    - production-canary
    - staging
    - test
  artifacts:
    paths:
      - dist/
      - version_tag
  tags:
    - docker

publish-cdn:
  stage: publish-cdn
  script:
    - PUBLISH_VERSION=$(cat version_tag)
    - echo $PUBLISH_VERSION
    - npm run publishCdn -- --version_tag="$PUBLISH_VERSION"
  cache:
    key: '$CI_COMMIT_REF_SLUG'
    paths:
      - node_modules/
    policy: pull
  artifacts:
    paths:
      - version_tag
  tags:
    - docker
  needs: ['build']
  dependencies:
    - build

.generate_rules: &generate_rules
  - if: $CI_COMMIT_BRANCH == 'test'
    variables:
      ACM_NAMESPACE: test
      ACM_GROUP: popo_open_fe_gz
  - if: $CI_COMMIT_BRANCH == 'staging'
    variables:
      ACM_NAMESPACE: staging
  - if: '$CI_COMMIT_BRANCH == "production" || $CI_COMMIT_BRANCH == "production-canary"'
    variables:
      ACM_NAMESPACE: prod
    when: manual

gray_publish:
  stage: gray_publish
  script:
    - PUBLISH_VERSION=$(cat version_tag)
    - echo $PUBLISH_VERSION
    - npm run genNginxConf -- --grayscale_tag=gray --env_tag=$CI_COMMIT_BRANCH --version_tag=$PUBLISH_VERSION --cimmit_user=$GITLAB_USER_EMAIL  --project=$DEPLOY_PROJECT --auth_user=$AUTH_USER --auth_key=$AUTH_KEY --acm_host=$ACM_HOST --acm_group=$ACM_GROUP --acm_namespace=$ACM_NAMESPACE --acm_config_name=$ACM_CONFIG_NAME --ci_pipeline_url=$CI_PIPELINE_URL
  cache:
    key: '$CI_COMMIT_REF_SLUG'
    paths:
      - node_modules/
    policy: pull
  artifacts:
    paths:
      - version_tag
  rules: *generate_rules
  needs: ['publish-cdn']
  tags:
    - docker
  dependencies:
    - publish-cdn

prod-publish:
  stage: prod-publish
  script:
    - PUBLISH_VERSION=$(cat version_tag)
    - echo $PUBLISH_VERSION
    - npm run genNginxConf -- --env_tag=$CI_COMMIT_BRANCH --version_tag=$PUBLISH_VERSION --cimmit_user=$GITLAB_USER_EMAIL --project=$DEPLOY_PROJECT --auth_user=$AUTH_USER --auth_key=$AUTH_KEY --acm_host=$ACM_HOST --acm_group=$ACM_GROUP --acm_namespace=$ACM_NAMESPACE --acm_config_name=$ACM_CONFIG_NAME --ci_pipeline_url=$CI_PIPELINE_URL
  cache:
    key: '$CI_COMMIT_REF_SLUG'
    paths:
      - node_modules/
    policy: pull

  rules: *generate_rules
  needs: ['gray_publish']
  tags:
    - docker
  dependencies:
    - gray_publish
