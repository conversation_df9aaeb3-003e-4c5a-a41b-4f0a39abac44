import React, { CSSProperties, useMemo } from 'react';
import type { UniqueIdentifier } from '@dnd-kit/core';
import { AnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { TreeItem, Props as TreeItemProps } from './TreeItem';
import { iOS } from '../../utilities';
import { getInsertPosition, InsertPosition } from '@/utils/dndkitUtils';
import { debounce, throttle } from 'lodash';

interface Props extends TreeItemProps {
  id: UniqueIdentifier;
  disabled?: boolean;
  data: {
    item: any;
  };
  projectedDepth: number;
  collisionDetectionData: any;
  scrollContainer: any;
  foldKeys: (string | number)[];
}

const animateLayoutChanges: AnimateLayoutChanges = ({ isSorting, wasDragging }) =>
  isSorting || wasDragging ? false : true;

export function SortableTreeItem({
  id,
  depth,
  projectedDepth,
  collisionDetectionData,
  scrollContainer,
  foldKeys = [],
  ...props
}: Props) {
  const {
    attributes,
    isDragging,
    isSorting,
    listeners,
    setDraggableNodeRef,
    setDroppableNodeRef,
    active,
    over,
    index,
    activeIndex,
    transform,
    transition,
  } = useSortable({
    id,
    disabled: !props.draggable,
    animateLayoutChanges,
    data: {
      item: props.data,
    },
  });

  // 计算指示器的 transform
  const indicatorTransform = useMemo(() => {
    if (!active || !over || !collisionDetectionData) {
      return transform;
    }

    // 获取活动元素和目标元素的位置信息
    const activeElement = scrollContainer?.querySelector(`[data-id="${active.id}"]`);
    const overElement = scrollContainer?.querySelector(`[data-id="${over.id}"]`);

    if (!activeElement || !overElement) {
      return transform;
    }

    const activeRect = activeElement.getBoundingClientRect();
    const overRect = overElement.getBoundingClientRect();

    // 根据 position 计算 Y 轴偏移
    let offsetY = 0;
    switch (collisionDetectionData.position) {
      case 'above':
        offsetY = overRect.top - activeRect.top;
        break;
      case 'below':
        offsetY = overRect.bottom - activeRect.top;
        break;
      case 'inside':
        // offsetY = overRect.top + overRect.height / 2 - activeRect.top;
        offsetY = overRect.top - activeRect.top;
        break;
      default:
        offsetY = transform?.y || 0;
    }

    return {
      x: transform?.x || 0,
      y: offsetY,
      scaleX: transform?.scaleX || 1,
      scaleY: transform?.scaleY || 1,
    };
  }, [active, over, collisionDetectionData, transform]);

  const style: CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
  };

  // 指示器的样式
  const indicatorStyle: CSSProperties = {
    transform: CSS.Translate.toString(indicatorTransform),
    position: 'absolute',
    height: '2px',
    width: `calc(100% - ${props.indentationWidth * projectedDepth}px)`,
    right: 0,
    background: 'var(--Brand500)',
    pointerEvents: 'none',
    zIndex: 100,
  };

  const overData = over?.data.current?.item;
  const activeData = active?.data.current?.item;

  const showIndicator = useMemo(
    throttle(() => {
      if (!over) {
        return false;
      }
      if (activeData?.isGroup) {
        if (!overData?.isGroup) {
          if (activeData?.depth !== overData?.depth) {
            return false;
          }
        } else if (collisionDetectionData?.position === 'below') {
          return false;
        }
      } else if (overData?.isGroup && collisionDetectionData?.position === 'below') {
        if (!props?.collapsible) {
          return false;
        }
        if (foldKeys.includes(overData.id)) {
          return false;
        }
      }
      if (overData?.isGroup && collisionDetectionData?.position === 'inside') {
        return false;
      }
      return active?.id === id && over?.id !== id && collisionDetectionData?.position;
    }, 16),
    [active?.id, over?.id, collisionDetectionData?.position, id]
  );

  const showAbover = useMemo(
    throttle(() => {
      if (
        over?.id === id &&
        overData?.isGroup &&
        !activeData?.isGroup &&
        collisionDetectionData?.position === 'inside'
      ) {
        return true;
      }
      return false;
    }, 160),
    [overData, activeData, id, collisionDetectionData?.position]
  );

  return (
    <>
      {showIndicator && !showAbover && (
        <div style={indicatorStyle} data-position={collisionDetectionData?.position} />
      )}
      <TreeItem
        ref={setDraggableNodeRef}
        wrapperRef={setDroppableNodeRef}
        depth={depth}
        ghost={isDragging}
        disableSelection={iOS}
        disableInteraction={isSorting}
        isAbover={showAbover}
        handleProps={
          props.draggable
            ? {
                ...attributes,
                ...listeners,
              }
            : {}
        }
        {...props}
      />
    </>
  );
}
