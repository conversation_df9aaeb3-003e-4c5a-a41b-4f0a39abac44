.search__input {
  margin-left: 8px;
  margin-right: 8px;
  width: calc(100% - 16px) !important;
  margin-bottom: 4px;
}

.search__list {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: calc(100vh - 260px);
  max-height: 400px;
  margin-right: -10px;
  padding-right: 8px;

  :global {
    .rock-checkbox-wrapper {
      margin-right: 0;
    }
  }
}

.project__item {
  padding: 6px 8px;
  border-radius: 6px;
  column-gap: 8px;
  transition: background-color 0.2s;

  .checkbox {
    top: unset;
  }

  &:hover {
    background-color: var(--aBlack6);
  }
}

.project__content {
  column-gap: 8px;
  min-width: 0;
  .project__name {
    color: var(--TextPrimary);
    font-size: 14px;
    line-height: 20px;
  }

  .project__icon {
    width: 28px;
    height: 28px;
  }
}

.load__more {
  position: relative;
}
