import classNames from 'classnames'
import { PropsWithChildren, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'

import { Button, Dropdown, Icon } from '@/components/basic'
import { Dispatch, RootState } from '@/models/store'
import { CustomField, FieldTypeEnum } from '@/types/custom-field'
import { ViewType } from '@/utils/const'
import { EnumField } from '@/utils/fields'
import I18N from '@/utils/I18N'
import { EnumTrackeKey } from '@/utils/skyline'

import s from './index.less'
import MultipleOverlay from './multiple-overlay'
import GroupOverlay from './overlay'
import { rIC } from '@/utils/requestIdleCallback'

interface Props {
  disabled?: boolean
  className?: string
}

interface GroupData {
  name: string
  fieldName: string
  customFieldId?: string
}

const Group: React.FC<PropsWithChildren<Props>> = props => {
  const { disabled, className = '', ...rest } = props
  const [visible, setVisible] = useState<boolean>(false)
  const { groupBy, queryGroupBys, viewType, customFields, currentViewTab } = useSelector((state: RootState) => {
    let view = state.viewSetting.currentViewTab
    return {
      groupBy: view.queryGroupBy?.customFieldId || view.queryGroupBy?.fieldName || '',
      queryGroupBys: view.queryGroupBys,
      viewType: view?.viewType,
      customFields: state.viewSetting.customFields,
      currentViewTab: state.viewSetting.currentViewTab,
    }
  })
  const dispatch = useDispatch<Dispatch>()
  const { groupListData, groupListDataMap } = useGetGroupListData({ viewType, customFields })

  const groupName = useMemo(() => {
    if (viewType !== ViewType.kanban) {
      return queryGroupBys?.length
        ? queryGroupBys.map(group => groupListDataMap[group.customFieldId || group.fieldName]?.name).join('/')
        : I18N.auto.noGrouping
    }
    const item = groupListData.find(item => item.fieldName === groupBy)
    if (item) {
      return item.name
    }
    return ''
  }, [groupBy, groupListData, queryGroupBys, viewType])

  return (
    <Dropdown
      className={`${s.dropdown} ${className}`}
      title={
        <Button
          className={classNames('mr-8', s.group)}
          type="text-subtle"
          icon={<Icon name="icon-soft_fz" fontSize={16} className="mr-4"></Icon>}
        >
          <div className={s.groupDsc}>
            {groupName ? I18N.template(I18N.auto.grouping, { val1: groupName }) : I18N.auto.grouping_2}
          </div>
        </Button>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      disabled={disabled}
      open={visible}
      onOpenChange={v => {
        setVisible(v)
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        currentViewTab?.type !== ViewType.kanban ? (
          <MultipleOverlay
            queryGroupBys={queryGroupBys}
            onChange={opts => {
              const groupBys = opts.map(opt =>
                opt.isCustomField
                  ? {
                      fieldName: 'customField',
                      customFieldId: opt.fieldName,
                      customFieldVersion: opt.customFieldVersion,
                    }
                  : opt,
              )

              // if (groupBy !== v) {
              dispatch.viewSetting.filterByGroups(groupBys)
              // 埋点 分组操作
              dispatch.user.trackingByView({
                key: EnumTrackeKey.TaskGroupOpt,
                groupBy: opts,
              })

              if (groupBys?.length === 1) {
                rIC(() => {
                  window.virtualizer.scrollToOffset(0)
                })
              }

              // }
              // setVisible(false);
            }}
            groupListData={groupListData}
            groupListDataMap={groupListDataMap}
          />
        ) : (
          <GroupOverlay
            activeKey={groupBy}
            onChange={v => {
              if (groupBy !== v) {
                dispatch.viewSetting.filterByGroup(v)
                // 埋点 分组操作
                dispatch.user.trackingByView({
                  key: EnumTrackeKey.TaskGroupOpt,
                  groupBy: v,
                })
              }
              setVisible(false)
            }}
            groupListData={groupListData}
          ></GroupOverlay>
        )
      }
      {...rest}
    />
  )
}

interface getGroupListDataProps {
  viewType?: ViewType
  customFields: CustomField[]
}

export const useGetGroupListData = ({ viewType, customFields }: getGroupListDataProps) => {
  const memoGroupListData = useMemo(() => {
    const list: GroupData[] = [
      {
        name: I18N.auto.deadline,
        fieldName: EnumField.deadline,
      },
      {
        name: I18N.auto.assignedTo,
        fieldName: EnumField.assignee,
      },
      {
        name: I18N.auto.priority,
        fieldName: EnumField.priority,
      },
      {
        name: I18N.auto.completionStatus,
        fieldName: EnumField.finished,
      },
    ].concat(
      customFields
        .filter(f => {
          if ([FieldTypeEnum.user, FieldTypeEnum.datetime].includes(f.type)) {
            return true
          }
          //过滤空选项的 单选多选
          if ([FieldTypeEnum.option].includes(f.type)) {
            return !!f.options?.length
          }
          return false
        })
        .map(f => {
          return {
            fieldName: f.fieldId,
            name: f.name,
            isCustomField: true,
            customFieldVersion: f.fieldVersion,
            type: f.type,
          }
        }),
    )
    // .filter(f => {
    //   // 甘特图一期先过滤所有人员相关分组

    //   if (viewType !== ViewType.timeline) {
    //     return true
    //   }

    //   if (f.isCustomField) {
    //     return f.type !== FieldTypeEnum.user && f.type !== FieldTypeEnum.datetime
    //   }

    //   return f.fieldName !== EnumField.assignee
    // })
    // if (![ViewType.kanban, ViewType.timeline].includes(viewType)) {
    //   list.unshift({ name: I18N.auto.noGrouping, fieldName: '' })
    // }
    return list
  }, [viewType, customFields])

  const groupListDataMap = useMemo(() => {
    return memoGroupListData.reduce((acc, cur) => {
      acc[cur.fieldName] = cur
      return acc
    }, {} as Record<string, GroupData>)
  }, [memoGroupListData])

  return { groupListData: memoGroupListData, groupListDataMap }
}

export default Group
