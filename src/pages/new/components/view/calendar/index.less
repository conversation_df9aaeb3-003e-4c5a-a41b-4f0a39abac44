.calendarList {
  display: flex;
  width: 100%;
  height: 100%;

  :global {
    .rock-btn {
      font-size: 13px;
    }
    .option-active {
      background-color: var(--aBrand12) !important;
      color: var(--Brand600) !important;
    }
  }

  .calendarListLeft {
    flex: 1;
    padding: 16px;
    min-width: 580px;
  }
  .calendarListRight {
    width: 232px;
    min-width: 220px;
    padding: 16px;
    border-left: 1px solid var(--aBlack8);
    transition: all 0.3s;
  }
  .hiddenUnplanned {
    width: 0;
    min-width: 0;
    padding: 16px 0;
  }
}

.todoContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}

.headerToolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  height: 26px;
  margin-bottom: 10px;
  .toolbarLeft {
    display: flex;
    .dateTime {
      min-width: 88px;
      margin-right: 14px;
      font-size: 16px;
      font-weight: 600;
      color: var(--TextPrimary-strong);
    }
    .toggleDate {
      display: flex;
    }
    .pre,
    .next {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      color: var(--IconTertiary);
      border-radius: 4px;
    }

    .today {
      font-size: 13px;
      line-height: 24px;
      padding: 0 4px;
      color: var(--TextPrimary);
      cursor: pointer;
      &:hover {
        border-radius: 6px;
        background-color: var(--aBlack6);
        border-radius: 4px;
      }
    }
  }
  .toolbarRight {
    display: flex;
    align-items: center;
    column-gap: 10px;
    :global {
      .mr-8 {
        margin-right: 0;
      }
    }
    .mode {
      display: flex;
      border-radius: 5px;
      background-color: var(--TasksTagbg);
      height: 26px;
      cursor: pointer;

      .modeBtn {
        height: 26px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border: 1px solid transparent;
        font-size: 13px;
      }
      .modeBtnActive {
        background-color: var(--bgBottom);
        border-color: var(--aBlack10);
        border-radius: 5px;
        color: var(--TextPrimary);
      }
    }

    .settingBtn {
      width: 24px;
      height: 24px;
      border-radius: 4px;
    }
  }
}

.fullCalendarContainer {
  flex: 1;
  :global {
    .fc-media-screen {
      //min-height: 600px;
      height: 100%;
    }
    .fc-scrollgrid-section-header {
      th {
        text-align: left;
        font-weight: normal;
        font-size: 12px;
        color: var(--TextTertiary);
      }
      .fc-scrollgrid-sync-inner {
        padding-left: 12px;
      }
    }
    .fc-col-header {
      background-color: var(--TasksTagbg);
      border-radius: 4px;
      height: 26px !important;
    }
    .fc-scrollgrid-sync-inner {
      .fc-col-header-cell-cushion {
        height: 100%;
        line-height: 26px;
        padding: 0;
      }
    }
    .fc-theme-standard td,
    .fc-theme-standard th,
    .fc-theme-standard .fc-scrollgrid {
      border-color: transparent;
    }
    // .fc-theme-standard td {
    //   border-bottom: 1px solid var(--aBlack8) !important;
    // }
    .fc-scrollgrid-sync-table {
      tr td {
        border-bottom: 1px solid var(--aBlack8) !important;
      }
    }

    .fc .fc-daygrid-day.fc-day-today {
      background-color: transparent;
      .fc-daygrid-day-number {
        color: var(--Brand500);
      }
    }
    .fc .fc-daygrid-day-top {
      flex-direction: column;
      font-size: 12px;
      color: var(--TextPrimary);
    }

    .fc .fc-day-other .fc-daygrid-day-top {
      opacity: 1;
      color: var(--TextQuartus);
    }
    .fc-h-event {
      border: none;
      border-radius: 3px;
      background-color: transparent;
    }

    .fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
      min-height: 0.16rem;
    }

    //自定义
    .fc-daygrid-day-todo-cell {
      display: flex;
      justify-content: space-between;
      padding-left: 10px;
      height: 18px;
    }

    .fc-daygrid-day-todo-add {
      display: none;
      cursor: pointer;
      color: var(--TextPrimary);
    }
    .fc-daygrid-day {
      &:hover {
        .fc-daygrid-day-todo-add.show {
          display: block;
        }
      }
    }
    .fc-daygrid-day-todo-cell-tody {
      padding: 0 5px;
      border-radius: 4px;
      background-color: var(--Brand500);
      color: var(--absWhite);
    }
    .fc-daygrid-more-link.fc-more-link {
      color: var(--TextSecondary);
      font-size: 11px;
      background-color: transparent !important;
      &:hover {
        color: var(--Brand500);
      }
    }
    .fc .fc-popover {
      display: flex;
      flex-direction: column;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%);
      max-height: 50vh;
      min-width: 320px;
      padding: 12px;
      border-radius: 12px;
      border-width: 0;
      box-shadow: var(--ComBoxShadow);
      background-color: var(--bgTop);
      border: 1px solid var(--aBlack12);
      overflow: hidden;
      z-index: 1000; //弹框内的tooltip会被影响到
      .fc-popover-header {
        background-color: transparent;
        padding: 0 0 12px 0;
        flex-shrink: 0;
      }
      .fc-more-popover-misc {
        display: none;
      }
      .fc-popover-body {
        flex: 1;
        min-height: 0;
        padding: 0px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .fc .fc-daygrid-day-frame {
      padding-bottom: 8px;
    }
    // .fc-h-event {
    //   .calendar-task-item {
    //     background-color: yellow !important;
    //   }
    // }
    //  .fc-direction-ltr .fc-daygrid-event.fc-event-end {
    //   margin-right: unset;
    // }
    .fc-direction-ltr .fc-daygrid-event.fc-event-end,
    .fc-direction-rtl .fc-daygrid-event.fc-event-start {
      margin-left: 2px;
    }
    .fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),
    .fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {
      margin-left: 2px;
    }
  }
}

.itemMoth {
  background-color: var(--TasksTagbg);
  color: var(--TextPrimary);
  padding: 6px;
  border-radius: 3px;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.level0 {
}

.level3 {
  background-color: red;
}

.addIcon {
  width: 18px;
  height: 18px;
  border-radius: 3px !important;
  background-color: var(--aBlack10);
  color: var(--IconPrimary);
  &:hover {
    color: var(--IconPrimary);
    background-color: var(--aBlack10);
  }
  &:active {
    background-color: var(--aBlack12);
  }
}
