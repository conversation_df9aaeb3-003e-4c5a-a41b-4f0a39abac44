import classNames from 'classnames';
import React from 'react';
import { Virtuoso } from 'react-virtuoso';

import { SortableItem } from '@/components/dndkit';
import { TaskGroupItem, TodoInfo } from '@/types';
import { TaskNavigatorType, TaskTableRowType } from '@/utils/const';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddBtn from './add-btn';
import Card, { CardType } from './card';
import CustomScrollbar from './custom-scrollbar';
import useGetPermissions, { getPermissionsByTaskId } from '@/hooks/useGetPermissions';

interface Props {
  getBtnRef: any;
  navigatorId: TaskNavigatorType | string | number;
  showAddBtn: boolean;
  list: TodoInfo[];
  groupInfo: TaskGroupItem;
  loadMore: (id: string) => void;
  moving?: boolean;
}

const MemoList: React.FC<Props> = React.memo(
  (props) => {
    const { getBtnRef, list, groupInfo, showAddBtn, loadMore } = props;

    return (
      <Virtuoso
        ref={getBtnRef ? getBtnRef(groupInfo.id) : undefined}
        context={{ groupInfo: groupInfo, list: list }}
        data={list}
        id={'scroll-content-group'}
        atBottomThreshold={150}
        components={{
          Scroller: CustomScrollbar,
          Footer: showAddBtn ? AddBtn : null,
        }}
        atBottomStateChange={(v) => {
          if (v) {
            loadMore(groupInfo.groupId!);
          }
        }}
        itemContent={(index) => {
          const taskInfo = list[index];

          if (!taskInfo) {
            return null;
          }
          if (taskInfo?._rowType === TaskTableRowType.add) {
            return (
              <Card
                className={classNames({ 'mt-0': index === 0 })}
                groupId={groupInfo.id}
                data={taskInfo}
                type={CardType.add}
              ></Card>
            );
          }

          const permissions = getPermissionsByTaskId(taskInfo?.taskId!);

          const [CAN_EDIT] = validatesPermission({
            permissions: permissions,
            key: [TaskPermissionEnum.CAN_EDIT],
          }) as boolean[];
          return (
            <SortableItem key={taskInfo.taskId} data={taskInfo} disabled={!CAN_EDIT}>
              <Card
                className={classNames({ 'mt-0': index === 0 })}
                groupId={groupInfo.id}
                data={taskInfo}
                type={CardType.onlyShow}
              ></Card>
            </SortableItem>
          );
        }}
      ></Virtuoso>
    );
  },
  (prevProps, nextProps) => {
    if (!nextProps.moving) {
      return false;
    }
    if (prevProps.navigatorId !== nextProps.navigatorId) {
      return false;
    }
    if (prevProps.list.length !== nextProps.list.length) {
      return false;
    }
    if (prevProps.groupInfo.groupId !== nextProps.groupInfo.groupId) {
      return false;
    }
    if (
      prevProps.list.map((item) => item.taskId).join('') !==
      nextProps.list.map((item) => item.taskId).join('')
    ) {
      return false;
    }
    return true;
  }
);

export default MemoList;
