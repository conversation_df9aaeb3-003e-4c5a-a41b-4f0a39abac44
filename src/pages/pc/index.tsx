import { useLayoutEffect } from 'react';
import { history, useSearchParams } from 'umi';

/**
 * taskId, sessionid, sessiontype
 * taskId 传了默认打开详情,不传打开列表 如果客户端需要从详情到列表或者列表到详情 调用桥去改变
 */
export default function Link() {
  const [searchParams] = useSearchParams();
  useLayoutEffect(() => {
    let taskId = searchParams.get('taskId');
    searchParams.delete('taskId');
    //sessionid, sessiontype
    //TODO: 获取群或人的信息
    if (taskId) {
      history.replace(`/pc/detail/${taskId}?${searchParams.toString()}`);
    } else {
      history.replace(`/pc/list?${searchParams.toString()}`);
    }
  }, []);

  return null;
}
