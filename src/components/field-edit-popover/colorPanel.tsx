import classNames from 'classnames';
import React, { PropsWithChildren, useState } from 'react';

import { getCustomColor } from '@/utils/customfields';
import I18N from '@/utils/I18N';

import { Popover } from '../basic';
import s from './colorPanel.less';
import { FieldOptionColor } from './const';

type PanelProps = {
  value: string;
  onChange: (val: string) => any;
};

export function ColorPanel({ value, onChange }: PanelProps) {
  return (
    <>
      <div className={s.label}>{I18N.auto.chooseAColor}</div>
      <div className={s.grid}>
        {Object.keys(FieldOptionColor).map((key) => (
          <div key={key} className={classNames(s.box__item, { [s.active]: value == key })}>
            <div
              className={s.box}
              style={{ backgroundColor: getCustomColor(FieldOptionColor[key]) }}
              onClick={() => onChange(key)}
            />
          </div>
        ))}
      </div>
    </>
  );
}

export default function ColorPanelPopover({
  children,
  ...restProps
}: PropsWithChildren<PanelProps>) {
  const [visible, setVisible] = useState(false);

  return (
    <Popover
      visible={visible}
      onVisibleChange={setVisible}
      content={
        <ColorPanel
          {...restProps}
          onChange={(val) => {
            restProps.onChange(val);
            setVisible(false);
          }}
        ></ColorPanel>
      }
      placement="bottomLeft"
      showArrow={false}
      overlayClassName={s.popover}
      trigger="click"
      align={{ offset: [0, -8] }}
    >
      {children}
    </Popover>
  );
}
