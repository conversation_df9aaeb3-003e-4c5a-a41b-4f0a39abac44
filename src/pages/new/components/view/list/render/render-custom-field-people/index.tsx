import React, { useEffect, useRef, useState } from 'react';

import { apiTaskFieldGetValueGet } from '@/api';
import { RenderPeoples } from '@/components/basic';
import { CustomFieldPeople } from '@/components/basic-project';
import { TodoInfo, UserInfo } from '@/types';
import { CustomField } from '@/types/custom-field';
import { onPageVisibleChange } from '@/utils';
import { TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const';
import I18N from '@/utils/I18N';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  taskInfo: TodoInfo;
  filedInfo: CustomField;
  onChange?: (users?: string[]) => void;
  disabled?: boolean;
}

const RenderCustomFieldPeople: React.FC<Props> = (props) => {
  let { taskInfo, filedInfo, onChange, disabled } = props;
  const { _rowType: rowType, taskId } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const ref = useRef(false);
  const [list, setList] = useState<UserInfo[]>([]);

  const changeVisible = (v: boolean) => {
    setVisible(v);
  };

  const change = (users: UserInfo[]) => {
    const uids = users.map((item) => item.uid!);
    // 新增逻辑
    if (rowType === TaskTableRowType.add) {
      onChange?.(uids);
      return null;
    }
    setList(users);
    onChange?.(uids);
  };

  useEffect(() => {
    if (taskId && visible) {
      if (String(taskId).includes(TaskTableRowTypeAdd)) {
        return;
      }
      const { customFieldValues } = taskInfo || {};
      //@ts-ignore
      if (customFieldValues?.values?.[filedInfo.fieldId]) {
        //@ts-ignore
        const { users = [], value = [] } = customFieldValues?.values?.[filedInfo.fieldId] || {};
        if (users.length === value.length) {
          return;
        }
      }
      apiTaskFieldGetValueGet({ taskId: taskId, fieldId: filedInfo.fieldId }).then((res) => {
        const users = [...(res?.users || [])];
        setList(users);
      });
    } else {
      const _list: UserInfo[] =
        //@ts-ignore
        taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.users || [];
      //@ts-ignore
      let _values: string[] = taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.value || [];
      const values: UserInfo[] = _values
        .filter((item) => _list.findIndex((v) => v.uid === item) === -1)
        .map((v) => ({
          uid: v,
          avatarUrl: '',
          name: '',
        }));
      setList(_list.concat(values) as UserInfo[]);
    }
  }, [taskId, visible]);

  useEffect(() => {
    onPageVisibleChange((pageShow) => {
      if (!pageShow) {
        setVisible(false);
      }
    });
  }, []);
  return (
    <AddRowBox
      iconName="icon-kit_user_add"
      tipTitle={list.length ? '' : I18N.template(I18N.auto.addTo_2, { val1: filedInfo.name })}
      disabled={disabled}
      taskId={taskInfo.taskId}
      focused={visible}
      onClick={(e) => {
        if (ref.current) {
          ref.current = false;
          return;
        }
        if (!visible) {
          changeVisible(!visible);
        }
      }}
      hasDelete={!disabled && !!list.length}
      onClear={() => {
        onChange?.([]);
        setList([]);
      }}
    >
      <div className={s.createTimeBox}>
        <CustomFieldPeople
          value={list}
          visible={visible}
          onVisible={(v) => {
            ref.current = true;
            changeVisible(v);
          }}
          onChange={change}
        >
          {list.length ? (
            <>
              <div className={s.listPeopleItem}></div>
              <RenderPeoples
                showFinishedIcon
                list={list || []}
                count={list.length}
                maxShowCount={3}
                avatarClassName="mr-4"
              ></RenderPeoples>
            </>
          ) : null}
        </CustomFieldPeople>
      </div>
    </AddRowBox>
  );
};

export default RenderCustomFieldPeople;
