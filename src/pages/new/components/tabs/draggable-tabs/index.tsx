import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  apiNavigatorViewDeleteDelete,
  apiNavigatorViewUpdateNamePost,
  apiProjectViewLayoutPost,
} from '@/api';
import { POPODropdown } from '@/components/basic';
import DnDList from '@/components/dndkit/dnd-list';
import { TabsSkeleton } from '@/components/loading-page';
import { Dispatch, RootState } from '@/models/store';
import { isTaskMenuId } from '@/models/utils';
import { ViewTab } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';
import TabItem from './tab-item';
export type Props = {
  className?: string;
  viewList?: ViewTab[];
  canEditSystemView?: boolean;
};

const DraggableTabs: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    viewTabList: viewList,
    currentViewTab,
    projectInfo,
    navigatorId,
  } = useSelector((state: RootState) => ({
    viewTabList: state.viewSetting.viewTabList!,
    currentViewTab: state.viewSetting.currentViewTab!,
    projectInfo: state.project.projectInfo,
    navigatorId: state.viewSetting.navigatorId,
  }));

  // console.log('viewList', viewList);

  const isProjectTab = !isTaskMenuId(navigatorId as number);

  const [reAdjustTabs, setReAdjustTabs] = useState(0);

  const [visibleViewTabs, setVisibleViewTabs] = useState<ViewTab[]>([]);
  const [visible, setVisible] = useState<boolean>(false);

  const adjustTabs = useMemoizedFn(() => {
    let sumWidth = 0;
    const visibleTabs = [];
    const totalBarWidth = (document.getElementById('viewTabWrapper')?.clientWidth as number) - 100;
    for (let i = 0; i < viewList.length; i++) {
      const item = viewList[i];
      const currentItemWidth = document.getElementById(`hidden-tab-${item.viewId}`)?.clientWidth;
      if (i !== viewList.length - 1) {
        sumWidth += 24;
      }
      sumWidth += currentItemWidth ?? 0;
      if (sumWidth <= totalBarWidth) {
        visibleTabs.push(item);
      } else {
        break;
      }
    }
    setVisibleViewTabs(visibleTabs);
  });

  useEffect(() => {
    adjustTabs();
  }, [viewList, projectInfo.projectId, reAdjustTabs]);

  const hiddenViewTabs = useMemo(() => {
    return viewList.filter((item) => {
      return !visibleViewTabs.some((val) => val?.viewId === item.viewId);
    });
  }, [viewList, visibleViewTabs]);

  const dispatch = useDispatch<Dispatch>();

  const currentActiveTabHidden = useMemo(() => {
    return hiddenViewTabs.some((tab) => tab.viewId === currentViewTab.viewId);
  }, [currentViewTab, hiddenViewTabs]);

  useEffect(() => {
    window.addEventListener('resize', adjustTabs);
    return () => {
      window.removeEventListener('resize', adjustTabs);
    };
  }, []);

  const changeCurrentViewTab = (item: ViewTab) => {
    setTimeout(() => {
      dispatch.viewSetting.toggeleView(item);
      setVisible(false);
    }, 0);
  };

  const renameViewTab = (name: string, viewId: number) => {
    apiNavigatorViewUpdateNamePost({ viewId: viewId, name }).then((res) => {
      const _list = viewList.map((item) => {
        if (item.viewId === viewId) {
          return { ...item, name };
        }
        return item;
      });
      dispatch.viewSetting.setViewTabList(_list);
      setReAdjustTabs((v) => v + 1);
    });
  };

  const onDelete = (id: number | string) => {
    apiNavigatorViewDeleteDelete({ viewId: String(id) }).then(() => {
      let index = 0;
      const _list = viewList.filter((item, idx) => {
        if (item.viewId === id) {
          index = idx;
        }
        return item.viewId !== id;
      });
      dispatch.viewSetting.setViewTabList(_list);
      changeCurrentViewTab(_list[index - 1 < 0 ? _list.length - 1 : index - 1]);
    });
  };

  return (
    <>
      <div className={s.hiddenTabs}>
        {viewList.map((item, index) => {
          return (
            <TabItem
              key={item.viewId}
              viewTab={item}
              viewList={viewList}
              activeTabId={currentViewTab.viewId!}
              idPrefix="hidden-tab"
              onDelete={onDelete}
              isProjectTab={isProjectTab}
              renameViewTab={renameViewTab}
              changeCurrentViewTab={changeCurrentViewTab}
            />
          );
        })}
      </div>
      <DnDList
        list={visibleViewTabs}
        disabled={!isProjectTab}
        onDragEnd={(item, list) => {
          const ids = list.map((item) => item.viewId);
          const hiddenList = viewList.filter(
            (view) => !list.some((item) => item.viewId === view.viewId)
          );

          const hiddenIds = hiddenList.map((view) => view.viewId);
          // 调用接口更新tab顺序
          apiProjectViewLayoutPost({
            projectId: projectInfo.projectId!,
            layout: { viewIds: [...ids, ...hiddenIds] },
          }).then((res) => {
            dispatch.viewSetting.setData({
              viewTabList: [...list, ...hiddenList],
            });
          });
          setReAdjustTabs((v) => v + 1);
        }}
        direction="horizontal"
        renderItem={(item, index) => {
          return (
            <TabItem
              key={item.id}
              viewTab={item as ViewTab}
              viewList={viewList}
              activeTabId={currentViewTab.viewId!}
              onDelete={onDelete}
              isProjectTab={isProjectTab}
              renameViewTab={renameViewTab}
              changeCurrentViewTab={changeCurrentViewTab}
              className="dnd-tab-item"
            />
          );
        }}
      ></DnDList>
      <POPODropdown
        clickItemCloseDropdown={false}
        visible={visible}
        onVisible={(open) => {
          setVisible(open);
        }}
        title={
          <div className={s.moreWrapperBtn}>
            {visibleViewTabs.length < viewList.length && (
              <>
                <div
                  className={`${s.moreBtn} ${currentActiveTabHidden && s.activeMoreBtn}`}
                  id="drag-view-tab-more"
                >
                  {I18N.template(I18N.auto.more_2, {
                    val1: viewList.length - visibleViewTabs.length,
                  })}
                </div>
                {currentActiveTabHidden ? (
                  <div
                    className={classNames(s.inkbar)}
                    style={{ width: document.getElementById('drag-view-tab-more')?.clientWidth }}
                  ></div>
                ) : null}
              </>
            )}
          </div>
        }
        className={s.tabMoreDropdown}
        placement="bottomLeft"
      >
        <div className={s.list}>
          {hiddenViewTabs.map((item, index) => {
            return (
              <TabItem
                key={item.viewId}
                viewTab={item}
                hiddenInMore={true}
                viewList={viewList}
                activeTabId={currentViewTab.viewId!}
                onDelete={onDelete}
                isProjectTab={isProjectTab}
                changeCurrentViewTab={changeCurrentViewTab}
                renameViewTab={renameViewTab}
                className={item}
              />
            );
          })}
        </div>
      </POPODropdown>
    </>
  );
};

export default DraggableTabs;
