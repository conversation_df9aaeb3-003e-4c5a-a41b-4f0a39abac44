import { SortableTree } from '@/components/SortableTree';
import { ProjectInfo, TableNoDataProp } from '@/types';
import s from './index.less';
import I18N from '@/utils/I18N';
import { FC, useMemo, useRef } from 'react';
import { OperateMove } from '@babylon/popo-icons';
import ProjectItem from '../../project-list/components/ProjectItem';
import { ListTableNoData } from '@/components/table/components/no-data';
import {
  ProjectNoDataDark,
  ProjectNoDataLight,
  ProjectSearchNoDataDark,
  ProjectSearchNoDataLight,
} from '@/utils/const';
import { useSelector } from 'react-redux';
import { RootState } from '@/models/store';
import { CorlorMode } from '@/utils/platform';

interface Props {
  handleDragEnd: (treeData: any, data: any) => void;
  keyword?: string;
  canLoadMore?: boolean;
  loading?: boolean;
  loadMore: () => Promise<any>;
  treeData: ProjectInfo[];
  [key: string]: any;
}

const ProjectTree: FC<Props> = ({
  handleDragEnd,
  keyword,
  canLoadMore,
  loading,
  loadMore,
  treeData,
}) => {
  const listNodeRef = useRef<HTMLDivElement>(null);
  const corlorMode = useSelector((state: RootState) => state.user.corlorMode);
  const memoNoDataProps: TableNoDataProp = useMemo(() => {
    return {
      isNoData: !keyword && !treeData.length && !loading,
      descriptive: I18N.auto.startImmediately,
      bgUrl: {
        dark: ProjectNoDataDark,
        light: ProjectNoDataLight,
      },
    };
  }, [keyword, treeData, loading]);

  const memoNoSearchDataProps = useMemo(() => {
    return {
      isNoData: !!keyword && !treeData.length && !loading,
      descriptive: (
        <span>
          {I18N.templateNode(I18N.auto.notFoundIncludes, {
            val1: <span className={s.keyword}>{keyword}</span>,
          })}
        </span>
      ),
      bgUrl: {
        dark: ProjectSearchNoDataDark,
        light: ProjectSearchNoDataLight,
      },
    };
  }, [keyword, treeData, loading]);

  return (
    <div className={`${s.list} project__list scroll__container`} ref={listNodeRef}>
      <div className={s.list__header}>
        <div className={s.content}>
          <div className={s.head}>
            <span className={s.text}>{I18N.auto.entryName}</span>
          </div>
          <div className={s.head}>{I18N.auto.state}</div>
          <div className={s.head}>{I18N.auto.projectMembers}</div>
          <div className={s.head}>{I18N.auto.creationTime}</div>
          <div className={s.head}></div>
        </div>
      </div>

      <SortableTree<ProjectInfo>
        handlerRender={(item) => {
          return (
            <div className={`${s.drag__handler} drag__handler flex-y-center`}>
              <OperateMove className="fs-16" />
            </div>
          );
        }}
        collapsible={false}
        draggable={!keyword}
        isPin={false}
        onDragEnd={handleDragEnd}
        canLoadMore={canLoadMore}
        scrollContainer={listNodeRef.current}
        loadMore={loadMore}
        // indicator={true}
        itemRender={(item) => <ProjectItem projectInfo={item} />}
        treeData={[...(treeData || [])]}
        estimateSize={(index, flattenedItems) => {
          return 51 * (window.fontSizeScaleRatio || 1);
        }}
        flattenDepth={0}
        showCollapse={false}
        overlayClassName={s.overlay}
      />

      {/* {!!treeData?.length && (
    <treeData
      treeData={treeData}
      titleRender={(node) => {
        return <ProjectItem projectInfo={node} />;
      }}
      indicatorIndent={60}
      height={treeHeight}
      switcherIcon={(props: TreeNodeProps) => {
        const { isGroup, expanded } = props as unknown as ProjectInfo;
        let switchIcon = null;
        if (isGroup) {
          switchIcon = (
            <OperateArrowdown
              className={classNames(s.switch__icon, 'fs-16', {
                [s.arrow__right]: !expanded,
                [s.hidden]: !isGroup,
              })}
            />
          );
        }

        return switchIcon;
      }}
      loadMore={loadMore}
      className={`${s.project__tree} project__tree`}
    />
  )} */}
      {!treeData?.length && keyword && (
        <ListTableNoData
          noSearchDataProps={memoNoSearchDataProps}
          dark={corlorMode === CorlorMode.dark}
          className={s.noData}
        />
      )}

      {!treeData?.length && !keyword && (
        <ListTableNoData
          noDataProps={memoNoDataProps}
          dark={corlorMode === CorlorMode.dark}
          className={s.noData}
        />
      )}
    </div>
  );
};
export default ProjectTree;
