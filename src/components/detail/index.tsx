import { pp } from '@popo-bridge/web'
import { useMemoizedFn } from 'ahooks'
import classNames from 'classnames'
import Delta from 'quill-delta'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { useDispatch, useSelector } from 'react-redux'
import { useSearchParams } from 'umi'

import { apiTaskFollowerTransferToAssigneesPost } from '@/api'
import { Divider, Icon, IconBtn, Message, Scrollbar } from '@/components/basic'
import Comment from '@/components/basic-task/comment'
import Editor from '@/components/basic-task/comment/editor'
import { getEmptyDelta, MentionExtraData } from '@/components/basic-task/comment/editor/utils'
import { Dispatch, RootState } from '@/models/store'
import { TodoDetailStatus, UserInfo } from '@/types'
import {
  DrawerClickOrignKey,
  DrawerOrigin,
  EventCategory,
  Finished,
  OneAndMoreServerParamsType,
  OneAndMoreType,
  PPTimeFormat,
  TodoNotificationCode,
} from '@/utils/const'
import I18N from '@/utils/I18N'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import {
  AttachmentItem,
  ChatRecordItem,
  DetailHeader,
  ExecutorItem,
  LevelItem,
  NewDatePickerItem,
  ProjectItem,
  RemarkItem,
  RenderItem,
  SourceItem,
  TitleItem,
} from './components'
import { AddFollower } from './components/add-follower'
import CustomRenderItems from './components/custom-render-items'
import NoData from './components/no-data'
import s from './index.less'
import SubTasks from './components/SubTasks'
import ParentTaskProfile from './components/ParentTaskProfile'
import { ConfigProvider } from '@bedrock/components'
import useGetPermissions from '@/hooks/useGetPermissions'
import { EditorContainer } from '../editor'
import { stopClickWhenDropdownPending } from '@/pages/new/components/view/list/utils'
export type Props = {
  className?: string
  taskId?: number
  onClose?: () => void
}

const Detail: React.FC<Props> = props => {
  const { onClose } = props
  const [commentValue, setCommentValue] = useState<Delta>(getEmptyDelta())
  const dispatch = useDispatch<Dispatch>()
  const [searchParams] = useSearchParams()
  const sessionId = searchParams.get('sessionId')
  const editorContainerRef = useRef<any>(null)

  const {
    userInfo,
    replyCommentInfo,
    taskInfo,
    eventCategory,
    detailStatus: status,
  } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    replyCommentInfo: state.record.replyCommentInfo,
    taskInfo: state.detail.taskInfo,
    eventCategory: state.record.eventCategory,
    detailStatus: state.detail.detailStatus,
  }))

  let permissions = useGetPermissions({ taskId: taskInfo?.taskId })

  permissions = permissions || taskInfo?.permissions

  const {
    participants,
    title,
    selfFinished,
    startTime,
    deadline,
    deadlineFormat,
    rrule,
    alarm,
    priority,
    attachments,
    remark,
    followerUids,
    assigneeUids,
    isExpired,
    isToday,
    project,
    taskId,
  } = taskInfo

  const memoTime = useMemo(() => {
    return {
      deadline,
      rrule,
      timeFormat: deadlineFormat as PPTimeFormat,
      startTime,
      alarm: {
        time: alarm?.alarmTimestamp,
        rrule: alarm?.alarmRrule,
        selectedOption: alarm?.selectedOption,
        timeFormat: PPTimeFormat.dateAndTime,
      },
    }
  }, [startTime, deadline, deadlineFormat, rrule, alarm])

  const ref = useRef<any>()
  const editorRef = useRef<{ clear: () => void }>({ clear: () => {} })

  useEffect(() => {
    const { participants, assigner } = taskInfo
    const _assignor = {
      uid: assigner?.assignerUid,
      name: assigner?.assignerName,
      avatarUrl: assigner?.assignerAvatarUrl,
    }
    MentionExtraData.assignor = _assignor
    MentionExtraData.userInfo = userInfo

    if (participants?.find(item => item.uid === _assignor.uid)) {
      //@ts-ignore
      MentionExtraData.participants = participants
    } else {
      //@ts-ignore
      MentionExtraData.participants = [_assignor].concat(participants || [])
    }
  }, [taskInfo])

  useEffect(() => {
    if (taskId) {
      //切换待办 清空评论和回复
      setCommentValue(new Delta())
      editorRef.current?.clear?.()
      dispatch.record.setReplyCommentId({})
    }
  }, [taskId])

  const handleTransferFollowerToParticipant = useMemoizedFn((atFollowers?: UserInfo[]) => {
    const assignUids = atFollowers?.map(follower => follower.uid)
    apiTaskFollowerTransferToAssigneesPost({
      taskId: taskId,
      newAssigners: assignUids as string[],
    }).then(() => {
      dispatch.viewSetting.updateItemByDetail({ taskId })
    })
  })

  // ux要求评论不要被盖住，所以需要滚动评论区域
  const commentScrollIntoView = () => {
    const commentEl = document.getElementById('commentsContainer') // 获取评论元素
    if (commentEl) {
      // 如果评论元素存在，则调用scrollIntoView方法来滚动到评论元素
      commentEl.scrollIntoView() // 平滑滚动到评论元素
    }
  }

  const handleAddCommentRecord = useMemoizedFn((content: string, inviteJoinTaskWhenAt: boolean) => {
    if (replyCommentInfo.commentId) {
      dispatch.record.setReplyCommentId({})
    }
    return dispatch.record
      .addComment({
        taskId: taskId,
        content,
        replyTo: replyCommentInfo.commentId,
        inviteJoinTaskWhenAt: inviteJoinTaskWhenAt,
        contentFormat: 'popoRichTag',
      })
      .then(() => {
        if (content.includes('popoAt') || content.includes('prosemirror-mention')) {
          dispatch.record.updateUnreadComment(taskId!)
          dispatch.viewSetting.updateItemByDetail({ taskId })
        }
      })
      .finally(() => {
        // 清除评论
        editorRef.current?.clear?.()
        if (EventCategory.comment !== eventCategory) {
          // dispatch.record.setReplyCommentId({});
          dispatch.record.setEventCategory(EventCategory.comment)
        }
        dispatch.record.getRecordList({ page: 1 }).then(() => {
          commentScrollIntoView()
        })
      })
      .catch(() => {
        Message.error(I18N.auto.commentFailedPlease)
      })
  })

  const onSubmit = (content: string, inviteJoinTaskWhenAt: boolean, atFollowers?: UserInfo[]) => {
    if (atFollowers?.length && inviteJoinTaskWhenAt) {
      handleTransferFollowerToParticipant(atFollowers)
    }
    return handleAddCommentRecord(content, inviteJoinTaskWhenAt)
  }

  useEffect(() => {
    pp.onTodoNotification(res => {
      const { commandId, data } = res
      if (commandId === TodoNotificationCode.refresh) {
        if (data && typeof data === 'string') {
          try {
            const { syncTaskToCalendar } = JSON.parse(data)
            if (syncTaskToCalendar !== undefined) {
              dispatch.detail.getTodoDetail(null)
            }
          } catch (error) {
            //
          }
        }
      }
    })
  }, [taskId])

  const [remarkEditing, setRemarkEditing] = useState(false)

  const hasSource = useMemo(() => {
    // -1无来源、0主动创建、 7日历创建
    if ([-1, 0, 7].includes(taskInfo.source!)) {
      return false
    }
    return true
  }, [taskInfo.source])
  //TODO disabled
  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_SET_COMPLETE_MODE, CAN_COMMENT, CAN_TRANSFER, CAN_ADD_TO_PROJECT, CAN_REMOVE_FROM_PROJECT] =
      validatesPermission({
        permissions: permissions,
        key: [
          TaskPermissionEnum.CAN_EDIT,
          TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
          TaskPermissionEnum.CAN_COMMENT,
          TaskPermissionEnum.CAN_TRANSFER,
          TaskPermissionEnum.CAN_ADD_TO_PROJECT,
          TaskPermissionEnum.CAN_REMOVE_FROM_PROJECT,
        ],
      }) as boolean[]
    return {
      CAN_EDIT,
      CAN_SET_COMPLETE_MODE,
      CAN_COMMENT,
      CAN_TRANSFER,
      CAN_ADD_TO_PROJECT,
      CAN_REMOVE_FROM_PROJECT,
    }
  }, [permissions])

  const disabled = useMemo(() => {
    return !memoPermissions.CAN_EDIT
  }, [memoPermissions.CAN_EDIT])

  //禁止项目修改
  const disabledProject = useMemo(() => {
    if (taskInfo?.parentId) {
      return true
    }
    if (project?.projectId) {
      // 有移除权限就可以修改
      return !memoPermissions.CAN_REMOVE_FROM_PROJECT
    }
    // 如果无项目, 有新增权限就可以修改
    return !memoPermissions.CAN_ADD_TO_PROJECT
  }, [memoPermissions.CAN_ADD_TO_PROJECT, memoPermissions.CAN_REMOVE_FROM_PROJECT, project])

  useEffect(() => {
    if (replyCommentInfo.commentId) {
      editorContainerRef.current?.setEditing?.(true)
    }
  }, [replyCommentInfo.commentId])

  return (
    <ErrorBoundary fallback={<NoData status={TodoDetailStatus.networkErr}></NoData>}>
      <div
        className={s.box}
        onClick={e => {
          //标记click事件源头
          ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.list
          //editorRef.current?.resetHeightSize();
        }}
        ref={ref}
        id="task-detail-wrapper"
        key={taskInfo.taskId}
        onClickCapture={e => stopClickWhenDropdownPending(e, '#task-detail-wrapper')}
      >
        <ConfigProvider getPopupContainer={() => document.querySelector('#task-detail-wrapper') as HTMLElement}>
          <DetailHeader
            permissions={permissions}
            taskInfo={taskInfo}
            containerElement={ref.current}
            onClose={onClose!}
          />
          {status === TodoDetailStatus.normal ? (
            <>
              <ConfigProvider getPopupContainer={() => document.querySelector('#detail_body') as HTMLElement}>
                <Scrollbar className={classNames(s.scrollbar, 'detail-scroll-content')} thumbShowWhenHover size="small">
                  <div className={s.body} id="detail_body">
                    <ParentTaskProfile taskInfo={taskInfo} />
                    <div className={s.detail}>
                      <RenderItem
                        className={classNames(
                          `${s.renderItemIitle} ${!(hasSource && !sessionId) && s.renderItemIitleNoSource}`,
                        )}
                        leftRender={null}
                      >
                        <TitleItem
                          isFinished={selfFinished === Finished.finished}
                          value={title}
                          taskId={taskId!}
                          disabled={disabled}
                        ></TitleItem>
                      </RenderItem>
                      {hasSource && !sessionId ? <SourceItem taskInfo={taskInfo}></SourceItem> : null}
                      {/* 执行人 */}
                      <RenderItem
                        className={classNames(s.itemFlexStart, {
                          [s.disabledParticipantWrapper]: disabled,
                        })}
                        leftRender={
                          <>
                            <Icon name="icon-details_data_user" />
                            <span className={s.labelText}>{I18N.auto.participants}</span>
                          </>
                        }
                      >
                        <ExecutorItem
                          containerElement={ref.current}
                          followerUids={followerUids}
                          assigneeUids={assigneeUids}
                          disabled={disabled}
                          showAddIcon={!disabled}
                          value={participants || []}
                          assignerUid={taskInfo.assigner?.assignerUid || ''}
                          taskId={taskId!}
                          showPercent={false}
                          canEditCompleteCondition={memoPermissions.CAN_SET_COMPLETE_MODE}
                          oneAndMoreType={
                            taskInfo.completeCondition === OneAndMoreServerParamsType.all
                              ? OneAndMoreType.all
                              : OneAndMoreType.one
                          }
                        ></ExecutorItem>
                      </RenderItem>
                      {/* 截止时间 */}
                      <RenderItem
                        leftRender={
                          <>
                            <Icon name="icon-Soft_qk_td"></Icon>
                            <span className={s.labelText}>{I18N.auto.time_2}</span>
                          </>
                        }
                      >
                        <NewDatePickerItem
                          value={memoTime}
                          taskId={taskId!}
                          disabled={disabled}
                          loopInducedDisabled={!taskInfo.deadlineEditable}
                          isExpired={isExpired}
                          isToday={isToday}
                          taskInfo={taskInfo}
                        />
                      </RenderItem>
                      <RenderItem
                        leftRender={
                          <>
                            <Icon name="icon-details_data_pojectline" />
                            <span className={s.labelText}>{I18N.auto.project}</span>
                          </>
                        }
                      >
                        <ProjectItem value={project} taskId={taskId!} disabled={disabledProject}></ProjectItem>
                      </RenderItem>
                      <RenderItem
                        leftRender={
                          <>
                            <Icon name="icon-details_data_redio" />
                            <span className={s.labelText}>{I18N.auto.priority}</span>
                          </>
                        }
                      >
                        <LevelItem value={priority!} taskId={taskId!} disabled={disabled}></LevelItem>
                      </RenderItem>
                      <RenderItem
                        leftRender={
                          <>
                            <Icon name="icon-details_data_file"></Icon>
                            <span className={s.labelText}>{I18N.auto.attachment}</span>
                          </>
                        }
                        className={classNames(
                          s.itemFlexStart,
                          s.attachmentWrapper,
                          !attachments?.length && s.noAttachmentWrapper,
                        )}
                      >
                        <AttachmentItem value={attachments} taskId={taskId!} disabled={disabled}></AttachmentItem>
                      </RenderItem>
                      <RenderItem
                        leftRender={
                          <>
                            <Icon name="icon-details_data_note"></Icon>
                            <span className={s.labelText}>{I18N.auto.remarks}</span>
                          </>
                        }
                        className={classNames(s.itemFlexStart, s.remark, {
                          // [s.hidden]: !taskInfo.multMsgContent && !remark && isNotParticipant,
                          [s.noRemark]: !taskInfo.multMsgContent && !remark,
                          [s.remarkEditing]: remarkEditing,
                          [s.disabledRemark]: disabled,
                        })}
                      >
                        <div style={{ width: '100%' }}>
                          <ChatRecordItem
                            taskId={taskId!}
                            title={taskInfo.multMsgTitle || ''}
                            digest={taskInfo.multMsgDigest ? JSON.parse(taskInfo.multMsgDigest) : []}
                            content={taskInfo.multMsgContent || ''}
                            disabled={disabled}
                          />

                          <RemarkItem
                            value={remark}
                            onEditStateChange={v => {
                              setRemarkEditing?.(v)
                            }}
                            taskId={taskId!}
                            placeholder={disabled ? '' : I18N.auto.edit}
                            disabled={disabled}
                            hasMultMsgContent={!!taskInfo.multMsgContent}
                          />
                        </div>
                      </RenderItem>
                      <CustomRenderItems taskInfo={taskInfo} />
                    </div>

                    {!taskInfo.parentId && (
                      <>
                        <Divider type="horizontal" className="mt-16 mb-16"></Divider>
                        {/* 子任务 */}
                        <SubTasks taskInfo={taskInfo} canEdit={memoPermissions.CAN_EDIT} />
                      </>
                    )}
                    <Divider
                      type="horizontal"
                      className="mt-24 mb-0"
                      style={{ borderTop: '4px solid var(--aBlack4)' }}
                    ></Divider>
                    {/* 不直接拿全局的id,而是拿taskInfo的id 可以避免当前无权限的情况 */}
                    <Comment taskId={taskInfo.taskId} key={taskInfo.taskId} canComment={memoPermissions.CAN_COMMENT} />
                  </div>
                  <div className={s.footer}>
                    {memoPermissions.CAN_COMMENT ? (
                      // <Editor
                      //   key={taskId}
                      //   value={commentValue}
                      //   onChange={setCommentValue}
                      //   permissions={permissions}
                      //   placeholder={replyCommentInfo.commentId ? '' : I18N.auto.enterComment}
                      //   reply={
                      //     replyCommentInfo.commentId ? (
                      //       <div className={s.replyWrap}>
                      //         <div className={s.reply}>
                      //           <div className="ellipsis">
                      //             {I18N.auto.reply} {replyCommentInfo.operatorName}
                      //           </div>
                      //           <IconBtn
                      //             title={I18N.auto.delete}
                      //             className={s.deleteReply}
                      //             iconClassName={s.iconClass}
                      //             fontSize={12}
                      //             iconName="icon-close"
                      //             onClick={(e) => {
                      //               dispatch.record.setReplyCommentId({});
                      //             }}
                      //           ></IconBtn>
                      //         </div>
                      //       </div>
                      //     ) : null
                      //   }
                      //   ref={editorRef}
                      //   onSubmit={onSubmit}
                      //   onKeyDown={(e) => {
                      //     if (e.key === 'Backspace' && !commentValue && replyCommentInfo.commentId) {
                      //       dispatch.record.setReplyCommentId({});
                      //     }
                      //   }}
                      //   alwaysShowBtn={!!replyCommentInfo.commentId}
                      //   taskInfo={taskInfo}
                      // ></Editor>

                      <EditorContainer
                        ref={editorContainerRef}
                        key={taskId}
                        taskId={taskId!}
                        value=""
                        type="comment"
                        placeholder={replyCommentInfo.commentId ? '' : I18N.auto.enterComment}
                        onSubmit={onSubmit}
                        taskInfo={taskInfo}
                        permissions={permissions}
                        reply={
                          replyCommentInfo.commentId ? (
                            <div className={s.replyWrap}>
                              <div className={s.reply}>
                                <div className="ellipsis">
                                  {I18N.auto.reply} {replyCommentInfo.operatorName}
                                </div>
                                <IconBtn
                                  title={I18N.auto.delete}
                                  className={s.deleteReply}
                                  iconClassName={s.iconClass}
                                  fontSize={12}
                                  iconName="icon-close"
                                  onClick={e => {
                                    dispatch.record.setReplyCommentId({})
                                  }}
                                ></IconBtn>
                              </div>
                            </div>
                          ) : null
                        }
                      />
                    ) : null}

                    <AddFollower
                      containerElement={ref.current}
                      value={taskInfo.followers!}
                      showPercent
                      maxShowCount={5}
                      taskInfo={taskInfo}
                    />
                  </div>
                </Scrollbar>
              </ConfigProvider>
            </>
          ) : status ? (
            <NoData status={status}></NoData>
          ) : null}
        </ConfigProvider>
      </div>
    </ErrorBoundary>
  )
}

export default Detail
