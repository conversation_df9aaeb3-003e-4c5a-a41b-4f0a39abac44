// 文件图标类型映射
export const FILE_EXT_MAP: Record<string, string> = {
  aac: 'music',
  ape: 'music',
  aiff: 'music',
  dsd: 'music',
  mp3: 'music',
  flac: 'music',
  mqa: 'music',
  ogg: 'music',
  wav: 'music',
  m4a: 'music',
  wma: 'music',
  aif: 'music',
  mid: 'music',
  caf: 'music',
  mic: 'music',
  pcm: 'music',
  webm: 'video',
  mkv: 'video',
  flv: 'video',
  vob: 'video',
  ogv: 'video',
  '3gp': 'video',
  avi: 'video',
  mov: 'video',
  wmv: 'video',
  ts: 'video',
  rmvb: 'video',
  rm: 'video',
  mp4: 'video',
  jpeg: 'image',
  jpg: 'image',
  jpe: 'image',
  png: 'image',
  tiff: 'image',
  bmp: 'image',
  svg: 'image',
  raw: 'image',
  webp: 'image',
  ico: 'image',
  tga: 'image',
  hdr: 'image',
  exr: 'image',
  dds: 'image',
  xcf: 'image',
  pcx: 'image',
  heif: 'image',
  gif: 'gif',
  asp: 'code',
  bat: 'code',
  c: 'code',
  class: 'code',
  cpp: 'code',
  h: 'code',
  html: 'code',
  java: 'code',
  js: 'code',
  jsp: 'code',
  r: 'code',
  css: 'code',
  less: 'code',
  scss: 'code',
  sass: 'code',
  php: 'code',
  styl: 'code',
  py: 'code',
  rb: 'code',
  xml: 'code',
  json: 'code',
  md: 'code',
  properties: 'code',
  vsh: 'code',
  fsh: 'code',
  lua: 'code',
  glsl: 'code',
  ini: 'code',
  conf: 'code',
  yml: 'code',
  sql: 'code',
  mel: 'code',
  pdf: 'pdf',
  max: '3dmax',
  fbx: '3dmax',
  aep: 'aep',
  ai: 'ai',
  eps: 'ai',
  ait: 'ai',
  c4d: 'c4d',
  dmg: 'dmg',
  docx: 'docs',
  doc: 'docs',
  exe: 'exe',
  fig: 'figma',
  key: 'key',
  numbers: 'numbers',
  pages: 'pages',
  ppt: 'ppt',
  pptx: 'ppt',
  pr: 'pr',
  psd: 'psd',
  psb: 'psd',
  rp: 'rp',
  xls: 'sheet',
  xlsx: 'sheet',
  csv: 'sheet',
  sketch: 'sketch',
  txt: 'txt',
  xd: 'xd',
  xmind: 'xmind',
  zip: 'zip',
  '7z': 'zip',
  rar: 'zip',
  rar4: 'zip',
  s7z: 'zip',
  rz: 'zip',
  kz: 'zip',
};

export const IconUnknow =
  'https://popo.res.netease.com/popo-assets/doc/file-icon/file_type_unknow.svg';

export const IconImg = 'https://popo.res.netease.com/popo-assets/doc/file-icon/file_type_image.svg';

export const getFileIcon = (fileExt: string) => {
  const type = FILE_EXT_MAP[fileExt] || 'unknow';
  return `https://popo.res.netease.com/popo-assets/doc/file-icon/file_type_${type}.svg`;
};
