import classNames from 'classnames';

import s from './index.less';
export type Props = {
  className?: string;
  fontSize?: number;
  name?: string;
  onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  color?: string;
  bgColor?: string;
  active?: boolean;
};

export default function Icon(props: Props) {
  const { bgColor, fontSize = 32, className, name, onClick, active } = props;
  return (
    <i
      className={classNames(s.iconBox, className)}
      style={{ backgroundColor: bgColor }}
      onClick={onClick}
    >
      {name ? (
        <i
          style={{ fontSize: `${fontSize / 100}rem` }}
          className={classNames('icon iconfont', `icon-${name}`, s.icon, s[`icon-${fontSize}`], {
            [s.active]: active,
          })}
        ></i>
      ) : null}
    </i>
  );
}
