.task__line {
  padding: 6px 8px 6px 22px;

  &.task__line--default {
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;

    &:not(.disabled):hover {
      background-color: var(--aBlack4);
    }

    &.disabled {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }

  &.task__line--editing {
    background-color: var(--aBrand6);
  }

  .default__create {
    height: 22px;
    color: var(--TextTertiary);
    column-gap: 10px;
    font-size: 14px;
  }

  .status__icon {
    font-size: 16px;
    color: var(--IconQuartus);
    background-color: var(--aBlack6);
    border-radius: 50%;
  }

  .title__input {
    background-color: transparent;
    flex: 1;
    border: none;
    min-height: 20px;
    padding-top: 0;
    padding-bottom: 0;
    line-height: 20px;
    &::before {
      display: none;
    }

    :global {
      .rock-input {
        padding: 0;
        line-height: 20px;
      }
    }
  }

  .cancel__btn {
    color: var(--IconPrimary);
  }

  :global {
    .rock-btn-xs {
      width: 22px;
      height: 22px;
    }

    .rock-btn-primary {
      color: var(--absWhite);
      background-color: var(--Brand500);

      &:hover {
        background-color: var(--Brand400);
      }

      &:active {
        background-color: var(--Brand600);
      }

      &[disabled]:hover {
        color: var(--absWhite);
      }
    }
  }
}
