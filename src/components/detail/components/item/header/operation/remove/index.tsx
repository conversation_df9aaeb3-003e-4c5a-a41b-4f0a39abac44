import React from 'react';

import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './web.less';
export interface Props {
  onRemove?: () => void;
  iconBtnClass?: string;
}

const Remove: React.FC<Props> = (props) => {
  const { onRemove, iconBtnClass } = props;
  return (
    <span className="ml-10">
      <IconBtn
        title={I18N.auto.delete}
        className={iconBtnClass}
        iconName="icon-details_nav_delete1"
        onClick={onRemove}
      ></IconBtn>
    </span>
  );
};

export default Remove;
