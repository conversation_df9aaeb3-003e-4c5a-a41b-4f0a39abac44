import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useMemo } from 'react';

import { TaskInfo } from '@/types';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import RenderItem, { UnplannedItemProps } from './item';
import useGetPermissions from '@/hooks/useGetPermissions';

export interface SortableItemProps extends UnplannedItemProps {
  taskId: number;
  data: TaskInfo;
}

const SortableItem = (props: SortableItemProps) => {
  const { taskId, title, data } = props;
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: taskId,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const permissions = useGetPermissions({ taskId });

  const CAN_EDIT = useMemo(() => {
    const [CAN_EDIT] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_EDIT],
    }) as boolean[];
    return CAN_EDIT;
  }, [permissions]);

  const retprops = useMemo(() => {
    if (CAN_EDIT) {
      return {
        ...listeners,
      };
    }
    return {};
  }, [CAN_EDIT, listeners]);

  return (
    <div
      style={style}
      ref={setNodeRef}
      {...attributes}
      {...retprops}
      className={CAN_EDIT ? 'dndDraggableEl' : ''}
      taskId={data.taskId!}
    >
      <RenderItem taskId={taskId} title={title} data={data}></RenderItem>
    </div>
  );
};

export default SortableItem;
