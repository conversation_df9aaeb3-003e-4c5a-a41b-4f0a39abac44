import * as VTable from '@visactor/vtable'
import * as VTableGantt from '@visactor/vtable-gantt'
import { useLatest, useMemoizedFn } from 'ahooks'
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef } from 'react'
import { createRoot } from 'react-dom/client'
import { useDispatch, useSelector } from 'react-redux'

import { getPermissionsByTaskList } from '@/api-common'
import { Message } from '@/components/basic'
import { AddTaskMessage } from '@/components/basic-task'
import Gantt from '@/components/gantt'
import { useGetThemeConfig } from '@/components/gantt/useGetThemeConfig'
import TimelineGuide from '@/components/guide/timeline-guide'
import { Dispatch, RootState } from '@/models/store'
import { AddMenuId, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import I18N from '@/utils/I18N'

import { createImplementer, createTaskHeader, createTaskName, onTaskNameCellClick } from './customLayout'
import { TaskNameEditor } from './customLayout/TaskNameEditor'
import s from './index.less'
import { planView2ScaleType, scaleType2PlanView } from './utils'
import { expandNodeSetMap } from '@/hooks/useGetGroupedList/utils/timelineExpandManage'
import { IGanttTaskGroupInfo } from '@/components/gantt/utils'
import { getGroupedList } from '@/hooks/useGetGroupedList'
import { getAddItem } from '@/models/utils'
import { rIC } from '@/utils/requestIdleCallback'

import { useTimelineGroup } from './view-models/use-timeline-group'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'
import { dragReorderIcon } from './customIcon/dragReOrder'
import * as _ from 'lodash'
import { useTimelineDrag } from './hooks/useTimelineDrag'
import { apiTaskResortPost } from '@/api'

const taskNameEditor = new TaskNameEditor()
VTable.register.editor('taskNameEditor', taskNameEditor)
VTable.register.icon('dragReorder', dragReorderIcon)

const Timeline = () => {
  const ganttInstance = useRef<VTableGantt.Gantt>()
  const listTableInstance = useRef<VTable.ListTable>()
  // 记录插入位置的下一个节点：
  // 在编辑状态时如果列表更新了，插入的数据[0,0,0]会被移除，但是编辑态并不会移除，
  // 导致编辑态出现在正常的cell[0,0,0]上，所以需要记录插入位置的下一个节点，以便在特殊情况下编辑态结束时更新
  const insertSiblingRecord = useRef(null)

  const { dataList, treeList, userInfo, user, theme, detail, currentViewTab, permissionsMap, viwePermissions } = useSelector(
    (state: RootState) => {
      return {
        dataList: state.gantt.dataList,
        treeList: state.gantt.treeList,
        userInfo: state.user.userInfo,
        theme: state.user.corlorMode,
        user: state.user,
        detail: state.detail,
        viwePermissions: state.viewSetting.permissions,
        currentViewTab: state.viewSetting.currentViewTab,
        permissionsMap: state.permissions.permissionsMap,
      }
    },
  )

  const isGroup = useMemo(() => {
    return !!currentViewTab?.queryGroupBys?.length
  }, [currentViewTab?.queryGroupBys])

  const tableList = isGroup ? treeList : dataList

  // 用于 vtable 中头部，防止闭包问题
  const dataListRef = useLatest(dataList)

  const canCreateTask = useMemo(() => {
    return validatesPermission({
      permissions: viwePermissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    })
  }, [viwePermissions])

  const { groupData } = useTimelineGroup({ ganttInstanceRef: ganttInstance, canCreateTask });

  const dispatch = useDispatch<Dispatch>()

  const { themeConfig, themeTokens } = useGetThemeConfig({
    theme,
  })

  const themeTokensRef = useLatest(themeTokens)

  const { hasDragIcon } = useTimelineDrag()
  const hasDragIconRef = useLatest(hasDragIcon)

  // 处理时间线视图自定义排序
  const onChangeHeaderPosition = args => {
    const { target, source } = args
    const targetRecord = listTableInstance.current?.getRecordByRowCol(target.col, target.row)
    const sourceRecord = listTableInstance.current?.getRecordByRowCol(source.col, source.row)
    const prevRecord = listTableInstance.current?.getRecordByRowCol(0, target.row - 1)
    const tailRecord = listTableInstance.current?.getRecordByRowCol(0, target.row + 1)

    // 如果发现是和新增任务行交换的话，需要重新调换位置
    // 判断target是不是最后一行
    const recordLength = listTableInstance.current?.records?.length
    const isLastRow = target.row === recordLength
    if (isLastRow) {
      // 交换位置
      const clonedRecords = _.clone(listTableInstance.current?.records) || []
      const newRecords = [
        ...clonedRecords.slice(0, source.row - 1),
        ...clonedRecords.slice(target.row - 1, target.row),
        ...clonedRecords.slice(source.row - 1, target.row - 2),
        ...clonedRecords.slice(target.row - 2, target.row - 1),
      ]
      listTableInstance.current?.setRecords(newRecords)
      ganttInstance.current?.setRecords(newRecords)
      listTableInstance.current?.render()
      // 直接退出,不走后续保存逻辑
      return false
    }
    // 如果是子任务的拖拽，则直接走排序保存
    const preTaskId = target.row === 1 ? undefined : prevRecord.taskId
    const tailTaskId = tailRecord ? tailRecord.taskId : undefined
    const currentTaskId = targetRecord.taskId
    if (!!targetRecord.parentId && !!sourceRecord.parentId) {
      apiTaskResortPost({
        preTaskId,
        tailTaskId,
        currentTaskId,
      })
    } else {
      // 否则走的是视图的保存逻辑
      dispatch.viewSetting.updateProjectTaskOrder({
        orderParam: {
          preTaskId,
          tailTaskId,
          currentTaskId,
        },
        hasViewChanged: true,
      })
    }
  }

  const handleReady = (gantt: VTableGantt.Gantt) => {
    ganttInstance.current = gantt
    listTableInstance.current = gantt.taskListTableInstance

    if (!listTableInstance.current) {
      return
    }

    // 单元格点击事件
    listTableInstance.current?.on('click_cell', (args: VTable.TYPES.MousePointerCellEvent) => {
      const { stopNext } =
        onTaskNameCellClick(args, {
          listTableInstance: listTableInstance.current,
          userInfo,
          dispatch,
          detail,
          hasDragIconRef,
        }) || {}

      if (!stopNext) {
        const { col, row } = args
        const record = listTableInstance.current?.getRecordByRowCol(col, row)
        // 打开任务详情
        if (record?.taskId && !record.isGroup) dispatch.detail.openDetail(record.taskId)
      }
    })

    // 监听行的拖拽
    listTableInstance.current?.on('change_header_position', onChangeHeaderPosition)
  }

  const handleAddTask = useCallback(
    (e: MessageEvent) => {
      const { data } = e
      if (data?.type === 'timeline-add-task') {
        if (!ganttInstance.current?.taskTableWidth) {
          //@ts-ignore
          ganttInstance.current?.toggleSidebar?.()
        }

        if (listTableInstance.current) {
          // 要插入的位置
          let recordIndex: number | number[] = 0
          const newRecords = listTableInstance.current?.records
          let currRecord = newRecords[0]
          // let children = groupDataRef.current[0]?.children;
          while (!currRecord.isParent && currRecord?.children?.length) {
            if (!Array.isArray(recordIndex)) {
              recordIndex = [recordIndex]
            }
            // 如果当前节点是折叠状态，则展开
            if (currRecord.children && currRecord.hierarchyState !== 'expand') {
              listTableInstance.current.toggleHierarchyState(0, recordIndex.length)
            }
            recordIndex.push(0)
            currRecord = currRecord.children[0]
          }

          insertSiblingRecord.current = { ...currRecord }

          rIC(() => {
            const id = `${TaskTableRowTypeAdd}header`

            listTableInstance.current?.addRecord(
              {
                type: 'new_task',
                _rowType: TaskTableRowType.addBtn,
                id: id,
                taskId: id,
                assigner: userInfo,
                hierarchyState: 'collapse',
                isHeader: true,
              },
              Array.isArray(recordIndex) ? [...recordIndex] : recordIndex,
            )

            listTableInstance.current?.startEditCell(
              1,
              Array.isArray(recordIndex) ? recordIndex.length : recordIndex + 1,
            )
            // const newRecords = listTableInstance.current?.records;
            ganttInstance.current?.setRecords(newRecords)
            // dispatch.gantt.getTodoList({
            //   page: 1,
            //   onlyGetData: false,
            // });
          })
        }
      }
    },
    [listTableInstance.current],
  )

  const onExpandChange = useMemoizedFn(
    (
      args: {
        col: number
        row: number
        hierarchyState: any
        originData?: IGanttTaskGroupInfo
      } | null,
    ) => {
      console.log('>>> onExpandChange >>>', args)
      const curViewId = currentViewTab.id || currentViewTab.viewId

      const expandNodeSet = expandNodeSetMap.getExpandNodeSet(curViewId)

      const groupId = args?.originData?.groupId

      if (groupId != null) {
        if (args?.hierarchyState === 'expand') {
          expandNodeSet.add(groupId)
        } else {
          expandNodeSet.delete(groupId)
        }
      } else {
        // 坑点：收起的时候没有 originData，只能自己拿
        const collapseGroup = ganttInstance.current?.taskListTableInstance?.getRecordByRowCol(args!.col, args!.row) as
          | IGanttTaskGroupInfo
          | undefined

        if (collapseGroup?.groupId) {
          expandNodeSet.delete(collapseGroup.groupId)
        }
      }
    },
  )

  useEffect(() => {
    function handleTaskNameEditEnd(e: MessageEvent) {
      const { data } = e
      if (data?.type === 'taskName-edit-end') {
        const { col, row, value } = data
        const record = listTableInstance.current?.getRecordByCell(col, row)

        const showIndex = listTableInstance.current?.getRecordIndexByCell(col, row)
        const trimValue = value?.trim()
        if (!trimValue) {
          let list = listTableInstance.current?.records || []

          if (Array.isArray(showIndex)) {
            let target = list

            for (let i = 0; i < showIndex.length - 1; i++) {
              const children = target[showIndex[i]]?.children
              if (children?.length) {
                target = children
              }
            }
            const removeIndex = showIndex[showIndex.length - 1]
            const removeRecord = target[removeIndex]
            if (removeRecord.taskId) {
              removeRecord.title = insertSiblingRecord.current?.title
            }
            if (record.isHeader) {
              if (target.length >= 1) {
                target.splice(showIndex[showIndex.length - 1], 1)
              }
            }
          } else {
            const record = listTableInstance.current?.getRecordByCell(col, row)
            // taskId === -1 表示在列表底部/分组底部新建，此时新建输入框无数据，不需要删除新建行
            // if (record?.taskId !== AddMenuId && typeof showIndex === 'number') {
            //   list.splice(showIndex, 1)
            // }
          }

          ganttInstance.current?.setRecords(list)
          insertSiblingRecord.current = null
          return
        }
        const list = listTableInstance.current?.records || []
        const groupData: any = {}
        let customFieldValues = null
        let target = {}
        let targetChildren = list
        if (Array.isArray(showIndex)) {
          showIndex.forEach(index => {
            target = targetChildren[index]

            const { customField, groupField, groupValue } = target

            if (customField?.fieldId) {
              customFieldValues = customFieldValues || {}
              customFieldValues[customField.fieldId] = {
                fieldId: customField.fieldId,
                value: groupValue,
                fieldVersion: customField.fieldVersion,
              }
            } else if (groupField) {
              groupData[groupField] = groupValue
              if (groupField === 'deadline') {
                groupData.deadlineFormat = 1
              }
            }
            if (target?.children?.length) {
              targetChildren = target?.children
            }
          })

          if (customFieldValues) {
            groupData.customFieldValues = {
              values: customFieldValues,
            }
          }
        }

        // 新建行，携带分组信息
        const lastLine = targetChildren?.[targetChildren.length - 1] || {}

        const info = lastLine?._rowType === TaskTableRowType.addBtn ? lastLine : {}
        const items = getAddItem({})
        dispatch.gantt.addTask({
          isHeader: record?.isHeader,
          ...items,
          ...info,
          ...target,
          ...groupData,
          title: trimValue,
          id: undefined,
          showIndex,
        })
        dispatch.gantt
          .addTask({
            title: value,
            assignees: [],
            completeCondition: 'all',
            followers: [],
            priority: 0,
            ...groupData,
          })
          .then(res => {
            if (res) {
              getPermissionsByTaskList([res], userInfo).then(permissionsMap => {
                res.permissions = permissionsMap[res.taskId].permissions
                res.finished = 0
                res.selfFinished = 0
                res.editable = true
                res.priority = groupData.priority
                // if (Array.isArray(showIndex)) {
                //   let target = null;
                //   let targetChildren = list;

                //   for (let i = 0; i < showIndex.length - 1; i++) {
                //     target = targetChildren[showIndex[i]];
                //     target.totalCount += 1;
                //     targetChildren = target?.children || [];
                //   }

                //   if (targetChildren.length) {
                //     targetChildren[showIndex[showIndex.length - 1]] = res;
                //   }
                // } else {
                //   list[showIndex] = res;
                // }

                // ganttInstance.current?.setRecords(list);
                Message.success({
                  content: (
                    <AddTaskMessage
                      onOpenDetail={() => {
                        dispatch.detail.openDetail(res.taskId as unknown as number)
                      }}
                    ></AddTaskMessage>
                  ),
                  duration: 3,
                })
                dispatch.gantt.setData({
                  dataList: [res, ...dataList],
                })
                dispatch.viewSetting.getNavigatorTaskCount()
              })
            } else {
              // TODO: 错误处理
            }
          })
        // .finally(() => {
        // });
        insertSiblingRecord.current = null
      }
    }

    window.addEventListener('message', handleAddTask, false)
    window.addEventListener('message', handleTaskNameEditEnd, false)
    listTableInstance.current?.on('tree_hierarchy_state_change', onExpandChange)

    return () => {
      window.removeEventListener('message', handleTaskNameEditEnd, false)
      window.removeEventListener('message', handleAddTask, false)
      listTableInstance.current?.off('tree_hierarchy_state_change', onExpandChange)
    }
  }, [listTableInstance.current, dataList])

  useLayoutEffect(() => {
    if (listTableInstance.current) {
      listTableInstance.current.completeEditCell()
    }
  }, [tableList])

  useEffect(() => {
    const currViewId = currentViewTab.id || currentViewTab.viewId
    expandNodeSetMap.setup({ viewId: currViewId, queryGroupBys: currentViewTab.queryGroupBys })
    expandNodeSetMap.setGanttInstanceRef(ganttInstance)
  }, [currentViewTab, ganttInstance])

  useEffect(() => {
    const container = document.querySelector(`.${s.timelineView}`)
    if (!container) {
      return
    }
    const observer = new MutationObserver(() => {
      const activeTabEl = document.querySelector('.dnd-tab-item.active__tab--item')
      if (activeTabEl) {
        const rect = activeTabEl.getBoundingClientRect()
        const { top, left } = rect

        const divEl = document.createElement('div')

        divEl.style.position = 'fixed'
        divEl.style.top = `${top + 24}px`
        divEl.style.left = `${left}px`
        divEl.style.zIndex = '9999'
        divEl.id = 'timeline-guide'

        const root = createRoot(divEl)
        root.render(<TimelineGuide user={user} />)

        document.body?.appendChild(divEl)

        // 停止观察
        observer.disconnect()
      }
    })

    // 开始观察整个文档的子树变化
    observer.observe(container, { childList: true, subtree: true })

    return () => {
      // 清理观察器
      observer.disconnect()
    }
  }, [])

  return (
    <div className={`${s.timelineView} timeline__view`}>
      <Gantt
        permissionsMap={permissionsMap}
        taskKeyField="taskId"
        startDateField="$startTime"
        endDateField="$deadline"
        onReady={handleReady}
        theme={theme}
        onTaskBarClick={evt => {
          if (evt.record?.taskId != null) {
            dispatch.detail.openDetail(evt.record?.taskId)
          }
        }}
        columns={[
          {
            field: 'title',
            title: I18N.auto.taskName,
            width: 260,
            tree: true,
            editor: 'taskNameEditor',
            // completeEditCell: (args) => {
            //   console.log('completeEditCell', args);
            // },
            headerCustomLayout: args => {
              return createTaskHeader(args, {
                dispatch,
                dataListRef,
                themeTokensRef: themeTokensRef,
              })
            },
            customLayout: args => createTaskName(args, { themeTokensRef: themeTokensRef, hasDragIcon }),
          },
          {
            field: 'start',
            title: I18N.auto.assignedTo,
            width: 80,
            headerCustomLayout: args => createTaskHeader(args, { themeTokensRef }),
            customLayout: args => createImplementer(args, { userInfo, themeTokensRef }),
          },
        ]}
        records={treeList}
        scaleType={planView2ScaleType(currentViewTab.planViewMode)}
        onScaleChange={scaleType => {
          dispatch.viewSetting.updateCurrentView({
            planViewMode: scaleType2PlanView(scaleType) as any,
          })
        }}
      />
    </div>
  )
}

export default Timeline
