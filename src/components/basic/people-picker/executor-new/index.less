.add {
  display: flex;
  align-items: center;
  color: var(--TextTertiary);
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  width: 100%;

  height: 30px;
  .height20 {
    display: flex;
    align-items: center;
    height: 20px;
  }
}
.addDisabled {
  :global {
    .com-placeholder {
      min-width: 0 !important;
      font-size: 14px !important;
    }
  }
}
.plus {
  margin-right: 4px;
  margin-top: 2px;
}

.count {
  height: 24px;
  margin-top: 3px;
  margin-bottom: 4px;
  line-height: 24px;
  font-size: 13px;
  font-weight: 400;
  color: var(--TextPrimary-strong);
}
