import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { apiProjectUpdateTaskPost } from '@/api';
import { ProjectPicker } from '@/components/basic-project';
import { Dispatch } from '@/models/store';
import { ProjectInfo, TodoInfo } from '@/types';
import { TaskTableRowType } from '@/utils/const';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  taskInfo: TodoInfo;
}

const RenderLevel: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const { taskId, projectId, _rowType: rowType, project } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();

  const permissions = useGetPermissions({ taskId });

  const change = (v: ProjectInfo) => {
    const params = {
      taskId: taskId,
      projectId: v.projectId,
    };
    if (rowType === TaskTableRowType.add) {
      dispatch.task.updateTodoList(Object.assign(taskInfo, params));
      return null;
    }
    const copyValue = projectId;
    if (v === copyValue) {
      return;
    }
    apiProjectUpdateTaskPost({
      projectId: v.projectId,
      taskId: taskId,
    })
      .then(() => {
        dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
      })
      .catch((error) => {});
  };

  const changeVisible = (v: boolean) => {
    setVisible(v);
  };

  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_ADD_TO_PROJECT, CAN_REMOVE_FROM_PROJECT] = validatesPermission({
      permissions: permissions,
      key: [
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_ADD_TO_PROJECT,
        TaskPermissionEnum.CAN_REMOVE_FROM_PROJECT,
      ],
    }) as boolean[];
    return {
      CAN_EDIT,
      CAN_ADD_TO_PROJECT,
      CAN_REMOVE_FROM_PROJECT,
    };
  }, [permissions]);

  const disabled = useMemo(() => {
    // 子任务不支持修改项目信息
    if (!!taskInfo.parentId) {
      return true;
    }
    if (project?.projectId) {
      // 有移除权限就可以修改
      return !memoPermissions.CAN_REMOVE_FROM_PROJECT && rowType !== TaskTableRowType.add;
    }
    // 如果无项目, 有新增权限就可以修改
    return !memoPermissions.CAN_ADD_TO_PROJECT && rowType !== TaskTableRowType.add;
  }, [
    memoPermissions.CAN_ADD_TO_PROJECT,
    memoPermissions.CAN_REMOVE_FROM_PROJECT,
    project,
    rowType,
  ]);

  return (
    <AddRowBox
      tipTitle=""
      placeholder={project?.projectId ? ' ' : '-'}
      taskId={taskInfo.taskId}
      disabled={disabled}
      focused={visible}
      onClick={(e) => {
        changeVisible(!visible);
      }}
      hasDelete={!!project?.projectId}
      onClear={() => {
        change({
          projectId: undefined,
        });
      }}
    >
      <ProjectPicker
        disabled={disabled}
        placeholder={' '}
        onChange={change}
        value={project}
        visible={visible}
        onVisible={(v) => {
          changeVisible(v);
        }}
      ></ProjectPicker>
    </AddRowBox>
  );
};

export default RenderLevel;
