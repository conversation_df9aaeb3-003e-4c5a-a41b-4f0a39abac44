import { useClick<PERSON>way } from 'ahooks'
import classNames from 'classnames'
import React, { useCallback, useEffect, useRef, useState, useMemo, useReducer, useLayoutEffect } from 'react'
import { useDispatch } from 'react-redux'

import { Message, Button } from '@/components/basic'
import { Space } from '@bedrock/components'
import ComFieldText from '@/components/basic-project/com-field-text'
import AddTaskMessage from '@/components/basic-task/add-task-message'
import TodoStatus from '@/components/basic-task/todo-status'
import { Dispatch, RootState, store } from '@/models/store'
import { ActionType, TaskInfo, TimeValue, TodoInfo } from '@/types'
import {
  EnumListAddSource,
  SUBTASK_MAX_COUNT,
  TaskTableRowType,
  TaskTableRowTypeAdd,
  ViewAddSource,
} from '@/utils/const'
import { Priority } from '@/utils/fields'
import I18N from '@/utils/I18N'
import { EnumTrack<PERSON><PERSON><PERSON> } from '@/utils/skyline'

import RowOperate from '../../row-operate'
import s from '../index.less'
import RenderTaskNameWrap from '../render-task-name-wrap'
import { getCreateParams, getIsInSession, getIsSessionTasks, getTaskByIndexes, handleSeverData } from '@/models/utils'
import { Row } from '@tanstack/react-table'
import ConciseOperation from './components/ConciseOperation'
import * as _ from 'lodash'
import { getExtraParams } from './utils'
import { apiTodoParticipantListGet } from '@/api'
import { useCurrentModel } from '@/hooks'

interface Props {
  groupId?: string
  taskInfo?: TaskInfo
  tableData?: TaskInfo[]
  tableRowData?: Row<any>
  onAddCreatingLine?: () => void
  parentRow: any | undefined
}

const getCurrentModelName = () => {
  const model = getIsSessionTasks() ? 'imTodoList' : 'task'
  return model
}

const RenderTaskNameAdd: React.FC<Props> = props => {
  const { groupId, taskInfo, tableData, tableRowData, onAddCreatingLine, parentRow } = props
  const { title, taskId } = taskInfo || {}
  const [editding, setEditding] = useState<boolean>(false)
  const loadingRef = useRef(false)
  const dispatch = useDispatch<Dispatch>()
  const isAddRow = taskInfo?._rowType === TaskTableRowType.add
  const initialWidth = useRef(0)
  const domRef = useRef()
  const textRef = useRef<string>(title || '')
  const [assignees, setAssignees] = useState(taskInfo?.assignees)

  const titleRef = useRef<{ getValue: () => string | undefined }>({
    getValue: () => {
      return undefined
    },
  })

  const currentModel = useCurrentModel()
  useEffect(() => {
    // 新增行的时候由于没有合法的taskId、且直接通过assignees获取到的执行人信息不对
    // 需要通过调用接口获取执行人
    // 现在默认拿其父行下的第一个子行taskId来获取(理论上应该没问题，因为是按照执行人分组)
    if (parentRow?.original?.groupBy !== 'assignee') {
      return
    }
    const _taskId = parentRow?.subRows?.[0]?.original?.taskId
    if (String(_taskId).includes(TaskTableRowTypeAdd) || !_taskId) {
      return
    }
    apiTodoParticipantListGet({ todoId: String(_taskId) }).then(res => {
      const list = [...(res || [])]
      setAssignees(list)
    })
  }, [])

  const initialTaskInfo = useMemo(() => {
    const initialData = {
      startTime: 0,
      deadline: 0,
      deadlineFormat: 0,
      rrule: '',
      alarm: {
        alarmTimestamp: 0,
        alarmRrule: '',
      },
      ...taskInfo,
    }
    return handleSeverData(initialData, store.getState().user.userInfo)
  }, [taskInfo])

  useEffect(() => {
    dispatchTaskInfo({
      type: 'merge',
      taskInfo: {
        assignees,
      },
    })
  }, [assignees])

  // 任务信息reducer
  const taskInfoReducer = (state: TodoInfo, action: { taskInfo: TodoInfo; type: 'merge' | 'replace' }): TodoInfo => {
    if (action.type === 'merge') {
      return {
        ...state,
        ...action.taskInfo,
      }
    } else if (action.type === 'replace') {
      return action.taskInfo
    }
    return state
  }

  // 这一部分数据是作为页面展示的,
  const [_taskInfo, dispatchTaskInfo] = useReducer(taskInfoReducer, null, () =>
    handleSeverData(initialTaskInfo, store.getState().user.userInfo),
  )

  const onClose = () => {
    currentModel.tableRemoveTaskItem({
      taskId: taskId,
      groupId,
      tableData,
      tableRowData,
    })
  }

  const onAdd = (title?: string, type: ActionType) => {
    if (loadingRef.current) {
      return
    }
    setEditding(false)

    // const tableData = dispatch.viewSetting.getTableList({});

    const item = _taskInfo

    // 修改行状态， 避免被新建行为将创建中的数据删除
    currentModel.updateTaskInfo({
      taskInfo: { ...item, _rowType: TaskTableRowType.creating, title },
      actionType: !title ? 'delete' : 'update',
    })

    if (!title) {
      return
    }

    loadingRef.current = true

    const extraParams = getExtraParams(_taskInfo)
    const baseParams = getCreateParams({ title, ..._taskInfo })
    currentModel
      .addTask({
        ...baseParams,
        ...extraParams,
      })
      .then(res => {
        // 列表新建埋点
        let key = EnumTrackeKey.ListNewGlobalCreate
        if (ViewAddSource.list === EnumListAddSource.bottomCreate) {
          key = EnumTrackeKey.ListNewBottomCreate
        } else if (ViewAddSource.list === EnumListAddSource.groupBottomCreate) {
          key = EnumTrackeKey.ListNewGroupBottomCreate
        }
        dispatch.user.trackingByView({
          key: key,
          taskId: res.taskId,
          groupId: groupId!,
        })
        dispatch.viewSetting.updateItemByDetail({
          targetTaskId: taskInfo?.taskId,
          taskId: res.taskId,
          detailPayload: {
            groupedIds: item?.groupedIds,
          },
        })

        Message.success({
          content: (
            <AddTaskMessage
              onOpenDetail={() => {
                dispatch.detail.openDetail(res.taskId as unknown as number)
              }}
            />
          ),
          duration: 3,
          className: s.successMessage,
        })
      })
      .catch(() => {
        dispatch.viewSetting
          .refreshDataByDataChange({
            refreshList: true,
            refreshCount: true,
          })
          .then(() => {
            //给定时间延迟,刷新视图等,降低影响
            setTimeout(() => {
              loadingRef.current = false
            }, 200)
          })
      })
      .finally(() => {
        setTimeout(() => {
          loadingRef.current = false
        }, 200)
      })

    if (type === ActionType.enter) {
      if (taskInfo?.parentId && (tableRowData?.index || 0) >= SUBTASK_MAX_COUNT - 1) {
        return
      }
      // 继续新建
      onAddCreatingLine?.({ isHeader: item.isHeader })
    }
  }
  const onTextChange = v => {
    dispatchTaskInfo({ taskInfo: { title: v }, type: 'merge' })
    textRef.current = v
  }

  useEffect(() => {
    function handleClick(e) {
      const tableDropdown =
        document.querySelector('#tanstack-table .rock-dropdown') ||
        (document.querySelector('#tanstack-table .rock-select-dropdown') as HTMLDivElement)
      if (tableDropdown) {
        return
      }

      const addingLines = document.querySelectorAll('#tanstack-table__wrapper_id .task-tr-add')

      if (
        Array.from(addingLines).some(line => {
          if (line.contains(e.target)) {
            return true
          }
        })
      ) {
        return
      }
      if (!loadingRef.current) {
        const title = titleRef.current.getValue()
        if (!title) {
          onClose()
        } else {
          onAdd(title)
        }
      }
    }

    document.body.addEventListener('mousedown', handleClick)
    return () => {
      document.body.removeEventListener('mousedown', handleClick)
    }
  }, [])

  // useClickAway(e => {}, document.querySelector('.task-tr-add'), ['mousedown'])
  //重要不要删除, useClickAway执行需要提前有task-tr-add元素, 组件渲染后设置 editding恰好能解决这个问题
  useEffect(() => {
    if (!taskInfo?.title) {
      setEditding(true)

      setTimeout(() => {
        document.querySelector('.editor-content-editable-wrap-editding .rock-input')?.scrollIntoView({
          block: 'nearest',
        })
      }, 160)
    }
  }, [])

  useEffect(() => {
    const dom = domRef.current?.closest?.('.todo-td-fixed-left')
    if (editding) {
      if (dom) {
        dom.style.zIndex = 999
      }
    } else {
      if (dom) {
        dom.style.zIndex = ''
      }
    }
  }, [domRef, editding])
  // useEffect(() => {
  //   return () => {
  //     if (textRef.current) {
  //       dispatch.task.updateTodoList({
  //         taskId,
  //         ...taskInfo,
  //         title: textRef.current,
  //         _rowType: TaskTableRowType.creating,
  //       });
  //     }
  //   };
  // }, []);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const title = titleRef.current.getValue()?.trim()
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (!title) {
        onClose()
        return
      }
      onAdd(title, ActionType.enter)
    } else if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleConfirm = () => {
    const title = titleRef.current.getValue()
    if (!title) {
      return
    }
    onAdd(title, ActionType.enter)
  }

  const handleTaskInfoChange = (newTaskInfo: TaskInfo) => {
    if (newTaskInfo.alarm) {
      newTaskInfo.alarm.alarmTimestamp = newTaskInfo.alarm.time
    }
    dispatchTaskInfo({ taskInfo: handleSeverData(newTaskInfo, store.getState().user.userInfo), type: 'merge' })
  }

  const titleDisabled = useMemo(() => {
    return !_taskInfo.title?.trim()
  }, [_taskInfo.title])

  useLayoutEffect(() => {
    const RIGHT_SPACE_WIDTH = 285 * (window.fontSizeScaleRatio || 1)
    const handleResize = () => {
      requestAnimationFrame(() => {
        const inputEle = document.querySelector('.task-tr-add .custom-input')
        const rect = inputEle?.getBoundingClientRect()
        const tableWrapperEl = document.getElementById('tanstack-table__wrapper_id') || {}
        const taskNameEl = document.getElementById('renderTaskNameId')
        if (!taskNameEl) {
          return
        }
        const { width, left } = rect || { width: 0, left: 0 }
        const paddingValue = parseFloat(getComputedStyle(taskNameEl!).paddingLeft) || tableWrapperEl?.scrollLeft
        if (!initialWidth.current) {
          initialWidth.current = width
          const distanceToRightEdge = window.innerWidth - (left + width)
          if (distanceToRightEdge < RIGHT_SPACE_WIDTH) {
            const delta = RIGHT_SPACE_WIDTH - distanceToRightEdge
            inputEle?.setAttribute('style', `width: ${width - delta - paddingValue}px!important`)
          }
        }
        const distanceToRightEdge = window.innerWidth - (left + (initialWidth.current || 0))
        if (distanceToRightEdge < RIGHT_SPACE_WIDTH) {
          const delta = RIGHT_SPACE_WIDTH - distanceToRightEdge
          inputEle?.setAttribute('style', `width: ${initialWidth.current - delta}px!important`)
        } else if (distanceToRightEdge > RIGHT_SPACE_WIDTH) {
          const delta = distanceToRightEdge - RIGHT_SPACE_WIDTH
          inputEle?.setAttribute('style', `width: ${initialWidth.current + delta}px!important`)
        }
      })
    }

    handleResize()

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [editding, window?.fontSizeScaleRatio])

  // 当输入框聚焦的时候,阻止表格的左右滚动
  useEffect(() => {
    const tableWrapperEl = document.getElementById('tanstack-table__wrapper_id')
    // 阻止表格的左右滚动
    document.body.style.overflowX = 'hidden'
    tableWrapperEl!.style.overflowX = 'hidden'

    return () => {
      document.body.style.overflowX = ''
      tableWrapperEl!.style.overflowX = ''
    }
  }, [_taskInfo])

  return (
    <RenderTaskNameWrap
      statusNode={
        <RowOperate className={s.operate}>
          <TodoStatus className={classNames(s.status)} disabled={true} taskInfo={taskInfo}></TodoStatus>
        </RowOperate>
      }
      isAdd={true}
    >
      <div
        className={classNames(s.titleWarp, s.focused, { ['add-row-focused']: isAddRow })}
        style={{ borderRadius: '0px' }}
      >
        <div
          className={classNames(s.titleWarpLeft, { [s.onlyLeft]: editding })}
          onClick={e => {
            e.stopPropagation()
          }}
          ref={domRef}
        >
          <ComFieldText
            ref={titleRef}
            className={classNames(s.listTitleEditor, {
              [s.fullWidth]: editding,
              ['custom-input']: isAddRow,
            })}
            contentClassName={s.listTitleContent}
            value={title}
            onChange={onAdd}
            onTextChange={onTextChange}
            onClose={onClose}
            editding={editding}
            onChangeEditding={v => {
              setEditding(v)
            }}
            placeholder={tableRowData?.original?.placeholder || I18N.auto.addTitleEnter}
            onKeyDown={e => {
              handleKeyDown(e)
            }}
            switchInput={true}
          />

          <div className={s.btnsWrapper}>
            <ConciseOperation taskInfo={_taskInfo} onChange={handleTaskInfoChange} />
            <Space size={4} className="ml-8 mr-8">
              <Button size="xSmall" onClick={onClose} aria-label="取消" className={s.cancel__btn}>
                {I18N.auto.cancel}
              </Button>
              <Button
                className={classNames({ [s.btnEdit]: !titleDisabled })}
                type="primary"
                size="xSmall"
                onClick={handleConfirm}
                disabled={titleDisabled}
                aria-label="保存"
              >
                {I18N.auto.preservation}
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </RenderTaskNameWrap>
  )
}

export default RenderTaskNameAdd
