.item {
  display: flex;
  align-items: center;
  height: 40px;
  .label {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 0 16px;
    font-size: 14px;
    line-height: 20px;
    color: var(--Brand500);
    cursor: pointer;
    .plus {
      margin-right: 4px;
    }
  }
  .select {
    flex: 1;
    overflow: hidden;
  }
  .clear {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin: 0 16px;
    height: 14px;
    width: 14px;
    font-size: 14px;
    color: var(--IconTertiary);
    cursor: pointer;
  }
  &.showSelect {
    .label {
      cursor: auto;
      color: var(--TextPrimary);
      .plus {
        display: none;
      }
    }
  }
}
