import { RootState } from "@/models/store"
import { useSelector } from "react-redux"
import * as _ from 'lodash'

const useTimelineDrag = () => {
  const { querySort, queryGroupBys } = useSelector((state: RootState) => {
     return {
       querySort: state.viewSetting.currentViewTab.querySort,
       queryGroupBys: state.viewSetting.currentViewTab.queryGroupBys,
     }
  })

  const hasDragIcon = 
      (querySort as any).fieldName === 'customize' && _.isEmpty(queryGroupBys)

  return {
    hasDragIcon,
  }
}



export { useTimelineDrag }