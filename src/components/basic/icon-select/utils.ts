export const icons = [
  {
    id: 1,
    name: 'pi_rocket',
  },
  {
    id: 2,
    name: 'pi_user',
  },
  {
    id: 3,
    name: 'pi_emoji',
  },
  {
    id: 4,
    name: 'pi_light',
  },
  {
    id: 5,
    name: 'pi_safety',
  },
  {
    id: 6,
    name: 'pi_color',
  },
  {
    id: 7,
    name: 'pi_community',
  },
  {
    id: 8,
    name: 'pi_cloud',
  },
  {
    id: 9,
    name: 'pi_medal',
  },
  {
    id: 10,
    name: 'pi_colorful',
  },
  {
    id: 11,
    name: 'pi_internation',
  },
  {
    id: 12,
    name: 'pi_flag',
  },
  {
    id: 13,
    name: 'pi_pennant',
  },
  {
    id: 14,
    name: 'pi_art',
  },
  {
    id: 15,
    name: 'pi_puzzle',
  },
  {
    id: 16,
    name: 'pi_lightning',
  },
  {
    id: 17,
    name: 'pi_game',
  },
  {
    id: 18,
    name: 'pi_wifi',
  },
  {
    id: 19,
    name: 'pi_efficiency',
  },
  {
    id: 20,
    name: 'pi_diamond',
  },
  {
    id: 21,
    name: 'pi_target',
  },
  {
    id: 22,
    name: 'pi_trophy',
  },
  {
    id: 23,
    name: 'pi_law',
  },
  {
    id: 24,
    name: 'pi_serve',
  },
  {
    id: 25,
    name: 'pi_calculate',
  },
  {
    id: 26,
    name: 'pi_communicate',
  },
  {
    id: 27,
    name: 'pi_food',
  },
  {
    id: 28,
    name: 'pi_heart',
  },
  {
    id: 29,
    name: 'pi_worldwide',
  },
  {
    id: 30,
    name: 'pi_music',
  },
];

export const iconColors = [
  {
    id: 1,
    name: 'pibg_red',
    color: '#F06A6A',
  },
  {
    id: 2,
    name: 'pibg_orange',
    color: '#EC8D71',
  },
  {
    id: 3,
    name: 'pibg_yellow',
    color: '#F1BD6C',
  },
  {
    id: 4,
    name: 'pibg_grass',
    color: '#83C9A9',
  },
  {
    id: 5,
    name: 'pibg_cyan',
    color: '#4ECBC4',
  },
  {
    id: 6,
    name: 'pibg_blue',
    color: '#5C9DFF',
  },
  {
    id: 7,
    name: 'pibg_purple',
    color: '#8D87CE',
  },
  {
    id: 8,
    name: 'pibg_neutral',
    color: '#6D6E6F',
  },
];

function getRandomInt(n: number) {
  return Math.floor(Math.random() * n);
}

export const getRandomIcon = () => {
  const iconLength = getRandomInt(30);
  const colorLength = getRandomInt(8);
  return { icon: icons[iconLength].name, iconColor: iconColors[colorLength].color };
};
