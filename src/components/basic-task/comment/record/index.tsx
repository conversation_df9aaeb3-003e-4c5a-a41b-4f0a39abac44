import { Progress } from '@bedrock/components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';

import { AvatarPeople, Icon, Timeline } from '@/components/basic';
import { RecordItem } from '@/types';
import {
  EventCategory,
  FORMAT_DD,
  FORMAT_DD_mm,
  LOG_FORMAT_MM_DD_HH_mm,
  LOG_FORMAT_YYYY_MM_DD_HH_mm,
  PPTimeFormat,
} from '@/utils/const';
import I18N from '@/utils/I18N';

import CommentItemRender from './comment-item';
import s from './index.less';
import RenderDesc from './render-desc';
import ItemRender from './render-item';
export type Props = {
  className?: string;
  readCommentTimestamp: number;
  item: RecordItem;
  userId?: number;
};

export enum RecordEvent {
  /**
   * 创建待办
   */
  createdToDo = 1,
  /**
   * 完成待办
   */
  toDoCompleted = 17,
  /**
   * 删除待办
   */
  deleteToDo = 99,
  /**
   * 重建待办
   */
  rebuildTheToDoList = 18,
  /**
   * 变更标题
   */
  changeTheTitleTo = 2,
  /**
   * 分配
   */
  assignToDoTo = 3,
  /**
   * 取消分配
   */
  unassignPending = 4,
  /**
   * 添加截止时间
   */
  addDeadline = 8,
  /**
   * 变更截止时间
   */
  changeDeadline = 9,
  /**
   * 删除截止时间
   */
  deleteDeadline = 10,
  /**
   * 删除评论
   */
  deleteComment = 20,
  /**
   * 新增评论
   */
  addComment = 19,
  /**
   * 验收待办任务
   */
  archive = 24,
  /**
   * 取消验收待办任务
   */
  unarchive = 25,
  /**
   * 转让指派人
   */
  transfer = 31,

  /**
   * 添加开始时间
   */
  addStartTime = 32,
  /**
   * 变更开始时间
   */
  changeStartTime = 33,
  /**
   * 删除开始时间
   */
  deleteStartTime = 34,
  /**
   * 完成整个任务
   */
  finishTotalTask = 40,
  /**
   * 完成了任务
   */
  finishOwnTask = 41,
  /**
   * 重启整个任务
   */
  rebuildTotalTask = 43,
  /**
   * 重启了自己的任务，整个任务已重启
   */
  rebuildMeToRebuildTotalTask = 44,
  /**
   * 关注该任务
   */
  followTask = 37,
  /**
   * 取消关注该任务
   */
  unFollowTask = 38,
  /**
   * 添加xx为关注者
   */
  addFollower = 35,
  /**
   * 移除关注者
   */
  removeFollower = 36,
  /**
   * 将关注者变更为执行人
   */
  transferFollowerToAssign = 42,
  /**
   * 变更任务完成方式-任意一人
   */
  changeTaskCompleteWay = 39,
  /**
   * 绑定任务和项目
   */
  bindingTaskAndProject = 51,
  /**
   * 解除任务和项目的绑定关系
   */
  unBindingTaskAndProject = 52,
}

const RecordMap = {
  [RecordEvent.finishTotalTask]: {
    title: I18N.auto.completedTheEntireTask,
  },
  [RecordEvent.finishOwnTask]: {
    title: I18N.auto.completedTheTask,
  },
  [RecordEvent.addFollower]: {
    title: I18N.auto.addVal,
  },
  [RecordEvent.removeFollower]: {
    title: I18N.auto.removedTask,
  },
  [RecordEvent.followTask]: {
    title: I18N.auto.valFollow,
  },
  [RecordEvent.unFollowTask]: {
    title: I18N.auto.cancelVal,
  },
  [RecordEvent.transferFollowerToAssign]: {
    title: I18N.auto.willValBe,
  },
  [RecordEvent.rebuildTotalTask]: {
    title: I18N.auto.restartedTheEntireSystem,
  },
  [RecordEvent.changeTaskCompleteWay]: {
    title: I18N.auto.updatedCompleted_3,
  },
  [RecordEvent.createdToDo]: {
    title: I18N.auto.createdToDo,
  },
  [RecordEvent.toDoCompleted]: {
    title: I18N.auto.toDoCompleted,
  },
  [RecordEvent.rebuildTheToDoList]: {
    title: I18N.auto.rebuildTheToDoList,
  },
  [RecordEvent.rebuildMeToRebuildTotalTask]: {
    title: I18N.auto.iRestartedMyself,
  },
  [RecordEvent.changeTheTitleTo]: {
    title: I18N.auto.changeTheTitleTo,
  },
  [RecordEvent.assignToDoTo]: {
    title: I18N.auto.assignToDoTo,
  },
  [RecordEvent.unassignPending]: {
    title: I18N.auto.unassignPending,
  },
  [RecordEvent.addDeadline]: {
    title: I18N.auto.addedDeadline,
  },
  [RecordEvent.changeDeadline]: {
    title: I18N.auto.changeDeadline,
  },
  [RecordEvent.deleteDeadline]: {
    title: I18N.auto.deletedDueDate,
  },
  [RecordEvent.deleteComment]: {
    title: I18N.auto.deletedComment,
  },
  [RecordEvent.addComment]: {
    title: I18N.auto.addedAComment,
  },
  [RecordEvent.deleteToDo]: {
    title: I18N.auto.deletedToDoList,
  },
  [RecordEvent.archive]: {
    title: I18N.auto.toDoCompleted_2,
  },
  [RecordEvent.unarchive]: {
    title: I18N.auto.assigneeCancellation,
  },
  [RecordEvent.transfer]: {
    title: I18N.auto.transferAssignor_2,
  },
  [RecordEvent.addStartTime]: {
    title: I18N.auto.addedStartTime,
  },
  [RecordEvent.changeStartTime]: {
    title: I18N.auto.changeStartTime,
  },
  [RecordEvent.deleteStartTime]: {
    title: I18N.auto.deletedStartTime,
  },
  [RecordEvent.bindingTaskAndProject]: {
    title: I18N.auto.associateTasks,
  },
  [RecordEvent.unBindingTaskAndProject]: {
    title: I18N.auto.terminateTheTaskAnd,
  },
};

interface ContextTimelineProps extends Props {
  className?: string;
  desc?: string;
  userId?: number;
  canComment?: boolean;
}

const ContextTimeline = (props: ContextTimelineProps) => {
  const { className, desc, canComment, readCommentTimestamp, userId } = props;
  const {
    operatorName,
    operatorUid,
    operatorAvatarUrl,
    eventType,
    eventDetail,
    taskId,
    timestamp = 0,
    scrollId,
  } = props.item;
  const {
    newTitle,
    newDeadline,
    newDeadlineFormat,
    prevDeadline,
    prevDeadlineFormat,
    content,
    replyToCommentId,
    replyToCommentCreatorName,
    commentId,
    newFinishPercent,
    participants,
    // commentDeleted,
    newCompleteCondition,
    newStartTime,
    prevStartTime,
    name,
    projectName,
  } = eventDetail || {};
  if (
    [
      RecordEvent.addFollower,
      RecordEvent.removeFollower,
      RecordEvent.assignToDoTo,
      RecordEvent.unassignPending,
      RecordEvent.transferFollowerToAssign,
      RecordEvent.unFollowTask,
      RecordEvent.followTask,
    ].includes(eventType!)
  ) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender
          event={RecordMap[eventType].title}
          right={(participants || []).map((item) => item.name).join('、')}
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.transfer) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender event={RecordMap[eventType].title} right={name}></ItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.changeTaskCompleteWay) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender
          event={RecordMap[eventType].title}
          right={
            newCompleteCondition === 'all' ? I18N.auto.everyoneNeedsTo : I18N.auto.anyoneCanFinishIt
          }
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.changeTheTitleTo) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender event={RecordMap[eventType].title} right={newTitle}></ItemRender>
      </Timeline.Item>
    );
  } else if (
    eventType === RecordEvent.toDoCompleted ||
    eventType === RecordEvent.finishTotalTask ||
    eventType === RecordEvent.finishOwnTask
  ) {
    return (
      <Timeline.Item
        className={classNames(className, s.completed)}
        dot={
          <Progress
            className={s.completedProgress}
            showInfo={false}
            type="circle"
            percent={(newFinishPercent || 0) * 100}
            strokeWidth={14}
            strokeLinecap="butt"
            // width={'0.15rem'}
          />
        }
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
      >
        <ItemRender
          strong
          suffixIcon={
            <img className={s.operationIcon} src={require('@/assets/images/finish-icon.png')} />
          }
          event={RecordMap[eventType].title}
          className={s.completedLabel}
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.archive) {
    return (
      <Timeline.Item
        className={classNames(className, s.completed)}
        dot={<Icon fontSize={12} className={s.completedIcon} name="icon-taskstate_down"></Icon>}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
      >
        <ItemRender
          strong
          highlight
          event={RecordMap[eventType].title}
          className={s.completedLabel}
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (
    eventType === RecordEvent.createdToDo ||
    eventType === RecordEvent.rebuildTheToDoList ||
    eventType === RecordEvent.rebuildMeToRebuildTotalTask ||
    eventType === RecordEvent.rebuildTotalTask ||
    eventType === RecordEvent.deleteToDo ||
    eventType === RecordEvent.unarchive
  ) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender event={RecordMap[eventType].title}></ItemRender>
      </Timeline.Item>
    );
  } else if (
    eventType === RecordEvent.addDeadline ||
    eventType === RecordEvent.changeDeadline ||
    eventType === RecordEvent.addStartTime ||
    eventType === RecordEvent.changeStartTime
  ) {
    let FORMAT = FORMAT_DD;
    if (newDeadlineFormat === PPTimeFormat.olayDay) {
      FORMAT = FORMAT_DD;
    }
    if (newDeadlineFormat === PPTimeFormat.dateAndTime) {
      FORMAT = FORMAT_DD_mm;
    }
    let time = newDeadline;
    if (eventType === RecordEvent.addStartTime || eventType === RecordEvent.changeStartTime) {
      time = newStartTime;
    }
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender
          event={RecordMap[eventType].title}
          right={dayjs(time).format(FORMAT)}
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (
    eventType === RecordEvent.deleteDeadline ||
    eventType === RecordEvent.deleteStartTime
  ) {
    let FORMAT = FORMAT_DD;
    if (prevDeadlineFormat === PPTimeFormat.olayDay) {
      FORMAT = FORMAT_DD;
    }
    if (prevDeadlineFormat === PPTimeFormat.dateAndTime) {
      FORMAT = FORMAT_DD_mm;
    }
    let time = prevDeadline;
    if (eventType === RecordEvent.deleteStartTime) {
      time = prevStartTime;
    }
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender
          event={RecordMap[eventType].title}
          right={dayjs(time).format(FORMAT)}
        ></ItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.deleteComment) {
    return (
      <Timeline.Item
        className={className}
        dot={<AvatarPeople className={s.avatar} avatarUrl={operatorAvatarUrl}></AvatarPeople>}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
      >
        <CommentItemRender
          canReply={false}
          replyToCommentCreatorName={replyToCommentCreatorName}
          operatorName={operatorName!}
          content={content} //不要传dom
          contentClassName={s.lineThrough}
          event={I18N.auto.deletedComment}
        ></CommentItemRender>
      </Timeline.Item>
    );
  } else if (eventType === RecordEvent.addComment) {
    const isreply = !!replyToCommentId;
    const isNewMessage = timestamp > readCommentTimestamp;
    return (
      <Timeline.Item
        className={className}
        dot={<AvatarPeople className={s.avatar} avatarUrl={operatorAvatarUrl}></AvatarPeople>}
        desc={
          <RenderDesc
            operatorName={operatorName}
            desc={desc}
            other={
              isNewMessage &&
              operatorUid !== userId && (
                <span className={s.newMessageTips}>{I18N.auto.newNews}</span>
              )
            }
          ></RenderDesc>
        }
      >
        <CommentItemRender
          scrollId={scrollId}
          isreply={isreply}
          replyToCommentCreatorName={replyToCommentCreatorName}
          operatorUid={operatorUid}
          operatorName={operatorName!}
          content={content!}
          taskId={taskId!}
          commentId={commentId!}
          event={isreply ? I18N.auto.replyTo : ''}
          canReply={canComment}
        ></CommentItemRender>
      </Timeline.Item>
    );
  } else if (
    eventType === RecordEvent.bindingTaskAndProject ||
    eventType === RecordEvent.unBindingTaskAndProject
  ) {
    return (
      <Timeline.Item
        className={className}
        desc={<RenderDesc operatorName={operatorName} desc={desc}></RenderDesc>}
        dot={<Icon className={s.defaultIcon} fontSize={24} name="icon-pc_comment_log"></Icon>}
      >
        <ItemRender event={RecordMap[eventType].title} right={projectName}></ItemRender>
      </Timeline.Item>
    );
  } else {
    return null;
  }
};
const Record: React.FC<Props> = (props: Props) => {
  const { timestamp } = props.item;
  const timeStr = useMemo(() => {
    const isThisYear = dayjs().isSame(dayjs(timestamp), 'year');
    return dayjs(timestamp).format(
      isThisYear ? LOG_FORMAT_MM_DD_HH_mm : LOG_FORMAT_YYYY_MM_DD_HH_mm
    );
  }, [timestamp]);
  return <ContextTimeline {...props} className={s.timeline} desc={timeStr}></ContextTimeline>;
};

export { Record };
