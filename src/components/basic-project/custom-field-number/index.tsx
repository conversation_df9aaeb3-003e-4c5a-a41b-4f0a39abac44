import React, { useMemo, useState } from 'react';

import { InputNumber, InputNumberProps } from '@/components/basic';
import { FieldNumberFormatEnum, NumberTypeField } from '@/types/custom-field';

import s from './index.less';

interface FieldNumberInputProps extends InputNumberProps {
  type?: FieldNumberFormatEnum;
  align?: 'left' | 'right'; //文字对齐
  noRadius?: boolean;
}

/**百分比类型的光标聚焦于%前 */
document.addEventListener('selectionchange', (e) => {
  let selection = document.getSelection();

  if (!selection || !selection.isCollapsed || selection.rangeCount == 0) {
    return;
  }
  let target = selection.anchorNode as HTMLDivElement;

  if (target && target.classList && target.classList.contains(s.inputNumber)) {
    let input = target.querySelector('.rock-input') as HTMLInputElement;
    if (!input) {
      return;
    }
    let value = input.value;

    if (value.endsWith(' %')) {
      let start = input.selectionStart;
      let end = input.selectionEnd;
      if (end && start == end && end >= value.length - 1) {
        input.setSelectionRange(value.length - 2, value.length - 2);
      }
    }
  }
});

const CustomFieldNumber: React.FC<FieldNumberInputProps> = ({
  value,
  onChange,
  size = 'large',
  align,
  type = FieldNumberFormatEnum.int,
  border = false,
  className = '',
  placeholder = '',
  noRadius = false,
  ...restProps
}) => {
  const { isPercent, toFixed, formatter, parser } = useMemo(() => {
    switch (type) {
      case FieldNumberFormatEnum.int:
        return {
          toFixed: 0,
        };
      case FieldNumberFormatEnum.float1:
        return {
          toFixed: 1,
        };
      case FieldNumberFormatEnum.float2:
        return {
          toFixed: 2,
        };
      case FieldNumberFormatEnum.percent:
      case FieldNumberFormatEnum.percent2:
        return {
          formatter: (value: any) => {
            return value || value === 0 ? `${value} %` : '';
          },
          parser: (value: any) => {
            return (value || '').replace('%', '');
          },
          toFixed: FieldNumberFormatEnum.percent2 ? 2 : 0,
          isPercent: true,
        };
      default: {
        return {
          toFixed: 0,
        };
      }
    }
  }, [type]);

  value = value || value === 0 ? formatNumber(value, type) : value;
  return (
    <InputNumber
      {...restProps}
      direction="vertical"
      className={`${s.inputNumber} ${className} ${align == 'right' ? s.rightAlign : ''} ${
        !placeholder ? s.noPlaceholder : ''
      } ${noRadius ? s.noRadius : ''}`}
      addonAfter={null}
      size={size}
      formatter={formatter}
      parser={parser}
      border={border}
      placeholder={placeholder}
      toFixed={toFixed}
      onKeyDown={(e) => {
        if (e.nativeEvent.isComposing) {
          e.preventDefault();
        }
      }}
      value={isPercent ? numberToPercent(value) : value}
      focusCaret={'end'}
      max={999999999999999.9} /*eslint-disable-line*/
      min={-999999999999999.9} /*eslint-disable-line*/
      onChange={(v) => {
        if (typeof v === 'number') {
          onChange?.(isPercent ? percentToNumber(v!) : v);
        } else {
          //@ts-ignore
          onChange?.(undefined);
        }
      }}
    />
  );
};

//乘以100，避免精度丢失
export function numberToPercent(num: number | string | undefined) {
  if (!num) {
    return num;
  }
  let str = num + '';
  let dotIndex = str.indexOf('.');
  let int = dotIndex > -1 ? str.slice(0, dotIndex) : str;
  let float = dotIndex > -1 ? str.slice(dotIndex + 1) : '';
  int = int == '0' ? '' : int;
  float = float.padEnd(2, '0');

  return parseFloat(int + float.slice(0, 2) + '.' + float.slice(2));
}

function percentToNumber(num: number) {
  if (!num) {
    return num;
  }
  let str = num + '';
  let dotIndex = str.indexOf('.');
  let int = dotIndex > -1 ? str.slice(0, dotIndex) : str;
  let float = dotIndex > -1 ? str.slice(dotIndex + 1) : '';
  int = int.padStart(2, '0');

  return parseFloat(int.slice(0, int.length - 2) + '.' + int.slice(-2) + float);
}

//inputNumber只在编辑态时，tofixed才会生效, 因此手动进行格式化
export function formatNumber(value: string | number | undefined, format: FieldNumberFormatEnum) {
  switch (format) {
    case FieldNumberFormatEnum.int:
      return value || value === 0 ? parseInt(value + '') : undefined;
    case FieldNumberFormatEnum.float1:
      return value || value === 0 ? parseFloat((+value).toFixed(1)) : undefined;
    case FieldNumberFormatEnum.float2:
      return value || value === 0 ? parseFloat((+value).toFixed(2)) : undefined;
    case FieldNumberFormatEnum.percent:
      return value || value === 0 ? parseFloat((+value).toFixed(2)) : undefined;
    case FieldNumberFormatEnum.percent2:
      return value || value === 0 ? parseFloat((+value).toFixed(4)) : undefined;
    default:
      return value;
  }
}
//控制整数位的长度,并截断
// function createFormatter(suffix = '') {
//   return (value: number | string | undefined) => {
//     if (!value && value !== 0) {
//       return '';
//     }
//     let dotIndex = (value + '').indexOf('.');
//     let int = dotIndex > -1 ? (value + '').slice(0, dotIndex) : value + '';
//     let dot = dotIndex > -1 ? (value + '').slice(dotIndex) : '';
//     //整数位最大15位
//     int = int.length > 5 ? int.slice(0, 15) : int;
//     if (int.length == 15) {
//       console.log('int', int, 'dot', dot, 'value', value);
//     }
//     return int + dot + suffix;
//   };
// }

export default CustomFieldNumber;
