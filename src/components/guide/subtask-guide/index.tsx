import { FC, useEffect, useMemo, useState } from 'react';

import { Button } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
import { createRoot } from 'react-dom/client';
import { useDispatch } from 'react-redux';
import { Dispatch, store } from '@/models/store';
import { scrollToIndex } from '@/models/utils';
import { isVerticalInViewport } from '@/utils';
import { debounce } from 'lodash';

interface SubtaskGuideProps {
  user: {
    corlorMode: string;
    language: string;
  };
}

const SubtaskGuide: FC<SubtaskGuideProps> = () => {
  const user = store.getState().user;
  const { corlorMode, language } = user;

  // const isShowGuide = useMemo(() => {
  //   const flag = window.localStorage.getItem('SUBTASK-GUIDE-DONE');
  //   return !flag;
  // }, [corlorMode, language]);

  const dispatch = useDispatch<Dispatch>();

  useEffect(() => {
    const isShowGuide = !window.localStorage.getItem('SUBTASK-GUIDE-DONE');

    let observer: MutationObserver;
    let targetEl: HTMLDivElement;
    let timer: any;
    if (isShowGuide) {
      let created = false;

      const handleHideGuide = () => {
        window.localStorage.setItem('SUBTASK-GUIDE-DONE', '1');
      };

      const handleCancel = () => {
        document.querySelector('#subtask-guide')?.remove();
        handleHideGuide();
        if (targetEl) {
          // 移除 targetEl style 上的宽度
          targetEl.style.width = '';
        }
      };

      const handleDone = () => {
        handleCancel();
        const trEl = targetEl.closest('.tanstack-table__tr');
        if (trEl) {
          const id = trEl.getAttribute('id');
          dispatch.detail.openDetail(Number(id)).then(() => {
            const subtasksEl = document.querySelector('#subtasks');
            subtasksEl?.scrollIntoView({ behavior: 'smooth' });
          });
        }
      };

      const imgSrc = `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/subtask/sub_guide_${corlorMode}_${language}.png?t=%${Date.now()}`;

      const getContent = ({ arrowStyle = {} }) => {
        return (
          <>
            <div className="rock-popover rock-popover-dark rock-popover rock-popover-placement-bottomLeft">
              <div
                className="rock-popover-arrow"
                style={{
                  left: '0.32rem',
                  top: '0.06rem',
                  backgroundColor: 'var(--TaskGuideBg)',
                  borderColor: 'var(--aBlack8)',
                  borderTopLeftRadius: '0.02rem',
                  borderTop: '1px solid var(--popover-border-color, var(--border-color-2))',
                  borderLeft: '1px solid var(--popover-border-color, var(--border-color-2))',
                  ...arrowStyle,
                }}
              ></div>
              <div
                className="rock-popover-inner"
                style={{
                  width: '3.8rem',
                  background: 'linear-gradient(180deg, var(--TaskGuideBg) 0%, var(--bgTop) 100%)',
                  borderColor: 'var(--aBlack8)',
                  borderRadius: '0.12rem',
                  padding: '0.2rem',
                }}
              >
                <div className={s.wrapper}>
                  <div className={s.title}>{I18N.auto.smartSubtask}</div>
                  <div className={s.des}>
                    {I18N.templateNode(I18N.auto.smartSubtaskDesc, {
                      val1: (
                        <span className="fs-18" style={{ fontWeight: 600 }}>
                          +
                        </span>
                      ),
                    })}
                  </div>
                  <div className={s.img__container}>
                    <img src={imgSrc} alt="img" />
                  </div>
                  <div className={s.btnWrapper}>
                    <div className={s.btnGroup}>
                      <Button type="checked-neutral" onClick={handleCancel}>
                        {I18N.auto.iGotIt}
                      </Button>
                      <Button className={s.btn} type="primary" onClick={handleDone}>
                        {I18N.auto.immediateMedicalExamination}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={s.mask}></div>
          </>
        );
      };

      observer = new MutationObserver(
        debounce(() => {
          const popoverEl = document.querySelector('.rock-popover-open');
          const dropdownEl = document.querySelector('.rock-dropdown-open');
          const guideEl = document.querySelector('#subtask-guide');
          const flag = window.localStorage.getItem('SUBTASK-GUIDE-DONE');

          if (guideEl || flag) {
            observer.disconnect();
            return;
          }
          if (popoverEl || dropdownEl || created) {
            return;
          }
          const btnEl = document.body?.querySelector('#tanstack-table .add_subtask');
          targetEl = btnEl?.parentElement as HTMLDivElement;
          if (targetEl && btnEl) {
            const closestTrEl = targetEl.closest('.tanstack-table__tr');

            created = true;
            timer = setTimeout(() => {
              if (closestTrEl) {
                const { isInViewport } = isVerticalInViewport(closestTrEl!);
                if (!isInViewport) {
                  created = false;
                  const index = Number(closestTrEl.getAttribute('data-index') || 0);
                  if (index) {
                    scrollToIndex({
                      index: Number(index) + 10,
                    });
                  }
                  return;
                }
              }

              targetEl.style.width = 'auto';
              const rect = btnEl.getBoundingClientRect();
              const { top, left } = rect;

              const divEl = document.createElement('div');

              divEl.style.position = 'fixed';
              divEl.style.top = `${top + 20}px`;
              divEl.style.left = `${left - 24}px`;
              divEl.style.zIndex = '9999';
              divEl.id = 'subtask-guide';
              let arrowStyle = {};
              if (top + 364 > window.innerHeight) {
                divEl.style.top = `${top - 394}px`;
                arrowStyle = {
                  top: 'calc(100% - 0.05rem)',
                  transform: 'rotate(225deg)',
                  background: 'var(--bgTop)',
                };
              } else {
                divEl.style.top = `${top + 20}px`;
              }

              const root = createRoot(divEl);
              const content = getContent({ arrowStyle });
              root.render(content);

              document.body?.appendChild(divEl);

              // 停止观察
              observer.disconnect();
            }, 320);
          }
        }, 300)
      );

      // 开始观察整个文档的子树变化
      observer.observe(document.body, { childList: true, subtree: true });
    } else {
      observer?.disconnect();
    }

    return () => {
      // 清理观察器
      observer?.disconnect();
      timer && clearTimeout(timer);
    };
  }, [user]);

  return null;
};

export default SubtaskGuide;
