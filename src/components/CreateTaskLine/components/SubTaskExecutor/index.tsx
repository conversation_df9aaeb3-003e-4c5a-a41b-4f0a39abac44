import { IconBtn, PeoplePicker, RenderPeoples } from '@/components/basic';
import { OneAndMoreType, PeoplePickerType } from '@/utils/const';
import { FC, useState } from 'react';
import s from './index.less';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { TaskInfo } from '@/types';
import I18N from '@/utils/I18N';
import { ExecutorContent } from '@/pages/new/components/view/list/render/render-executor';

const MAX_SHOW_COUNT = 2;

interface ISubTaskExecutorProps {
  taskInfo: TaskInfo;
  disabled?: boolean;
  oneAndMoreType?: OneAndMoreType;
  onChange?: (value: any) => void;
  handleExecuteTypeChange?: (value: any) => void;
  onSuccess?: () => void;
  emptyNode?: () => React.ReactNode;
  maxShowCount?: number;
  size?: 'small' | 'default';
}
const SubTaskExecutor: FC<ISubTaskExecutorProps> = ({
  taskInfo,
  disabled,
  oneAndMoreType,
  onChange,
  handleExecuteTypeChange,
  onSuccess,
  emptyNode,
  maxShowCount,
  size = 'small',
}) => {
  const [visible, setVisible] = useState(false);
  const {
    taskId,
    assignees = [],
    assigner,
    assigneeCount,
    userInfo,
    assigneeUids,
    followerUids,
  } = taskInfo;
  const { uid: assignerUid } = assigner || {};

  const handleVisibleChange = (visible: boolean) => {
    setVisible(visible);
  };

  const handleChange = (value) => {
    onChange?.(value);
  };

  const onOneAndMoreChange = (value) => {
    handleExecuteTypeChange?.(value);
  };

  return (
    <ExecutorContent
      onChange={handleChange}
      onSuccess={onSuccess}
      oneAndMoreType={oneAndMoreType}
      taskInfo={taskInfo}
      size={size}
      className={s.peoplePicker}
      canOpenUserProfile={false}
      maxShowCount={maxShowCount || MAX_SHOW_COUNT}
      emptyNode={
        emptyNode ? emptyNode() : <IconBtn
          title={I18N.auto.addParticipants}
          iconName="icon-kit_user_add"
          className={s.user__icon}
        />
      }
    />
  );

  return (
    <PeoplePickerContext.Provider
      value={{
        assignerUid: assignerUid,
        assigneeUids: assigneeUids,
        followerUids: followerUids,
        pickerType: PeoplePickerType.executor,
        currentUser: userInfo,
        canEditCompleteCondition: !disabled,
      }}
    >
      <PeoplePicker
        taskId={taskId!}
        disabled={disabled}
        assignerUid={assignerUid}
        search
        hasArrow={false}
        value={[...assignees]}
        onChange={handleCange}
        onOneAndMoreChange={onOneAndMoreChange}
        hasMinimum
        visible={visible}
        onVisible={handleVisibleChange}
        placeholder=" "
        oneAndMoreType={oneAndMoreType}
        className={s.peoplePicker}
      >
        {!!assignees.length ? (
          <div className={s.selected__users}>
            <RenderPeoples
              showFinishedIcon={oneAndMoreType === OneAndMoreType.all}
              list={assignees || []}
              count={assignees?.length || 0}
              maxShowCount={2}
              avatarClassName={s.avatar}
              canOpenUserProfile={false}
            />
          </div>
        ) : (
          <IconBtn
            title={I18N.auto.addParticipants}
            iconName="icon-kit_user_add"
            className={s.user__icon}
          />
        )}
      </PeoplePicker>
    </PeoplePickerContext.Provider>
  );
};
export default SubTaskExecutor;
