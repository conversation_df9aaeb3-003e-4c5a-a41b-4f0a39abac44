import { OperateCloseS } from '@babylon/popo-icons';
import classNames from 'classnames';
import { PropsWithChildren, useMemo } from 'react';

import { AvatarPeople, IconBtn } from '@/components/basic';
import { Member, Permission, UserInfo } from '@/types';
import { EnumRole } from '@/utils/const';
import I18N from '@/utils/I18N';

import SearchPeoplePicker from '../people-search';
import SwitchPermission from '../switch-permission';
import s from './index.less';
import { ConfigProvider } from '@bedrock/components';

interface Props {
  className?: string;
  value: Member[];
  onChange: (v: Member[]) => void;
  showLabel?: boolean;
  excludeUsers?: Member[];
  permissions?: Permission[];
}

const EditMember: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, onChange, className, showLabel = true, excludeUsers, permissions } = props;
  const updateMember = (v: Member) => {
    const _members = value.map((item) => {
      if (item.uid === v.uid) {
        return v;
      }
      return item;
    });
    onChange(_members);
  };
  const onDelete = (uid: string) => {
    onChange([...value.filter((item) => item.uid !== uid)]);
  };
  const addvalue = (v: UserInfo[]) => {
    const _v: Member[] = v
      .map((item) => {
        return {
          uid: item.uid!,
          name: item.name!,
          role: EnumRole.members,
          avatarUrl: item.avatarUrl!,
          sessionType: item.sessionType,
        };
      })
      .reverse();
    value.unshift(..._v);
    const uniqueData = Array.from(new Map(value.map((item) => [item.uid, item])).values());
    onChange(uniqueData);
  };

  const memoExcludeUsers = useMemo(() => {
    return value.concat(excludeUsers || []);
  }, [value, excludeUsers]);

  return (
    <ConfigProvider getPopupContainer={() => document.body}>
      <div className={classNames(s.editMember, className)}>
        {showLabel ? <div className={s.label}>{I18N.auto.inviteMembersToAdd}</div> : null}

        <div className={s.value}>
          <SearchPeoplePicker
            chooseIMContactsPlaceholder={I18N.auto.inviteTheProjectToBecome}
            uncheckableDefaultItems={memoExcludeUsers}
            value={value}
            onChange={(v) => {
              addvalue(v);
            }}
          ></SearchPeoplePicker>
        </div>
        {value.length ? (
          <div className={s.member}>
            <div className={s.dsec}>{I18N.auto.theFollowingUsersWill}</div>
            <div className={s.list}>
              {value?.map((item, index) => {
                return (
                  <div className={s.item} key={index}>
                    <AvatarPeople className={s.avatar} avatarUrl={item.avatarUrl}></AvatarPeople>
                    <div className={s.name}>{item.name}</div>
                    <SwitchPermission
                      permissions={permissions}
                      value={item.role}
                      onChange={(v) => {
                        updateMember({
                          ...item,
                          role: v,
                        });
                      }}
                      className={s.permission}
                    ></SwitchPermission>
                    {
                      //@ts-ignore
                      item.hiddenDel ? null : (
                        <IconBtn
                          className="flex-no-shrink"
                          icon={<OperateCloseS className={s.close}></OperateCloseS>}
                          title={I18N.auto.remove}
                          onClick={() => {
                            onDelete(item.uid);
                          }}
                        ></IconBtn>
                      )
                    }
                  </div>
                );
              })}
            </div>
          </div>
        ) : null}
      </div>
    </ConfigProvider>
  );
};

export default EditMember;
