import classNames from 'classnames';
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Icon } from '@/components/basic';
import {
  allTaskStatus,
  OneAndMoreType,
  TaskCompleteStatus,
  TaskStatus,
} from '@/components/basic-task/task-status';
import { Dispatch } from '@/models/store';
import { DetailTodoInfo } from '@/types';
import { OneAndMoreServerParamsType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  taskInfo?: DetailTodoInfo;
  disabled?: boolean;
}
const DetailTaskStatus = (props: Props) => {
  const { disabled, taskInfo } = props;
  const {
    taskId,
    completeCondition,
    selfFinished,
    finished,
    assigneeUids,
    finishedParticipantCount,
    participantCount,
    isCoordinator,
  } = taskInfo || {};
  const dispatch = useDispatch<Dispatch>();
  const [taskState, setTaskState] = useState<TaskCompleteStatus>(
    TaskCompleteStatus.CompleteMeOrCompleteAll
  );
  const permissions = useGetPermissions({ taskId });
  const taskFinished = useMemo(() => {
    return [
      TaskCompleteStatus.InstantaneousFinished,
      TaskCompleteStatus.RebuildMeOrCompleteAll,
      TaskCompleteStatus.RebuildMeOrRebuildAll,
    ].includes(taskState);
  }, [taskState]);

  const oneAndMoreType = useMemo(() => {
    return completeCondition === OneAndMoreServerParamsType.all
      ? OneAndMoreType.all
      : OneAndMoreType.one;
  }, [completeCondition]);

  const showDropdownIcon = useMemo(() => {
    return (
      [
        TaskCompleteStatus.CompleteMeOrCompleteAll,
        TaskCompleteStatus.RebuildMeOrCompleteAll,
        TaskCompleteStatus.RebuildMeOrRebuildAll,
      ].includes(taskState) && completeCondition === OneAndMoreServerParamsType.all
    );
  }, [taskState, completeCondition]);

  const isManager = useMemo(() => {
    const [CAN_SET_COMPLETE_MODE] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_SET_COMPLETE_MODE],
    }) as boolean[];
    return !!CAN_SET_COMPLETE_MODE;
  }, [permissions]);

  return (
    <TaskStatus
      oneAndMoreType={oneAndMoreType}
      isManager={isManager}
      isCoordinator={!!isCoordinator}
      selfFinished={selfFinished}
      finished={finished}
      assigneeUids={assigneeUids}
      taskId={taskId!}
      finishedParticipantCount={finishedParticipantCount}
      participantCount={participantCount}
      taskState={taskState}
      inDetail
      onTaskStateChange={setTaskState}
      onChange={() => {
        dispatch.detail.getTodoDetail(taskId);
      }}
      disabled={disabled}
    >
      <div>
        <div
          className={classNames(s.taskStatusBtn, {
            [s.taskFinishedStatusBtn]: taskFinished,
            [s.taskFollowerBtn]: disabled,
          })}
        >
          <span className={s.text}>{allTaskStatus[taskState].name}</span>
          {showDropdownIcon && <Icon className={s.icon} name="icon-sys_open" fontSize={16} />}
        </div>
      </div>
    </TaskStatus>
  );
};

export default DetailTaskStatus;
