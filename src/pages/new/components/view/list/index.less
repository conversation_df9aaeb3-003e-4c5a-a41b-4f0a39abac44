.tableView {
  display: flex;
  height: 100%;
  padding: 0 0 8px 20px;
  :global {
    .list-add-icon {
      width: 22px;
      height: 22px;
      flex-shrink: 0;
      .icon {
        color: var(--IconQuartus) !important;
      }
    }
    .todo-level-label {
      .com-level-bg {
        height: 22px;
      }
      .com-level-icon {
        width: 13px;
        height: 13px;
        font-size: 12px;
      }
      .com-level-label {
        display: flex;
        align-items: center;
        font-size: 13px;
      }
    }
    .todo-rendedr-peoples-avatar {
      width: 22px !important;
      height: 22px !important;
    }
    .rock-select-single .custom-select-label {
      margin-left: 0px !important;
    }

    .todo-td,
    .todo-th {
      .rock-checkbox-wrapper {
        display: none;
      }
    }

    .todo-tr {
      &:hover {
        .rock-checkbox-wrapper {
          display: block;
        }
      }
    }
    .todo-table-has-selectkeys {
      .rock-checkbox-wrapper {
        display: block !important;
      }
    }
    .todo-table .todo-tr .todo-th:hover {
      background-color: var(--N50);
    }

    .tanstack-table__tr {
      td {
        .box {
          top: -2px;

          .todo-content-editable {
            width: 100%;
          }
        }
      }
    }
  }
  .customSetting {
    transform: rotate(90deg);
    display: none;
  }
  .columnbox {
    &:hover {
      .customSetting {
        display: flex;
      }
    }
  }

  .arrow {
    width: 16px;
    height: 16px;
    color: var(--IconPrimary);
    transition: transform ease 0.25s;

    :global {
      .iconfont {
        transform: rotate(0deg);
      }
    }
  }

  .expand__container {
    display: flex;
    justify-content: flex-end;
    width: 20px;
    margin-left: 2px;
    margin-right: 6px;
    flex-shrink: 0;
  }

  .task__name {
    flex: 1;
    min-width: 0;
  }
}

.batch {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 99;
}

.dragItem {
  :global {
    .todo-tr {
      box-shadow: 0px 2px 12px 0px var(--absaBlack6) !important;
      .todo-td {
        background-color: var(--aWhite90) !important;
        // border-top: 1px solid var(--aBlack12);
        // border-bottom: 1px solid var(--aBlack12);
      }
    }
  }
}
