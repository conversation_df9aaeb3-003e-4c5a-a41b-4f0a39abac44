import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { apiTodoParticipantCandidateSearchGet } from '@/api';
import { Button, CommonSearch, Divider, IconBtn, Tooltip } from '@/components/basic';
import { UserInfo } from '@/types';
import { OneAndMoreType, PEOPLE_MAX_LENGTH, TaskTableRowTypeAdd } from '@/utils/const';
import I18N from '@/utils/I18N';

import SearchList from '../../components/search-list';
import SelectedList from '../../components/selected-list';
import SelectedTabList from '../../components/selected-tab-list';
import { PeoplePickerContext } from '../../context';
import s from './index.less';
import OneAndMore from './one-and-more';
import { debounce } from 'lodash';

export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  value: UserInfo[];
  onChange: (users: UserInfo[]) => void;
  hasMinimum?: boolean;
  selectedPanelUsers: UserInfo[];
  disabled?: boolean;
  taskId: number;
  oneAndMoreType?: OneAndMoreType;
  onOneAndMoreChange?: (oneAndMoreType: OneAndMoreType) => void;
  handleOpenChange?: (v: boolean, force: boolean) => void;
};

enum Panel {
  search,
  tabSelect,
  select,
}

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    search,
    value,
    onChange,
    selectedPanelUsers,
    taskId,
    oneAndMoreType: baseOneAndMoreType = OneAndMoreType.all,
    onOneAndMoreChange,
    disabled,
    handleOpenChange,
  } = props;
  const {
    followerUids,
    currentUser,
    canEditCompleteCondition,
    isAssigneeUpdated,
    onCancel,
    onConfirm,
  } = useContext(PeoplePickerContext);
  //面板类型
  const [panelType, setPanelType] = useState<Panel>();
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchList, setSearchList] = useState<UserInfo[]>([]);
  // 最近联系人
  const [loading, setLoading] = useState<boolean>(true);
  //一人完成还是多人完成
  const [oneAndMoreType, setOneAndMoreType] = useState<OneAndMoreType>(baseOneAndMoreType);
  const commonSearchRef = useRef<any>();
  // 禁止搜索 人数超限
  const disabledSearch = useMemo(() => {
    return value?.length >= PEOPLE_MAX_LENGTH;
  }, [value]);
  // 搜索list选择人员后，更新为true，作用：mac输入法会出现推荐选项，失焦的时候推荐选项会填入输入框，触发搜索，导致无法切换panelType
  const isSelectedRef = useRef(false);
  const [userList, setUserList] = useState<UserInfo[]>([]);

  /**
   * 搜索接口
   * @param v
   */
  const searchPeople = (v: string) => {
    setLoading(true);
    apiTodoParticipantCandidateSearchGet({ keyword: v })
      .then((res) => {
        // 遍历数据 设置被选中的数据selected为1, 并且排序按照selected排序
        const ret = res
          .map((item) => ({
            ...item,
            selected: value.findIndex((v) => v.uid === item.uid) > -1 ? 1 : 0,
          }))
          .sort((a, b) => a.selected - b.selected);
        setSearchList(ret);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const change = (userList: UserInfo[]) => {
    if (!value.length) {
      setPanelType(Panel.select);
    }
    setSearchValue('');
    setUserList(userList);
    onChange([...userList]);
    const copySelectedList = [...selectedPanelUsers];
    userList.forEach((item) => {
      const index = selectedPanelUsers.findIndex((v) => v.uid === item.uid);
      if (index === -1) {
        copySelectedList.push(item);
      }
    });
  };

  const onSearchChange = (userList: UserInfo[]) => {
    change(userList);
    if (taskId && !String(taskId).includes(TaskTableRowTypeAdd)) {
      setPanelType(Panel.tabSelect);
    } else {
      setPanelType(Panel.select);
    }
    setTimeout(() => {
      isSelectedRef.current = true;
    }, 16);
  };

  // 调用im组件批量选择
  const openIMContacts = () => {
    const checkableDefaultItems = value.map((item) => ({
      id: item.uid!,
      type: 1,
    }));
    pp.chooseIMContacts({
      groupCollapse: true,
      uncheckableDefaultItems: checkableDefaultItems,
      title: I18N.auto.addParticipants,

      maxCount: 120,
    })
      .then((res) => {
        if (Array.isArray(res.data)) {
          const obj: Record<string, UserInfo> = {};
          const _list = res.data.map((item) => ({ ...item, uid: item.id }));
          value.concat(_list).forEach((item) => {
            if (!obj[item.uid!]) {
              obj[item.uid!] = item;
            }
          });

          const newList = Object.values(obj);

          if (newList.length) {
            change(newList);
          }
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        });
      });
  };

  // 设置当前的面板类型
  const setPanelTypebyData = () => {
    if (taskId && !String(taskId).includes(TaskTableRowTypeAdd) && value.length) {
      console.log(taskId);
      setPanelType(Panel.tabSelect);
    } else {
      setPanelType(Panel.select);
    }
  };

  useEffect(() => {
    setPanelTypebyData();
  }, [taskId]);

  useEffect(() => {
    setOneAndMoreType(baseOneAndMoreType);
  }, [baseOneAndMoreType]);

  const disableEditHeader =
    oneAndMoreType === OneAndMoreType.all ? null : (
      <div className={s.participantTip}>
        {I18N.auto.assignedTo} {value.length}
        {I18N.auto.people}
      </div>
    );
  return (
    <div
      className={classNames('com-dropdown-select', s.panel, s.searchingPanel)}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {search ? (
        <>
          {disabledSearch ? (
            <Tooltip title={I18N.template(I18N.auto.addUpToPeople, { val1: PEOPLE_MAX_LENGTH })}>
              <div className={classNames(s.input, s.disabledSearch)}>{I18N.auto.addTo}</div>
            </Tooltip>
          ) : (
            <>
              <div className={s.panelSearchPd}>
                {!disabled ? (
                  <>
                    <CommonSearch
                      autoFocus
                      ref={commonSearchRef}
                      className={classNames(s.input)}
                      placeholder={I18N.auto.addParticipants}
                      onSearch={(v) => {
                        if (!v) {
                          setPanelTypebyData();
                        } else {
                          if (isSelectedRef.current) {
                            return;
                          }
                          setLoading(true);
                          setPanelType(Panel.search);
                          searchPeople(v);
                        }
                      }}
                      value={searchValue}
                      onChange={(v) => {
                        isSelectedRef.current = false;
                        setSearchValue(v);
                      }}
                      fill={false}
                      border={false}
                      round={false}
                      debounceTime={350}
                      disabled={disabledSearch}
                    />
                    <Divider type="vertical" className={s.verticalLine}></Divider>
                    <IconBtn
                      className={s.iconAdd}
                      iconName="icon-kit_user_add"
                      onClick={openIMContacts}
                      title={I18N.auto.batchAdd}
                    ></IconBtn>
                  </>
                ) : null}
              </div>
              {disabled ? null : <Divider type="horizontal" className={s.divider}></Divider>}
              {disableEditHeader}
            </>
          )}
        </>
      ) : null}
      <div className={s.container}>
        {panelType === Panel.search ? (
          <SearchList
            value={value}
            loading={loading}
            list={searchList}
            onChange={onSearchChange}
            className={s.list}
            multiple={true}
            extraItemTag={(user) => {
              const isFollower = followerUids?.includes(user.uid!);
              if (isFollower) {
                return {
                  tooltipTitle: '',
                  disabled: false,
                  tagName: I18N.auto.followPeople,
                };
              }
            }}
            currentUser={currentUser}
          ></SearchList>
        ) : null}
        {panelType === Panel.tabSelect ? (
          oneAndMoreType === OneAndMoreType.all ? (
            <SelectedTabList
              value={value}
              onChange={change}
              list={value}
              className={s.list}
              disabled={disabled}
              emptyTip={{ finished: I18N.auto.noExecutorYet, unfinished: I18N.auto.allExecutors }}
              currentUser={currentUser}
            ></SelectedTabList>
          ) : (
            <SelectedList
              value={value}
              onChange={change}
              canSelect={!disabled}
              list={value}
              className={s.list}
              multiple={true}
              currentUser={currentUser}
            ></SelectedList>
          )
        ) : null}
        {panelType === Panel.select ? (
          <SelectedList
            value={value}
            onChange={change}
            // list={value}
            list={selectedPanelUsers}
            className={s.list}
            multiple={true}
            currentUser={currentUser}
            canSelect={!disabled}
          ></SelectedList>
        ) : null}
      </div>
      {panelType !== Panel.search && (
        <div>
          {canEditCompleteCondition && (
            <>
              <Divider type="horizontal" className={s.divider}></Divider>
              {value.length >= 2 && (
                <div>
                  <OneAndMore
                    className={s.footer__item}
                    oneAndMoreType={oneAndMoreType}
                    onChange={(v) => {
                      onOneAndMoreChange?.(v);
                      setOneAndMoreType(v);
                    }}
                  ></OneAndMore>
                </div>
              )}
            </>
          )}
          {!disabled && (
            <div className={`${s.footer__option} ${s.footer__item} flex-y-center`}>
              <Button onClick={onCancel} type="checked-neutral">
                {I18N.auto.cancel}
              </Button>
              <Button
                onClick={() => onConfirm?.(userList)}
                disabled={!isAssigneeUpdated}
                className="origin__primary__btn confirm__btn"
                type="primary"
              >
                {I18N.auto.determine}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PeoplePicker;
