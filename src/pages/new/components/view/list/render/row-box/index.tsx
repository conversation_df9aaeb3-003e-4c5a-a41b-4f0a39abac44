import classNames from 'classnames'
import React, { memo, PropsWithChildren, useEffect } from 'react'
import { useDispatch } from 'react-redux'

import { IconBtn } from '@/components/basic'
import { Dispatch } from '@/models/store'
import { Drawer<PERSON>lick<PERSON><PERSON><PERSON><PERSON><PERSON>, Drawer<PERSON>rigin } from '@/utils/const'
import I18N from '@/utils/I18N'

import s from './index.less'
import { stopClickWhenDropdownPending } from '../../utils'
export type Props = {
  className?: string
  focused: boolean
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  onClick?: React.MouseEventHandler<HTMLDivElement>
  hasDelete?: boolean
  onClear?: (e: React.MouseEvent<HTMLDivElement>) => void
  //控制组件能否编辑, 目前有些字段没有任何话操作, 最多只打开详情
  canEdit?: boolean
  disabled?: boolean
  taskId?: number
  iconName?: string
  tipTitle?: string
  placeholder?: string
  align?: 'left' | 'right'
}
const RowBox: React.FC<PropsWithChildren<Props>> = props => {
  const {
    children,
    focused,
    onMouseEnter,
    onMouseLeave,
    onClick,
    hasDelete,
    onClear,
    canEdit = true,
    disabled,
    taskId,
    tipTitle,
    placeholder,
    iconName,
    align,
    className,
  } = props

  const dispatch = useDispatch<Dispatch>()
  return (
    <div
      className={classNames(s.rowbox, 'row__box', { [s.focused]: focused, [s.disabled]: disabled }, className)}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onFocus={e => {
        e.stopPropagation()
        e.preventDefault()
      }}
      onClickCapture={stopClickWhenDropdownPending}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
        if (!disabled && canEdit) {
          onClick?.(e)
        } else {
          ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.list
          dispatch.detail.openDetail(Number(taskId))
        }
      }}
    >
      {tipTitle || placeholder ? (
        <div className={`${s.placeholder} ${align == 'right' ? s.rightAlign : ''}`}>
          {tipTitle ? (
            <IconBtn
              title={disabled ? I18N.auto.noEditingPermission : tipTitle}
              iconName={iconName}
              fontSize={16}
              className={'list-add-icon'}
            ></IconBtn>
          ) : null}
          {placeholder}
        </div>
      ) : null}
      <div className={classNames(s.rowboxWrap, 'todo-row-box-wrap', { [s.disabled]: disabled })}>{children}</div>
      {hasDelete && !disabled ? (
        <div
          className={`${s.iconWrap} iconWrap`}
          onMouseDown={e => {
            e.stopPropagation()
            onClear?.(e)
          }}
        >
          <IconBtn fontSize={13} iconName="icon-close" className={s.delete}></IconBtn>
        </div>
      ) : null}
    </div>
  )
}

export default memo(RowBox)
