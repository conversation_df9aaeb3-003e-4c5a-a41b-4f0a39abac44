.input-auto-width {
  position: relative;
  display: flex;
  align-items: center;
  height: 28px;
  min-width: 10px;
  padding: 0 8px;
  font-weight: bold;
  color: var(--TextPrimary-strong);
  font-size: 16px;
  overflow: hidden;
  background-color: var(--bgBottom);
  .todo-input-origin {
    visibility: hidden;
  }
  .todo-input {
    position: absolute;
    left: 0;
    right: 0;
    height: 28px;
    line-height: 28px;
    padding: 0 6px 0 4px;
    border: 1px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: var(--bgBottom);
    color: var(--TextPrimary);
    &:hover:not(.todo-input-disabled) {
      background-color: var(--aBlack6);
    }
    &.todo-input-active {
      border-color: var(--Brand600);
      background-color: var(--bgBottom) !important;
    }
  }
}
