.label {
  display: flex;
  align-items: center;
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 24px;
  }
  .name {
    margin-left: 8px;
    line-height: 20px;
    font-size: 13px;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.tag {
  display: flex;
  align-items: center;
  height: 20px;
  border-radius: 3px;
  padding-right: 6px;
  background-color: var(--aBlack6);
  cursor: default;
  .avatar {
    width: 20px;
    height: 20px;
    border-radius: 20px;
  }
  .name {
    margin-left: 2px;
    margin-right: 2px;
    height: 20px;
    line-height: 20px;
    font-size: 13px;
  }
  .icon {
    font-size: 12px;
    color: var(--IconTertiary);
    cursor: pointer;
  }
}

.noData {
  min-height: 180px;
  :global {
    .rock-empty {
      .rock-empty-description {
        color: var(--TextTertiary);
        font-size: 13px;
        font-weight: normal;
      }
    }
  }
}

.fieldSelect {
  :global {
    .rock-select-item {
      height: 36px;
    }
    .rock-select-item.rock-select-item-selected {
      background-color: transparent;
    }
  }
}
