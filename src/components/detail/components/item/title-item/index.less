.title {
  width: 100%;
  padding: 0;
  margin: 0;
  border: 0;
  line-height: 26px;
  font-weight: 600;
  font-size: 16px;
  user-select: none;
  color: var(--TextPrimary);
  outline: none;

  a[data-popo='tag'] {
    color: var(--LinkNormal);
  }
  &::before {
    border-color: var(--Brand600);
  }

  :global {
    .rock-input-textarea {
      font-weight: 600;
      font-size: 17px;
      min-height: 28px;
      line-height: 28px !important;
      color: var(--TextPrimary-strong);
    }
  }

  &:global(.todo-content-editable) {
    border-radius: var(--border-radius-base);
    transition: background-color 0.2s ease;
    &:hover {
      background-color: var(--aBlack4);
    }
  }
}
.detailTitle {
  width: 100%;
  user-select: none;
  padding: 1px;

  :global {
    .editor-content-editable-wrap {
      width: 100%;
    }
  }

  .title {
    color: var(--TextPrimary-strong);
    line-height: 28px;
    font-size: 17px;
    font-weight: 600;
    width: 100%;
    padding: 0 2px !important;
  }
}

.subTip {
  height: 26px;
  border-radius: 3px;
  background-color: var(--aBlack4);
}

.titleWrapper {
  margin-bottom: 12px;
}
.finished {
  :global {
    .todo-content-editable {
      // text-decoration-line: line-through;
    }
  }
}
