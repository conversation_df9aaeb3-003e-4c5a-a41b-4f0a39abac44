import classNames from 'classnames';
import React from 'react';

import { Icon } from '@/components/basic';

import s from './index.less';

export type Props = {
  className?: string;
  name: string;
  iconName: string;
  onClick?: () => void;
  disabled?: boolean;
};
const Batch: React.FC<Props> = (props) => {
  const { className, name, iconName, onClick, disabled } = props;

  return (
    <div
      className={classNames(s.batchItem, { [s.disabled]: disabled }, className)}
      onClick={onClick}
    >
      <div className={classNames(s.batchBtn, 'todo-batch-btn')}>
        <Icon
          fontSize={14}
          className={classNames(s.batchIcon, 'todo-batch-icon')}
          name={iconName}
        ></Icon>
        <div className={classNames(s.desc, 'todo-batch-desc')}>{name}</div>
      </div>
    </div>
  );
};

export default Batch;
