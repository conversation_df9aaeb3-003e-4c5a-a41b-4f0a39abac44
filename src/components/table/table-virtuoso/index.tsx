import { POPOBridgeEmitter } from '@popo-bridge/web';
import React, { PropsWithChildren, useEffect, useMemo, useRef } from 'react';
import type { ListRange } from 'react-virtuoso';
import { GroupedVirtuoso, Virtuoso } from 'react-virtuoso';

import { CustomScrollbar } from '@/components/basic';
import { EnumEmitter } from '@/utils/const';

import NoData from '../components/no-data';
import { TableGroupItem } from '../types';
import s from './index.less';
export { ListRange };
export type Props<RecordType = any> = {
  isGroup?: boolean;
  atBottomStateChange?: (atBottom: boolean) => void;
  groupData?: TableGroupItem[];
  data: RecordType[];
  itemContent: (index: number) => React.ReactNode;
  groupContent?: (index: number) => React.ReactNode;
  onChange?: (v: TableGroupItem[]) => void;
  rangeChanged?: (range: ListRange) => void;
};

const CustomizeTopItemList: React.FC<PropsWithChildren<any>> = (props) => {
  const { children } = props;
  return <div>{children}</div>;
};

function Footer() {
  return <NoData></NoData>;
}

const TableVirtuoso: React.FC<PropsWithChildren<Props>> = (props) => {
  const virtuoso = useRef(null);
  const { isGroup, groupData, data, atBottomStateChange, rangeChanged, itemContent, groupContent } =
    props;
  const groupCounts = useMemo(() => {
    //根据data 计算需要返回的数量, 不要让总数超过data的长度
    let totalCount = 0;
    const counts: number[] =
      groupData?.map((item) => {
        if (!item.unfold) {
          return 0;
        } else {
          return item.count || 0;
        }
      }) || [];
    const showCounts = [];
    for (let index = 0; index < counts.length; index++) {
      totalCount += counts[index];
      if (totalCount > data?.length) {
        showCounts.push(counts[index] - totalCount + data?.length);
        break;
      } else {
        showCounts.push(counts[index]);
      }
    }
    return showCounts as number[];
  }, [groupData, data]);
  useEffect(() => {
    POPOBridgeEmitter.addListener(EnumEmitter.VirtuoSoscrollToIndex, (index) => {
      virtuoso.current?.scrollToIndex({
        index: index,
        align: 'end',
        behavior: 'auto',
      });
    });
    return () => {
      POPOBridgeEmitter.removeAllListeners(EnumEmitter.VirtuoSoscrollToIndex);
    };
  }, [virtuoso]);
  // 如果没有数据提前渲染虚拟区域,组件不会自动调整分组和不分组的区别
  // if (!groupData?.length && !data?.length) {
  //   return null;
  // }
  if (isGroup) {
    if (!groupCounts?.length) {
      return null;
    }
    return (
      <GroupedVirtuoso
        ref={virtuoso}
        id="virtuoso-table-group"
        style={{ height: '100%', overflowX: 'hidden' }}
        atBottomThreshold={150}
        components={{
          Scroller: CustomScrollbar,
          TopItemList: CustomizeTopItemList, //去除分组项的悬浮
        }}
        atBottomStateChange={atBottomStateChange}
        groupCounts={groupCounts}
        groupContent={groupContent}
        increaseViewportBy={{ top: 20, bottom: 20 }}
        itemContent={itemContent}
      ></GroupedVirtuoso>
    );
  }
  if (groupCounts?.length) {
    return null;
  }
  return (
    <Virtuoso
      key={Number(isGroup)}
      ref={virtuoso}
      data={data}
      id="virtuoso-table"
      style={{ height: '100%', overflowX: 'hidden' }}
      atBottomThreshold={150}
      components={{
        Scroller: CustomScrollbar,
        Footer: Footer,
      }}
      atBottomStateChange={atBottomStateChange}
      rangeChanged={rangeChanged}
      increaseViewportBy={{ top: 300, bottom: 300 }}
      itemContent={itemContent}
    ></Virtuoso>
  );
};

export default TableVirtuoso;
