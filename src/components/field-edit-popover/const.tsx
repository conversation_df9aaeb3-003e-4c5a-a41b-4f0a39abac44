import {
  <PERSON>16<PERSON>alendar,
  <PERSON>16<PERSON>heck,
  Data16Number,
  Data16Redio,
  Data16Title,
} from '@babylon/popo-icons';

import { FieldNumberFormatEnum, FieldTypeEnum } from '@/types/custom-field';
import I18N from '@/utils/I18N';

export * from '@/types/custom-field';

import { Icon } from '../basic';

// 项目引导用的也是这一份, 如果引导不变的话, 后续有修改则单独处理下
export const FiledTypeList = [
  {
    icon: <Data16Title></Data16Title>,
    name: I18N.auto.text,
    value: FieldTypeEnum.text,
  },
  {
    icon: <Data16Redio></Data16Redio>,
    name: I18N.auto.singleChoice,
    value: FieldTypeEnum.option,
  },
  {
    icon: <Data16Check></Data16Check>,
    name: I18N.auto.multipleChoice,
    value: FieldTypeEnum.multiOption,
  },
  {
    icon: <Icon name="icon-tage_implement_line"></Icon>,
    name: I18N.auto.personnel,
    value: FieldTypeEnum.user,
  },
  {
    icon: <Data16Calendar></Data16Calendar>,
    name: I18N.auto.date,
    value: FieldTypeEnum.datetime,
  },
  {
    icon: <Data16Number></Data16Number>,
    name: I18N.auto.number,
    value: FieldTypeEnum.number,
  },
];

export const FieldNumberFormatOptions = [
  {
    name: I18N.auto.integer,
    value: FieldNumberFormatEnum.int,
  },
  {
    name: I18N.auto.retainDecimalPlaces,
    value: FieldNumberFormatEnum.float1,
  },
  {
    name: I18N.auto.retainDecimalPlaces_2,
    value: FieldNumberFormatEnum.float2,
  },
  {
    name: I18N.auto.percentage,
    value: FieldNumberFormatEnum.percent,
  },
  {
    name: I18N.auto.percentageIsSmall,
    value: FieldNumberFormatEnum.percent2,
  },
];
