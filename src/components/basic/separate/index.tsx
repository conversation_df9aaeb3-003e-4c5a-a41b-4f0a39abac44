import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { IconBtn } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { BaseIconSizeEnum } from '@/utils/const';
import I18N from '@/utils/I18N';
import { EnumTrackeKey } from '@/utils/skyline';
import { validatesVersion } from '@/utils/validate-version';

import s from './index.less';

export type Props = {
  className?: string;
};
const Separate: React.FC<Props> = (props) => {
  const { className } = props;
  const [show, setShow] = useState<boolean>(false);
  const [showTooltip, setShowTooltip] = useState<boolean>(true);
  const { showSeparate } = useSelector((state: RootState) => ({
    showSeparate: state.user.showSeparate,
  }));
  const dispatch = useDispatch<Dispatch>();

  const toggleSeparate = () => {
    setShowTooltip(false);
    // 调用桥
    pp.switchSeparateOfTodoPage({
      separate: !showSeparate,
    }).then((res) => {
      if (res.separate) {
        //埋点: 独立窗口埋点
        dispatch.user.tracking({
          key: EnumTrackeKey.OpenInIndependentWindow,
        });
      }
      setShowTooltip(true);
      dispatch.user.setShowSeparate(res.separate);
    });
  };

  useEffect(() => {
    if (validatesVersion('getSeparateOfTodoPage')) {
      pp.getSeparateOfTodoPage().then((res) => {
        setShow(true);
        dispatch.user.setShowSeparate(res.separate);
      });
    } else if (validatesVersion('switchSeparateOfTodoPage')) {
      setShow(true);
    }
  }, []);

  const getTitle = () => {
    if (!showTooltip) {
      return '';
    }
    return showSeparate ? I18N.auto.closeIndependentWindows : I18N.auto.inASeparateWindow;
  };

  return show ? (
    <div className={classNames(s.separate, className)}>
      <IconBtn
        size={BaseIconSizeEnum.large}
        placement="topRight"
        title={getTitle()}
        iconName={showSeparate ? 'icon-view_open_inside' : 'icon-view_open_out'}
        onClick={toggleSeparate}
      />
    </div>
  ) : null;
};

export default Separate;
