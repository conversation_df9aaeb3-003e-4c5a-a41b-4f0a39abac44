import { Popover } from '@bedrock/components';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, Dropdown, Icon } from '@/components/basic';
import { RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
}

const Filter = React.forwardRef<
  {
    changeVisible: (v: boolean) => void;
  },
  PropsWithChildren<Props>
>((props, refs) => {
  const { children, disabled, className, ...rest } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { conditions } = useSelector((state: RootState) => ({
    conditions: state.viewSetting.currentViewTab?.conditions || [],
  }));
  const length = useMemo(() => {
    const valideConditions = conditions.filter((item) => {
      return !!item.values?.length;
    });
    return valideConditions?.length;
  }, [conditions]);
  useImperativeHandle(refs, () => ({
    changeVisible: (v) => {
      setVisible(v);
    },
  }));
  return (
    <Popover
      overlayClassName={s.popover}
      placement="bottomLeft"
      trigger="click"
      showArrow={false}
      disabled={disabled}
      visible={visible}
      onVisibleChange={(v) => {
        setVisible(v);
      }}
      destroyTooltipOnHide
      content={<Overlay></Overlay>}
      {...rest}
    >
      <Button
        className={classNames('mr-8', s.select, className, {
          'option-active': !!length,
          'option-opened': visible,
        })}
        type="text-subtle"
        icon={<Icon name="icon-soft_sx" fontSize={16}></Icon>}
      >
        {I18N.auto.screen}
        {length ? `: ${length}` : null}
      </Button>
    </Popover>
  );
});

export default Filter;
