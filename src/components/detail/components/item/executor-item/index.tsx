import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  apiTaskUpdateCompleteConditionPost,
  apiTodoParticipantListGet,
  apiTodoParticipantUpdatePost,
} from '@/api';
import { Modal } from '@/components/basic';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { Dispatch, RootState } from '@/models/store';
import { DetailTodoInfo, UserInfo } from '@/types';
import I18N from '@/utils/I18N';
import * as _ from 'lodash';

export type Value = { list: UserInfo[]; count?: number };

export type Props = {
  taskId: number;
  className?: string;
  value: UserInfo[];
  showPercent?: boolean;
  oneAndMoreType?: OneAndMoreType;
  showFinishPercentTip?: boolean;
  maxShowCount?: number;
  // 指派者信息
  assigner?: UserInfo;
  // 只显示指派者 为 true 时，assigner 必填
  onlyAssigner?: boolean;
  // 指派者 id
  assignerUid: string;
  disabled?: boolean;
  count?: number; //分配人数
  hasArrow?: boolean;
  assigneeUids?: string[];
  followerUids?: string[];
  showAddIcon?: boolean;
  containerElement: HTMLElement;
  canEditCompleteCondition?: boolean;
  taskInfo: DetailTodoInfo;
};
import { useMemoizedFn } from 'ahooks';

import Executor from '@/components/basic/people-picker/executor-new';
import { useExecutorRemoveSelf } from '@/components/basic-task/use-executor-remove-self';
import {
  OneAndMoreServerParamsType,
  OneAndMoreType,
  PeoplePickerType,
  ViewType,
} from '@/utils/const';

import s from './index.less';
import { arraysEqualIgnoreOrder } from '@/utils';

const ListExecutorItem: React.FC<Props> = (props) => {
  const {
    value,
    taskId,
    assignerUid,
    showPercent,
    assigner,
    disabled,
    showAddIcon,
    hasArrow,
    oneAndMoreType,
    assigneeUids,
    followerUids,
    containerElement,
    canEditCompleteCondition,
    taskInfo,
  } = props;
  const [list, setList] = useState<UserInfo[]>([]);
  const { userInfo, currentViewTab } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const [pickerVis, setPickerVis] = useState(false);
  const { removeSelf } = useExecutorRemoveSelf({
    className: s.confirm,
    taskId: taskId,
  });
  const ref = useRef<any>();
  const executorRef = useRef<any>(null);
  const dispatch = useDispatch<Dispatch>();
  const latestUidsRef = useRef({ updated: false, uids: [], srcAssignees: [] as UserInfo[] });
  const assigneesUidsRef = useRef<string[]>(value?.map((item) => item.uid!) || []);

  useEffect(() => {
    if (taskId) {
      apiTodoParticipantListGet({ todoId: String(taskId) }).then((res) => {
        const list = [...(res || [])];
        latestUidsRef.current.srcAssignees = list;
        setList(list);
      });
    }
  }, [taskId]);

  const uidsSubmit = (userList: UserInfo[]) => {
    if (latestUidsRef.current.updated) {
      const uids = latestUidsRef.current.uids;
      apiTodoParticipantUpdatePost({
        taskId: taskId,
        uids: uids,
      }).then(() => {
        latestUidsRef.current.updated = false;
        assigneesUidsRef.current = userList.map((item) => item.uid!);
        dispatch.viewSetting.updateItemByDetail({ taskId });
        latestUidsRef.current.srcAssignees = userList;
      });
    }

    return Promise.resolve();
  };

  const updateExecutor = (users: UserInfo[], updated: boolean = true) => {
    const uids: string[] = users.map((item) => item.uid!);
    if (uids.length === 0 && list.length === 0) {
      return;
    }
    latestUidsRef.current = {
      ...latestUidsRef.current,
      updated,
      uids: uids,
    };
    dispatch.detail.setTaskInfo({
      participants: users,
    });
    // apiTodoParticipantUpdatePost({
    //   taskId: taskId,
    //   uids: uids,
    // })
    //   .then(() => {
    //     setList(users);
    //     // 重新请求详情数据
    //   })
    //   .finally(async () => {
    //     dispatch.detail.getTodoDetail(taskId);
    //     if (currentViewTab.type) {
    //       if (currentViewTab.type === ViewType.list) {
    //         await dispatch.task.updatePermissionsWithTask({ taskId });
    //       }
    //       dispatch.viewSetting.refreshDataByDataChange({
    //         refreshList: true,
    //         refreshCount: true,
    //       });
    //     }
    //   });
  };
  const change = async (users: UserInfo[]) => {
    const updated = !arraysEqualIgnoreOrder(
      assigneesUidsRef.current,
      users?.map((item) => item.uid!)
    );
    const oldSelf = list.findIndex((item) => item.uid === userInfo?.uid) > -1;
    const moveSelf = users.findIndex((item) => item.uid === userInfo?.uid) === -1;
    if (moveSelf && oldSelf) {
      removeSelf().then((persist) => {
        updateExecutor(users, updated);
        if (!persist) {
          dispatch.detail.closeDetail({});
        }
      });
    } else {
      updateExecutor(users, updated);
    }
  };
  const onOneAndMoreChange = useMemoizedFn((val: number) => {
    const _completeCondition =
      val === OneAndMoreType.all ? OneAndMoreServerParamsType.all : OneAndMoreServerParamsType.one;
    // 调用更改任务完成方式接口逻辑
    taskId &&
      apiTaskUpdateCompleteConditionPost({
        taskId,
        completeCondition: _completeCondition,
      }).then(() => {
        // 接口调用成功后，拉取更新日志
        // dispatch.viewSetting.updateItemByDetail({ taskId });
        dispatch.detail.updateDetail({
          taskId,
          ...taskInfo,
          completeCondition: _completeCondition,
        });
      });
  });
  useEffect(() => {
    //TODO 判断被指派人是否有变化
    setList(value);
  }, [value]);

  const completedCount = useMemo(() => {
    return list.filter((item) => item.finished).length || 0;
  }, [list]);

  const handleConfirmUpdate = async (userList: UserInfo[]) => {
    uidsSubmit(userList);
    executorRef.current?.toggleVisible(false);
    setPickerVis(false);
  };

  const handleCancelUpdate = async () => {
    await change(latestUidsRef.current.srcAssignees);
    executorRef.current?.toggleVisible(false);
    latestUidsRef.current.updated = false;
    setPickerVis(false);
  };

  return (
    <div
      ref={ref}
      className={`people-picker-executor-item ${
        pickerVis && 'people-picker-executor-item-visible'
      }`}
    >
      <PeoplePickerContext.Provider
        value={{
          assignerUid: assignerUid,
          assigneeUids: assigneeUids,
          followerUids: followerUids,
          pickerType: PeoplePickerType.executor,
          currentUser: userInfo,
          canEditCompleteCondition: canEditCompleteCondition,
          isAssigneeUpdated: latestUidsRef.current.updated,
          onConfirm: handleConfirmUpdate,
          onCancel: handleCancelUpdate,
        }}
      >
        <Executor
          ref={executorRef}
          value={list}
          onChange={change}
          showPercent={showPercent}
          taskId={taskId}
          assigner={assigner}
          disabled={disabled}
          showAddIcon={showAddIcon}
          showCountIcon={false}
          hasArrow={hasArrow}
          oneAndMoreType={oneAndMoreType}
          assignerUid={assignerUid}
          setPickerVis={setPickerVis}
          onVisibleChange={uidsSubmit}
          finishPercentTip={
            <div className={s.finishPercentTip}>
              <div className={s.divider}></div>
              <div className={s.completeTip}>
                {oneAndMoreType === OneAndMoreType.all
                  ? I18N.template(I18N.auto.personCompleted_2, {
                      val1: `${completedCount}/${list.length}`,
                    })
                  : I18N.template(I18N.auto.humanExecution, { val1: list.length })}
              </div>
            </div>
          }
          onOneAndMoreChange={onOneAndMoreChange}
        ></Executor>
      </PeoplePickerContext.Provider>
    </div>
  );
};

export default ListExecutorItem;
