import { Gantt } from '@visactor/vtable-gantt'
import { useMemoizedFn } from 'ahooks'
import dayjs from 'dayjs'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

import { FieldLabelMap, IGanttTaskGroupInfo, IGanttTaskInfo } from '@/components/gantt/utils'
import { RootState } from '@/models/store'
import { CustomField, FieldTypeEnum } from '@/types/custom-field'
import { EnumField } from '@/utils/fields'
import I18N from '@/utils/I18N'
import { AddMenuId, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'

export interface IUseTimelineGroupOption {
  ganttInstanceRef: React.MutableRefObject<Gantt | undefined | null>
  canCreateTask?: boolean
}

const genGroupTaskId = (fieldName: string, value: string, parentTaskId: string | number = '$BJ$') =>
  `[${parentTaskId}]group-${fieldName}-${value}`

const genUnknowGroup = (parentValue?: any) => {
  return {
    taskId: genGroupTaskId('none', I18N.auto.nothing, parentValue),
    title: I18N.auto.nothing,
    isGroup: true,
    isGroupUnknown: true,
    children: [],
    finishedCount: 0,
    totalCount: 0,
    groupOrder: Number.MAX_SAFE_INTEGER,
  } as IGanttTaskGroupInfo
}

const expandNodeSetMap = {
  projectId: 0,
  viewId: 0,
  expandNodeSets: {} as Record<number, Set<string | number>>,
  expandAll: () => {},
  collapseAll: () => {},
  isAllExpand: () => false,
}

window.expandNodeSetMap = expandNodeSetMap

export function useTimelineGroup(opts: IUseTimelineGroupOption) {
  const { ganttInstanceRef, canCreateTask } = opts
  // 记录分组的排序，在第一次获取到分组后初始化该值，后续的分组操作会根据该值来排序
  const groupOrderRef = useRef({} as Record<string, number>)

  const { dataList, queryGroupBys, customFields, conditions, currentViewTab } = useSelector((state: RootState) => {
    return {
      dataList: state.gantt.dataList,
      userInfo: state.user.userInfo,
      queryGroupBys: state.viewSetting.currentViewTab?.queryGroupBys,
      customFields: state.viewSetting.customFields,
      conditions: state.viewSetting.currentViewTab.conditions,
      currentViewTab: state.viewSetting.currentViewTab,
    }
  })
  const curViewId = currentViewTab.id || currentViewTab.viewId

  // 避免其他 redux 状态更新导致分组逻辑重新计算
  const memoGroups = useMemo(() => {
    return queryGroupBys?.map(g => (g.customFieldId ? g.customFieldId : g.fieldName))
  }, [queryGroupBys])

  // 初始化或者切换 projectId 后，重置展开收起状态
  if (expandNodeSetMap.projectId !== currentViewTab.projectId || expandNodeSetMap.viewId !== curViewId) {
    expandNodeSetMap.projectId = currentViewTab.projectId
    expandNodeSetMap.viewId = curViewId
    expandNodeSetMap.expandNodeSets[curViewId] = new Set<string | number>()
    expandNodeSetMap.expandAll = () => {
      const curExpandNodeSet = expandNodeSetMap.expandNodeSets[curViewId]

      const dfs = (groups: IGanttTaskGroupInfo[] | undefined) => {
        if (!groups) {
          return
        }

        groups?.forEach(g => {
          curExpandNodeSet.add(g.taskId)
          const subs = g.children?.length ? g.children : g.subtask
          dfs(subs as IGanttTaskGroupInfo[])
        })
      }

      dfs(ganttInstanceRef.current?.records)
    }

    expandNodeSetMap.collapseAll = () => {
      const curExpandNodeSet = expandNodeSetMap.expandNodeSets[curViewId]
      curExpandNodeSet.clear()
    }

    expandNodeSetMap.isAllExpand = () => {
      let ret = true

      const dfs = (groups: IGanttTaskGroupInfo[] | undefined) => {
        if (!groups || !ret) {
          return
        }

        groups?.forEach(g => {
          /**
           * 这里必须要用 expandNodeSet 来判断，g.hierarchyState 不准确
           */
          if (g.isGroup && !expandNodeSet.has(g.taskId)) {
            ret = false
            return
          }

          dfs(g.children as IGanttTaskGroupInfo[])
        })
      }

      dfs(ganttInstanceRef.current?.records)

      return ret
    }
  }

  let expandNodeSet = expandNodeSetMap.expandNodeSets[curViewId]

  if (!expandNodeSet) {
    expandNodeSet = new Set<string | number>()
    expandNodeSetMap.expandNodeSets[curViewId] = expandNodeSet
  }

  // BUG：由于筛选、分组的按钮都在外层，没法在动作发生阶段执行清空，容易导致视图切换等的清空，暂时先不支持
  // 筛选或者分组一旦有改动，就清空之前的展开收起状态
  // useSyncUpdateEffect(() => {
  //   console.log('>>> clear expand nodes >>>', Array.from(expandNodeSet), curViewId);
  //   expandNodeSet.clear();
  // }, [memoGroups, conditions]);

  // 格式化时间，保证 vtable 能识别
  const normalizeTimestamp = useMemoizedFn((r: IGanttTaskInfo) => {
    if (r.startTime && r.deadline) {
      r.$startTime = dayjs(r.startTime).format('YYYY-MM-DD')
      r.$deadline = dayjs(r.deadline).format('YYYY-MM-DD')
    } else if (r.startTime && !r.deadline) {
      r.$startTime = dayjs(r.startTime).format('YYYY-MM-DD')
      r.$deadline = dayjs(r.startTime).format('YYYY-MM-DD')
    } else if (!r.startTime && r.deadline) {
      r.$startTime = dayjs(r.deadline).format('YYYY-MM-DD')
      r.$deadline = dayjs(r.deadline).format('YYYY-MM-DD')
    }
    if (r.isParent) {
      r.children = (r.subtask as IGanttTaskGroupInfo[]).map(s => normalizeTimestamp(s as IGanttTaskInfo))
    }
    return r
  })

  // 保存节点的展开收起状态
  const normalizeExpandState = useMemoizedFn(
    (matchedGroup: IGanttTaskGroupInfo | undefined, lastMatchGroup?: IGanttTaskGroupInfo) => {
      if (!matchedGroup) {
        return matchedGroup
      }
      if (expandNodeSet.has(matchedGroup.taskId!)) {
        matchedGroup.hierarchyState = 'expand'
      } else {
        matchedGroup.hierarchyState = 'collapse'
      }

      return matchedGroup
    },
  )

  // 统计分组节点的时间
  const getDate = useMemoizedFn((a: string, b: string, type: 'min' | 'max') => {
    if (!a || !b) {
      return a || b
    }

    const dayA = dayjs(a)
    const dayB = dayjs(b)
    const method = type === 'max' ? 'isAfter' : 'isBefore'

    return (dayA[method](dayB) ? dayA : dayB).format('YYYY-MM-DD')
  })

  // 查找当前分组列表下匹配的分组
  const findMatchGroup = useMemoizedFn(
    (
      groupList: IGanttTaskGroupInfo[],
      field: string,
      value: string | undefined,
      customField: CustomField | undefined,
    ) => {
      let matchedGroup: IGanttTaskGroupInfo | undefined

      if (!groupList?.length) {
        return
      }

      const customOptionsMap = customField?.options?.reduce((acc, cur) => {
        acc[cur.value] = cur.name
        return acc
      }, {} as Record<string, string>)

      for (let i = 0; i < groupList.length; i++) {
        const g = groupList[i]

        if (g?.isGroupUnknown && customField?.fieldId && !customOptionsMap?.[value]) {
          matchedGroup = g
          break
        }

        // 匹配 "无" 分组
        // 优先级、开始时间、结束时间 都用 0 表示空值；自定义字段都是字符串，为空也是 falsy 的值，可以在这里统一归类到无
        // 完成态需要特殊处理，0 表示未完成，不能归类到 无
        if (!value && g.isGroupUnknown && ![EnumField.finished].includes(field as EnumField)) {
          matchedGroup = g
          break
        }

        // 匹配 时间 类型分组
        // 时间分组需要特殊处理，忽略时分，不能直接比较 value
        // TODO: 做了太多的 dayjs 实例化，可能对性能有影响，不过还可以接受
        if (g.groupType === 'date') {
          const groupDate = dayjs(g.groupValue).format('YYYY-MM-DD')
          const valueDate = value != null ? dayjs(value).format('YYYY-MM-DD') : undefined

          if (groupDate === valueDate) {
            matchedGroup = g
            break
          }
        }

        // 正常匹配
        if (g.isGroup && g.groupValue === value) {
          matchedGroup = g
          break
        }
      }

      return matchedGroup
    },
  )

  // 格式化每个分组的的名称
  const normaliGroupName = useMemoizedFn(
    (field: string, value: any, record: IGanttTaskGroupInfo | IGanttTaskInfo, customField?: CustomField) => {
      switch (field) {
        case EnumField.finished: {
          return `${FieldLabelMap[EnumField.finished]?.[value]}`
        }
        case EnumField.priority: {
          return `${FieldLabelMap[EnumField.priority]?.[value]}`
        }
        case EnumField.deadline: {
          return record.$deadline
        }
        default: {
          if (customField) {
            switch (customField.type) {
              case FieldTypeEnum.datetime: {
                return dayjs(value).format('YYYY-MM-DD')
              }
              case FieldTypeEnum.option:
              case FieldTypeEnum.multiOption: {
                return customField?.options?.find(opt => opt.value === value)?.name
              }
              default: {
                return `${value}`
              }
            }
          }
          return `${value}`
        }
      }
    },
  )

  const normalizeGroupType = useMemoizedFn(
    (field: string, group: IGanttTaskGroupInfo | undefined, customField: CustomField | undefined) => {
      if (!group) {
        return group
      }

      if (
        [EnumField.startTime, EnumField.deadline].includes(field as any) ||
        customField?.type === FieldTypeEnum.datetime
      ) {
        group.groupType = 'date'
      }

      return group
    },
  )

  const normalizeGroupOrder = useMemoizedFn((field: string, value: any, record: IGanttTaskGroupInfo | undefined) => {
    if (!record) {
      return undefined
    }

    // 优先级分组按照排序
    if (field === 'priority') {
      record.groupOrder = value === 0 ? 0 : value
      return record
    }

    record.groupOrder = 0
  })

  /**
   * 后置处理分组逻辑：由于部分逻辑无法前置处理，只能在后置处理中再遍历一遍
   * 1. 过滤空的分组
   * 2. 初始化分组默认展开逻辑
   * 3. 分组排序
   */
  const postProcessGroups = useMemoizedFn((groups: IGanttTaskGroupInfo[], expandNodesSnapshot: (string | number)[]) => {
    if (!groups?.length) {
      return groups
    }

    return groups
      .map((g, index) => {
        const group = g as IGanttTaskGroupInfo
        group.children = postProcessGroups(g.children as IGanttTaskGroupInfo[], expandNodesSnapshot)

        const children = group.children as IGanttTaskGroupInfo[]

        if (group.isGroup && !groupOrderRef.current[group.taskId]) {
          groupOrderRef.current[group.taskId] = group.groupOrder || index
        }

        // 2级分组，采用类似 LL(1) 的策略向后再看一个，如果需要支持2级或者以上的需要重写
        if (!expandNodesSnapshot.length && group.isGroup && children?.[0]?.isGroup) {
          group.hierarchyState = 'expand'
          expandNodeSet.add(group.taskId)
        }

        if (canCreateTask && children?.length && !children?.[0]?.isGroup && !group.isParent) {
          children.push({
            taskId: AddMenuId,
            isGroup: false,
            children: [],
            finishedCount: 0,
            totalCount: 0,
            groupOrder: Number.MAX_SAFE_INTEGER,
            type: 'new_task',
            _rowType: TaskTableRowType.addBtn,
            id: TaskTableRowTypeAdd,
          })
        }

        return g
      })
      .filter(g => !(g.isGroup && !g.children?.length))
      .sort((a, b) => {
        const aGroupOrder = groupOrderRef.current[a.taskId] || a.groupOrder
        const bGroupOrder = groupOrderRef.current[b.taskId] || b.groupOrder
        return aGroupOrder - bGroupOrder
      })
  })

  // 前端分组核心逻辑
  const groupData = useMemo(() => {
    const t0 = performance.now()

    // 不是分组状态下，在列表最后添加创建行
    if (!memoGroups?.length) {
      const newList = dataList.map(r => {
        normalizeExpandState(r)
        return normalizeTimestamp(r)
      })
      const last = newList[newList.length - 1]
      if (canCreateTask && last?.tastId !== AddMenuId) {
        newList.push({
          taskId: AddMenuId,
          isGroup: false,
          children: [],
          finishedCount: 0,
          totalCount: 0,
          groupOrder: Number.MAX_SAFE_INTEGER,
          type: 'new_task',
          _rowType: TaskTableRowType.addBtn,
          id: TaskTableRowTypeAdd,
        })
      }
      return newList
    }

    const initialNoneGroup = normalizeExpandState(genUnknowGroup())!
    initialNoneGroup.groupLevel = 0
    const childNoneGroup = normalizeExpandState(genUnknowGroup(initialNoneGroup.title))!
    childNoneGroup.groupLevel = 1
    initialNoneGroup.children.push(childNoneGroup)

    const ret = (dataList as IGanttTaskInfo[]).reduce(
      (groupList, record) => {
        let lastMatchGroup: IGanttTaskGroupInfo | undefined
        let matchedGroup: IGanttTaskGroupInfo | undefined

        normalizeTimestamp(record)

        memoGroups.forEach((g, groupLevel) => {
          if (groupLevel > 0 && !lastMatchGroup) {
            return
          }

          const field = g as keyof IGanttTaskInfo
          const customField = customFields.find(f => f.fieldId === field)

          const fieldValue = customField ? record.customFieldValues?.values?.[field]?.value : (record[field] as string)
          const curGroupList =
            (groupLevel === 0 ? groupList : (lastMatchGroup?.children as IGanttTaskGroupInfo[])) ?? []

          matchedGroup = findMatchGroup(curGroupList, field, fieldValue, customField)

          if (matchedGroup) {
            if (!matchedGroup.children) {
              matchedGroup.children = []
            }

            if (lastMatchGroup) {
              // 优先匹配子级分组，从父级分组移除
              lastMatchGroup.children?.pop()
              lastMatchGroup.$startTime = getDate(lastMatchGroup.$startTime!, record.$startTime!, 'min')
              lastMatchGroup.$deadline = getDate(lastMatchGroup.$deadline!, record.$deadline!, 'max')
            }
            matchedGroup.children.push(record)

            matchedGroup.$startTime = getDate(matchedGroup.$startTime!, record.$startTime!, 'min')
            matchedGroup.$deadline = getDate(matchedGroup.$deadline!, record.$deadline!, 'max')
            matchedGroup.totalCount += 1
            matchedGroup.finishedCount += record.finished ? 1 : 0
          } else {
            matchedGroup = {
              taskId: genGroupTaskId(field, fieldValue, lastMatchGroup?.taskId),
              title: normaliGroupName(field, fieldValue, record, customField),
              groupField: field,
              groupValue: fieldValue,
              customField: customField,
              isGroup: true,
              $startTime: record.$startTime,
              $deadline: record.$deadline,
              finishedCount: record.finished ? 1 : 0,
              totalCount: 1,
              children: [],
              groupLevel,
            }

            // 每次新建分组时，同时创建二级“无”分组，确保二级分组在 record 之前，因为二级匹配时，会将上次匹配的分组 children.pop()
            const curChildNoneGroup = normalizeExpandState(genUnknowGroup(matchedGroup.title))!
            curChildNoneGroup.groupLevel = 1
            matchedGroup?.children.push(curChildNoneGroup, record)

            normalizeGroupType(field, matchedGroup, customField)
            normalizeGroupOrder(field, fieldValue, matchedGroup)
            normalizeExpandState(matchedGroup, lastMatchGroup)

            if (lastMatchGroup) {
              // 优先匹配子级分组，从父级分组移除
              lastMatchGroup.children?.pop()
            }

            matchedGroup && curGroupList.push(matchedGroup)
          }

          lastMatchGroup = matchedGroup
        })

        if (record.isParent) {
          normalizeExpandState(record)
        }

        return groupList
      },
      [initialNoneGroup] as IGanttTaskGroupInfo[],
    )

    console.log('>>> groupdata >>>', ret, 'takes: ', performance.now() - t0)

    const expandsSnapshot = Array.from(expandNodeSet)

    return postProcessGroups(ret, expandsSnapshot)
  }, [memoGroups, dataList, customFields, conditions])

  const onExpandChange = useMemoizedFn(
    (
      args: {
        col: number
        row: number
        hierarchyState: any
        originData?: IGanttTaskGroupInfo
      } | null,
    ) => {
      console.log('>>> onExpandChange >>>', args)

      const taskId = args?.originData?.taskId

      if (taskId != null) {
        if (args?.hierarchyState === 'expand') {
          expandNodeSet.add(taskId)
        } else {
          expandNodeSet.delete(taskId)
        }
      } else {
        // 坑点：收起的时候没有 originData，只能自己拿
        const collapseGroup = ganttInstanceRef.current?.taskListTableInstance?.getRecordByRowCol(
          args!.col,
          args!.row,
        ) as IGanttTaskGroupInfo | undefined

        if (collapseGroup?.taskId) {
          expandNodeSet.delete(collapseGroup.taskId)
        }
      }

      console.log('expandNodeSet', expandNodeSet)
    },
  )

  useEffect(() => {
    ganttInstanceRef.current?.taskListTableInstance?.on('tree_hierarchy_state_change', onExpandChange)

    return () => {
      ganttInstanceRef.current?.taskListTableInstance?.off('tree_hierarchy_state_change', onExpandChange)
    }
  }, [ganttInstanceRef.current])

  return {
    groupData,
  }
}
