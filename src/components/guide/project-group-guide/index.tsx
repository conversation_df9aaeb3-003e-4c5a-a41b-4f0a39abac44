import { FC, useEffect, useMemo, useState } from 'react';

import { Button } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
import { createRoot } from 'react-dom/client';
import { debounce } from 'lodash';

// https://popo.gsf.netease.com/project-group-guide/date-dark.png
// https://popo.gsf.netease.com/project-group-guide/date-light.png
// https://popo.gsf.netease.com/project-group-guide/timeline-dark.png
// https://popo.gsf.netease.com/project-group-guide/timeline-light.png

interface ProjectGroupGuideProps {
  user: {
    corlorMode: string;
    language: string;
  };
}

const ProjectGroupGuide: FC<ProjectGroupGuideProps> = ({ user }) => {
  const [currStep, setCurrStep] = useState(1);

  const { corlorMode, language } = user;

  const handleHideGuide = () => {
    window.localStorage.setItem('PROJECT-GROUP-GUIDE-DONE', '1');
  };

  const handleDone = () => {
    document.querySelector('#project-group-guide')?.remove();
    handleHideGuide();
  };

  useEffect(
    debounce(() => {
      const isShowGuide = !window.localStorage.getItem('PROJECT-GROUP-GUIDE-DONE');
      const getContent = (arrowLeft = 32) => {
        return (
          <>
            <div className="rock-popover rock-popover-dark rock-popover rock-popover-placement-bottomLeft">
              <div
                className="rock-popover-arrow"
                style={{
                  left: arrowLeft + 'px',
                  top: '0.06rem',
                  backgroundColor: 'var(--TaskGuideBg)',
                  borderColor: 'var(--aBlack8)',
                  borderTopLeftRadius: '0.02rem',
                  borderTop: '1px solid var(--popover-border-color, var(--border-color-2))',
                  borderLeft: '1px solid var(--popover-border-color, var(--border-color-2))',
                }}
              ></div>
              <div
                className="rock-popover-inner"
                style={{
                  width: '2.8rem',
                  background: 'linear-gradient(180deg, var(--TaskGuideBg) 0%, var(--bgTop) 100%)',
                  borderColor: 'var(--aBlack8)',
                  borderRadius: '0.12rem',
                  padding: '0.2rem',
                }}
              >
                <div className={s.wrapper}>
                  <div className={s.title}>{I18N.auto.newProjectGroup}</div>
                  <div className={s.des}>{I18N.auto.projectGroupGuideDesc}</div>
                  {/* <div className={s.img__container}>
              <img src={currStepData.img} alt="img" />
            </div> */}
                  <div className={s.btnWrapper}>
                    <Button className={s.btn} type="primary" onClick={handleDone}>
                      {I18N.auto.iGotIt}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            {/* <div className={s.mask}></div> */}
          </>
        );
      };
      const guideEl = document.querySelector('#project-group-guide');
      if (guideEl) {
        return;
      }
      if (isShowGuide) {
        const btnEl = document.querySelector('#add_project_or_group');
        if (!btnEl) {
          return;
        }

        btnEl.addEventListener('click', handleDone);

        const rect = btnEl.getBoundingClientRect();
        const { top, left, width } = rect;
        const divEl = document.createElement('div');
        divEl.style.position = 'fixed';
        divEl.style.top = `${top + 20}px`;
        divEl.style.left = `${left - 24}px`;
        divEl.style.zIndex = Number.MAX_SAFE_INTEGER + '';
        divEl.id = 'project-group-guide';
        const root = createRoot(divEl);
        const content = getContent(19.5 + width / 2);
        root.render(content);

        document.body?.appendChild(divEl);
      }

      return () => {
        // document.querySelector('#project-group-guide')?.remove();
        const btnEl = document.querySelector('#add_project_or_group');
        if (btnEl) {
          btnEl.removeEventListener('click', handleDone);
        }
      };
    }, 1000),
    [user]
  );

  return null;
};

export default ProjectGroupGuide;
