body {
  .rock-btn {
    //UX要求按钮都不加粗
    --font-weight-medium: 400;
    // --control-height-md: 28px;
    padding: 0 8px;
    & > i {
      margin-right: 4px;
    }

    &.rock-btn-sm {
      font-size: 13px;

      & > span + span {
        margin-left: 4px;
      }
    }
  }

  .rock-btn-checked-neutral {
    border-color: var(--aBlack16);
    color: var(--TextSecondary);
    &:not([disabled]):hover {
      border-color: var(--aBlack16);
      background-color: var(--aBlack4);
      color: var(--TextSecondary);
    }
    &:not([disabled]):active {
      border-color: var(--aBlack16);
      background-color: var(--aBlack8);
      color: var(--TextSecondary);
    }
    &.rock-btn-checked {
      border-color: var(--aBrand24);
      background: var(--aBrand12);
      color: var(--Brand600);
    }
    &[disabled] {
      color: var(--TextQuartus);
      border-color: var(--aBlack6);
    }
  }
  .rock-btn-text-subtle {
    color: var(--TextSecondary);
    &:not([disabled]):hover {
      background-color: var(--aBlack4);
      color: var(--TextSecondary);
    }
    &:not([disabled]):active {
      background-color: var(--aBlack6);
      color: var(--TextSecondary);
    }
  }

  .rock-btn-primary {
    background: var(--aBrand12);
    color: var(--Brand600);
    border: none;
    &[disabled] {
      background-color: var(--Brand500);
      opacity: 0.6;
      &:hover {
        background-color: var(--Brand500);
      }
    }
    &:not([disabled]):hover {
      background-color: var(--Brand600);
      color: var(--absWhite);
    }
    &:not([disabled]):active {
      background-color: var(--Brand700);
    }
    &.rock-btn-danger {
      background-color: var(--R600);
      &:not([disabled]):hover {
        background-color: var(--R500);
        color: var(--absWhite);
      }
    }
  }
}
