.sortable__subtask {
  position: relative;

  .icon {
    position: absolute;
    left: 0.06rem;
    cursor: pointer;
    color: var(--IconSecondary);
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  &:hover {
    .icon {
      opacity: 1;
    }
  }
}

.subtask__item {
  padding: 0 0.08rem 0 0.22rem;
  border-bottom: 1px solid var(--aBlack6);
  width: 100%;
  min-height: 36px;

  :global {
    .listTitleEditor {
      min-height: 34px;
      .editor-content-editable-wrap {
        min-height: 34px;
        top: 0;
      }
      .listTitleContent {
        font-size: 14px;
        line-height: 1.4;
        padding-top: 3px;
        .rock-input {
          font-size: 14px;
          &::placeholder {
            font-size: 14px;
          }
        }
      }
    }
  }
}
