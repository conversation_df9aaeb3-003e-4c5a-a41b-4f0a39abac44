import dayjs from 'dayjs';

import { Icon, IconBtn } from '@/components/basic';

import RemindStr from '../remind-item/remind-label';
import { TimeValue } from '../utils';
export type Props = {
  alarm?: TimeValue;
  className?: string;
  renderContent?: (alarm?: React.ReactNode) => React.ReactNode;
};

export default function RenderAlarm(props: Props) {
  const { alarm, className, renderContent } = props;

  const getContent = () => {
    const remindStr = (
      <RemindStr
        time={dayjs(alarm?.time)}
        timeFormat={alarm?.timeFormat}
        rrule={alarm?.rrule}
      ></RemindStr>
    );
    const content = renderContent ? renderContent(remindStr) : null;

    return (
      content || (
        <IconBtn
          placement="top"
          key={2}
          title={
            <RemindStr
              time={dayjs(alarm?.time)}
              timeFormat={alarm.timeFormat}
              rrule={alarm.rrule}
            ></RemindStr>
          }
          className={className}
          iconName="icon-quick_remind"
          fontSize={16}
        ></IconBtn>
      )
    );
  };

  return alarm?.time ? getContent() : null;
}
