// function getContentByPathLevel(url: string, pathLevel: number) {
//   let pattern = /^https?:\/\/[^/\s]+(\/.*)$/;
//   let match = url.match(pattern);

//   if (match) {
//     let path = match[1];
//     let pathLevels = path.split('/').filter(Boolean);

//     if (pathLevel <= pathLevels.length) {
//       return pathLevels[pathLevel - 1];
//     }
//   }

//   return null; // 或者返回适当的默认值
// }

export const isContainer = () => {
  return window.location.href.includes('/pc/'); //注意 所有/pc/路径下都是im内嵌界面
};
