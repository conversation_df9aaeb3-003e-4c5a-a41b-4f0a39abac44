import { store } from '@/models/store'
import { TaskInfo } from '@/types'
import { CustomField, FieldTypeEnum } from '@/types/custom-field'
import { EnumField } from '@/utils/fields'
import dayjs from 'dayjs'
import { get } from 'lodash'
import { GroupByField, GroupConfigMap } from './types'
import { GroupConfigFactory } from './GroupConfigFactory'
import { UserGroupManager } from './UserGroupManager'
import { getGroupConfigsByField } from './utils'

// 分组字段处理器
export class GroupFieldProcessor {
  static customFieldsMap: Record<string, CustomField> | null = null

  static getCustomFieldsMap() {
    const customFields = store.getState().viewSetting.customFields
    GroupFieldProcessor.customFieldsMap =
      customFields?.reduce((acc, field) => {
        acc[field.fieldId] = field
        return acc
      }, {} as Record<string, CustomField>) || {}

    return GroupFieldProcessor.customFieldsMap
  }

  static isUserField(groupBy: GroupByField): boolean {
    const { fieldName, customFieldId } = groupBy

    if (fieldName === EnumField.assignee) {
      return true
    }

    if (customFieldId) {
      const customField = this.getCustomFieldsMap()[customFieldId]
      return customField?.type === FieldTypeEnum.user
    }

    return false
  }

  static getFieldKey(groupBy: GroupByField): string {
    const { fieldName, customFieldId } = groupBy

    if (fieldName === EnumField.assignee) {
      return 'assigneeUids'
    }

    if (customFieldId) {
      return `customFieldValues.values.${customFieldId}.value`
    }

    return fieldName || ''
  }

  static getStaticConfigs(groupBy: GroupByField, groupLevel: number): GroupConfigMap {
    const { fieldName, customFieldId } = groupBy

    console.log('getStaticConfigs', groupBy, groupLevel)

    // 处理自定义字段
    if (customFieldId) {
      const customField = this.getCustomFieldsMap()[customFieldId]

      if (customField?.type === FieldTypeEnum.datetime) {
        return GroupConfigFactory.createTimeConfigs(dayjs(), customFieldId, groupLevel)
      }

      if (customField?.type === FieldTypeEnum.option) {
        return GroupConfigFactory.createOptionConfigs(customField, groupLevel)
      }

      // 用户类型字段返回空配置，在循环中动态生成
      if (customField?.type === FieldTypeEnum.user) {
        return {}
      }
    }

    // 处理内置字段
    return getGroupConfigsByField(fieldName, groupLevel)
  }

  static processUserField(task: TaskInfo, groupBy: GroupByField, groupLevel: number): void {
    const { fieldName, customFieldId } = groupBy

    if (fieldName === EnumField.assignee) {
      UserGroupManager.updateUserGroup({
        groupBy: fieldName,
        userIds: task.assigneeUids,
        users: task.assignees,
        groupLevel,
      })
    } else if (customFieldId) {
      const customData = get(task, `customFieldValues.values.${customFieldId}`)
      UserGroupManager.updateUserGroup({
        groupBy: customFieldId,
        userIds: customData?.value,
        users: customData?.users,
        groupLevel,
      })
    }
  }
}
