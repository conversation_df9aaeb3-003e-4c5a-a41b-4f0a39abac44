import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { ReactNode, useMemo } from 'react';

import { Icon, IconBtn } from '@/components/basic';
import RemindStr from '@/components/basic/popo-convergence-date-picker/remind-item/remind-label';
import {
  FORMAT_DD_mm,
  getRepeatTypeByRuleStr,
  getTimePickerFormat,
  repeatOptions,
} from '@/components/basic/popo-date-picker/utils';
import { TimeValue } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  startTime?: number | Dayjs | Date;
  endTime?: number | Dayjs | Date;
  rrule?: string;
  type?: string;
  format?: string;
  onRemove?: () => void;
  renderLabel?: (dayjs: Dayjs) => ReactNode;
  alarm?: TimeValue;
  showLabelRRuleIcon?: boolean;
  showAlarmIcon?: boolean;
  showLabelDeleteIcon?: boolean;
};
export default function RenderTime(props: Props) {
  const {
    className,
    startTime,
    endTime,
    alarm,
    rrule,
    showLabelRRuleIcon,
    showLabelDeleteIcon,
    showAlarmIcon,
    format = FORMAT_DD_mm,
    onRemove,
    renderLabel,
    type,
  } = props;
  const showLabel = useMemo(() => {
    let hasHHmm = false;
    let _startTime = '';
    let _endTime = '';
    if (format === FORMAT_DD_mm) {
      hasHHmm = true;
    }
    if (startTime) {
      _startTime = getTimePickerFormat({
        time: dayjs(startTime),
        hasHHmm,
      });
    }
    if (endTime) {
      _endTime = getTimePickerFormat({
        time: dayjs(endTime),
        hasHHmm,
      });
    }
    if (_startTime && _endTime) {
      return `${_startTime} - ${_endTime}`;
    } else if (_startTime) {
      return _startTime;
    } else if (_endTime) {
      return _endTime;
    }
    return '';
  }, [startTime, endTime, format]);

  const repeatName = useMemo(() => {
    if (rrule) {
      const type = getRepeatTypeByRuleStr(rrule);
      const repeat = repeatOptions.filter((item) => item.value === type)[0];
      return repeat.name;
    }
    return '';
  }, [rrule]);

  return (
    <div className={classNames(s.labelTime, className, 'todo-base-label')}>
      <div className={s.labelTimeLeft}>
        {renderLabel
          ? type === 'start-end'
            ? startTime
              ? `${renderLabel(dayjs(startTime))}-${renderLabel(dayjs(endTime))}`
              : I18N.template(I18N.auto.end_2, { val1: renderLabel(dayjs(endTime)) })
            : renderLabel(dayjs(startTime))
          : showLabel}
        {rrule && showLabelRRuleIcon ? (
          <IconBtn
            placement="top"
            key={1}
            title={I18N.template(I18N.auto.repeat_2, {
              val1: repeatName,
            })}
            className={s.icon}
            iconName="icon-quick_round"
            fontSize={16}
          ></IconBtn>
        ) : null}
        {alarm?.time && showAlarmIcon ? (
          <IconBtn
            placement="top"
            key={2}
            title={
              <RemindStr
                time={dayjs(alarm?.time)}
                timeFormat={alarm.timeFormat}
                rrule={alarm.rrule}
              ></RemindStr>
            }
            className={s.icon}
            iconName="icon-quick_remind"
            fontSize={16}
          ></IconBtn>
        ) : null}
        {showLabelDeleteIcon ? (
          <Icon
            className={classNames(s.icon, s.iconDelete)}
            name="icon-close"
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
          ></Icon>
        ) : null}
      </div>
    </div>
  );
}
