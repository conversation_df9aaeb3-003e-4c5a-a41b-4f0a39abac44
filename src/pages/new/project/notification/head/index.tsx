import classNames from 'classnames';
import I18N from '@/utils/I18N';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { useParams } from 'umi';

import { Icon, IconProject, ToggleMenu } from '@/components/basic';
import Separate from '@/components/basic/separate';
import { Dispatch, RootState } from '@/models/store';
import { setStorage, StorageType } from '@/utils';
import Const from '@/utils/const';

import s from './index.less';

interface Props {
  className?: string;
}

const Head: React.FC<Props> = (props) => {
  const { className } = props;
  const params = useParams();
  const { id } = params as { id: string };
  const dispatch = useDispatch<Dispatch>();
  const { projectInfo } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo,
  }));

  useEffect(() => {
    setStorage(Const.RetryReloadPageTimes, 0, StorageType.local);
  }, []);
  return (
    <div className={classNames(s.head, className)}>
      <div className={s.headLeft}>
        <ToggleMenu></ToggleMenu>
        <div
          className={s.breadcrumb1}
          onClick={() => {
            dispatch.viewSetting.openNavigator({
              navigatorId: +id,
            });
          }}
        >
          <IconProject
            fontSize={24}
            name={projectInfo.icon}
            bgColor={projectInfo.iconColor}
            active
            className={s.projectIcon}
          ></IconProject>
          <div className={classNames(s.projectName, 'ml-8')}>{projectInfo.name}</div>
        </div>
        <Icon name="icon-sys_open" className={s.arrow}></Icon>
        <div className={s.breadcrumb2}>{I18N.auto.subscriptionNotification}</div>
      </div>
      <div className={s.headRight}>
        {/* <Button type="primary" className={classNames(s.btn, 'ml-12 mr-12')}>
           <Icon name="icon-kit_user_add" className="mr-4"></Icon>
           {I18N.auto.inviteMembers}
          </Button> */}
        <Separate />
      </div>
    </div>
  );
};
export default Head;
