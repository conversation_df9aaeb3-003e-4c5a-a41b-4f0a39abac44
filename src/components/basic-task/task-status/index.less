.taskStatusDropdown {
  min-width: 0;
  :global {
    .rock-dropdown-menu {
      min-width: 0;
      border: 1px solid var(--aBlack12);
      padding: 4px;
      .rock-dropdown-menu-item {
        padding: 8px;
        margin: 0 !important;
        span {
          line-height: 20px;
        }
      }
    }
  }
}

.menuItem {
  &:global(.rock-dropdown-menu-item) {
    color: var(--TextPrimary);
  }
}
