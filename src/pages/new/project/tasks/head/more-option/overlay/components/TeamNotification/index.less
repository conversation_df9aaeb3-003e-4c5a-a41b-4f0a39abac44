.team__noti {
  &--header {
    margin-bottom: 12px;
    font-size: 13px;
    line-height: 16px;
    color: var(--TextSecondary-ongrey);
  }

  .header__highlight {
    color: var(--Brand700);
    cursor: pointer;
    padding-left: 4px;
    padding-right: 4px;
  }

  .cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;

    .card {
      display: flex;
      flex-direction: column;
      gap: 8px;

      background-color: var(--N0);
      border: 1px solid var(--aBlack6);
      border-radius: 8px;
      padding: 12px;
      transition: box-shadow 0.2s;

      &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &__content {
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex: 1;
      }

      &__footer {
        .button {
          padding: 0;
          color: var(--TextTertiary);
        }
      }

      &__title {
        font-size: 14px;
        line-height: 1.5;
        color: var(--TextPrimary);
      }

      &__desc {
        font-size: 12px;
        line-height: 1.4;
        color: var(--TextTertiary);
      }

      .divider {
        margin: 0;
      }

      &:hover {
        box-shadow: 0px 2px 4px 0px #00000014, 0px 1px 2px 0px #0000000a;
        .card__footer {
          .button {
            color: var(--Brand600);

            &:hover {
              color: var(--Brand500);
            }

            &:active {
              color: var(--Brand700);
            }
          }
        }
      }
    }

    .noti__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--Brand500);
      width: 20px;
      height: 20px;
      border-radius: 4px;
      color: var(--N0);
    }
  }
}
.preview {
  width: 280px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;

  & > img {
    width: 100%;
    height: 100%;
  }
}
