.session__container {
  &.session__full {
    :global {
      .tanstack-table__wrapper {
        padding-right: 0;
        height: 100vh;
        overflow: scroll;

        .tanstack-table__thead {
          & > .tanstack-table__tr {
            &::before {
              display: none;
            }
          }
        }

        .expand__container {
          width: 16px;
          margin-right: 3px;
        }

        .resizer.isResizing {
          &::after {
            height: calc(100vh - 20px);
          }
        }

        &:has(.no__data) {
          height: 100vh;
          max-height: 100vh;
        }

        .no__data {
          height: calc(100vh - 114px);
        }
      }
    }
  }

  :global {
    .tanstack-table__wrapper,
    .timeline__view {
      height: calc(100vh - 60px);

      .resizer.isResizing {
        &::after {
          height: calc(100vh - 60px);
        }
      }
    }
    .line__wrapper {
      height: calc(100vh - 72px);
    }

    .option__add--task {
      margin-right: 8px;
    }

    .no__data {
      margin-top: 37px;
    }
  }
}

.tableView__wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.sticky__addline {
  position: sticky;
  z-index: 1;
  bottom: 0;
  left: 0;
  padding: 0 23px;
  border-top: 1px solid var(--aBlack6);
  background-color: var(--bgBottom);
}

.none__data--line {
  position: absolute;
  top: 41px;
  bottom: unset;
  width: 100%;
  border-top: none;
}

:global {
  .tobedone__root {
    overflow: unset;
    background-color: var(--bgBottom);
  }
}

.loading {
  position: absolute;
  inset: 0;
  z-index: 1000;
  background-color: var(--bgBottom);
}

// p2p 会话样式定制
.session__tasks {
  :global {
    .tanstack-table__thead {
      & > .tanstack-table__tr {
        & > th:first-child {
          padding-left: 48px;
        }
      }
    }
    .expand__container {
      width: 16px;
      margin-right: 3px;
    }
  }

  .sticky__addline {
    padding: 0 23px;
  }
}
