.dropdown {
  padding: 0;
  height: auto;

  :global {
    .rock-btn:hover {
      background-color: var(--aBlack6) !important;
      color: var(--IconPrimary) !important;
    }
  }

  &:global(.rock-dropdown-open .rock-btn) {
    background-color: var(--aBlack6);
    color: var(--IconPrimary);
  }
}

.menu {
  .icon {
    color: var(--IconPrimary);
  }

  :global {
    .rock-dropdown-menu-item {
      height: 34px;
      color: var(--TextPrimary);

      &:hover {
        background-color: var(--aBlack6);
      }
    }

    .rock-dropdown-menu-prefix-icon {
      margin-right: 4px;
    }
  }
}
