import React, { Suspense, useContext, useMemo, useRef, useState } from 'react'

import { Button, Dropdown } from '@/components/basic'
import { UserInfo } from '@/types'
import I18N from '@/utils/I18N'

import { OperateUserset } from '@babylon/popo-icons'

import MembersOverlay from '../MembersOverlay'
import { ConfigProvider } from '@bedrock/components'

export type Props = {
  className?: string
  list: UserInfo[]
  showPercent?: boolean
  count?: number // 不用list的长度, 是因为在列表页面最多只返回两条数据
  maxShowCount?: number
  avatarClassName?: string
  showCountIcon?: boolean
  renderCountIcon?: (v: number) => React.ReactNode
  finishPercentTip?: React.ReactNode
  onClick?: React.MouseEventHandler<HTMLDivElement>
  canShare?: boolean
}
export default function RenderMembers(props: Props) {
  const { count = 0, canShare } = props
  const peoplePickerOpenRef = useRef(false)
  const [open, setOpen] = useState(false)

  const toggleOpen = () => {
    if (peoplePickerOpenRef.current) {
      return
    }
    setOpen(!open)
  }

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext)
  const zIndex = useMemo(() => getGlobalZIndex(), [open])

  return (
    <Dropdown
      open={open}
      trigger="click"
      placement="bottomRight"
      zIndex={zIndex}
      onOpenChange={toggleOpen}
      overlay={
        <MembersOverlay
          onPeoplePickerOpenChange={open => {
            peoplePickerOpenRef.current = open
          }}
          canShare={canShare}
          onClose={toggleOpen}
        />
      }
    >
      <Button icon={<OperateUserset className="fs-16" />} type="checked-neutral">
        {/* {I18N.template(I18N.auto.members, { val1: count })} */}
        {I18N.auto.member}
      </Button>
    </Dropdown>
  )
}
