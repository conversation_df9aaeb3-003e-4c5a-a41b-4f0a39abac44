import classNames from 'classnames';
import { useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { useParams } from 'umi';

import { Button, Icon, IconProject, ToggleMenu } from '@/components/basic';
import Separate from '@/components/basic/separate';
import { InvitedMembers } from '@/components/basic-project/more-option';
import { Dispatch, RootState } from '@/models/store';
import { Permission } from '@/types';
import { setStorage, StorageType } from '@/utils';
import Const from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';

interface Props {
  className?: string;
  permissions?: Permission[];
}

const Head: React.FC<Props> = (props) => {
  const { className, permissions } = props;
  const params = useParams();
  const { id } = params as { id: string };
  const dispatch = useDispatch<Dispatch>();
  const { projectInfo } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo,
  }));
  const memoPermissions = useMemo(() => {
    const [CAN_ADD_MANAGER, CAN_ADD_EDITOR, CAN_ADD_VIEWER] = validatesPermission({
      permissions: permissions,
      key: [
        ProjectPermissionEnum.CAN_ADD_MANAGER,
        ProjectPermissionEnum.CAN_ADD_EDITOR,
        ProjectPermissionEnum.CAN_ADD_VIEWER,
      ],
    }) as boolean[];

    const CAN_EDIT_MEMBER = CAN_ADD_MANAGER || CAN_ADD_EDITOR || CAN_ADD_VIEWER;
    return {
      CAN_EDIT_MEMBER: CAN_EDIT_MEMBER,
    };
  }, [permissions]);
  const changeMembers = () => {
    dispatch.project.getMemberList(String(projectInfo.projectId));
  };
  useEffect(() => {
    setStorage(Const.RetryReloadPageTimes, 0, StorageType.local);
  }, []);
  return (
    <div className={classNames(s.head, className)}>
      <div className={s.headLeft}>
        <ToggleMenu></ToggleMenu>
        <div
          className={s.breadcrumb1}
          onClick={() => {
            // history.go(-1);
            dispatch.viewSetting.openNavigator({
              navigatorId: +id,
            });
          }}
        >
          <IconProject
            fontSize={24}
            name={projectInfo.icon}
            bgColor={projectInfo.iconColor}
            active
            className={s.projectIcon}
          ></IconProject>
          <div className={classNames(s.projectName, 'ml-8')}>{projectInfo.name}</div>
        </div>
        <Icon name="icon-sys_open" className={s.arrow}></Icon>
        <div className={s.breadcrumb2}>
          {I18N.auto.memberManagement}
          {/* <span>（{projectInfo.memberCount}）</span> */}
        </div>
      </div>
      <div className={s.headRight}>
        {memoPermissions.CAN_EDIT_MEMBER ? (
          <InvitedMembers onChange={changeMembers} projectId={projectInfo?.projectId}>
            <Button type="primary" className={classNames(s.btn, 'ml-12 mr-12')}>
              <Icon name="icon-kit_user_add" className="mr-4"></Icon>
              {I18N.auto.inviteMembers}
            </Button>
          </InvitedMembers>
        ) : null}
        <Separate />
      </div>
    </div>
  );
};
export default Head;
