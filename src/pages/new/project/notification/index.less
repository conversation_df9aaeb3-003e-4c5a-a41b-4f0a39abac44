.projectList {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.list {
  flex: 1;
  padding: 0 20px 8px 20px;
  .colum {
    height: 60px;
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .preview {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--Brand600);
    cursor: pointer;
  }

  :global {
    .todo-thead {
      .todo-th {
        border-top: none;
      }
    }
    .todo-table .todo-tr .todo-th,
    .todo-table .todo-tr .todo-td {
      border-right: none;
    }
    .todo-table .todo-tr .todo-th:nth-child(1),
    .todo-table .todo-tr .todo-td:nth-child(1) {
      padding-left: 10px;
    }
  }
}

.popoverBox {
  :global {
    .rock-popover-inner-content {
      padding: 0;
    }
  }
}
.previewPopover {
  display: flex;
  width: 280px;
  min-height: 120px;
  font-size: 0;
  .img {
    width: 280px;
  }
}
