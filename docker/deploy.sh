#!/bin/bash

NCC_DOCKER_HUB=ncr.nie.netease.com
NCC_DOCKER_REPO=$NCC_DOCKER_HUB/popo
NCC_DOCKER_IMG_NAME=popo-tobedone-fe
NCC_BUILD_TIMESTAMP=$(TZ=UTC-8 date "+%Y%m%d%H%M%S")

if [ $CI_COMMIT_REF_NAME == "test" ]
then
  NCC_ENV=test
  # UINFOAPI=http://42.186.122.253:8083
  # API=https://test-ncc.popo.netease.com
  # SERVER_NAME=test-todo.popo.netease.com
elif [ $CI_COMMIT_REF_NAME == "staging" ]
then
  NCC_ENV=staging
  # UINFOAPI=http://42.186.122.253:8083
  # API=https://staging-ncc.popo.netease.com
  # SERVER_NAME=staging-todo.popo.netease.com
elif [ $CI_COMMIT_REF_NAME == "production" ]
then
  NCC_ENV=prod
  # UINFOAPI=http://42.186.122.253:8083
  # API=https://ncc.popo.netease.com
  # SERVER_NAME=todo.popo.netease.com
else
  NCC_ENV=test
  # UINFOAPI=http://42.186.122.253:8083
  # API=http://10.204.60.103:8383/
  # SERVER_NAME=test-todo.popo.netease.com
fi

# sed -i "s|<UINFOAPI>|$UINFOAPI|g" ./docker/nginx.conf
# sed -i "s|<API>|$API|g" ./docker/nginx.conf
# sed -i "s|<SERVER_NAME>|$SERVER_NAME|g" ./docker/nginx.conf

# cat ./docker/nginx.conf

TAG="${NCC_ENV}-${NCC_BUILD_TIMESTAMP}"
image_version=${NCC_DOCKER_REPO}/${NCC_DOCKER_IMG_NAME}:${TAG}
echo $TAG > publish_version

cat publish_version

docker build -t $image_version .

# push
echo "push image $image_version"
docker push $image_version

echo "delete temp file..."
docker rmi $image_version
rm ./Dockerfile
