import { TaskInfo } from '@/types'
import { FinishedStatus } from '@/utils/fields'
import dayjs from 'dayjs'
import { GroupStats } from './types'
import { normalizeTimestamp } from './utils'

// 深度复制任务数据，确保每个分组中的任务都是独立的副本
function deepCopyTask(task: TaskInfo, groupKey: string): TaskInfo {
  const copySubtasks = (subtasks: TaskInfo[] | undefined): TaskInfo[] => {
    if (!subtasks) return []
    return subtasks.map(subTask => ({
      ...subTask,
      subtask: copySubtasks(subTask.subtask),
      // 为每个子任务也添加分组标识
      _groupKey: groupKey,
    }))
  }

  return {
    ...task,
    subtask: copySubtasks(task.subtask),
    // 保留原始任务的引用，用于后续操作
    _originalTask: task,
    // 添加分组标识，用于区分不同分组中的相同任务
    _groupKey: groupKey,
  }
}

// 分组任务收集器
export class TaskGroupCollector {
  groupMap = new Map<string, TaskInfo[]>()
  groupStats = new Map<string, GroupStats>()
  groupDuration = new Map<string, any>()

  getDate(a: string, b?: string | number, type?: 'min' | 'max') {
    if (!a || !b) {
      return a || b
    }

    const dayA = dayjs(a)
    const dayB = dayjs(b)
    const method = type === 'max' ? 'isAfter' : 'isBefore'

    return dayA[method](dayB) ? dayA : dayB
  }

  setDuration(groupKey: string, task: TaskInfo) {
    normalizeTimestamp(task)
    const duration = this.groupDuration.get(groupKey)
    this.groupDuration.set(groupKey, {
      $startTime: this.getDate(duration?.$startTime, task.$startTime, 'min'),
      $deadline: this.getDate(duration?.$deadline, task.$deadline, 'max'),
    })
  }

  addTask(groupKey: string, task: TaskInfo): void {
    // 添加任务到分组
    if (!this.groupMap.has(groupKey)) {
      this.groupMap.set(groupKey, [])
      this.groupStats.set(groupKey, { totalCount: 0, finishedCount: 0 })
    }

    // 为每个分组创建任务的独立副本，避免共享同一个对象引用
    // 这样可以防止在一个分组中添加编辑行时影响其他分组中的相同任务
    const taskCopy = deepCopyTask(task, groupKey)

    this.groupMap.get(groupKey)!.push(taskCopy)

    // 实时更新统计信息
    const stats = this.groupStats.get(groupKey)!
    stats.totalCount += 1
    if (task.finished === FinishedStatus.Finished) {
      stats.finishedCount += 1
    }
  }

  getGroups(): Map<string, TaskInfo[]> {
    return this.groupMap
  }

  getGroupStats(groupKey: string): GroupStats {
    return this.groupStats.get(groupKey) || { totalCount: 0, finishedCount: 0 }
  }

  getAllGroupStats(): Map<string, GroupStats> {
    return this.groupStats
  }

  getGroupDuration(groupKey: string): any {
    return this.groupDuration.get(groupKey) || {}
  }

  getAllGroupDuration(): Map<string, any> {
    return this.groupDuration
  }
}
