import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useRef, useState } from 'react';

import { TextArea, Tooltip } from '@/components/basic';
import { emojiServer2EmojiStr } from '@/components/basic-task/emoji/util';
import { text2Href } from '@/utils';

import { CompositionTextArea, Variant } from '../input';
import s from './index.less';
import { getSelectionRange } from './utils';
import { ActionType } from '@/types';

export type Props = {
  contentClassName?: string;
  className?: string;
  focusedClassName?: string;

  placeholder?: string;
  maxLength?: number;
  enterCombination?: ('ctrlKey' | 'altKey' | 'metaKey' | 'shiftKey')[]; //组合键 才执行文本change
  id?: string;
  allowedEmpty?: boolean; // 允许编辑返回空字符串
  editding?: boolean;
  onEditding?: (v: boolean) => void; // onFocus和onBlur 可以实现
  disableSelect?: boolean;
  onFocus?: React.FocusEventHandler<HTMLTextAreaElement>;
  onBlur?: React.FocusEventHandler<HTMLTextAreaElement>;
  disabled?: boolean;
  editable?: boolean;
  normalTextArea?: boolean;
  value?: string;
  onChange?: (v: string, type: ActionType) => void;
  onTextChange?: (v: string) => void;
  onEditStateChange?: (v: boolean) => void;
  variant?: Variant;
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  // 是否将textArea切换成input
  switchInput?: boolean;
};

const ContentEditableEditor: React.FC<PropsWithChildren<Props>> = React.forwardRef(
  (props, refs) => {
    const {
      className,
      contentClassName,
      focusedClassName,
      value: propValue = '',
      onChange,
      onEditStateChange,
      onTextChange,
      placeholder,
      maxLength,
      enterCombination,
      id,
      allowedEmpty = true,
      editding: baseEditding = false,
      onEditding,
      onFocus,
      onBlur: propOnBlur,
      disabled,
      editable = true,
      normalTextArea = false,
      variant,
      onKeyDown,
      switchInput = false,
    } = props;
    const [contentState, setContentState] = useState<string>(propValue);
    const [value, setValue] = useState<string>(propValue);
    const [editding, setEditing] = useState<boolean>(baseEditding);
    const centerRef = useRef<boolean>(false);
    const textAreaRef = useRef<{ input: HTMLInputElement }>();
    const focusOffsetRef = useRef<{ start: number; end: number }>({
      start: propValue?.length || 0,
      end: propValue?.length || 0,
    });
    useEffect(() => {
      setEditing(baseEditding);
      onEditStateChange?.(baseEditding);
    }, [baseEditding]);

    useEffect(() => {
      const html = text2Href(propValue!);
      setContentState(html);
      setValue(propValue);
    }, [propValue]);
    const change = (v: string, type: ActionType) => {
      const text = v.trim();
      // if (!text && type === ActionType.enter) {
      //   return;
      // }
      onChange?.(text, type);
    };
    const changeEditding = (v: boolean) => {
      if (editable && !disabled) {
        setEditing(v);
        onEditStateChange?.(v);
        onEditding?.(v);
      }
    };
    const setCursorPosition = (ctrl: HTMLInputElement, obj: { start: number; end: number }) => {
      if (!ctrl?.focus) {
        return;
      }
      ctrl.focus?.({ preventScroll: true });
      ctrl.setSelectionRange(obj.start, obj.end);
      setTimeout(() => {
        ctrl.setSelectionRange(obj.start, obj.end);
      }, 0); // 在调用setCursorPosition的之前设置了值, onChange?.是异步的, 基于宏微任务 可以写个定时器将设置光标放在值变化之后
    };
    const onBlur: React.FocusEventHandler<HTMLTextAreaElement> = (e) => {
      propOnBlur?.(e);
      if (baseEditding) {
        return;
      }
      changeEditding(false);
      if (allowedEmpty || e.target.value) {
        change(e.target.value);
        setValue(e.target.value);
        const html = text2Href(propValue!);
        setContentState(html);
      } else {
        if (!e.target.value) {
          setValue(propValue);
        }
      }
    };
    useEffect(() => {
      if (editding) {
        setTimeout(() => {
          setCursorPosition(textAreaRef.current?.input, focusOffsetRef.current);
        }, 10);
      }
    }, [editding]);

    return (
      <div
        className={classNames(
          s.editor,
          { [s.normal]: normalTextArea, [s.unnormal]: !normalTextArea, [s.disabled]: disabled },
          className
        )}
        ref={refs}
      >
        {editding ? (
          <div
            className={classNames(
              s.editableWrap,
              'flex',
              'editor-content-editable-wrap',
              'editor-content-editable-wrap-editding'
            )}
          >
            <CompositionTextArea
              disabled={disabled}
              ref={textAreaRef}
              className={classNames(
                contentClassName,
                focusedClassName,
                'contentEditableEditor-edit',
                switchInput ? s.editorInput : s.editorTextArea
              )}
              element={switchInput ? 'input' : 'textarea'}
              rows={1}
              autoSize={true}
              value={value}
              onChange={(e) => {
                setValue(emojiServer2EmojiStr(e.target.value));
                onTextChange?.(e.target.value);
              }}
              placeholder={placeholder}
              maxLength={maxLength}
              showCount={normalTextArea}
              onKeyDown={(e) => {
                if (onKeyDown) {
                  onKeyDown(e);
                } else {
                  if (e.key === 'Enter') {
                    if (enterCombination) {
                      if (enterCombination.some((key) => e[key])) {
                        textAreaRef.current?.input.blur();
                        return;
                      } else {
                        return;
                      }
                    } else {
                      centerRef.current = true;
                    }
                    change(e.target.value, ActionType.enter);
                    textAreaRef.current?.input.blur();
                    e.preventDefault();
                  }
                }
              }}
              onFocus={onFocus}
              onBlur={(e) => {
                if (centerRef.current) {
                  centerRef.current = false;
                  return;
                }
                onBlur(e);
              }}
              variant={variant}
            />
          </div>
        ) : contentState ? (
          <div key={contentState} className={classNames('flex', 'editor-content-editable-wrap')}>
            <Tooltip title={propValue} onlyEllipsis>
              <div
                contentEditable={editable}
                className={classNames(
                  s.content,
                  contentClassName,
                  { [s.disabled]: disabled },
                  'todo-content-editable'
                )}
                id={id}
                onClick={(e) => {
                  const herf = e.target?.attributes?.href?.value;

                  if (herf) {
                    pp.openSysBrowser({ url: herf });
                    e.preventDefault();
                    e.stopPropagation();
                  } else {
                    const sel = window.getSelection();
                    if (sel) {
                      focusOffsetRef.current = getSelectionRange(sel);
                      changeEditding(true);
                    }
                  }
                  //e.stopPropagation();
                }}
                suppressContentEditableWarning={true}
                dangerouslySetInnerHTML={{ __html: contentState }}
              ></div>
            </Tooltip>
            <div contentEditable={false} className={s.barrier}></div>
          </div>
        ) : (
          <div
            className={classNames(s.placeholder, 'com-placeholder')}
            onClick={() => {
              focusOffsetRef.current = { start: 0, end: 0 };
              changeEditding(true);
            }}
          >
            {placeholder}
          </div>
        )}
      </div>
    );
  }
);

export default ContentEditableEditor;
