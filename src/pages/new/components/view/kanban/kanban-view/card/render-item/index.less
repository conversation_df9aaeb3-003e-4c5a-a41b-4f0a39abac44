.item {
  display: flex;
  align-items: center;
  margin-top: 4px;
  min-height: 28px;
  padding: 0 6px;

  .left {
    min-width: 0;
    position: relative;
    color: var(--IconTertiary);
    // padding-top: 1px;
    margin-right: 6px;
    flex-shrink: 0;
  }
  .right {
    width: 100%;
    word-break: break-word;
    min-width: 1px;
  }
  :global {
    .com-level-bg {
      height: 20px;
    }
    .todo-rendedr-peoples-avatar {
      width: 20px !important;
      height: 20px !important;
    }
  }
}
