.modal {
  :global {
    .rock-modal-body {
      height: 453px;
    }
  }
  .viewTabList {
    border: 1px solid var(--aBlack12);
    border-radius: 10px;
    margin-bottom: 12px;
    flex: 1;
    overflow: auto;
    max-height: 408px;
    scrollbar-color: initial;
    scrollbar-width: initial;
    // [role='button'] {
    //   opacity: 1 !important;
    // }
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: transparent;
    }

    &::-webkit-scrollbar {
      width: 4px;
      background-color: transparent;
    }

    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: var(--aBlack12);
      }
    }
  }
  .addViewBtn {
    display: flex;
    align-items: center;
    color: var(--TextSecondary);
    cursor: pointer;
    margin-bottom: 12px;
    i {
      margin-right: 4px;
    }
    .tip {
      font-size: 13px;
    }
  }
  .disabledBtn {
    color: var(--TextQuartus);
    i {
      color: var(--TextQuartus);
    }
  }
}
