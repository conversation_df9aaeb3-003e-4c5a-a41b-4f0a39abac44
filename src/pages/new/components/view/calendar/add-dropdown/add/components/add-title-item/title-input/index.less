.textAreaBox {
  display: flex;
  align-items: center;
  width: 100%;
  //min-height: 30px; //
  padding: 0;
  max-height: 100px;
  overflow-y: auto;
}
.textArea {
  &:global(.rock-input-wrapper) {
    width: 100%;
    padding: 0;
    border-color: transparent !important;
    border: 1px solid transparent !important;
    background-color: var(--bgTop) !important;
    &:hover,
    &-focused {
      border-radius: 0;
      background-color: transparent;
    }
  }
  &:global(.rock-input-wrapper-focused) {
    border-radius: 4px !important;
    box-sizing: border-box !important;
  }
  :global {
    .rock-input.rock-input-textarea {
      border-radius: 4px;
      width: 100%;
      padding: 0;
      margin: 0;
      min-height: 24px;
      line-height: 24px;
      border-radius: 0;
      border: none;
      font-size: 18px;
      font-weight: 600;
      color: var(--TextPrimary);
      &::placeholder {
        font-size: 18px;
        font-weight: 600;
        color: var(--TextTertiary);
      }
    }
  }
}
