import { NodeSpec } from 'prosemirror-model';

const rootClassName = 'prosemirror-popo-doc';

const PopoDocSpecs: NodeSpec = {
  popoDoc: {
    attrs: {
      src: {},
      text: {},
      docIcon: {}
    },

    group: 'inline',
    inline: true,
    selectable: true,

    toDOM: (node) => [
      'span',
      {
        class: rootClassName,
        'data-src': node.attrs.src,
        'data-text': node.attrs.text,
        'data-docIcon': node.attrs.docIcon,
      },
      [
        'span',
        { class: 'editor-popo-doc-span' },
        [
          'img',
          {
            src: node.attrs.docIcon,
            class: 'editor-popo-doc-img',
            alt: 'doc-icon'  // 建议添加alt属性
          }
        ],
        node.attrs.text || 'Untitled Document'  // 添加默认值
      ]
    ],

    parseDOM: [
      {
        tag: `span.${rootClassName}`,

        getAttrs: (dom: any) => {
          const src = dom.getAttribute('data-src');
          const text = dom.getAttribute('data-text');
          const docIcon = dom.getAttribute('data-docIcon');
          return { src, text, docIcon };
        },
      },
    ],
  },
};
export default PopoDocSpecs;
