.head {
  display: flex;
  align-items: center;
  padding: 18px 20px 18px 20px;
  flex-shrink: 0;
  min-width: 680px;
  .headLeft {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .breadcrumb1 {
      display: flex;
      cursor: pointer;
      // flex: 1;
      overflow: hidden;
    }
    .breadcrumb2 {
      font-weight: bold;
      font-size: 16px;
      flex-shrink: 0;
    }
    .arrow {
      margin-left: 8px;
      margin-right: 8px;
      color: var(--IconTertiary);
    }
    .projectIcon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
    }
    .projectName {
      position: relative;
      align-items: center;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: var(--TextSecondary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .headRight {
    display: flex;
    flex-shrink: 0;
    margin-left: 30px;
    .search {
      height: 28px;
      :global {
        .rock-input {
          padding-top: 3px;
          padding-bottom: 3px;
        }
      }
    }
    .members {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .more {
      width: 28px;
      height: 28px;
    }
  }
}
.btn {
  display: flex;
  align-items: center;
  height: 28px;
  padding-left: 10px;
  padding-right: 12px;
  margin-right: 12px;
  &:global(.rock-btn-primary) {
    background-color: var(--Brand500);
    color: var(--absWhite);
    &:hover {
      background-color: var(--Brand600);
    }
  }
}
