import { Button, Dropdown } from '@/components/basic';
import I18N from '@/utils/I18N';
import { SoftZd } from '@babylon/popo-icons';
import classNames from 'classnames';
import React from 'react';
import s from './index.less';
import FieldSetting from '@/pages/new/components/view/calendar/setting/field-set';
import Container from '@/pages/new/components/options/overlay-container';

const CalendarFieldsSetting = () => {
  return (
    <Dropdown
      className={s.dropdown}
      style={{ padding: 0 }}
      title={
        <Button
          className={classNames('mr-8', s.hidden, 'task-fields-setting')}
          type="text-subtle"
          icon={<SoftZd className="fs-16" />}
        >
          {I18N.auto.fieldConfiguration}
        </Button>
      }
      trigger="click"
      arrow={false}
      // defaultOpen={visible}
      // open={visible}
      // onOpenChange={(v) => {
      //   setVisible(v);
      // }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Container className={s.config__pane} title={I18N.auto.fieldConfiguration}>
          <FieldSetting />
        </Container>
      }
    />
  );
};

export default CalendarFieldsSetting;
