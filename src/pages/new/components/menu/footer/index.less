body[data-theme='dark'] {
  .footerBox {
    background-image: url('https://popo.res.netease.com/popo-assets/todo/project/survey-bg-dark.png');
  }
  .footerIcon {
    background-image: url('https://popo.res.netease.com/popo-assets/todo/project/survey-icon-dark.png');
  }
}

.footer {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  padding: 12px;

  .footerBox {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--aBlack6);
    border-radius: 8px;
    width: 100%;
    padding: 0 8px 0 12px;
    background-repeat: no-repeat;

    cursor: pointer;

    &.expand {
      height: 120px;
      width: 196px;
      background-position: center;
      background-size: cover;
    }

    &.fold {
      height: 36px;
      background-position: right;
      background-size: auto 100%;
    }
  }

  .fold__icon {
    position: absolute;
    top: 6px;
    right: 6px;
    font-size: 16px;
  }

  .footerIcon {
    width: 16px;
    height: 16px;
    background-image: url('https://popo.res.netease.com/popo-assets/todo/project/survey-icon.png');
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .footerText {
    font-weight: bold;
    font-size: 13px;
    color: var(--Brand600);
    white-space: nowrap;
  }
  .arrowIcon {
    color: var(--Brand700);
    font-size: 16px;
  }
  :global {
    .iconfont {
      font-size: 16px;
      color: var(--Brand700);
    }
  }
}

.popover {
  padding-left: 0px;
  :global {
    .rock-popover-inner {
      border-radius: 8px;
      //background: linear-gradient(180deg, rgba(47, 143, 255, 0.08) 0%, rgba(47, 143, 255, 0) 100%);
    }
    .rock-popover-inner-content {
      padding: 4px;
    }
    .rock-popover-content > .rock-popover-arrow {
      left: 0;
    }
  }
  .popoverContent {
    .popoverItem {
      display: flex;
      align-items: center;
      padding: 8px 40px 8px 8px;
      border-radius: 4px;
      line-height: 20px;
      font-size: 13px;
      cursor: pointer;
      color: var(--TextPrimary);
      .popoverIcon {
        font-size: 16px;
        margin-right: 8px;
      }
      &:hover {
        background-color: var(--aBlack6);
      }
    }
  }
}
