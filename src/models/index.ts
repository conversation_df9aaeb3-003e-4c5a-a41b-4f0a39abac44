import { Models } from '@rematch/core';

import { batch } from './batch';
import { detail } from './detail';
import { gantt } from './gantt';
import { imTodoList } from './im-todo-list';
import { kanban } from './kanban';
import { project } from './project';
import { record } from './record';
import { task } from './task';
import { todoCalendar } from './todo-calendar';
import { user } from './user';
import { viewSetting } from './viewSetting';
import { permissions } from './permission';

export interface RootModel extends Models<RootModel> {
  user: typeof user;
  task: typeof task;
  record: typeof record;
  viewSetting: typeof viewSetting;
  batch: typeof batch;
  detail: typeof detail;
  imTodoList: typeof imTodoList;
  todoCalendar: typeof todoCalendar;
  kanban: typeof kanban;
  project: typeof project;
  gantt: typeof gantt;
  permissions: typeof permissions;
}

export const models: RootModel = {
  user,
  task,
  imTodoList,
  record,
  viewSetting,
  batch,
  detail,
  todoCalendar,
  kanban,
  project,
  gantt,
  permissions,
} as RootModel;
