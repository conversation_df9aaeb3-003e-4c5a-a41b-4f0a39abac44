import { Member } from '@/types';
import { createContext } from 'react';

// 定义上下文类型
interface MembersContextType {
  onClose: () => void;
  onStepChange: (step: number) => void;
  selectedMembers: Member[];
  onMembersChange: (members: Member[]) => void;
  currentStep?: number;
  canShare?: boolean;
  updatePermissions?: () => void;
  onPeoplePickerOpenChange?: (open: boolean) => void;
}

// 创建默认值
const defaultContext: MembersContextType = {
  onClose: () => {},
  onStepChange: () => {},
  selectedMembers: [],
  onMembersChange: () => {},
};

// 创建上下文
export const MembersContext = createContext<MembersContextType>(defaultContext);
