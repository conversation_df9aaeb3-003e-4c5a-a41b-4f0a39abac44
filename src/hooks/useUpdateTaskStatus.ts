import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { apiTaskFinishPost, apiTaskRebuildPost } from '@/api';

export default function useUpdateTaskStatus(props: {
  isFinished: boolean;
  onChange?: (v: boolean) => void;
}) {
  const { isFinished, onChange } = props;
  const [finished, setFinished] = useState<boolean>(isFinished);

  useEffect(() => {
    setFinished(isFinished);
  }, [isFinished]);

  const todoFinish = useCallback(
    (taskId: number) => {
      apiTaskFinishPost({ taskId: taskId })
        .then(() => {
          onChange?.(true);
          setFinished(true);
        })
        .catch(() => {
          setFinished(false); //完成失败的话 就重置回待完成状态
        });
    },
    [onChange]
  );

  const todoRebuild = useCallback(
    (taskId: number) => {
      apiTaskRebuildPost({ taskId: taskId })
        .then(() => {
          // 这里的取消待办是重建待办
          //Message.success(I18N.auto.cancelToDo);
          onChange?.(false);
          setFinished(false);
        })
        .catch(() => {
          setFinished(true); //重建失败的话 就重置回完成状态
        });
    },
    [onChange]
  );

  const onClick = useMemo(() => {
    // 我是指派人，也是被指派人 属于个人待办 重建待办
    // 如果我是被指派人 不是指派人 重建待办
    // 如果我是指派人 不是被指派人 先取消验收 再重建待办
    // 我是指派人，也是被指派人 属于个人待办 完成 + 验收，直接使用完成接口(服务端判断合并两个状态)
    // 如果我是被指派人 不是指派人 用完成接口
    // 如果我是指派人 不是被指派人 用验收接口
    return debounce((taskId, isFinished) => {
      if (isFinished) {
        todoRebuild(taskId);
      } else {
        todoFinish(taskId);
      }
    }, 200);
  }, [todoFinish, todoRebuild]);
  return {
    onClick,
    finished,
    setFinished,
  };
}
