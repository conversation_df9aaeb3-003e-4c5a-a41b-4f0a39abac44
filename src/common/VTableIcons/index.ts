import { CorlorMode } from '@/utils/platform';

/** 展开、收起所有分组 */
// https://popo.gsf.netease.com/fold-all/dark-expand.png
// https://popo.gsf.netease.com/fold-all/dark-fold.png
// https://popo.gsf.netease.com/fold-all/light-expand.png
// https://popo.gsf.netease.com/fold-all/light-fold.png

/**
 * 从CDN获取图片
 * @param module 图片模块，fold-all\task-status\task-fold ...
 * @param theme 主题色
 * @param name 图片名字
 * @returns
 */
export function getVTableIcons(module: string, theme = CorlorMode.light, name = '') {
  if (name) {
    name = `-${name}`;
  }
  return `https://popo.gsf.netease.com/${module}/${theme}${name}.png`;
}
