import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Image } from '@/components/basic';
import { filepickerFop } from '@/utils/const';

const peopleLight = require('@/assets/images/people.png');

import s from './index.less';

export type Props = {
  className?: string;
  avatarUrl?: string;
  name?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
};
const AvatarPeople: React.FC<PropsWithChildren<Props>> = (props) => {
  const { className, avatarUrl, onClick } = props;
  return (
    <Image
      onClick={onClick}
      className={classNames(s.avatar, className)}
      //src={avatarUrl!}
      src={`${avatarUrl!}${filepickerFop}`}
      errorSrc={peopleLight}
    ></Image>
  );
};

export default AvatarPeople;
