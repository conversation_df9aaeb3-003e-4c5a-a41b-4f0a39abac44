import classNames from 'classnames';
import { useEffect, useState } from 'react';

import s from './index.less';
interface Props {
  /**
   * img地址
   */
  src: string;
  /**
   * 默认图片地址 如果是数组,第一个表示的是light模式,第二个表示暗黑模式
   */
  errorSrc: string;
  className?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}

const Image: React.FC<Props> = (props) => {
  const { src, errorSrc, className, onClick } = props;
  const [url, setUrl] = useState<string>(src);
  useEffect(() => {
    setUrl(src);
  }, [src]);
  return (
    <div className={classNames(s.imgBox, className)} onClick={onClick}>
      <img
        className={classNames(s.img)}
        onError={(e) => {
          setUrl(errorSrc);
        }}
        src={url}
      ></img>
    </div>
  );
};

export default Image;
