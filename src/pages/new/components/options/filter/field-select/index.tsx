import { Select } from '@/components/basic';
import { IField } from '@/utils/fields';

import s from './index.less';

interface TabsProps {
  value: string;
  data: IField[];
  onChange?: (v: string, f: IField) => void;
}

const FieldSelect: React.FC<TabsProps> = (props) => {
  const { data, value, onChange } = props;
  return (
    <Select
      value={value}
      onChange={(v, params) => onChange?.(v as string, params!.option! as any)}
      options={data}
      zIndex={999999}
    />
  );
};
export default FieldSelect;
