import { Button, Icon, IconBtn } from '@/components/basic';
import { TaskInfo } from '@/types';
import { SubTaskExecutor } from '@/components/CreateTaskLine/components/index';
import I18N from '@/utils/I18N';
import s from './index.less'

interface INewTaskExecutorProps {
  taskInfo: TaskInfo;
  onChange?: (value: any) => void;
}
const NewTaskExecutor: React.FC<INewTaskExecutorProps> = ({
  taskInfo,
  onChange,
}) => {

  const emptyNode = () => {
    return (
      <Button
        size="xSmall"
        icon={<Icon name="icon-kit_user_add" />}
        aria-label="取消"
        className={s.cancel_btn}
      >
        {I18N.auto.participants}
      </Button>
    )
  }

  return (
    <div className={s.wrapper}>
      <SubTaskExecutor
        taskInfo={taskInfo}
        onChange={onChange}
        emptyNode={emptyNode}
        maxShowCount={3}
        size='default'
      />
    </div>
  )
};
export default NewTaskExecutor;
