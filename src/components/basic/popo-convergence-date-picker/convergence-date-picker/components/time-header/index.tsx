import { ImCloseSolid2 } from '@babylon/popo-icons';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

import { Input } from '@/components/basic';
import RepeatSelect from '@/components/basic/popo-date-picker/time';
import { TimeValueAndTimeNum } from '@/types';
import { FORMAT_DD } from '@/utils/const';

import s from './index.less';

export enum TimeInputType {
  start,
  end,
}

const getDateByInput = (value: string) => {
  // 使用正则表达式提取文本中的所有数字
  const extractedNumbers = value.match(/\d+/g) || [];
  const combinedNumbers = extractedNumbers.join('').substring(0, 8); // 将提取的数字拼接在一起
  const match = combinedNumbers.match(/\b(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\b/);
  // 如果匹配到了年月日格式的字符串
  if (match) {
    const matchedDate = `${match[1]}/${match[2]}/${match[3]}`; // 构建日期字符串
    return matchedDate;
  }
  return undefined;
};

interface PickerDateSelectHeaderProps {
  value: TimeValueAndTimeNum;
  showTime?: boolean;
  onChange: (value: TimeValueAndTimeNum) => void;
  onClick?: () => void;
  active?: boolean;
  datePlaceholder?: string;
  timePlaceholder?: string;
  className?: string;
  prefix?: React.ReactNode;
}
//截止时间 "截止日期"  截止时间
export const PickerDateSelect = (props: PickerDateSelectHeaderProps) => {
  const {
    value,
    showTime,
    onChange,
    onClick,
    active,
    datePlaceholder,
    timePlaceholder,
    className,
    prefix,
  } = props;
  const { time, timeNum } = value;
  const [timeStr, setTimeStr] = useState<string>();

  useEffect(() => {
    if (time) {
      setTimeStr(dayjs(time).format(FORMAT_DD));
    } else {
      setTimeStr('');
    }
  }, [time]);

  const onInputChange = (str: string) => {
    const dateStr = getDateByInput(str);
    if (dateStr) {
      setTimeStr(dateStr);
      onChange?.({
        ...value,
        time: dateStr ? dayjs(dateStr).valueOf() : 0,
      });
    } else {
      setTimeStr('');
    }
  };

  const handleClear = () => {
    setTimeStr('');
    onChange?.({
      timeNum: undefined,
      time: 0,
    });
  };

  return (
    <div
      onClick={() => {
        onClick?.();
      }}
      className={classNames(
        s.newDateSelectWrapper,
        {
          [s.currentDateSelectWrapper]: active,
        },
        className
      )}
    >
      <span className={s.prefix}>{prefix}</span>
      <input
        className={s.input}
        value={timeStr}
        onChange={(e) => {
          const value = e.target.value;
          setTimeStr(value);
        }}
        placeholder={datePlaceholder}
        onKeyDown={(e) => {
          if (e.nativeEvent.isComposing) {
            return;
          }
          let target = e.target as HTMLInputElement;
          if (e.key === 'Enter') {
            onInputChange(target.value);
          }
        }}
        onBlur={(e) => {
          // onInputChange(e.target.value);
        }}
      ></input>
      {showTime ? (
        <>
          <div className={s.verticalSplitter}></div>
          <RepeatSelect
            placeholder={timePlaceholder}
            value={timeNum}
            onChange={(v) => {
              onChange?.({
                ...value,
                timeNum: v,
              });
            }}
          />
        </>
      ) : null}
      {(!!time || !!timeNum) && <ImCloseSolid2 onClick={handleClear} className={s.clear__icon} />}
    </div>
  );
};
