import { type Node, PluginKey, RockEditorPlugin, RockEditorView } from '@bedrock/editor';
import * as _ from 'lodash';
import { Decoration, DecorationSet } from 'prosemirror-view';
import { useContext, useState } from 'react';

import { apiTodoCommentAtSearchPost } from '@/api';
import { IconBtn } from '@/components/basic';
import { MentionExtraData } from '@/components/basic-task/comment/editor/utils';
import { UserInfo } from '@/types';
import { AtType } from '@/utils/const';
import I18N from '@/utils/I18N';

import { MentionPopoverRef, UserOption } from './at-popover';
import AtNodeSpecs from './AtNodeSpecs';
import s from './index.less';

export type LangType = 'en-US' | 'ja-JP' | 'ko-KR' | 'zh-CN' | 'fr-FR';

function triggerCharacter(char: string, { allowSpaces = false } = {}) {
  return ($position: any) => {
    // 优化正则表达式，确保每个@作为独立的触发字符
    const regexp = allowSpaces
      ? new RegExp(`${char}[^${char}\\s]*(?:\\s+[^${char}\\s]+)*`, 'g')
      : new RegExp(`${char}[^${char}\\s]*`, 'g');

    // 获取文本范围
    const textFrom = $position.before();
    const textTo = $position.pos;
    const text = $position.doc.textBetween(textFrom, textTo, '\0', '\0');

    // 存储所有匹配结果
    const matches: Array<{ index: number; text: string }> = [];
    let match: RegExpExecArray | null;

    // 找出所有匹配
    while ((match = regexp.exec(text))) {
      matches.push({
        index: match.index,
        text: match[0],
      });
    }

    // 按照位置排序,这样我们可以找到$position最接近的匹配
    for (const match of matches) {
      const from = match.index + $position.start();
      const to = from + match.text.length;

      // 如果当前位置在匹配范围内，返回该范围
      if (from < $position.pos && to >= $position.pos) {
        return { range: { from, to }, text: match.text };
      }
    }

    return null;
  };
}

function findNodeRange(doc: Node, target: Node): MentionRange | null {
  let result: MentionRange | null = null;
  doc.descendants((node: Node, pos: number) => {
    if (target === node) {
      result = { from: pos, to: pos + target.nodeSize };
      return false;
    }
  });
  return result;
}

export interface MentionPluginState {
  active: boolean;
  triggerPos: number | null;
  query: string;
}

const getElementByRange = (view: RockEditorView, range: any) => {
  const node = view.domAtPos(range.from)?.node;
  let parent: Element | undefined;
  // 部分用户查出来的节点竟然是文本节点，非常奇怪，先兼容性避免报错
  if (node.nodeType === node.TEXT_NODE) {
    parent = node.parentNode as Element;
  } else {
    parent = node as Element | undefined;
  }
  // return parent?.querySelector?.(`.${decorateClassName}`)
  return parent;
};

const At: React.FC = (props) => {
  const { onClick, editorView, editorState } = props;
  const [list, setList] = useState<UserInfo[]>([]);
  const [state, setState] = useState<MentionPluginState>({
    active: false,
    triggerPos: null,
    query: '',
  });

  // 处理@按钮点击事件
  const handleAtClick = () => {
    if (!editorView) return;

    // 插入@符号
    const tr = editorView.state.tr
      .insertText('@')
      .setMeta('inputType', 'insertText') // 标记为用户主动输入
      .setMeta('userTriggered', true); // 添加用户主动输入标记
    editorView.dispatch(tr);

    // 设置光标位置
    const pos = editorView.state.selection.$from.pos;
    const range = { from: pos - 1, to: pos };
  };

  return (
    <div className={s.atIconWrapper}>
      <IconBtn
        title={I18N.auto.mention}
        iconName="icon-pc_comment_aite"
        onClick={handleAtClick}
        iconClassName={s.atIcon}
      />
    </div>
  );
};

export const mentionPluginKey = new PluginKey<MentionPluginState>('mention');

interface MentionRange {
  from: number;
  to: number;
}

// 更新状态接口，添加inputType字段
interface MentionState {
  active: boolean;
  range: Partial<MentionRange>;
  text: string | null;
  inputType: string | null; // 追踪输入类型
}

interface MentionContext {
  view: RockEditorView;
  range?: MentionState['range'];
  text?: MentionState['text'];
  event?: Event;
}

type MentionHook = (ctx: MentionContext) => any;

interface Base {
  createTime: string;
  createUser: string;
  modifyTime: string;
  modifyUser: string;
  isDeleted: number;
  tenantId: string;
  remark: string;
}

export interface AtUser extends Base {
  id: number;
  commentId: string;
  isAt: number; // 是否为at人员：0-非at人员，1-at人员
  isViewed: number; // 是否已查看：0-未查看，1-已查看
  processInstanceId: string;
  userCode: string;
  viewTime: string;
}

interface PluginOptions {
  type?: 'comment' | 'remark';
  mentionRef?: any;
}

function createAtPlugin(options: PluginOptions = {}) {
  const { mentionRef, type } = options;
  const createRenderDiv = (view: RockEditorView) => {
    // const root = view.root
    const root = view.dom.parentElement;
    const container = document.createElement('div');
    (root as HTMLElement).appendChild(container);
    return container;
  };

  const matcher = triggerCharacter('@');

  let activeRange: any = null;
  let initialFrom = 0;
  let initialBottom = 0;

  let popover: MentionPopoverRef | null = null;
  let destroyPopover: null | (() => void) = null;
  const currentAtNode: Node | null = null;
  const destroyPopConfirm: null | (() => void) = null;

  // @人选择弹窗
  const createPopover = (reference: Element, view: RockEditorView, text?: string) => {
    // 记录初始的from位置，用于替换时使用
    initialFrom = view.state.selection.from;
    const searchTextLength = text?.slice(1)?.length || 0; // 去掉@符号后的长度
    // const container = createRenderDiv(view);
    // const renderRoot = createRoot(container);
    const coords = view.coordsAtPos(initialFrom);
    const editorRect = view.dom.getBoundingClientRect();
    let offsetLeft = coords?.left - editorRect.left;
    const offsetTop = coords?.top - editorRect.top;
    // 防止弹窗超出页面边界
    offsetLeft =
      type === 'comment'
        ? offsetLeft > 200
          ? 200
          : offsetLeft
        : offsetLeft > 50
          ? 50
          : offsetLeft;
    mentionRef.current?.setPosition([offsetLeft, offsetTop]);
    let isMounted = true; // 用于判断组件是否已经卸载
    destroyPopover?.();
    destroyPopover = () => {
      // renderRoot.unmount();
      // container.parentNode?.removeChild(container);
      popover = null;
      mentionRef.current?.hide();
      destroyPopover = null;
      initialFrom = 0;
    };

    mentionRef.current?.show();
    mentionRef.current?.injectFn({
      change: (user: UserInfo, isFlowUser?: boolean) => {
        const { selection } = view.state;

        const node = view.state.schema.nodes.popoAt.create({
          uid: user.uid,
          name: user.name,
          avatarUrl: user.avatarUrl,
          attype: user.attype || AtType.user,
        });
        let prefix = null;
        const nodeBefore = selection.$head.nodeBefore;
        // 前面没有空格时添加一个空格
        if (nodeBefore?.isText ? nodeBefore.text && /\S@$/.test(nodeBefore.text) : nodeBefore) {
          prefix = view.state.schema.text(' ');
        }
        const suffix = view.state.schema.text(' ');
        const { from, to } = activeRange;
        // 使用保存的初始位置进行替换
        const tr = view.state.tr.replaceWith(initialFrom - 1 - searchTextLength, selection.to, [
          node,
          suffix,
        ]);
        view.dispatch(tr);
        view.focus();
      },
      hidden: () => {
        if (isMounted) {
          destroyPopover?.();
          initialFrom = 0;
        }
      },
    });

    return () => {
      isMounted = false;
      initialFrom = 0;
    };
  };

  const onEnter: MentionHook = (ctx) => {
    const { view, range, text } = ctx;
    activeRange = range;
    const reference = getElementByRange(view, range);
    if (reference) {
      createPopover(reference, view, text);
    }
  };

  const onChange: MentionHook = (ctx) => {
    const { view, range, text } = ctx;
    activeRange = range;
    if (mentionRef) {
      const reference = getElementByRange(view, range);
      if (reference) {
        mentionRef.current?.setKeyword(text ? text.trim().replace(/^@\s*/, '') : '');
      }
    }
  };

  const onExit: MentionHook = () => {
    activeRange = null;
    destroyPopover?.();
  };

  const onKeyDown: MentionHook = () => {
    return false;
  };

  const decorateClassName = 'ProseMirror-mention';

  const cancleAt = (view: RockEditorView) => {
    // 取消@，并删除二次确认框
    if (currentAtNode) {
      const tr = view.state.tr;
      const range = findNodeRange(view.state.doc, currentAtNode);
      // @ts-ignore
      range &&
        tr.replaceRangeWith(
          range.from,
          range.to,
          view.state.schema.text(`@${currentAtNode.attrs.label || ''}`)
        );
      view.dispatch(tr);
    }
    // destroyPopConfirm?.()
  };

  return new RockEditorPlugin({
    key: mentionPluginKey,
    getToolbarConfig: (plugins) => {
      return [...plugins, [(props) => At({ ...props, ...options })]];
    },
    nodeSpecs: AtNodeSpecs,
    state: {
      /**
       * Initialize the plugin's internal state.
       */
      init() {
        return {
          active: false,
          range: {},
          text: null,
          inputType: null, // 添加inputType字段来跟踪输入类型
        };
      },

      /**
       * Apply changes to the plugin state from a view transaction.
       */
      apply(tr, prev) {
        const { selection } = tr;
        const next = { ...prev };

        const prevInput = prev.inputType;
        // 捕获当前操作的类型
        const inputType = tr.getMeta('inputType');
        if (inputType) {
          next.inputType = inputType;
        }

        const allowBackspace = prevInput === 'insertText' && inputType === 'backspace';
        // 捕获用户主动触发的标记
        const userTriggered = tr.getMeta('userTriggered');
        if (userTriggered) {
          next.inputType = 'insertText';
        }

        // 这是为了输入@后还能继续输入文字进行搜索
        if (allowBackspace) {
          next.inputType = 'insertText';
        }

        // 判断删除的是不是@
        if (prevInput === 'insertText' && inputType === 'backspace') {
          const nodeBefore = selection.$head.nodeBefore;
          if (nodeBefore?.isText && nodeBefore.text && /\S@$/.test(nodeBefore.text)) {
            next.inputType = 'deleteAt';
          }
        }

        // We can only be suggesting if there is no selection
        if (selection.from === selection.to) {
          // Reset active state if we just left the previous suggestion range
          if (selection.from < prev.range.from || selection.from > prev.range.to) {
            next.active = false;
          }

          // Try to match against where our cursor currently is
          const $position = selection.$from;
          const match = matcher($position);

          // If we found a match, update the current state to show it
          if (match) {
            // 判断是否是用户直接输入@触发的
            const isDirectInput =
              next.inputType === 'insertText' || next.inputType === 'insertCompositionText';
            console.log('-----> next.inputType: ', next.inputType);
            // 只有在直接输入@时才激活弹窗
            if (isDirectInput) {
              next.active = true;
              next.range = match.range;
              next.text = match.text;
            } else {
              // 匹配到了@但不是通过直接输入触发的（例如粘贴），不激活弹窗
              next.active = false;
              next.range = match.range;
              next.text = match.text;
            }
          } else {
            next.active = false;
          }
        } else {
          next.active = false;
        }

        // Make sure to empty the range if suggestion is inactive
        if (!next.active) {
          next.range = {};
          next.text = null;
        }

        return next;
      },
    },
    props: {
      /**
       * Call the keydown hook if suggestion is active.
       */
      handleKeyDown(view, event) {
        // 二次确认弹窗存在时禁止回车
        if (event.key === 'Enter' && destroyPopConfirm) return true;

        const { active } = (this as any).getState(view.state);

        if (!active || !destroyPopover) return false;
        // 上下方向键时阻止默认行为
        if (['ArrowUp', 'ArrowDown', 'Enter'].includes(event.key)) return true;

        return onKeyDown({ view, event });
      },

      // 处理输入事件，标记用户直接输入@
      handleTextInput(view, from, to, text) {
        if (text === '@') {
          // 设置元数据表明这是用户直接键入的@
          const tr = view.state.tr
            .insertText(text, from, to)
            .setMeta('inputType', 'insertText')
            .setMeta('userTriggered', true)
            .setMeta('eventKey', text);

          view.dispatch(tr);
          return true;
        }
        return false;
      },

      // handlePaste(view) {
      //   console.log('-------------------------------');
      //   // 粘贴时设置一个特殊标记，表明这不是用户直接输入
      //   const tr = view.state.tr.setMeta('inputType', 'paste');
      //   view.dispatch(tr);
      //   console.log('tr: ', tr);
      //   console.log('view: ', view);

      //   // 粘贴时不会触发input事件，因此也要手动执行一次
      //   cancleAt(view);
      //   return false;
      // },

      handleDOMEvents: {
        input(view) {
          // 输入文字后取消at
          cancleAt(view);
          return false;
        },
        paste(view) {
          const tr = view.state.tr.setMeta('inputType', 'paste');
          view.dispatch(tr);
          return false;
        },
        keydown(view, event) {
          const tr = view.state.tr
          if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
            tr.setMeta('inputType', 'arrow');
          } else if (event.key === 'Backspace') {
            tr.setMeta('inputType', 'backspace');
          } else if (event.key === 'Escape') {
            tr.setMeta('inputType', 'Escape');
          }
          view.dispatch(tr);
          return false;
        },
        click(view, event) {
          const tr = view.state.tr.setMeta('inputType', 'click');
          view.dispatch(tr);
          return false;
        },
      },
    },
    view() {
      return {
        update: (view, prevState) => {
          const key = this.key as PluginKey<MentionState>;
          const prev = key.getState(prevState)!;
          const next = key.getState(view.state)!;

          // See how the state changed
          const moved = prev.active && next.active && prev.range.from !== next.range.from;
          // 只在用户直接输入@时触发，现在我们检查active状态和inputType
          const started = !prev.active && next.active && next.text?.includes('@');
          const stopped = prev.active && !next.active;
          const changed = !started && !stopped;

          // Trigger the hooks when necessary
          if (stopped || moved) onExit({ view, range: prev.range, text: prev.text });
          if (changed && !moved) onChange({ view, range: next.range, text: next.text });
          if (started || moved) onEnter({ view, range: next.range, text: next.text });
        },
      };
    },
    decorations(editorState) {
      const { active, range } = this.getState(editorState);

      if (!active) return null;

      return DecorationSet.create(editorState.doc, [
        Decoration.inline(range.from, range.to, {
          nodeName: 'span',
          class: decorateClassName,
        }),
      ]);
    },
  });
}

export default createAtPlugin;
