import { Dispatch, RootState } from '@/models/store'
import { ServerSourceTypeMapKey } from '@/utils'
import { useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useSearchParams } from 'umi'
import TableList from '@/pages/pc/list/table-list'
import { Button, Icon, IconBtn } from '@/components/basic'
import { RenderAddRow, RenderGroupRow } from '@/components/table/components'
import {
  RenderTaskNameAdd,
  RenderAssignor,
  RenderExecutor,
  RenderWatcher,
  RenderDeadline,
  RenderStartTime,
  RenderCreatTime,
  RenderProject,
  RenderLevel,
  RenderTaskName,
} from '@/pages/new/components/view/list/render'
import { ActionType, TodoInfo } from '@/types'
import {
  TaskTableRowType,
  OneAndMoreServerParamsType,
  OneAndMoreType,
  ViewAddSource,
  EnumListAddSource,
} from '@/utils/const'
import {
  attrTaskName,
  attrAssigner,
  attrAssignee,
  attrFollower,
  attrDeadline,
  attrStartTime,
  attrCreatTime,
  attrPriority,
  attrProject,
  combineDisplayFields,
  DisplayField,
  EnumField,
} from '@/utils/fields'
import I18N from '@/utils/I18N'
import { ColumnDef, Row } from '@tanstack/react-table'
import classNames from 'classnames'
import s from '@/pages/new/components/view/list/index.less'
import style from '../index.less'
import { DetailDrawerContainer } from '@/pages/new/components/page-tasks'
import LoadingPage from '@/components/loading-page'
import { stopClickWhenDropdownPending } from '@/pages/new/components/view/list/utils'
import { getIsSessionTasks } from '@/models/utils'

const SessionTasks = () => {
  const dispatch = useDispatch<Dispatch>()
  const [searchParams] = useSearchParams()
  const { displayFields, sessionId, navigatorId, dataList, loading } = useSelector((state: RootState) => {
    let currentView = state.viewSetting.currentViewTab
    return {
      displayFields: currentView.displays || [],
      dataList: state.imTodoList.dataList || [],
      sessionId: state.imTodoList.sessionId,
      loading: state.imTodoList.loading,
      navigatorId: state.viewSetting.navigatorId,
    }
  })

  useEffect(() => {
    let sessionId = searchParams.get('sessionId')
    let sessionType = searchParams.get('sessionType')
    dispatch.imTodoList.setSessionId(String(sessionId))
    dispatch.imTodoList.setSessionType(Number(sessionType) as ServerSourceTypeMapKey)
    // 请求数据
  }, [])

  const onAddBtnClick = (id: number | string, groupId?: string, tableRowData?: Row<TodoInfo>, params = {}) => {
    // 标注新建来源
    if (groupId) {
      ViewAddSource.list = EnumListAddSource.groupBottomCreate
    } else {
      ViewAddSource.list = EnumListAddSource.bottomCreate
    }

    const { original } = tableRowData || {}
    if (original?.parentId) {
      dispatch.imTodoList.addSubLine({
        taskInfo: original,
        tableRowData,
        isSubTask: false,
        addItemData: {
          placeholder: I18N.auto.subTaskPlaceholder_2,
        },
        ...params,
      })
    } else {
      // 在新增按钮前面追加一个编辑态
      dispatch.imTodoList.tableAddTaskItem({
        taskId: Number(id),
        isHeader: false,
        groupId,
        tableRowData,
        isSubtask: false,
        ...params,
      })
    }
  }

  const memoColumns = useMemo(() => {
    const taskNameColumn: ColumnDef<any> = {
      header: () => <div className={s.headTaskName}>{I18N.auto.taskName}</div>,
      accessorKey: attrTaskName.value,
      minSize: 320,
      enableResizing: true,
      cell: ({ row, getValue }) => {
        const original = row.original
        const canExpand = row.getCanExpand()
        const isGroup = original.groupBy

        const renderContent = () => {
          if (original._rowType === TaskTableRowType.addBtn) {
            return (
              <RenderAddRow
                itemData={original}
                onAddBtnClick={() => {
                  onAddBtnClick(original.taskId, original.groupId, row)
                }}
              />
            )
          }
          if (original._rowType === TaskTableRowType.add) {
            return (
              <div className="task-tr-add" style={{ width: '100%' }}>
                <RenderTaskNameAdd
                  tableData={dataList}
                  tableRowData={row}
                  groupId={original.groupId}
                  taskInfo={original}
                  onAddCreatingLine={(params = {}) =>
                    onAddBtnClick(original.taskId, original.groupId, row, {
                      actionType: ActionType.enter,
                      ...params,
                    })
                  }
                />
              </div>
            )
          }
          if (canExpand || isGroup) {
            if (!isGroup) {
              return (
                <RenderTaskName
                  key={original.title}
                  taskInfo={original}
                  tableRowData={row}
                  // visibleDetail={visibleDetail}
                />
              )
            }
            return (
              <RenderGroupRow
                groupInfo={original}
                // onCollapse={onCollapse}
                assignees={original.assignees}
              ></RenderGroupRow>
            )
          }
          return (
            <RenderTaskName
              key={original.title}
              taskInfo={original}
              tableRowData={row}
              // visibleDetail={visibleDetail}
            />
          )
        }

        return (
          <div
            className={`${s.task__name} flex-y-center`}
            onClickCapture={stopClickWhenDropdownPending}
            style={{ paddingLeft: `${row.depth * 26}px` }}
          >
            <div className={`${s.expand__container} expand__container`}>
              {canExpand && (
                <IconBtn
                  className={classNames(s.arrow, 'expanded__icon')}
                  onClick={row.getToggleExpandedHandler()}
                  fontSize={16}
                  iconName="icon-sys_Expand"
                />
              )}
            </div>

            {renderContent()}
          </div>
        )
      },
      meta: {
        // sortable: true,
      },
    }
    const assignerColumn = {
      header: () => I18N.auto.assignedBy,
      accessorKey: attrAssigner.value,
      align: 'left',
      size: 120,
      minSize: 100,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderAssignor taskInfo={original} />
      },
    }
    const assigneeColumn = {
      header: () => I18N.auto.assignedTo,
      accessorKey: attrAssignee.value,
      align: 'left',
      size: 120,
      minSize: 100,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return (
          <RenderExecutor
            oneAndMoreType={
              original.completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one
            }
            taskInfo={original}
          ></RenderExecutor>
        )
      },
    }
    const followerColumn = {
      header: () => I18N.auto.followPeople,
      accessorKey: attrFollower.value,
      align: 'left',
      size: 120,
      minSize: 120,
      cell: info => {
        const original = info.row.original
        return <RenderWatcher taskInfo={original}></RenderWatcher>
      },
    }
    const deadlineColumn = {
      header: () => I18N.auto.deadline_2,
      accessorKey: attrDeadline.value,
      align: 'left',
      size: 220,
      minSize: 130,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderDeadline taskInfo={original}></RenderDeadline>
      },
    }
    const startTimeColumn = {
      header: () => I18N.auto.startTime,
      accessorKey: attrStartTime.value,
      align: 'left',
      size: 180,
      minSize: 120,
      cell: info => {
        const original = info.row.original
        return <RenderStartTime taskInfo={original}></RenderStartTime>
      },
    }
    const creatTimeColumn = {
      header: () => I18N.auto.creationTime,
      accessorKey: attrCreatTime.value,
      align: 'left',
      size: 180,
      minSize: 120,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderCreatTime taskInfo={original}></RenderCreatTime>
      },
    }
    const priorityTimeColumn = {
      header: () => I18N.auto.priority,
      accessorKey: attrPriority.value,
      align: 'left',
      size: 120,
      minSize: 120,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderLevel taskInfo={original}></RenderLevel>
      },
    }

    const projectColumn = {
      header: () => I18N.auto.project,
      accessorKey: attrProject.value,
      align: 'left',
      size: 180,
      minSize: 120,
      meta: {
        // sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderProject taskInfo={original}></RenderProject>
      },
    }

    const columnList =
      combineDisplayFields(displayFields as DisplayField[], [], navigatorId)
        ?.filter(item => !!item.visible)
        .map(item => {
          if (item.fieldName === EnumField.assigner) {
            return assignerColumn
          } else if (item.fieldName === EnumField.assignee) {
            return assigneeColumn
          } else if (item.fieldName === EnumField.follower) {
            return followerColumn
          } else if (item.fieldName === EnumField.deadline) {
            return deadlineColumn
          } else if (item.fieldName === EnumField.startTime) {
            return startTimeColumn
          } else if (item.fieldName === EnumField.createTime) {
            return creatTimeColumn
          } else if (item.fieldName === EnumField.priority) {
            return priorityTimeColumn
          } else if (item.fieldName === EnumField.project) {
            return projectColumn
          }
          // else if (item.customFieldId) {
          //   const filedInfo = customFields.find((v) => v.fieldId === item.customFieldId);

          //   const customFieldColumn = {
          //     header: () => filedInfo?.name,
          //     accessorKey: filedInfo?.fieldId,
          //     align: 'left',
          //     size: 180,
          //     minSize: [FieldTypeEnum.text, FieldTypeEnum.number].includes(filedInfo?.type)
          //       ? 80
          //       : 120,
          //     meta: {
          //       className: s.columnbox,
          //       suffix: canEditCustomField ? (
          //         <div
          //           onClick={(e) => {
          //             e.stopPropagation();
          //           }}
          //           className="suffix"
          //         >
          //           <CustomFieldDropdown fieldId={filedInfo?.fieldId} name={filedInfo?.name}>
          //             <IconBtn iconName="icon-sys_open" iconClassName={s.customSetting}></IconBtn>
          //           </CustomFieldDropdown>
          //         </div>
          //       ) : null,
          //       sortable: filedInfo
          //         ? [FieldTypeEnum.text, FieldTypeEnum.datetime, FieldTypeEnum.number].includes(
          //             filedInfo?.type
          //           )
          //         : false,
          //     },

          //     cell: (info) => {
          //       const original = info.row.original;
          //       //根据类型匹配自定义组件 filedInfo?.type
          //       return filedInfo ? (
          //         <RenderCustomField filedInfo={filedInfo} taskInfo={original}></RenderCustomField>
          //       ) : null;
          //     },
          //   };
          //   return customFieldColumn;
          // }
          return undefined
        })
        .filter(item => !!item) || []
    columnList.unshift(taskNameColumn)
    return columnList
  }, [displayFields, navigatorId])

  const handleAdd = () => {
    dispatch.imTodoList.tableAddTaskItem({
      groupId: undefined,
      isHeader: true,
      defaultParams: {},
      isSubtask: true,
    })
  }

  const rows = window.tanstackTable?.getRowModel().rows
  let lastRow = null
  let length = rows?.length || 0
  // 获取最后一个非子任务
  while (!lastRow && length > 0) {
    const currRow = rows[length - 1]
    if (!currRow?.parentId) {
      lastRow = currRow
    }
    length--
  }

  const hasData = dataList.length > 0

  useEffect(() => {
    const tableWrapper = document.querySelector('.tanstack-table__wrapper') as HTMLDivElement
    if (tableWrapper) {
      // 下拉元素是否跟随单元格滚动，500为截止时间下拉的高度
      const isScroll = tableWrapper.scrollHeight > tableWrapper.clientHeight && tableWrapper.clientHeight > 500
      if (isScroll) {
        tableWrapper.style.position = 'relative'
      }
    }
  }, [dataList])

  const className = getIsSessionTasks() && searchParams.get('sessionType') === '1' ? style.session__tasks : ''

  return (
    <>
      <div className={`${style.session__container} ${style.session__full} ${className}`}>
        {sessionId ? (
          <div className={`${s.tableView} ${style.tableView__wrapper}`}>
            <TableList
              extra={
                <div
                  className={classNames(style.sticky__addline, 'sticky__addline', 'task-tr-add-btn', 'flex-y-center', {
                    [style['none__data--line']]: !hasData,
                  })}
                >
                  <RenderAddRow
                    itemData={lastRow?.original}
                    onAddBtnClick={() => {
                      dispatch.detail.closeDetail({})
                      onAddBtnClick(lastRow?.original.taskId, lastRow?.original.groupId, lastRow)
                    }}
                  />
                </div>
              }
              columns={memoColumns}
              className={style.table}
            />

            <DetailDrawerContainer />
          </div>
        ) : null}
      </div>
      {!hasData && loading && (
        <LoadingPage showHeader={false} showOptions={false} className={style.loading}></LoadingPage>
      )}
    </>
  )
}

export default SessionTasks
