import React, { PropsWithChildren } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useDispatch } from 'react-redux';

import { Dispatch } from '@/models/store';
import scout from '@/utils/scout';
import { EnumTrackeKey } from '@/utils/skyline';

export interface PageErrorBoundary {
  errorId: string; //'Page-Task-Render-Error'
  errorMsg: string; //'任务界面异常',
}

const PageErrorBoundary: React.FC<PropsWithChildren<PageErrorBoundary>> = (props) => {
  const { errorId, errorMsg, children } = props;
  const dispatch = useDispatch<Dispatch>();
  //进行5次重试
  return (
    <ErrorBoundary
      fallback={<div></div>}
      onError={() => {
        console.log(`${errorId}-${errorMsg}`);
        scout.captureError({
          name: errorId,
          //@i18n-ignore
          message: errorMsg,
          stack: '',
        });
        dispatch.user.trackingByView({ key: EnumTrackeKey.Error, msg: errorId });
        // pp.showToast({ title: I18N.auto.reloadingInProgress });
        // //进行2次重试
        // const time = getStorage(Const.RetryReloadPageTimes, StorageType.local) || 0;
        // if (time < 2) {
        //   window.location.reload();
        //   setStorage(Const.RetryReloadPageTimes, Number(time) + 1, StorageType.local);
        // }
        //异常之后3s重新加载数据
        // setTimeout(() => {
        //   window.location.reload();
        // }, 3000);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default PageErrorBoundary;
