import classNames from 'classnames';
import React, { PropsWithChildren, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Icon, POPODropdown, Switch } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import Fieldset from './field-set';
import s from './index.less';
export type Props = {
  className?: string;
};

const Setting: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [showSetting, setShowSetting] = useState<boolean>(false);
  const { showWeekend } = useSelector((state: RootState) => ({
    showWeekend: state.viewSetting.currentViewTab.showWeekend,
  }));
  const dispatch = useDispatch<Dispatch>();
  return (
    <POPODropdown
      title={children}
      className={s.sortDropdown}
      visible={visible}
      onVisible={(v) => {
        setVisible(v);
        if (v) {
          setShowSetting(false);
        }
      }}
    >
      {showSetting ? (
        <div>
          <div className={classNames(s.descTitle)}>
            <div
              className={s.back}
              onClick={() => {
                setShowSetting(false);
              }}
            >
              <Icon name="icon-pc_plane_day_before" className="mr-8"></Icon>
              {I18N.auto.return}
            </div>
          </div>
          <Fieldset></Fieldset>
        </div>
      ) : (
        <div className={s.list}>
          <div className={s.descTitle}>{I18N.auto.allocation}</div>
          <div className={s.item}>
            <div className={s.itemLeft}>
              <div className={s.fieldName}>{I18N.auto.showWeekends}</div>
            </div>
            <Switch
              value={showWeekend}
              className={s.switch}
              onChange={(v) => {
                //TODO 跟视图走
                dispatch.viewSetting.updateCurrentViewAndUpdateServer({
                  showWeekend: v,
                });
              }}
            ></Switch>
          </div>
          <div
            className={s.item}
            onClick={() => {
              setShowSetting(true);
            }}
          >
            <div className={s.itemLeft}>
              <div className={s.fieldName}>{I18N.auto.fieldConfiguration}</div>
            </div>
            <Icon className={s.icon} name="icon-triangleLineRight_1" fontSize={12}></Icon>
          </div>
        </div>
      )}
    </POPODropdown>
  );
};

export default Setting;
