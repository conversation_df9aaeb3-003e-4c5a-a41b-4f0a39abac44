.rangeTimePickerWrapper {
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 4px;
  border-radius: 4px;
  // cursor: pointer;
  &:hover {
    background-color: var(--aBlack6);
  }
  &.disabled {
    &:hover {
      background-color: transparent;
    }
  }
  .timeStr {
    display: flex;
    align-items: center;
    min-height: 32px;
    line-height: 20px;
    color: var(--TextPrimary);
  }
}

.rangeTimePickerWrapperNoDate {
  background-color: transparent;
  &:hover {
    background-color: transparent;
  }
}

.noDate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.noDateItem {
  background-color: var(--aBlack4);
  border-radius: 4px;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  color: var(--TextSecondary);
  display: inline-block;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--aBlack8);
  }
}
