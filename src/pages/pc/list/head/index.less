.head {
  flex-shrink: 0;
  font-size: 20px;
  color: var(--TextPrimary);
  .title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 18px 60px 18px 20px;
    margin-right: 20px;
    width: 100%;
    height: 60px;
    border-bottom: 1px solid var(--aBlack6);
    line-height: 55px;
    font-weight: 600;
    color: var(--TextPrimary);
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .options {
    padding: 14px 20px;
    height: 56px;
    display: flex;
    align-items: center;
    .add {
      color: var(--TextSecondary);
    }
  }
}
