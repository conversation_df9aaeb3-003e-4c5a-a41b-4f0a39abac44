import classNames from 'classnames';
import { PropsWithChildren, ReactNode } from 'react';

import { Placeholder } from '@/components/basic';

import LevelTag from '../level-tag';
import s from './index.less';
export type Props = {
  className?: string;
  value?: number;
  placeholder?: ReactNode;
};
const RenderLevel: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, className, placeholder } = props;

  return (
    <div className={classNames('todo-level-label', s.leveLabel, className)}>
      {!value ? (
        <Placeholder text={placeholder}></Placeholder>
      ) : (
        <>
          <LevelTag value={value!}></LevelTag>
        </>
      )}
    </div>
  );
};

export default RenderLevel;
