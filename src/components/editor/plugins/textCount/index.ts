import {
  RockEditorPlugin,
  Plugin<PERSON>ey,
  RockEditorView,
} from '@bedrock/editor';
import * as _ from 'lodash';

const MAX_COUNT = 5000;

export default function createTextCountPlugin(options = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('textCountPlugin'),
    priority: 8888,
    state: {
      init() {
        return 0;
      },
      apply(tr, count) {
        const newDoc = tr.doc;
        const textContent = newDoc.textContent;
        return textContent.length;
      }
    },
    props: {
      handleTextInput(view, from, to, text) {
        const currentCount = (this as any).getState(view.state);
        const newCount = currentCount + text.length
        if (newCount > MAX_COUNT) {
          return true
        }
        return false;
      },
      handlePaste(view: RockEditorView, event) {
        const clipBoardText = event.clipboardData?.getData('text/plain') || '';
        const currentCount = (this as any).getState(view.state);
        // 剩余可输入字符数
        const allowedLength = MAX_COUNT - currentCount;
        if (allowedLength <= 0) {
          return true;
        }

        if (clipBoardText.length > allowedLength) {
          const truncatedText = clipBoardText.slice(0, allowedLength); // 截断到允许长度
          const tr = view.state.tr.replaceSelectionWith(view.state.schema.text(truncatedText));
          view.dispatch(tr); // 手动插入截断后的内容
          event.preventDefault(); // 阻止默认粘贴
          return true;
        }
        return false
      },
    },
    view(editorView) {
      return {
        destroy() { },
      };
    },
  });
}
