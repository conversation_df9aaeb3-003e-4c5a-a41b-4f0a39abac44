import { PPTimeFormat } from '@/utils/const'

//通用字段
export interface FieldSchema {
  name: string
  value?: any
  type: FieldTypeEnum

  options?: SelectFieldOption[]
  multip?: boolean
  format?: FieldNumberFormatEnum | PPTimeFormat
}

//文本
export interface TextTypeField extends FieldSchema {
  value?: string
}
//单选
export interface SelectTypeField extends FieldSchema {
  value?: string
  options: SelectFieldOption[]
}

//多选
export interface MultipSelectTypeField extends FieldSchema {
  value?: string[]
  options: SelectFieldOption[] //选项
}
//人员
export interface UserTypeField extends FieldSchema {
  value?: string[]
  multip: boolean //是否多选
}

//时间
export interface DateTypeField extends FieldSchema {
  value?: number
  format: PPTimeFormat //展示时分
}

//数字
export interface NumberTypeField extends FieldSchema {
  value?: number
  format: FieldNumberFormatEnum //数字格式
}

export interface SelectFieldOption {
  name: string
  value: string
  color: string
  sortOrder?: number | string
}

export enum FieldTypeEnum {
  text = 'text',
  option = 'option',
  multiOption = 'multi_option',
  datetime = 'datetime',
  number = 'number',
  user = 'multi_user',
}

export const FieldOptionColor = {
  pibg_red: '#FF525D',
  pibg_orange: '#FF842C',
  pibg_yellow: '#FFC529',
  pibg_grass: '#24C446',
  pibg_cyan: '#00BDBD',
  pibg_blue: '#2F8FFF',
  pibg_purple: '#8E31E3',
  pibg_magenta: '#F5278A',
  pibg_neutral: '#999999',
} as Record<string, string>

export const FieldOptionColorSequence = [
  'pibg_blue',
  'pibg_red',
  'pibg_grass',
  'pibg_yellow',
  'pibg_cyan',
  'pibg_orange',
  'pibg_purple',
  'pibg_magenta',
  'pibg_neutral',
]

export enum FieldNumberFormatEnum {
  int = 'int',
  float1 = 'float-1',
  float2 = 'float-2',
  percent = 'percent',
  percent2 = 'percent-2',
}

/**加上了id和version的自定义字段 */
export type CustomField<T = FieldSchema> = T & { fieldId: any; fieldVersion: any }
