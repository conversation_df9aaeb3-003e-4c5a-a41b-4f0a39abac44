import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Divider } from '@/components/basic';
import PeopleItem from '@/components/basic/people-picker/components/people-item';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  name: string;
  avatarUrl: string;
};
const AssignerList: React.FC<PropsWithChildren<Props>> = (props) => {
  const { name, avatarUrl, className } = props;
  return (
    <div className={classNames(s.assignerList, className)}>
      <div className={classNames(s.groupTitle)}>{I18N.auto.assignedBy}</div>
      <Divider type="horizontal" className={s.listDivider} />
      <PeopleItem avatarUrl={avatarUrl} name={name}></PeopleItem>
    </div>
  );
};

export default AssignerList;
