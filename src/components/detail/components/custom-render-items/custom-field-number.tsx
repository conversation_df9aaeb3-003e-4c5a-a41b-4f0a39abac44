import { useState } from 'react';

import CustomFieldNumber from '@/components/basic-project/custom-field-number';
import { CustomField, FieldNumberFormatEnum } from '@/types/custom-field';
import I18N from '@/utils/I18N';

import s from './index.less';

type FieldNumberInputProps = {
  value?: number;
  onChange?: (v: any) => any;
  disabled?: boolean;
  className?: string;
  filedInfo?: CustomField;
  placeholder?: string;
};

export const FieldNumber = (props: FieldNumberInputProps) => {
  const { value, onChange, disabled, filedInfo, placeholder = I18N.auto.addTo } = props;
  const [inputNumber, setInputNumber] = useState(value);
  return (
    <CustomFieldNumber
      value={value}
      placeholder={placeholder}
      onChange={(v) => {
        setInputNumber(v as number);
      }}
      type={filedInfo?.format as FieldNumberFormatEnum}
      className={s.customNumber}
      disabled={disabled}
      onBlur={() => {
        onChange?.(inputNumber);
      }}
    />
  );
};
