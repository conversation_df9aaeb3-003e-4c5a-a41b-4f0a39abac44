const axios = require('axios');

const V2ToeknUrl = 'https://auth.nie.netease.com/api/v2/tokens';

// interface ACMProps {
//   acm_host: string;
//   acm_group: string;
//   acm_config_name: string;
//   auth_user: string;
//   auth_key: string;
//   project: string;
//   acm_namespace: string;
// }

// interface ACMNginxProps extends ACMProps {
//   graytag: string;
// }

async function getToken(authUser, authKey) {
  try {
    const response = await axios.post(
      V2ToeknUrl,
      {
        user: authUser,
        key: authKey,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    return undefined;
  }
}

/**
 * 根据acm的信息获取单个配置内容
 * @param option
 * @returns
 */
const getAcmNginxConfigContent = async (option) => {
  const { acm_host, acm_group, acm_config_name, auth_user, auth_key, project, acm_namespace } =
    option;
  const { token } = (await getToken(auth_user, auth_key)) || {};
  const url = `https://${acm_host}/api/v1/configs/${acm_group}:${acm_config_name}?_embed=content`;
  try {
    const response = await axios.get(url, {
      headers: {
        'X-Access-Token': token,
        'X-Auth-Project': project,
        'X-ACM-Namespace': acm_namespace,
      },
    });
    return response.data?.content;
  } catch (error) {
    return undefined;
  }
};

//``
const updateAcmNginxConfigContent = async (option) => {
  const {
    content,
    acm_host,
    acm_group,
    acm_config_name,
    auth_user,
    auth_key,
    project,
    acm_namespace,
  } = option;
  const { token } = (await getToken(auth_user, auth_key)) || {};
  const url = `https://${acm_host}/api/v1/configs/${acm_group}:${acm_config_name}:content`;
  try {
    const response = await axios.put(url, content, {
      headers: {
        'Content-Type': 'text/plain',
        'X-Access-Token': token,
        'X-Auth-Project': project,
        'X-ACM-Namespace': acm_namespace,
      },
    });
    return response.data;
  } catch (error) {
    return undefined;
  }
};

module.exports = { getAcmNginxConfigContent, updateAcmNginxConfigContent };
