.search:global(.rock-input-wrapper.rock-input-wrapper-inline) {
  width: 100%;
  border-radius: 6px;
  padding: 0 8px;
  // border: none;
}

.search {
  border-bottom: 1px solid var(--aBlack6);
  &:global(.rock-input-wrapper-no-border) {
    &::before {
      opacity: 0 !important;
    }
  }
  :global {
    .rock-input::placeholder {
      color: var(--TextTertiary) !important;
      font-size: 13px;
    }
    .rock-btn.rock-btn-translucent {
      background-color: var(--IconPrimary);
      &:hover {
        background-color: var(--IconPrimary);
      }
    }
    .rock-clear-btn.rock-btn.rock-btn-icon-only .rock-icon {
      opacity: 1;
    }
  }
}
