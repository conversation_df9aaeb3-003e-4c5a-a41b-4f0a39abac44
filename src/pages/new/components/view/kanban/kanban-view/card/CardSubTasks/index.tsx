import { RenderTaskName } from '@/pages/new/components/view/list/render';
import { TodoInfo } from '@/types';
import { FC, useMemo, useRef, useState } from 'react';
import RenderItem from '../render-item';
import { Data16Sub } from '@babylon/popo-icons';
import I18N from '@/utils/I18N';
import { CaretRight } from '@bedrock/icons-react';
import s from './index.less';
import classNames from 'classnames';
import RowOperate from '../../../../list/render/row-operate';
import { TodoStatus } from '@/components/basic-task';
import { validatesPermission, TaskPermissionEnum } from '@/utils/permission';
import useGetPermissions from '@/hooks/useGetPermissions';
import { Dispatch } from '@/models/store';
import { useDispatch } from 'react-redux';

interface ISubTasksProps {
  taskInfo?: TodoInfo;
}
const CardSubTasks: FC<ISubTasksProps> = ({ taskInfo }) => {
  const isSubVisitedRef = useRef(false);
  const [visible, setVisible] = useState(false);
  const subTasks = taskInfo?.subtask as TodoInfo[];

  const dispatch = useDispatch<Dispatch>();

  if (!subTasks?.length) {
    return null;
  }

  const toggleVisible = (e: React.MouseEvent) => {
    e.stopPropagation();
    const nextVisible = !visible;
    if (nextVisible && !isSubVisitedRef.current) {
      isSubVisitedRef.current = true;
    }
    setVisible(nextVisible);
  };

  const handleOpenSubDetail = (e: React.MouseEvent, subtask: TodoInfo) => {
    e.stopPropagation();
    dispatch.detail.openDetail(Number(subtask.taskId));
  };

  return (
    <div className={s.card__subtask}>
      <RenderItem
        leftNode={
          <div className="card__item--name ellipsis">
            <span className="card__item--name--icon">
              <Data16Sub />
            </span>
            <span className="card__item--name--text">{I18N.auto.subTask}</span>
          </div>
        }
      >
        <div className={s.right} onClick={toggleVisible}>
          <span>
            {taskInfo?.subtaskFinishedCount || 0}/{subTasks.length}
          </span>
          <CaretRight className={classNames(s.right__icon, { [s.open]: visible })} />
        </div>
      </RenderItem>
      {isSubVisitedRef.current && (
        <div style={{ display: visible ? 'block' : 'none' }}>
          {subTasks?.map((task) => {
            return (
              <SubtaskItem task={task} onClick={(e) => handleOpenSubDetail(e, task)} />
              // <div
              //   onClick={(e) => handleOpenSubDetail(e, task)}
              //   className={`${s.sub__item} flex-y-center`}
              //   key={task.taskId}
              // >
              //   <RowOperate className={s.operate}>
              //     <TodoStatus disabled={!task?.editable && disabledTodoStatus} taskInfo={task} />
              //   </RowOperate>
              //   <div
              //     className={classNames(s['sub__item--title'], 'ellipsis', {
              //       [s.finished]: task.finished,
              //     })}
              //   >
              //     {task?.title}
              //   </div>
              //   {/* <RenderTaskName showViewBtn={false} editable={false} size="small" taskInfo={task} /> */}
              // </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const SubtaskItem = ({ task, onClick }) => {
  let permissions = useGetPermissions({ taskId: task?.taskId });
  const disabledTodoStatus = useMemo(() => {
    const CAN_COMPLETE = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_COMPLETE,
    });
    return !CAN_COMPLETE;
  }, [task]);

  return (
    <div onClick={onClick} className={`${s.sub__item} flex-y-center`} key={task.taskId}>
      <RowOperate className={s.operate}>
        <TodoStatus disabled={!task?.editable && disabledTodoStatus} taskInfo={task} />
      </RowOperate>
      <div
        className={classNames(s['sub__item--title'], 'ellipsis', {
          [s.finished]: task.finished,
        })}
      >
        {task?.title}
      </div>
      {/* <RenderTaskName showViewBtn={false} editable={false} size="small" taskInfo={task} /> */}
    </div>
  );
};

export default CardSubTasks;
