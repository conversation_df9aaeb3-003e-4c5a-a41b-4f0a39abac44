import { TaskInfo } from '@/types'
import { FinishedStatus } from '@/utils/fields'
import dayjs from 'dayjs'
import { GroupStats } from './types'
import { normalizeTimestamp } from './utils'

// 分组任务收集器
export class TaskGroupCollector {
  groupMap = new Map<string, TaskInfo[]>()
  groupStats = new Map<string, GroupStats>()
  groupDuration = new Map<string, any>()

  getDate(a: string, b?: string | number, type?: 'min' | 'max') {
    if (!a || !b) {
      return a || b
    }

    const dayA = dayjs(a)
    const dayB = dayjs(b)
    const method = type === 'max' ? 'isAfter' : 'isBefore'

    return dayA[method](dayB) ? dayA : dayB
  }

  setDuration(groupKey: string, task: TaskInfo) {
    normalizeTimestamp(task)
    const duration = this.groupDuration.get(groupKey)
    this.groupDuration.set(groupKey, {
      $startTime: this.getDate(duration?.$startTime, task.$startTime, 'min'),
      $deadline: this.getDate(duration?.$deadline, task.$deadline, 'max'),
    })
  }

  addTask(groupKey: string, task: TaskInfo): void {
    // 添加任务到分组
    if (!this.groupMap.has(groupKey)) {
      this.groupMap.set(groupKey, [])
      this.groupStats.set(groupKey, { totalCount: 0, finishedCount: 0 })
    }

    this.groupMap.get(groupKey)!.push(task)

    // 实时更新统计信息
    const stats = this.groupStats.get(groupKey)!
    stats.totalCount += 1
    if (task.finished === FinishedStatus.Finished) {
      stats.finishedCount += 1
    }
  }

  getGroups(): Map<string, TaskInfo[]> {
    return this.groupMap
  }

  getGroupStats(groupKey: string): GroupStats {
    return this.groupStats.get(groupKey) || { totalCount: 0, finishedCount: 0 }
  }

  getAllGroupStats(): Map<string, GroupStats> {
    return this.groupStats
  }

  getGroupDuration(groupKey: string): any {
    return this.groupDuration.get(groupKey) || {}
  }

  getAllGroupDuration(): Map<string, any> {
    return this.groupDuration
  }
}
