import classNames from 'classnames';
import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { history } from 'umi';

import { Icon } from '@/components/basic';
import ComFieldText from '@/components/basic-project/com-field-text';
import TodoStatus from '@/components/basic-task/todo-status';
import { Dispatch } from '@/models/store';
import { TaskInfo } from '@/types';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import RowOperate from '../row-operate';
import s from './index.less';
import RenderTaskNameWrap from './render-task-name-wrap';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  taskInfo: TaskInfo;
}

const RenderTaskName: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const { taskId, title, selfFinished, finished, commentCount = 0 } = taskInfo || {};
  const dispatch = useDispatch<Dispatch>();

  let permissions = useGetPermissions({ taskId });
  // 兼容半屏容器
  permissions = permissions || taskInfo?.permissions;

  const onItemClick = () => {
    history.push(`/pc/detail/${taskId}${location.search}&from=pc-list`);
  };

  const disabledTodoStatus = useMemo(() => {
    const CAN_COMPLETE = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_COMPLETE,
    });
    return !CAN_COMPLETE;
  }, [permissions]);

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    });
    return !CAN_EDIT;
  }, [permissions]);

  const disabledView = useMemo(() => {
    const CAN_VIEW = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_VIEW,
    });
    return !CAN_VIEW;
  }, [permissions]);

  return (
    <RenderTaskNameWrap
      disabled={disabledView}
      onClick={() => {
        if (!disabledView) {
          onItemClick();
        }
      }}
      statusNode={
        <RowOperate className={s.operate}>
          <TodoStatus
            placement={'topRight'}
            onChange={() => {
              // dispatch.batch.exitBatchMode();
              dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
            }}
            disabled={disabledTodoStatus}
            taskInfo={taskInfo}
          ></TodoStatus>
        </RowOperate>
      }
    >
      <div className={s.titleWarp}>
        <div
          className={classNames(s.titleWarpLeft, {
            [s.disabled]: disabled,
            [s.finished]: selfFinished || finished,
            [s.hasComment]: commentCount,
          })}
        >
          <ComFieldText
            key={title}
            className={classNames(s.listTitleEditor)}
            contentClassName={s.listTitleContent}
            focusedClassName={s.focused}
            value={title}
            editable={false}
            disabled={true}
          ></ComFieldText>
        </div>
        <div className={s.titleWarpRight}>
          {commentCount ? (
            <>
              <Icon name="icon-quick_chat" className="mr-2"></Icon> {commentCount}
            </>
          ) : null}
        </div>
      </div>
    </RenderTaskNameWrap>
  );
};

export default RenderTaskName;
