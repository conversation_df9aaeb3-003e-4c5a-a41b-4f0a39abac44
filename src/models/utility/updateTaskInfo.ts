import { TaskInfo, TodoInfo } from '@/types'
import { store } from '../store'
import dayjs, { Dayjs } from 'dayjs'
import { ViewType } from '@/utils/const'

export const updateTaskInfo = (opt, { modelName, dispatch }) => {
  const { taskInfo, actionType, targetTaskId } = opt
  const { parentId, taskId } = taskInfo
  const childrenFieldName = modelName === 'task' ? 'subtask' : 'children'
  const tableListKey = 'treeList'
  const tableList = store.getState()[modelName][tableListKey]
  const newTargetId = targetTaskId || taskId

  // 通用更新函数
  const updateItem = (items: TodoInfo[], targetId: string, matchedGroups: TaskInfo[] = []) => {
    const index = items.findIndex(v => v.taskId === targetId)
    if (index === -1) return false

    if (actionType === 'delete') {
      items.splice(index, 1)
      matchedGroups.forEach(group => {
        group.totalCount = Math.max(0, (group.totalCount || 0) - 1)
      })
    } else {
      const prevTask = items[index]
      items[index] = { ...taskInfo }

      // 处理完成状态变化
      if (taskInfo.finished !== prevTask?.finished || taskInfo.selfFinished !== prevTask?.selfFinished) {
        updateFinishedCount(items, matchedGroups)
      }

      // 处理新增任务
      if (!('_rowType' in taskInfo) && prevTask.taskId !== taskInfo.taskId) {
        addToDataList(taskInfo)
        matchedGroups.forEach(group => {
          group.totalCount = Math.max(0, (group.totalCount || 0) + 1)
        })
      }
    }
    if (store.getState().viewSetting.currentViewTab.viewType === ViewType.timeline) {
      updateGroupDuration(items, matchedGroups)
    }
    return true
  }

  const updateGroupDuration = (items: TodoInfo[], matchedGroups: TaskInfo[]) => {
    if (!matchedGroups.length) return

    // 递归计算时间范围（支持嵌套分组）
    const calculateTimeRange = (children: TodoInfo[]): { startTime: Dayjs | null; deadline: Dayjs | null } => {
      if (!children?.length) return { startTime: null, deadline: null }

      let minStartTime: Dayjs | null = null
      let maxDeadline: Dayjs | null = null

      children.forEach(item => {
        if (!item._rowType) {
          // 处理实际任务
          const startTime = dayjs(item.$startTime) as Dayjs
          const deadline = dayjs(item.$deadline) as Dayjs

          if (startTime && (!minStartTime || startTime.isBefore(dayjs(minStartTime)))) {
            minStartTime = startTime
          }
          if (deadline && (!maxDeadline || deadline.isAfter(dayjs(maxDeadline)))) {
            maxDeadline = deadline
          }
        }
      })

      return { startTime: minStartTime, deadline: maxDeadline }
    }

    // 批量更新分组时间
    const updateGroupTime = (group: TaskInfo, startTime: Dayjs | null, deadline: Dayjs | null) => {
      if (startTime) {
        group.$startTime = startTime
      } else {
        delete group.$startTime
      }

      if (deadline) {
        group.$deadline = deadline
      } else {
        delete group.$deadline
      }
    }

    // 从当前层级开始，逐级向上更新
    for (let i = matchedGroups.length - 1; i >= 0; i--) {
      const group = matchedGroups[i]
      const isCurrentLevel = i === matchedGroups.length - 1

      // 当前层级使用传入的items，其他层级使用自己的children
      const targetItems = isCurrentLevel ? items : group[childrenFieldName] || []
      const { startTime, deadline } = calculateTimeRange(targetItems)

      updateGroupTime(group, startTime, deadline)
    }
  }

  // 更新完成数量
  const updateFinishedCount = (items: TodoInfo[], matchedGroups: TaskInfo[]) => {
    if (!matchedGroups.length) return

    const finishedCount = items.reduce((count, curr) => (curr.finished ? count + 1 : count), 0)
    const lastGroup = matchedGroups[matchedGroups.length - 1]
    if (lastGroup) lastGroup.finishedCount = finishedCount

    // 更新第一级分组
    if (matchedGroups.length > 1) {
      const firstGroup = matchedGroups[0]
      firstGroup.finishedCount =
        firstGroup[childrenFieldName]?.reduce((sum: number, curr) => sum + (curr?.finishedCount || 0), 0) || 0
    }
  }

  // 添加到数据列表
  const addToDataList = (task: TodoInfo) => {
    if (!task.parentId) {
      const dataList = store.getState()[modelName].dataList as TodoInfo[]
      dataList.unshift(task)
      dispatch[modelName].setData({ dataList: [...dataList] })
    }
  }

  // 查找父任务
  const findParentTask = (taskId: string): TaskInfo | null => {
    const stack = [...tableList]
    while (stack.length) {
      const item = stack.shift()
      if (item?.taskId === taskId) return item
      if (item?.[childrenFieldName]) {
        stack.push(...item[childrenFieldName])
      }
    }
    return null
  }

  // 导航到分组任务
  const navigateToGroupedTask = (groupIds: string[]): { group: TaskInfo | null; matchedGroups: TaskInfo[] } => {
    let currGroup = tableList
    const matchedGroups: TaskInfo[] = []

    for (const groupId of groupIds) {
      if (!Array.isArray(currGroup)) {
        currGroup = currGroup?.[childrenFieldName]
      }
      currGroup = currGroup?.find(v => `${v.taskId || v.groupId}` === `${groupId}`)
      if (currGroup) matchedGroups.push(currGroup)
    }

    return { group: currGroup, matchedGroups }
  }

  // BFS 查找并更新任务
  const bfsUpdate = (items: TodoInfo[], matchedGroups: TaskInfo[] = []): boolean => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if ((item.taskId || item.groupId) === newTargetId) {
        return updateItem(items, newTargetId, matchedGroups)
      }
      if (item[childrenFieldName]) {
        const newMatchedGroups = [...matchedGroups, item]
        if (bfsUpdate(item[childrenFieldName], newMatchedGroups)) {
          return true
        }
      }
    }
    return false
  }

  // 更新数据列表
  const updateDataList = () => {
    const dataList = store.getState()[modelName].dataList as TodoInfo[]
    const index = dataList.findIndex(v => v.taskId === newTargetId)
    if (index !== -1) {
      if (actionType === 'delete') {
        dataList.splice(index, 1)
      } else {
        dataList[index] = taskInfo
      }
      dispatch[modelName].setData({ dataList: [...dataList] })
    }
  }

  // 主逻辑
  if (parentId) {
    // 处理子任务
    const parentTask = findParentTask(parentId)
    if (parentTask?.[childrenFieldName] && updateItem(parentTask[childrenFieldName], newTargetId)) {
      dispatch[modelName].setData({ [tableListKey]: [...tableList] })
    }
  } else {
    let updated = false

    // 处理分组任务
    if (taskInfo.groupedIds?.length) {
      const { group, matchedGroups } = navigateToGroupedTask(taskInfo.groupedIds)
      if (group?.[childrenFieldName] && updateItem(group[childrenFieldName], newTargetId, matchedGroups)) {
        updated = true
      }
    }

    // 处理根级任务
    if (!updated) {
      bfsUpdate(tableList)
    }

    dispatch[modelName].setData({ [tableListKey]: [...tableList] })
    updateDataList()
  }
}
