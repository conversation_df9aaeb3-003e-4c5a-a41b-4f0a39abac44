import classNames from 'classnames';
import { PropsWithChildren, useState } from 'react';

import { Dropdown } from '@/components/basic';
import { ProjectInfo } from '@/types';

import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
}

const ProjectShare: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled, className, projectInfo } = props;
  const [visible, setVisible] = useState<boolean>(false);
  return (
    <div className={classNames(s.projectShare, className)}>
      <Dropdown
        className={s.dropdown}
        title={children}
        trigger="click"
        arrow={false}
        defaultOpen={visible}
        disabled={disabled}
        open={visible}
        onOpenChange={(v) => {
          setVisible(v);
        }}
        destroyPopupOnHide={false}
        overlay={<Overlay projectInfo={projectInfo}></Overlay>}
      />
    </div>
  );
};

export default ProjectShare;
