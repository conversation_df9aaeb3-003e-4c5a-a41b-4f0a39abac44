.levelSelectDropdown {
  //cursor: auto !important;
  &:global(.rock-dropdown-trigger-default) {
    width: 100%;
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    background-color: transparent !important;
  }
}

.label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}
.panel {
  width: 160px;
  padding: 4px;

  .levelItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    padding: 0 8px;
    border-radius: 4px;
    color: var(--TextPrimary);
    font-size: 13px;
    cursor: pointer;
    &:hover,
    .active {
      background-color: var(--aBlack6);
    }
    .checkIcon {
      color: var(--Brand600);
    }
  }
  .icon {
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-size: 16px;
    margin-right: 7px;
  }
}
