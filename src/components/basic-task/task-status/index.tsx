import React, { PropsWithChildren, useEffect } from 'react';

import { Dropdown, Menu } from '@/components/basic';
import { OneAndMoreType, TaskStatusCompleteType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import useCompleteTaskStatus, {
  TaskCompleteStatus,
  TaskOperateStatus,
} from './use-complete-task-status';

export { TaskCompleteStatus, TaskOperateStatus };

export { OneAndMoreType };
export interface TaskStatusProps {
  oneAndMoreType?: OneAndMoreType;
  isManager: boolean;
  /**
   * 执行人
   */
  isCoordinator: boolean;
  inDetail?: boolean;
  selfFinished?: number;
  finished?: number;
  taskId: number;
  taskState?: TaskCompleteStatus;
  onChange?: (finished: boolean) => void;
  onTaskStateChange?: (taskState: TaskCompleteStatus, type: string) => void;
  assigneeUids?: string[];
  disabled?: boolean;
  finishedParticipantCount?: number;
  participantCount?: number;
}

export const allTaskStatus = {
  [TaskCompleteStatus.InstantaneousFinished]: {
    finished: true,
    name: I18N.auto.taskCompleted,
    type: TaskStatusCompleteType.me,
  },
  [TaskCompleteStatus.InstantaneousUnfinished]: {
    finished: false,
    name: I18N.auto.clickToCompleteTheTask,
    type: TaskStatusCompleteType.me,
  },
  [TaskCompleteStatus.CompleteMeOrCompleteAll]: {
    finished: false,
    name: I18N.auto.clickToCompleteTheTask,
    type: TaskStatusCompleteType.all,
    options: [
      {
        name: I18N.auto.onlyIHaveCompletedIt,
        key: TaskOperateStatus.completeMyTask,
      },
      {
        name: I18N.auto.allCompleted_2,
        key: TaskOperateStatus.completeAllTask,
      },
    ],
  },
  [TaskCompleteStatus.RebuildMeOrCompleteAll]: {
    finished: true,
    name: I18N.auto.iHaveCompletedIt,
    type: TaskStatusCompleteType.all,
    options: [
      {
        name: I18N.auto.restartMyTask,
        key: TaskOperateStatus.resetMyTask,
      },
      {
        name: I18N.auto.allCompleted_2,
        key: TaskOperateStatus.completeAllTask,
      },
    ],
  },
  [TaskCompleteStatus.RebuildMeOrRebuildAll]: {
    finished: true,
    name: I18N.auto.taskCompleted,
    type: TaskStatusCompleteType.all,
    options: [
      {
        name: I18N.auto.restartMyTask,
        key: TaskOperateStatus.resetMyTask,
      },
      {
        name: I18N.auto.restartEverything,
        key: TaskOperateStatus.resetAllTask,
      },
    ],
  },
};

export const TaskStatus: React.FC<PropsWithChildren<TaskStatusProps>> = (props) => {
  const { taskState, children, onTaskStateChange, disabled } = props;
  // onChange 在useCompleteTaskStatus中接口调用成功后使用
  const { selectOperationMethod, getTaskState, loadingRef } = useCompleteTaskStatus(props);
  useEffect(() => {
    const taskState = getTaskState();
    onTaskStateChange?.(taskState, 'init');
  }, [getTaskState]);

  if (
    taskState === TaskCompleteStatus.InstantaneousFinished ||
    taskState === TaskCompleteStatus.InstantaneousUnfinished
  ) {
    return React.cloneElement(children, {
      onClick: () => {
        if (disabled) {
          return;
        }
        if (!loadingRef.current) {
          loadingRef.current = true;
          selectOperationMethod();
        }
      },
    }) as React.ReactNode;
  }

  return disabled ? (
    children
  ) : (
    <Dropdown
      trigger="click"
      overlay={
        <TaskStatusMenu
          taskState={taskState}
          loadingRef={loadingRef}
          selectOperationMethod={selectOperationMethod}
        />
      }
      overlayClassName={s.taskStatusDropdown}
    >
      {children}
    </Dropdown>
  );
};

interface TaskStatusMenuProps {
  taskState: TaskCompleteStatus;
  loadingRef: React.MutableRefObject<boolean>;
  selectOperationMethod: (params: { key: TaskOperateStatus }) => void;
}

export const TaskStatusMenu: React.FC<TaskStatusMenuProps> = ({
  taskState,
  loadingRef,
  selectOperationMethod,
}) => {
  return (
    <Menu
      selectedKeys={[]}
      onClick={(value) => {
        if (!loadingRef.current) {
          loadingRef.current = true;
          selectOperationMethod({
            key: Number(value.key) as TaskOperateStatus,
          });
        }
      }}
    >
      {allTaskStatus[taskState]?.options?.map((option) => (
        <Menu.Item className={s.menuItem} key={option.key}>
          {option.name}
        </Menu.Item>
      ))}
    </Menu>
  );
};
