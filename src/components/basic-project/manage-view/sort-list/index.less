.list {
  background: var(--bgBottom);
}
.descTitle {
  padding: 14px 12px 8px 12px;
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 22px;
  font-weight: bold;
}
.back {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  padding: 0 12px;
  background-color: var(--bgTop);
  cursor: grab;
  .content {
    display: flex;
    margin-left: 16px;
    align-items: center;
    flex: 1;
    width: calc(100% - 32px);
    .icon {
      margin-right: 8px;
    }
    .title {
      color: var(--TextPrimary);
      font-size: 13px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .itemLeft {
    display: flex;
    align-items: center;
    overflow: hidden;
    flex: 1;
    .icon {
      color: var(--IconPrimary);
    }
  }
  .itemRight {
    display: flex;
    align-items: center;
    .icon {
      &:last-child {
        color: var(--IconSecondary);
      }
    }
    .sysIcon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:hover {
        background-color: var(--aBlack6);
        border-radius: 4px;
      }
    }
  }

  .itemRight > .icon {
    margin-left: 8px;
    &:hover {
      background-color: var(--aBlack6);
      border-radius: 4px;
    }
  }
}
.originalItem {
  border-bottom: 1px solid var(--aBlack10);
  z-index: 100000;
}
.lastItem {
  border-bottom: none;
}

.draggingItem {
  box-shadow: var(--ComBoxShadow);
  border-radius: 8px;
}
