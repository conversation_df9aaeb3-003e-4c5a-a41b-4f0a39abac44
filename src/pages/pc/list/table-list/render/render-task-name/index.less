.operate {
  // margin-top: 4px;
  // padding-right: 6px !important;
  &:hover {
    .checkbox {
      display: block;
      // transition: all 0.3s;
    }
  }
}

.listTitleEditor {
  height: 36px;
  cursor: pointer;
}
.listTitleContent {
  width: 100%;
  max-width: unset !important;
  padding: 3px 7px;
  margin-top: 4px;
  margin-bottom: 4px;
  margin-right: -1px !important;
  border: 0;
  line-height: 20px;
  font-weight: 400;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  border: 1px solid transparent !important;
  border-radius: 0;
  color: var(--TextPrimary-strong);
  outline: none;
  user-select: none !important;
  -webkit-user-select: none !important;
  a[data-popo='tag'] {
    color: var(--LinkNormal);
  }

  :global {
    .rock-input-textarea {
      min-height: 20px !important;
      line-height: 20px !important;
      //color: var(--TextPrimary-strong) !important;
    }
  }
}

.titleWarp {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  .titleWarpLeft {
    max-width: calc(100%);
    flex: none;
  }
  .hasComment {
    max-width: calc(100% - 40px);
  }
  .onlyLeft {
    max-width: 100%;
    flex: 1;
  }
  .disabled {
    pointer-events: none;
    cursor: default;
  }
  .finished {
    :global {
      .todo-content-editable {
        opacity: 0.4;
      }
    }
  }
  .titleWarpRight {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-content: flex-start;
    flex: none;
    color: var(--TextTertiary);
  }
}
