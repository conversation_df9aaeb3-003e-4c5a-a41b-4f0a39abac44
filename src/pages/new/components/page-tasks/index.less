.taskView {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  width: 100%;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftToolbar {
    flex: 1;
  }

  .rightToolbar {
    display: inline-flex;
    align-items: center;
    height: 33px;
    z-index: 1;
    padding: 4px 0;

    .shadow {
      width: 12px;
      height: 100%;
      background: linear-gradient(270deg, var(--bgBottom) 0%, rgba(255, 255, 255, 0) 100%);
    }

    .right {
      display: inline-flex;
      align-items: center;
      height: 30px;
      background-color: var(--bgBottom);
      z-index: 1;
      padding: 4px 12px 4px 0;
    }

    .backTodayBtn {
      margin-right: 12px;
      height: 24px;
      line-height: 24px;
    }

    .pre,
    .next {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      color: var(--IconTertiary);
      border-radius: 4px;
    }
  }
}

.tabBox {
  padding: 0 20px;
  border-bottom: 1px solid var(--aBlack6);
}

.view {
  flex: 1;
}
