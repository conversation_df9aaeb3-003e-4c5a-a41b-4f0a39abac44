import classNames from 'classnames'
import { Dayjs } from 'dayjs'
import React, { PropsWithChildren, useState } from 'react'

import { Dropdown } from '@/components/basic'
import { UserInfo } from '@/types'
import { EnumCalendarAddSource, ViewAddSource } from '@/utils/const'

import Add from './add'
import s from './index.less'

export type Props = {
  className?: string
  date?: Dayjs
  onAddSuccess?: (opt: { hasDeadline: boolean }) => void
  assignees?: UserInfo[]
  source: EnumCalendarAddSource
}

const AddDropdown: React.FC<PropsWithChildren<Props>> = props => {
  const { children, date, onAddSuccess, assignees, source } = props
  const [open, setOpen] = useState(false)
  return (
    <div className={s.AddDropdownWrap}>
      <Dropdown
        title={children}
        overlayClassName="calender__create--dropdown"
        trigger="click"
        arrow={false}
        defaultOpen={false}
        open={open}
        onOpenChange={v => {
          setOpen(v)
          if (v) {
            if (source === EnumCalendarAddSource.unplanned) {
              ViewAddSource.calendar = EnumCalendarAddSource.unplanned
            } else {
              ViewAddSource.calendar = EnumCalendarAddSource.calendar
            }
          }
        }}
        //@ts-ignore
        destroyPopupOnHide
        minOverlayWidthMatchTrigger={false}
        overlay={
          <div className={classNames('com-dropdown-select', s.panel)}>
            <Add
              onClose={() => {
                setOpen(false)
              }}
              onAddSuccess={onAddSuccess}
              date={date}
              assignees={assignees}
            ></Add>
          </div>
        }
      />
    </div>
  )
}

export default AddDropdown
