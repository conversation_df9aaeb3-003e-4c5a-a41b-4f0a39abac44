// 废弃
// 不在引入文件 写入plugin.ts文件中
var match = navigator.userAgent.match(/POPOTheme\/(\S+)/);
var skinType = 'light'; //TODO 默认记得改成light
if (match && match[1]) {
  skinType = match[1];
}
console.log('userAgent-----', navigator.userAgent, 'skinType---', skinType);
document.body.setAttribute('data-theme', skinType);

window.POPOTaskTrackTimeMap = {
  firstScript: Date.now(), //进入第一个Script时间
};

window.onload = function () {
  window.POPOTaskTrackTimeMap = {
    ...(window.POPOTaskTrackTimeMap || {}),
    onload: Date.now(), //onload时间
  };
};
