.box {
  position: relative;
  border-radius: 6px;
  flex: 1;
  min-width: 0;

  &.insertBefore {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--Brand500);
    }
  }

  &.insertAfter {
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--Brand500);
    }
  }

  &:hover {
    .right {
      width: auto;
      :global {
        .operation__container {
          width: auto;
          margin-left: 4px;
        }
      }
    }

    :global {
      .todo-menu-more {
        visibility: visible;
      }
    }
  }
  &.active {
    background-color: var(--aBrand8) !important;
    &.menuName {
      color: var(--TextPrimary-strong);
    }
  }
}

.menuItem {
  position: relative;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  height: 34px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  .menuName {
    display: inline;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    align-items: center;
    color: var(--TextPrimary);
    height: 24px;
    line-height: 24px;
    .count {
      color: var(--TextTertiary);
      font-size: 11px;
    }
    .hidden {
      display: none;
    }
  }

  .placeholder {
    font-size: 12px;
    line-height: 18px;
    color: var(--TextSecondary-ongrey, #11111199);
    line-height: 24px;
  }

  .left {
    position: relative;
    display: flex;
    align-items: center;
    column-gap: 8px;
    flex: 1;
    overflow: hidden;
    .menuName {
      flex: 1;
    }
  }
  // &:hover {
  //   background-color: var(--aBlack6);
  // }
  .projectIcon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    border-radius: 4px;
  }

  &.over {
    background-color: var(--aBlack6);
  }
}

.editing__input {
  width: 100%;
  height: 26px;
  padding-left: 3px !important;
  margin-left: -4px !important;
  border-radius: 4px;
  &::before {
    border-radius: 4px;
  }
}

.right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  overflow: hidden;
  :global {
    .operation__container {
      transition: none;

      &.more__visible {
        margin-left: 4px;
      }
      &:not(.more__visible) {
        width: 0;
      }
    }
  }

  .editIcon {
    color: var(--IconPrimary);
    font-size: 16px;
  }
}
