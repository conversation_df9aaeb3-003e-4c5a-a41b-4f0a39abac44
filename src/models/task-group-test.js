// 测试分组任务编辑行重复问题的修复
// 这个文件用于验证修复是否有效

// 模拟分组任务数据
const mockGroupedTask = {
  taskId: 123,
  title: '测试任务',
  groupId: 'user-1',
  groupedIds: [['user-1'], ['user-2']], // 任务出现在多个分组中
  subtask: [
    { taskId: 'sub-1', title: '子任务1', subtask: [] },
    { taskId: 'sub-2', title: '子任务2', subtask: [] },
  ],
}

// 模拟深度复制函数
function deepCopyTask(task, groupKey) {
  const copySubtasks = subtasks => {
    if (!subtasks) return []
    return subtasks.map(subTask => ({
      ...subTask,
      subtask: copySubtasks(subTask.subtask),
      _groupKey: groupKey,
    }))
  }

  return {
    ...task,
    subtask: copySubtasks(task.subtask),
    _originalTask: task,
    _groupKey: groupKey,
  }
}

// 测试深度复制功能
function testDeepCopy() {
  console.log('=== 测试深度复制功能 ===')

  // 创建两个不同分组的副本
  const group1Copy = deepCopyTask(mockGroupedTask, 'group-1')
  const group2Copy = deepCopyTask(mockGroupedTask, 'group-2')

  console.log('原始任务:', mockGroupedTask)
  console.log('分组1副本:', group1Copy)
  console.log('分组2副本:', group2Copy)

  // 测试是否为独立副本
  const isIndependent = group1Copy.subtask !== group2Copy.subtask
  console.log('subtask数组是否独立:', isIndependent)

  // 测试修改一个副本是否影响另一个
  group1Copy.subtask.push({ taskId: 'new-sub', title: '新子任务', _groupKey: 'group-1' })

  console.log('修改group1Copy后:')
  console.log('group1Copy.subtask长度:', group1Copy.subtask.length)
  console.log('group2Copy.subtask长度:', group2Copy.subtask.length)
  console.log('原始任务subtask长度:', mockGroupedTask.subtask.length)

  const noInterference =
    group1Copy.subtask.length !== group2Copy.subtask.length &&
    group2Copy.subtask.length === mockGroupedTask.subtask.length

  return {
    isIndependent,
    noInterference,
    group1Length: group1Copy.subtask.length,
    group2Length: group2Copy.subtask.length,
    originalLength: mockGroupedTask.subtask.length,
  }
}

// 测试分组标识
function testGroupKey() {
  console.log('=== 测试分组标识 ===')

  const group1Copy = deepCopyTask(mockGroupedTask, 'group-1')
  const group2Copy = deepCopyTask(mockGroupedTask, 'group-2')

  console.log('group1Copy._groupKey:', group1Copy._groupKey)
  console.log('group2Copy._groupKey:', group2Copy._groupKey)
  console.log(
    'group1Copy子任务的_groupKey:',
    group1Copy.subtask.map(sub => sub._groupKey),
  )
  console.log(
    'group2Copy子任务的_groupKey:',
    group2Copy.subtask.map(sub => sub._groupKey),
  )

  const hasCorrectGroupKeys =
    group1Copy._groupKey === 'group-1' &&
    group2Copy._groupKey === 'group-2' &&
    group1Copy.subtask.every(sub => sub._groupKey === 'group-1') &&
    group2Copy.subtask.every(sub => sub._groupKey === 'group-2')

  return {
    hasCorrectGroupKeys,
    group1Key: group1Copy._groupKey,
    group2Key: group2Copy._groupKey,
  }
}

// 测试原始引用保留
function testOriginalReference() {
  console.log('=== 测试原始引用保留 ===')

  const group1Copy = deepCopyTask(mockGroupedTask, 'group-1')

  console.log('原始任务引用是否保留:', group1Copy._originalTask === mockGroupedTask)
  console.log('原始任务taskId:', group1Copy._originalTask?.taskId)
  console.log('副本taskId:', group1Copy.taskId)

  return {
    hasOriginalReference: group1Copy._originalTask === mockGroupedTask,
    originalTaskId: group1Copy._originalTask?.taskId,
    copyTaskId: group1Copy.taskId,
  }
}

// 运行测试
console.log('=== 测试分组任务编辑行重复问题修复 ===')
console.log()

console.log('1. 测试深度复制功能:')
const deepCopyTest = testDeepCopy()
console.log('结果:', deepCopyTest.isIndependent && deepCopyTest.noInterference ? '✅ 通过' : '❌ 失败')
console.log(`详情: 独立性=${deepCopyTest.isIndependent}, 无干扰=${deepCopyTest.noInterference}`)
console.log()

console.log('2. 测试分组标识:')
const groupKeyTest = testGroupKey()
console.log('结果:', groupKeyTest.hasCorrectGroupKeys ? '✅ 通过' : '❌ 失败')
console.log(`详情: group1Key=${groupKeyTest.group1Key}, group2Key=${groupKeyTest.group2Key}`)
console.log()

console.log('3. 测试原始引用保留:')
const originalRefTest = testOriginalReference()
console.log('结果:', originalRefTest.hasOriginalReference ? '✅ 通过' : '❌ 失败')
console.log(`详情: 原始taskId=${originalRefTest.originalTaskId}, 副本taskId=${originalRefTest.copyTaskId}`)
console.log()

console.log('=== 测试完成 ===')
console.log()

// 总结测试结果
const allTestsPassed =
  deepCopyTest.isIndependent &&
  deepCopyTest.noInterference &&
  groupKeyTest.hasCorrectGroupKeys &&
  originalRefTest.hasOriginalReference

console.log('🎯 总体结果:', allTestsPassed ? '✅ 所有测试通过' : '❌ 部分测试失败')

if (allTestsPassed) {
  console.log('✨ 修复成功！分组任务编辑行重复问题已解决')
  console.log('📋 修复要点:')
  console.log('  - 每个分组中的任务都有独立的subtask数组')
  console.log('  - 修改一个分组中的任务不会影响其他分组')
  console.log('  - 保留了原始任务的引用便于后续操作')
  console.log('  - 每个副本都有正确的分组标识')
}

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testDeepCopy,
    testGroupKey,
    testOriginalReference,
    deepCopyTask,
  }
}
