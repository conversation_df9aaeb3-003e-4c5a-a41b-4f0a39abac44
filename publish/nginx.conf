server {
    listen 80;
    server_name localhost;
    resolver *******:53 *******:53 valid=60s ipv6=off;
    client_max_body_size 10m;
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 0;
    gzip_types text/plain application/javascript text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/vnd.ms-fontobject application/x-font-ttf font/opentype;
    underscores_in_headers on;
    
    set $cdn_host popo.gsf.netease.com;
    
    set $file_path dist;
    
    set $cdn_host_path https://$cdn_host/popo-todo-fe;

    set $default_version_tag 20240815205541_54a26ed1;

    set $grayscale_version_tag 20240913192321_e5fca832;

    set $grayscale_flag grayscale;

    location ~* ^.+\.(ttf|eot|woff|woff2|jpg|jpeg|gif|png|svg|ico|lasso|pl|txt|fla|swf|zip|wav|json|js|js.map|css|css.map|less)$ {
      proxy_connect_timeout   60;
      proxy_send_timeout      120;
      proxy_read_timeout      120;
      proxy_set_header Host   $proxy_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto https;
      proxy_set_header REMOTE-HOST $remote_addr;
      
      set $grayscaletag $cookie_grayscale_tag;
      if ($grayscaletag ~ grayscale) {
        proxy_pass $cdn_host_path/$grayscale_version_tag/$file_path/$request_uri;
      }
      proxy_pass $cdn_host_path/$default_version_tag/$file_path/$request_uri;
    }

     location / {
      proxy_connect_timeout   60;
      proxy_send_timeout      120;
      proxy_read_timeout      120;
      proxy_set_header Host   $proxy_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto https;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header Cache-Control "no-store, no-cache, must-revalidate";
      
      set $grayscaletag $cookie_grayscale_tag;
      if ($grayscaletag ~ grayscale) {
        proxy_pass $cdn_host_path/$grayscale_version_tag/$file_path/index.html;
      }
      proxy_pass $cdn_host_path/$default_version_tag/$file_path/index.html;

      proxy_hide_header Cache-Control;
      proxy_hide_header Pragma;
      proxy_hide_header Expires;
      add_header Cache-Control "no-cache, no-store, must-revalidate" always;
      add_header Pragma "no-cache" always;
      add_header Expires "0" always;
    }
    
    location ^~/api/ {
      client_max_body_size 10m;
      proxy_set_header Host $http_host;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-For $remote_addr;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_pass https://ncc.popo.netease.com; # interface address
    }
  
    # redirect server error pages to the static page /50x.html
    #
    error_page 404 /home/<USER>/app/index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root html;
    }
  }
