.taskStatusBtn {
  height: 28px;
  border: 1px solid var(--Brand600);
  padding-right: 8px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  color: var(--Brand700);
  border-radius: 6px;
  i {
    color: var(--Brand700);
    line-height: 14px;
  }
  cursor: pointer;
  .text {
    margin-left: 4px;
    margin-right: 4px;
  }
  .icon {
    transform: rotate(90deg);
  }
  &:hover {
    background-color: var(--aBrand12);
  }
}

.taskFinishedStatusBtn {
  height: 28px;
  padding-right: 8px;
  padding-left: 8px;
  border: 1px solid var(--cardgreen1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  color: var(--G700);
  background-color: var(--cardgreen3);
  cursor: pointer;
  i {
    color: var(--G700);
    line-height: 14px;
  }
  .text {
    margin-left: 4px;
    margin-right: 4px;
  }
  &:hover {
    background-color: var(--cardgreen2);
  }
}

.taskStatusDropdown {
  min-width: 0;
  :global {
    .rock-dropdown-menu {
      min-width: 0;
      border: 1px solid var(--aBlack12);
      padding: 4px;
      .rock-dropdown-menu-item {
        padding: 8px;
        margin: 0 !important;
        span {
          line-height: 20px;
        }
      }
    }
  }
}

.taskFollowerBtn {
  height: 28px;
  border: 1px solid var(--TextQuartus);
  color: var(--TextQuartus);
  background-color: transparent;
  cursor: default;
  i {
    color: var(--TextQuartus);
  }
  &:hover {
    background-color: transparent;
  }
}
