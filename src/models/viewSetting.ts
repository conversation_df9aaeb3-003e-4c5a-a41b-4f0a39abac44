import { createModel } from '@rematch/core'
import { history } from 'umi'

import { apiNavigatorTaskCountGet, apiProjectCheckRolePost, apiProjectFieldListPost } from '@/api'
import { apiGetViewList, apiNavigatorViewUpdateSetting, getBatchViewCheckParams, getGroupsData } from '@/api-common'
import { OrderOption, Permission, ProjectTaskResortParams, TodoInfo, ViewInfo, ViewTab } from '@/types'
import { CustomField, FieldSchema } from '@/types/custom-field'
import { getStorage, getViewChanged, parseViewConfig, setStorage, StorageType } from '@/utils'
import Const, { AddMenuId, NONE_GROUP_BY, ProjectListMenuId, TaskNavigatorType, ViewType } from '@/utils/const'
import {
  combineDisplayFields,
  CustomFieldName,
  DisplayField,
  EnumField,
  EnumFieldNONE,
  EnumGroupBy,
} from '@/utils/fields'
import { ProjectDetailPermissions, ProjectPermissionEnum, validatesPermission, ViewTypeEnum } from '@/utils/permission'
import { EnumTrackeKey } from '@/utils/skyline'

import type { RootModel } from '.'
import { getIsInSession, getPageIsProjectGroup, isProjectId, isTaskMenuId, normalizeView } from './utils'
import { store } from './store'
import * as _ from 'lodash'
import { planView2ScaleType } from '@/pages/new/components/view/timeline/utils'
import { Message } from '@bedrock/components'
import I18N from '@/utils/I18N'

type Icondition = {
  fieldName?: string
  values?: string[]
}

interface ViewSettingState {
  //快捷视图导航或项目id
  navigatorId?: TaskNavigatorType | string | number
  addFromNavigatorId?: TaskNavigatorType | number
  viewTabList: ViewTab[]
  currentViewTab: ViewTab
  allTaskCount?: number
  /**
   * 当前项目的权限, 快捷视图目前也当项目处理
   */
  permissions: Permission[]
  customFields: CustomField[]
  viewTabListLoading: boolean
}

const initData: ViewSettingState = {
  navigatorId: undefined,
  allTaskCount: 0,
  viewTabList: [],
  //@ts-ignore
  currentViewTab: {
    conditions: [],
    querySort: {
      order: 'desc',
      fieldName: '',
    },
    queryGroupBy: {
      fieldName: '',
    },
    displays: [],
    parsedViewConfig: {
      cardConfig: {
        showCompleteIcon: true,
      },
    },
  },
  permissions: [],
  customFields: [],
  viewTabListLoading: false,
}

export const viewSetting = createModel<RootModel>()({
  state: initData,
  reducers: {
    initData(state) {
      return {
        ...initData,
      }
    },
    setData(state, payload: Partial<ViewSettingState>) {
      return { ...state, ...payload }
    },
    setTaskMenu(state, menu: TaskNavigatorType | number | string) {
      return {
        ...state,
        navigatorId: menu as unknown as TaskNavigatorType,
      }
    },
    setViewTabList(state, payload: ViewTab[]) {
      return { ...state, viewTabList: payload }
    },
    setCurrentViewTab(state, payload: ViewTab) {
      return { ...state, currentViewTab: payload }
    },
  },
  effects: dispatch => ({
    /**
     * 获取通用参数 所有视图的公共参数都用这个方法
     * @param opt
     * @param state
     * @returns
     */
    getComParams(opt: any, state) {
      const { navigatorId, currentViewTab } = state.viewSetting as ViewSettingState
      const { conditions, querySort, queryGroupBy, queryGroupBys } = state.viewSetting.currentViewTab || {}
      return {
        navigatorId,
        viewId: currentViewTab?.id ?? currentViewTab?.viewId,
        conditions: conditions,
        querySort,
        queryGroupBy,
        queryGroupBys,
        /**
         * 以下是旧的查询字段，不支持自定义字段。为避免修改，暂时保留。后续合适时机删除
         */
        // orderBy: querySort?.fieldName,
        // order: querySort?.order,
        // groupBy: queryGroupBy?.customFieldId || queryGroupBy?.fieldName,
      }
    },
    async getNavigatorTaskCount() {
      setTimeout(async () => {
        const { navigatorTaskCount = 0 } = await apiNavigatorTaskCountGet({
          navigatorId: TaskNavigatorType.allTask,
        })
        dispatch.viewSetting.setData({
          allTaskCount: navigatorTaskCount,
        })
      }, 500)
    },
    async openNavigator(
      opt: { navigatorId: TaskNavigatorType | number | string; taskId?: number; isGroup?: boolean },
      state,
    ) {
      const { navigatorId, taskId, isGroup } = opt
      //记录从什么导航跳转过去的
      setStorage(
        Const.PreNavigatorId,
        getPageIsProjectGroup() ? `${state.viewSetting.navigatorId}-group` : state.viewSetting.navigatorId,
        StorageType.session,
      )
      setStorage(`${Const.NavigatorId}`, navigatorId, StorageType.local)
      dispatch.task.initData()
      // 切换导航
      if (navigatorId === AddMenuId) {
        dispatch.viewSetting.setTaskMenu(AddMenuId)
        history.push('/new/project-add')
        return
      }
      if (navigatorId === ProjectListMenuId) {
        dispatch.viewSetting.setTaskMenu(ProjectListMenuId)
        history.push('/new/project-list')
        return
      }
      let viewList: ViewTab[] = []
      dispatch.viewSetting.setData({
        viewTabListLoading: true,
      })
      if (isTaskMenuId(navigatorId)) {
        dispatch.viewSetting.setTaskMenu(navigatorId)
        let url = `/new/task`
        if (taskId) {
          url = `${url}?id=${taskId}`
        }
        history.push(url)
        viewList = await dispatch.viewSetting.fetchViewList(navigatorId)
        await dispatch.viewSetting.updatePermissions({})
      } else {
        dispatch.viewSetting.setTaskMenu(navigatorId)

        if (isGroup) {
          let url = `/new/project-group/${navigatorId}`
          history.push(url)
          return
        }

        const permissions = await dispatch.viewSetting.updatePermissions({})
        const [CAN_VIEW] = validatesPermission({
          permissions,
          key: [ProjectPermissionEnum.CAN_VIEW],
        }) as boolean[]
        if (CAN_VIEW) {
          let url = `/new/project/${navigatorId}/tasks`
          if (taskId) {
            url = `${url}?id=${taskId}`
          }

          history.push(url)
          viewList = await dispatch.viewSetting.fetchViewList(navigatorId)
        } else {
          // 会话中的页面如果没有项目权限，不跳转
          if (getIsInSession()) {
            Message.error(I18N.auto.noPermissions)
          } else {
            //项目无权限,打开全部任务
            dispatch.viewSetting.setTaskMenu(TaskNavigatorType.allTask)
            let url = '/new/task'
            if (taskId) {
              url = `${url}?id=${taskId}`
            }
            history.push(url)
            viewList = await dispatch.viewSetting.fetchViewList(TaskNavigatorType.allTask)
            await dispatch.viewSetting.updatePermissions({})
          }
        }
      }
      dispatch.viewSetting.setData({
        viewTabListLoading: false,
      })
      // const activeView = viewList[0];
      if (getPageIsProjectGroup()) {
        return
      }
      let activeView = dispatch.viewSetting.getDefaultView({ viewList })
      if (activeView) {
        dispatch.viewSetting.toggeleView(activeView)
      }
    },

    getDefaultView({ viewList = [] }, state) {
      const { navigatorId } = state.viewSetting
      const viewId = getStorage(`${Const.NavigationView}-${navigatorId}`)
      let activeView = viewList.find((item: any) => item.viewId === viewId)
      if (!activeView) {
        activeView = viewList[0]
      }
      return activeView
    },

    /**
     *
     * @param menu
     * @param state
     * @returns
     */
    async toggeleView(activeView: ViewTab, state) {
      const { navigatorId } = state.viewSetting
      if (navigatorId) {
        //缓存每个快捷选项的视图类型
        setStorage(`${Const.NavigationView}-${navigatorId}`, activeView.viewId)
      }
      dispatch.viewSetting.setCurrentViewTab(activeView)
      const { type } = activeView
      if (type === ViewType.list) {
        dispatch.task.initData()
      } else if (type === ViewType.timeline) {
        dispatch.gantt.initData()
      } else if (type === ViewType.kanban) {
        dispatch.kanban.initData()
      } else if (type === ViewType.calendar) {
        dispatch.todoCalendar.initData()
      }
      if (getPageIsProjectGroup()) {
        return
      }
      dispatch.viewSetting.refreshData({})
      dispatch.viewSetting.setInitialSearchParmas({})
    },
    /**
     * 刷新视图数据
     * @param opt
     * @param state
     */
    async refreshData(opt: any, state) {
      const { navigatorId } = state.viewSetting
      const { type } = state.viewSetting.currentViewTab || {}

      if (type === ViewType.list) {
        dispatch.task.getTodoList({
          page: 1,
        })
      } else if (type === ViewType.timeline) {
        dispatch.gantt.getTodoList({
          page: 1,
          onlyGetData: false,
        })
      } else if (type === ViewType.kanban) {
        dispatch.kanban.getKanbanGroupList({ page: 1 })
      } else if (type === ViewType.calendar) {
        //TODO 更新计划视图数据
        dispatch.todoCalendar.getCalendarTodoList({})
        dispatch.todoCalendar.getUnplannedList({ page: 1 })
      }
    },
    /**
     * 更新数据
     * @param opt
     * @param opt.refreshCount 是否刷新总数
     * @param opt.refreshList 是否刷新列表
     * @param opt.detail 详情数据, 用于更新列表，传入该数据后，则只在前端本地处理数据，不拉取数据更新
     * @param opt.actionType 更新类型, delete: 删除, replace: 替换
     * @param state
     * @returns
     */
    async refreshDataByDataChange(
      opt: {
        refreshCount?: boolean
        refreshList: boolean
        detail?: TodoInfo
        actionType?: 'delete' | 'replace'
        detailPayload?: Record<string, any>
      },
      state,
    ) {
      // 根据详情更新列表
      const { refreshCount, refreshList, detail, actionType, ...rest } = opt
      const { type } = state.viewSetting.currentViewTab!
      if (refreshCount) {
        if (type) {
          dispatch.viewSetting.getNavigatorTaskCount()
        }
      }
      try {
        if (refreshList) {
          if (type === ViewType.list) {
            if (detail) {
              dispatch.task.updateTaskInfo({ taskInfo: detail, actionType, ...rest })
              return
            }
            const { dataList } = state.task
            return dispatch.task.getTodoList({
              page: 1,
              size: dataList.length || 100,
            })
          } else if (type === ViewType.timeline) {
            if (detail) {
              dispatch.gantt.updateTaskInfo({ taskInfo: detail, actionType, ...rest })
              return
            }

            return dispatch.gantt.getTodoList({
              page: 1,
            })
          } else if (type === ViewType.kanban) {
            // if (detail) {
            //   dispatch.kanban.updateTaskInfo({ taskInfo: detail, actionType });
            //   return;
            // }
            dispatch.kanban.getKanbanGroupList({ page: 1 })
          } else if (type === ViewType.calendar) {
            //TODO 更新计划视图数据
            const { unplannedList } = state.todoCalendar
            dispatch.todoCalendar.getCalendarTodoList({})
            dispatch.todoCalendar.getUnplannedList({ page: 1, size: unplannedList.length })
          } else if (!type) {
            if (detail) {
              dispatch.imTodoList.updateTaskInfo({ taskInfo: detail, actionType, ...rest })
              return
            }
            const { dataList, sessionId } = state.imTodoList
            // 快速刷popo自带的菜单, 接口还为返回视图类型, 误操作
            if (sessionId) {
              if (detail) {
                dispatch.imTodoList.updateTaskInfo({ taskInfo: detail, actionType, ...rest })
                return
              }
              dispatch.imTodoList.getTodoList({
                page: 1,
                size: dataList.length || 100,
              })
            }
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    async updateSetting(options: any, state) {
      const { viewTabList, currentViewTab } = state.viewSetting
      const {
        viewId,
        conditions,
        queryGroupBy,
        queryGroupBys,
        querySort,
        displays,
        showWeekend,
        planViewMode,
        viewConfig,
      } = currentViewTab || {}
      // 过滤掉conditions中未选值的数据
      const _conditions = conditions?.filter(item => !!item.values?.length)
      const params = {
        viewId: viewId,
        conditions: _conditions,
        displays,
        queryGroupBy: !queryGroupBy?.fieldName ? undefined : queryGroupBy,
        queryGroupBys,
        querySort,
        showWeekend,
        planViewMode,
        viewConfig,
      }

      let _newcurrentViewTab: ViewTab = {} as ViewTab
      const _viewTabList = viewTabList.map(item => {
        if (item.viewId === params.viewId) {
          _newcurrentViewTab = {
            ...item,
            ...params,
          }
          return _newcurrentViewTab
        }
        return {
          ...item,
        }
      })
      dispatch.viewSetting.setData({
        viewTabList: _viewTabList,
      })

      //特殊处理, 服务端需要传递None
      if (!queryGroupBy?.fieldName) {
        delete params.queryGroupBy
        params.queryGroupBy = {
          fieldName: NONE_GROUP_BY,
        }
      }
      return apiNavigatorViewUpdateSetting(params)
    },
    /**
     * 分组操作
     * @param groupBy
     */
    async filterByGroup(groupBy: string, state) {
      if (!groupBy) {
        dispatch.viewSetting.updateCurrentView({
          queryGroupBy: {
            fieldName: '',
          },
        })
      } else {
        let isCustomField = !(groupBy in EnumField)
        dispatch.viewSetting.updateCurrentView({
          queryGroupBy: isCustomField
            ? {
                fieldName: CustomFieldName,
                customFieldId: groupBy,
                customFieldVersion: state.viewSetting.customFields.find(f => f.fieldId == groupBy)?.fieldVersion,
              }
            : { fieldName: groupBy },
        })
      }

      // if (type === ViewType.list) {
      //   if (groupBy) {
      //     await dispatch.task.getGroupList({});
      //   }
      // }
      dispatch.viewSetting.refreshData({})
    },
    /**
     * 多条件分组操作
     * @param groupBy
     */
    async filterByGroups(groupBys: any[] = [], state) {
      if (!groupBys?.length) {
        dispatch.viewSetting.updateCurrentView({
          queryGroupBys: [],
        })

        dispatch.viewSetting.refreshData({})
      } else {
        // const customFieldsMap = state.viewSetting.customFields.reduce((acc, cur) => {
        //   acc[cur.fieldId] = cur;
        //   return acc;
        // }, {} as Record<string, CustomField>);

        // 处理 groupBys
        // const queryGroupBys = groupBys?.map((groupBy) => {
        //   let isCustomField = !(groupBy?.fieldName in EnumField);
        //   return isCustomField
        //     ? {
        //         fieldName: CustomFieldName,
        //         customFieldId: groupBy?.fieldName,
        //         customFieldVersion: customFieldsMap[groupBy?.fieldName]?.fieldVersion,
        //       }
        //     : groupBy;
        // });

        dispatch.viewSetting.updateCurrentView({
          queryGroupBys: groupBys,
        })
      }

      const { type } = state.viewSetting.currentViewTab!

      if (type === ViewType.list) {
        dispatch.task.updateTreeList({})
      } else if (type === ViewType.timeline) {
        dispatch.gantt.updateTreeList({})
      }

      // dispatch.viewSetting.refreshData({});
    },
    /**
     * 修改字段操作
     * @param opt
     */
    async changeDisplayFields(opt: { displays: ViewInfo['displays'] }) {
      const { displays } = opt
      //先设置本地值,在修改远程数据
      dispatch.viewSetting.updateCurrentView({ displays })
    },
    /**
     * 排序操作
     * @param groupBy
     */
    async sort(order: Required<OrderOption>, state) {
      let isCustomField = !(order.orderBy in EnumField || order.orderBy === 'customize')
      dispatch.viewSetting.updateCurrentView({
        querySort: !isCustomField
          ? {
              fieldName: order.orderBy,
              order: order.order,
            }
          : {
              fieldName: CustomFieldName,
              customFieldId: order.orderBy,
              customFieldVersion: state.viewSetting.customFields.find(f => f.fieldId == order.orderBy)?.fieldVersion,
              order: order.order,
            },
      })

      // 排序埋点
      dispatch.user.trackingByView({
        key: EnumTrackeKey.TaskSortOpt,
        order: order.order!,
        orderBy: order.orderBy!,
      })
      dispatch.viewSetting.refreshData({})
    },
    /**
     * 普通字段筛选
     */
    async changeCondition(opt: { conditions: Icondition[] }, state) {
      dispatch.viewSetting.updateCurrentView({ conditions: opt.conditions })
      dispatch.viewSetting.refreshData({})
      if (state.viewSetting.currentViewTab.viewDistribution === ViewTypeEnum.Personal) {
        dispatch.viewSetting.updateSetting({})
      }
    },
    /**
     * 更新当前视图的数据
     */
    updateCurrentView(data: Partial<ViewTab>, state) {
      let currentView = state.viewSetting.currentViewTab
      let newView = { ...currentView, ...data }

      const hasViewChanged = data.hasViewChanged || getViewChanged(state, newView)

      dispatch.viewSetting.setData({
        currentViewTab: {
          ...newView,
          hasViewChanged,
          changeSearchParams: true,
        },
      })

      if (newView.viewDistribution === ViewTypeEnum.Personal || isTaskMenuId(newView.navigatorId!)) {
        dispatch.viewSetting.updateSetting({})
      }
    },

    updateViewConfig(data, state) {
      const { parsedViewConfig } = state.viewSetting.currentViewTab
      const newConfig = {
        ...parsedViewConfig,
        ...data,
      }
      dispatch.viewSetting.updateCurrentView({
        parsedViewConfig: newConfig,
        viewConfig: JSON.stringify(newConfig),
      })
    },
    /**
     * 更新当前视图的数据并上传数据
     */
    updateCurrentViewAndUpdateServer(data: Partial<ViewTab>, state) {
      dispatch.viewSetting.updateCurrentView(data)
      if (data.viewDistribution === ViewTypeEnum.Personal) {
        dispatch.viewSetting.updateSetting({})
      }
    },
    /**
     * 获取权限
     * @param opt
     * @param state
     */
    async updatePermissions(opt: { permissions?: ProjectPermissionEnum[] }, state) {
      const { navigatorId, permissions } = state.viewSetting
      if (!isTaskMenuId(navigatorId) && !isProjectId(navigatorId)) {
        return
      }
      const { permissions: _keys = [] } = opt
      let keys = []
      let list: { name: ProjectPermissionEnum; value: boolean }[] = []
      if (isTaskMenuId(navigatorId!)) {
        //快捷导航  新增任务权限 CAN_CREATE_TASK
        if (_keys.length) {
          keys = _keys
        } else {
          keys = [ProjectPermissionEnum.CAN_CREATE_TASK]
        }
        list = [
          {
            name: ProjectPermissionEnum.CAN_CREATE_TASK,
            value: navigatorId === TaskNavigatorType.myFollow ? false : true,
          },
        ]
      } else {
        if (_keys.length) {
          keys = _keys
        } else {
          keys = ProjectDetailPermissions
        }
        list = (await apiProjectCheckRolePost(
          {
            roles: keys,
            projectId: navigatorId! as number,
          },
          { errorSilent: getIsInSession() },
        )) as { name: ProjectPermissionEnum; value: boolean }[]
      }
      // 如果有值 表示是更新权限
      if (_keys.length) {
        list = permissions.filter(item => !list.find(v => v.name === item.name)).concat(list)
      }
      dispatch.viewSetting.setData({
        permissions: list,
      })
      return list
    },

    /**
     * 项目自定义字段获取
     */
    async fetchCustomFields(projectId: any): Promise<CustomField[]> {
      let res = await apiProjectFieldListPost({
        projectId,
      })
      let fields = (res.fields || []).map(item => {
        let id = item.fieldId!
        let shcema = JSON.parse(item.fieldSchema!) as FieldSchema
        return {
          ...shcema,
          fieldId: id,
          fieldVersion: item.fieldVersion,
        }
      })
      dispatch.viewSetting.setData({ customFields: fields })
      return fields
    },
    /**
     * 获取视图列表
     */
    async fetchViewList(projectId = '', state) {
      projectId = projectId || state.viewSetting.navigatorId
      let viewList: ViewInfo[] = await apiGetViewList(projectId)

      let newList: ViewTab[] = viewList.map(item => {
        return {
          ...item,
          id: item.viewId,
          distribution: item.viewDistribution!,
          type: item.viewType!,
          permissions: [],
        }
      })
      const viewPermissions = await getBatchViewCheckParams(newList)
      newList.forEach(item => {
        item.permissions = viewPermissions[item.id].permissions
      })

      newList.forEach(item => {
        //看板默认分组
        if (item.viewType === ViewType.kanban) {
          if (!item.queryGroupBy?.fieldName) {
            item.queryGroupBy = {
              fieldName: EnumGroupBy.DEADLINE!,
            }
          }
          if (!item.queryGroupBys?.length) {
            item.queryGroupBys = [
              {
                fieldName: EnumGroupBy.DEADLINE!,
              },
            ]
          }
        }

        // 兼容线上之前的分组方式
        if ([ViewType.list, ViewType.timeline].includes(item.viewType)) {
          if (!item.queryGroupBys?.length && (item.queryGroupBy?.fieldName || item.queryGroupBy?.customFieldId)) {
            item.queryGroupBys = [item.queryGroupBy]
          }
        }

        parseViewConfig(item)

        if (item.viewType === ViewType.list && item.queryGroupBy?.fieldName === EnumFieldNONE.NONE) {
          delete item.queryGroupBy
        }
      })
      // 更新项目自定义字段
      if (!isTaskMenuId(projectId)) {
        const fields = await dispatch.viewSetting.fetchCustomFields(projectId)
        newList = newList.map(view => normalizeView(view, fields))
        dispatch.viewSetting.setViewTabList(newList)
      } else {
        //个人维度，清除自定义字段
        dispatch.viewSetting.setData({ viewTabList: newList, customFields: [] })
      }
      dispatch.viewSetting.setViewTabList(newList)
      return newList
    },
    /**
     * 添加和编辑自定义字段成功后，更新本地数据
     */
    async updateCustomField(
      { fieldId, schema, fieldVersion }: { fieldId: any; schema: FieldSchema; fieldVersion: any },
      state,
    ) {
      let fields = [...state.viewSetting.customFields]
      let index = fields.findIndex(field => field.fieldId == fieldId)
      if (index > -1) {
        fields.splice(index, 1, { fieldId, ...schema, fieldVersion })
      } else {
        fields.push({ fieldId, ...schema, fieldVersion })
      }
      dispatch.viewSetting.setData({ customFields: fields })
    },
    /**
     * 删除自定一字段
     */
    async deleteCustomField(fieldId: any, state) {
      let fields = [...state.viewSetting.customFields]
      let index = fields.findIndex(field => field.fieldId == fieldId)
      if (index > -1) {
        fields.splice(index, 1)
        let newViewList = state.viewSetting.viewTabList.map(view => normalizeView(view, fields))
        let curView = normalizeView(state.viewSetting.currentViewTab, fields)
        dispatch.viewSetting.setData({
          customFields: fields,
          viewTabList: newViewList,
          currentViewTab: curView,
        })
      }
    },

    /**
     * 获取对应tab下的list数据
     * @param param0
     * @param state
     * @returns
     */
    getTableList: ({}, state) => {
      const { type } = state.viewSetting.currentViewTab || {}
      if (type === ViewType.list) {
        return state.task.treeList
      } else if (type === ViewType.timeline) {
        return state.gantt.dataList
      } else if (type === ViewType.kanban) {
        return state.kanban.groupList
      } else if (type === ViewType.calendar) {
        return state.todoCalendar.unplannedList
      } else {
        return state.imTodoList.dataList
      }
    },

    updateItemByDetail(
      {
        taskId,
        parentId,
        detail,
        actionType,
        detailPayload = {},
        ...rest
      }: {
        taskId?: number
        parentId?: number
        detail?: TodoInfo
        actionType?: 'delete' | 'replace'
        targetTaskId?: number
        detailPayload?: Record<string, any>
      },
      state,
    ) {
      return new Promise((resolve, reject) => {
        if (detail) {
          dispatch.viewSetting
            .refreshDataByDataChange({
              refreshList: true,
              refreshCount: false,
              detail: detail,
              actionType,
              ...rest,
            })
            .then(resolve)
        } else if (taskId) {
          if (parentId) {
            dispatch.permissions.setPermissionsMap({ list: [{ taskId }] })
          }
          dispatch.detail.getDetailInfo({ taskId: parentId || taskId }).then(res => {
            dispatch.viewSetting.refreshDataByDataChange({
              refreshList: true,
              refreshCount: false,
              detail: { ...res, ...detailPayload },
              actionType,
              ...rest,
            })
            resolve(res)
          })
        }
      })
    },

    setInitialSearchParmas(__, state) {
      const { currentViewTab, customFields, navigatorId } = state.viewSetting
      let {
        conditions,
        querySort,
        queryGroupBy = {},
        displays = [],
        planViewMode,
        parsedViewConfig,
        queryGroupBys,
      } = currentViewTab
      /**
       * 做一层兼容，方便后续对比
       * 因为在服务端返回的数据中，queryGroupBy中的fieldName字段可能不存在
       */
      _.defaults(queryGroupBy, { fieldName: '' })
      dispatch.viewSetting.setData({
        currentViewTab: {
          ...currentViewTab,
          initialSearchParams: {
            conditions,
            querySort,
            queryGroupBy,
            displays: combineDisplayFields(displays as DisplayField[], customFields, navigatorId),
            planViewMode: planView2ScaleType(planViewMode),
            cardConfig: parsedViewConfig?.cardConfig,
            queryGroupBys,
          },
        },
      })
    },

    /**
     * 记录并更新项目中任务自定义排序下的所有排序操作
     * @param orderParam ProjectTaskResortParams
     * @param state
     */
    updateProjectTaskOrder(
      payload: {
        orderParam: ProjectTaskResortParams
        hasViewChanged?: boolean
      },
      state,
    ) {
      const currentView = state.viewSetting.currentViewTab
      const { orderParam, hasViewChanged = false } = payload

      const currentProjectTaskParamList = currentView.projectTaskResortParamsList || []

      const newProjectTaskParamList = currentProjectTaskParamList?.concat(orderParam)

      dispatch.viewSetting.updateCurrentView({
        projectTaskResortParamsList: newProjectTaskParamList,
        hasViewChanged,
      })
    },
  }),
})
