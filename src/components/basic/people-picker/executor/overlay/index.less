.panel {
  width: 240px;
  //padding: 12px;
  cursor: auto;

  .panelSearchPd {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 0 8px;
  }
  .input {
    flex: 1;
    height: 40px;
    border: none;
    padding-left: 4px !important;
    padding-right: 0 !important;
  }
  .verticalLine {
    margin: 0 8px;
  }
  .iconAdd {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    text-align: center;
    font-size: 14px;
    color: var(--IconPrimary);
    cursor: pointer;
    i {
      color: var(--IconPrimary);
    }
  }
}
.searchingPanel {
  max-height: 320px;
  min-height: 320px;
  .divider {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
}
.divider {
  margin: 0;
  flex-shrink: 0;
}
.container {
  display: flex;
  flex: 1;
  min-height: 0px;
}
.list {
  flex: 1;
  //padding: 4px 12px;
  width: 100%;
}

.participantTip {
  height: 30px;
  line-height: 30px;
  padding: 4px 8px;
  font-size: 12px;
  text-align: left;
  color: var(--TextSecondary);
}

.footer__item.footer__item {
  padding-left: 10px;
  padding-right: 10px;
}

.footer__option {
  column-gap: 12px;
  margin-bottom: 8px;
  :global {
    .rock-btn {
      flex: 1;
    }
  }
}

.divider + .footer__option {
  margin-top: 8px;
}
