import { pp } from '@popo-bridge/web';
import { useMount } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { history } from 'umi';

import { Separate, ToggleMenu } from '@/components/basic';
import FollowGuide from '@/components/guide/follow-guide';
import useGuide from '@/components/guide/useGuide';
import LoadingPage from '@/components/loading-page';
import PageErrorBoundary from '@/components/page-error-boundary';
import { Dispatch, RootState } from '@/models/store';
import { isTaskMenuId } from '@/models/utils';
import { getUrlParams } from '@/utils';
import { GuideType, TaskNavigatorMap, TaskNavigatorType } from '@/utils/const';
import { EnumTrackeKey } from '@/utils/skyline';

import PageTasks from '../components/page-tasks';
import s from './index.less';

const Task: React.FC = () => {
  const { navigatorId } = useSelector((state: RootState) => ({
    navigatorId: state.viewSetting.navigatorId as TaskNavigatorType,
  }));

  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.menuFollow,
  });
  const [delay, setDelay] = useState(true);

  const dispatch = useDispatch<Dispatch>();
  // 如果带id 默认打开详情
  const { location } = history;
  const { id } = getUrlParams(location.search);
  if (id) {
    dispatch.detail.openDetail(Number(id));
    if (location.search) {
      history.replace(location.pathname);
    }
  }
  useMount(() => {
    window.POPOTaskTrackTimeMap = {
      ...(window.POPOTaskTrackTimeMap || {}),
      pagesNew: Date.now(), //进入pages-new时间
    };
    let timeArr: { point: string; time: number }[] = [];
    Object.keys(window.POPOTaskTrackTimeMap).forEach((key) => {
      timeArr.push({
        point: key,
        //@ts-ignore
        time: window.POPOTaskTrackTimeMap[key],
      });
    });
    //@ts-ignore
    timeArr = timeArr
      .sort((a, b) => a.time - b.time)
      .map((item) => ({
        point: item.point,
        time: item.time ? dayjs(item.time).format('YYYY-MM-DD HH:mm:ss.SSS') : '',
      }));
    const _data = {
      key: EnumTrackeKey.LoadingPointsTime,
      times: timeArr,
    };
    dispatch.user.tracking(_data);
    try {
      pp.log({
        key: EnumTrackeKey.LoadingPointsTime,
        times: JSON.stringify(timeArr),
      });
    } catch (error) {
      console.log(error);
    }
  });

  useEffect(() => {
    if (navigatorId === TaskNavigatorType.myFollow) {
      getGuideData();
    }
  }, [navigatorId]);
  const memoShowGuide = useMemo(() => {
    return showGuide && navigatorId === TaskNavigatorType.myFollow;
  }, [showGuide, navigatorId]);

  useEffect(() => {
    //增加本地延迟加载500ms
    setDelay(true);
    setTimeout(() => {
      setDelay(false);
    }, 500);
  }, [navigatorId]);

  //如果路由不对 直接返回
  if (!isTaskMenuId(navigatorId)) {
    return null;
  }
  return (
    <PageErrorBoundary
      errorId="Page-Task-Render-Error"
      //@i18n-ignore
      errorMsg="任务界面异常"
    >
      {memoShowGuide ? (
        <FollowGuide onHideGuide={() => handleHideGuide()} />
      ) : (
        <div className={s.page}>
          <div className={s.head}>
            <div className={s.headLeft}>
              <ToggleMenu></ToggleMenu>
              <div className={s.title}>{navigatorId ? TaskNavigatorMap[navigatorId].name : ''}</div>
            </div>
            <Separate className={s.headRight} />
          </div>
          <PageTasks></PageTasks>
          <LoadingPage className={classNames(s.loading, { [s.hidden]: !delay })}></LoadingPage>
        </div>
      )}
    </PageErrorBoundary>
  );
};
export default Task;
