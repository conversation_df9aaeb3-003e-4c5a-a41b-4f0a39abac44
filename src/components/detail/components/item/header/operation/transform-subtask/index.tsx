import React, { useContext, useEffect, useState, useRef, useLayoutEffect } from 'react'
import { IconBtn, Modal } from '@/components/basic'
import I18N from '@/utils/I18N'
import { apiTaskAttachSubtaskPost, apiV3TaskRecentTitlesByKeywordPost, apiV3TaskRecentTitlesPost } from '@/api'

import s from './index.less'
import { useDebounceFn } from 'ahooks'
import { Data16Sub, GlobalLoading1, ToolSearch1 } from '@babylon/popo-icons'
import * as _ from 'lodash'
import { useSelector, useDispatch } from 'react-redux'
import { Dispatch, RootState } from '@/models/store'
import message from '@/components/basic/message'
import classNames from 'classnames'
import { ConfigProvider, Dropdown, Input, Menu } from '@bedrock/components'
import { getIsInSession } from '@/models/utils'

export interface Props {
  onTransform?: () => void
  iconBtnClass?: string
}

const RECENT_TASK_COUNT = 5 // 最近创建的任务数量，可根据需要调整

const TransformSubtask: React.FC<Props> = props => {
  const { iconBtnClass } = props
  const [visible, setVisible] = useState(false)
  const [value, setValue] = useState('')
  const [keyword, setKeyword] = useState('')
  const [list, setList] = useState<any[]>([]) // 最近创建的任务名称列表，可根据需要调整类型和初始值
  const [loading, setLoading] = useState(false)
  const { taskId } = useSelector((state: RootState) => {
    return {
      taskId: state.detail.taskId,
    }
  })
  const dispatch = useDispatch<Dispatch>()
  const inputRef = useRef(null)
  const dropDownRef = useRef(null)
  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext)

  const modalZIndex = getGlobalZIndex() + 10000
  const dropdownZIndex = getGlobalZIndex() + 100001

  const onTransformClick = () => {
    setVisible(true)
  }

  // 获取最近创建好的五个父任务名称
  const getRecentTitle = () => {
    return apiV3TaskRecentTitlesPost({
      count: RECENT_TASK_COUNT,
      ignoreTaskId: taskId,
    }).then(res => {
      if (res?.titles) {
        const resList = res?.titles
          ?.sort((a, b) => {
            return b.createTime! - a.createTime!
          })
          .slice(0, RECENT_TASK_COUNT)
          ?.map(item => {
            return {
              name: item.title,
              value: item.taskId,
            }
          })
        setList(resList || [])
      }
    })
  }

  useEffect(() => {
    getRecentTitle()
  }, [])

  const { run: debouncedSearch } = useDebounceFn(
    (v: string) => {
      if (v) {
        apiV3TaskRecentTitlesByKeywordPost({
          keyword: v,
          ignoreTaskId: taskId,
        })
          .then(res => {
            if (res?.titles?.length) {
              setList(
                res.titles.map(item => ({
                  name: item.title,
                  value: item.taskId,
                })),
              )
            } else {
              setList([])
            }
            setLoading(false)
          })
          .catch(console.error)
          .finally(() => {
            setLoading(false)
          })
      } else {
        getRecentTitle().finally(() => {
          setLoading(false)
        })
      }
    },
    { wait: 300 },
  )

  const handleSearchChange = (v: string) => {
    changeDropdownWidth()
    if (!v) {
      setList([])
    }
    setLoading(true)
    debouncedSearch(v)
    setKeyword(v)
    setValue(v)
  }

  const handleOkClick = () => {
    // 调用接口 任务转为子任务并且关闭弹窗
    if (value) {
      apiTaskAttachSubtaskPost({
        parentTaskId: Number(value),
        taskId,
      })
        .then(res => {
          // 刷新详情页并且关闭弹窗
          dispatch.detail.getDetailInfo({ taskId })

          // 刷新列表
          dispatch.viewSetting.refreshDataByDataChange({
            refreshList: true,
          })
          setVisible(false)
        })
        .catch(err => {
          message.error(err?.message)
        })
    }
  }

  const handleClearClick = () => {
    setValue('')
    setKeyword('')
    setList([])
  }

  const renderContent = () => {
    const listContent = list.map(i => <Menu.Item key={i.value}>{i.name}</Menu.Item>)
    if (list.length > 0) {
      if (!keyword && !loading) {
        return (
          <>
            <div className={s.recent}>{I18N.auto.recent}</div>
            {listContent}
          </>
        )
      }
      return listContent
    }

    return (
      <Menu.Item key="noContent" className={s.noContent}>
        <div style={{ color: 'var(--text-color-3)' }}>{I18N.auto.searchNoResult}</div>
      </Menu.Item>
    )
  }

  const handleFocus = () => {
    if (!value && list.length === 0) {
      setLoading(true)
      getRecentTitle().then(() => {
        setLoading(false)
      })
    }

    changeDropdownWidth()
  }

  const changeDropdownWidth = () => {
    const menu = document.getElementById('task-dropdown-menu')
    const input = inputRef.current?.input
    if (menu && input) {
      const parentWidth = input?.parentElement?.parentElement?.clientWidth + 2
      menu.style.width = `${parentWidth || 356}px`
    }
  }

  return (
    <span className="ml-10">
      <IconBtn
        title={I18N.auto.transformToSubtask}
        className={iconBtnClass}
        icon={<Data16Sub />}
        onClick={onTransformClick}
      />
      <Modal
        className="taskModal"
        centered
        title={I18N.auto.setParentTask}
        wrapClassName={classNames(s.modalWrapper)}
        visible={visible}
        width={400}
        closable
        mask={!getIsInSession()}
        onCancel={() => {
          handleClearClick()
          setVisible(false)
        }}
        onOk={handleOkClick}
        zIndex={modalZIndex}
        okButtonProps={{
          disabled: !list.find(i => String(i.value) === value),
        }}
      >
        <Dropdown
          ref={dropDownRef}
          overlay={
            <Menu
              id="task-dropdown-menu"
              className={s.ulWrapper}
              selectedKeys={[value]}
              onClick={value => {
                setValue(value.key)
              }}
            >
              {loading ? (
                <Menu.Item key="loading" className={s.loading}>
                  <div>
                    <IconBtn iconClassName={s.icon} icon={<GlobalLoading1 />} />
                  </div>
                </Menu.Item>
              ) : (
                renderContent()
              )}
            </Menu>
          }
          forceRender
          trigger="click"
          getPopupContainer={() => document.body}
          zIndex={dropdownZIndex}
          minOverlayWidthMatchTrigger
        >
          <Input.Search
            ref={inputRef}
            prefix={<ToolSearch1 className={s.searchIcon} />}
            value={list.find(i => String(i.value) === value)?.name || value}
            style={{ width: '100%' }}
            placeholder={I18N.auto.searchTask}
            onFocus={handleFocus}
            onClear={handleClearClick}
            onChange={e => {
              handleSearchChange(e.target.value)
            }}
            allowEnterSearch={false}
          />
        </Dropdown>
      </Modal>
    </span>
  )
}

export default TransformSubtask
