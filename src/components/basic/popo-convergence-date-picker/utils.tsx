/**
 * rrule 规则参考链接 https://github.com/jakubroztocil/rrule
 */
import dayjs, { Dayjs } from 'dayjs';
import { RRule, rrulestr, Weekday } from 'rrule';
export { FORMAT_DD, FORMAT_DD_mm, PPTimeFormat } from '@/utils/const';
import { TimeValue } from '@/types';
import { getCalendarFormat } from '@/utils/date-format';
import I18N, { getCurrentLang } from '@/utils/I18N';

export type { TimeValue };

export enum RepeatType {
  None = 0,
  WorkDay = 1,
  Dayly = 2,
  Weekly = 3,
  Biweekly = 4,
  Monthly = 5,
  Yearly = 6,
  ThreeDays = 7,
}

export interface RepeatOption {
  name: string;
  value: RepeatType;
}

export const repeatOptions: RepeatOption[] = [
  { name: I18N.auto.nothing, value: RepeatType.None },
  { name: I18N.auto.everyWorkingDay, value: RepeatType.WorkDay },
  { name: I18N.auto.everyDay, value: RepeatType.Dayly },
  { name: I18N.auto.everyDay_2, value: RepeatType.ThreeDays },
  { name: I18N.auto.weekly, value: RepeatType.Weekly },
  { name: I18N.auto.biweekly, value: RepeatType.Biweekly },
  { name: I18N.auto.monthly, value: RepeatType.Monthly },
  { name: I18N.auto.annually, value: RepeatType.Yearly },
];

const repeatOptionsMap = repeatOptions.reduce((pre, cur) => {
  pre[cur.value] = cur.name;
  return pre;
}, {} as { [k in RepeatType]: string });

export const getRepeatNameByRRule = (rrule: string) => {
  const value = getRepeatTypeByRuleStr(rrule)!;
  return repeatOptionsMap[value] || '';
};

// 15分钟增加一列选项
export const getTimePeriodOptions = () => {
  const arr: { name: string; value: string }[] = [];
  let date = dayjs().startOf('day');
  const copyDate = date.add(1, 'day');
  while (!date.isSame(copyDate)) {
    arr.push({
      value: date.format('HH:mm'),
      name: date.format('HH:mm'),
    });
    date = date.add(15, 'minutes');
  }
  return arr;
};

/**
 * 获取这一天的0时0秒
 * @param date
 */
export const getDayStart = (date: number | Dayjs) => {
  return dayjs(date).startOf('D');
};

export const getDayStartTimeValue = (date: number | Dayjs) => {
  if (!date) {
    return 0;
  }
  return dayjs(date).startOf('D').valueOf();
};

/**
 * 获取这一天的23:59:59
 * @param date
 */
export const getDayEnd = (date: number | Dayjs) => {
  return dayjs(date).endOf('D');
};

/**
 * 获取当前时间距离当天零时的毫秒数
 * @param date
 * @returns
 */
export const getTimePeriodMilliseconds = (date: Dayjs | number) => {
  return dayjs(date).valueOf() - dayjs(date).startOf('D').valueOf();
};

export const getHourMilliseconds = (str: string) => {
  const dateStr = `${dayjs().format('YYYY-MM-DD')} ${str}`;
  const date = dayjs(dateStr);
  return getTimePeriodMilliseconds(date);
};

/**
 * 根据毫秒数获取当前的小时分钟字符串
 * @param milliseconds 必须是一天以内的毫秒时间
 * @returns
 */
export const getHourStr = (milliseconds: number) => {
  const date = getDayStart(dayjs()).add(milliseconds, 'milliseconds');
  return date.format('HH:mm');
};

/**
 * 获取当前准确时间
 * @param milliseconds 必须是一天以内的毫秒时间
 * @returns
 */
export const revisionTime = (time: number | Dayjs, milliseconds = 0) => {
  if (!time) {
    return undefined;
  }
  return getDayStart(dayjs(time)).add(milliseconds, 'milliseconds').valueOf();
};

// export const repeatOptions = [
//   { name: '每个工作日(周一到周五)', value: 'RRULE:FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,TU,WE,TH,FR' },
//   { name: '每天', value: 'RRULE:FREQ=DAILY;INTERVAL=1;' },
//   { name: '每周', value: 'RRULE:FREQ=WEEKLY;INTERVAL=1;' },
//   { name: '每双周', value: 'RRULE:FREQ=WEEKLY;INTERVAL=2;' },
//   { name: '每月', value: 'RRULE:FREQ=MONTHLY;INTERVAL=1;' },
//   { name: '每年', value: 'RRULE:FREQ=YEARLY;INTERVAL=1;' },
// ];

/**
 * 获取每个工作日(周一到周五)的规则
 * @returns RRule string
 */
export const getWorkDayRRule = () => {
  const rule = new RRule({
    freq: RRule.WEEKLY,
    interval: 1,
    byweekday: [RRule.MO, RRule.TU, RRule.WE, RRule.TH, RRule.FR],
  });
  return rule.toString();
};

/**
 * 获取每天规则
 * @returns RRule string
 */
export const getEveryDayRRule = () => {
  const rule = new RRule({
    freq: RRule.DAILY,
    interval: 1,
  });
  return rule.toString();
};

/**
 * 获取每3天规则
 * @returns RRule string
 */
export const getEvery3DayRRule = () => {
  const rule = new RRule({
    freq: RRule.DAILY,
    interval: 3,
  });
  return rule.toString();
};

/**
 *获取每周规则
 * @param byweekday
 * @returns RRule string
 */
export const getEveryWeeklyRRule = (byweekday: Weekday[]) => {
  const rule = new RRule({
    freq: RRule.WEEKLY,
    interval: 1,
    byweekday: byweekday,
  });
  return rule.toString();
};

/**
 *获取每3周规则
 * @param byweekday
 * @returns RRule string
 */
export const getEvery3WeeklyRRule = (byweekday: Weekday[]) => {
  const rule = new RRule({
    freq: RRule.WEEKLY,
    interval: 3,
    byweekday: byweekday,
  });
  return rule.toString();
};

/**
 *获取双每周规则
 * @param byweekday
 * @returns RRule string
 */
export const getEveryTwoWeeklyRRule = (byweekday: Weekday[]) => {
  const rule = new RRule({
    freq: RRule.WEEKLY,
    interval: 2,
    byweekday: byweekday,
  });
  return rule.toString();
};

/**
 * 获取每月规则
 * @param bymonthday
 * @returns string
 */
export const getEveryMonthlyRRule = (bymonthday: number[]) => {
  const rule = new RRule({
    freq: RRule.MONTHLY,
    interval: 1,
    bymonthday: bymonthday,
  });
  return rule.toString();
};

/**
 * 获取每年规则
 * @param bymonth
 * @param bymonthday
 * @returns RRule string
 */
export const getEveryYearlyRRule = (bymonth: number[], bymonthday: number[]) => {
  const rule = new RRule({
    freq: RRule.YEARLY,
    interval: 1,
    bymonth: bymonth,
    bymonthday: bymonthday,
  });
  return rule.toString();
};

// console.log(rrulestr('RRULE:FREQ=YEARLY;INTERVAL=1;BYMONTH=2;BYMONTHDAY=24'));
// console.log(rrulestr(getWorkDayRRule()));
// console.log(getWorkDayRRule());
// console.log(getEveryYearlyRRule());

/**
 * 根据rrule的规则字符串获得rrule对象
 * @param str
 * @returns RRule
 */
export const getRRuleByRuleStr = (str: string) => {
  return rrulestr(str);
};

/**
 * 根据rrule的字符串规则判断当前的选项值
 * @param str
 */
export const getRepeatTypeByRuleStr = (str: string) => {
  const { options } = rrulestr(str.replace(/;$/, ''));
  const { freq, interval, byweekday } = options;
  //   { name: '每个工作日(周一到周五)', value: 'RRULE:FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,TU,WE,TH,FR' },
  if (freq === RRule.WEEKLY && interval === 1 && byweekday.join() === '0,1,2,3,4') {
    return RepeatType.WorkDay;
  }
  //   { name: '每天', value: 'RRULE:FREQ=DAILY;INTERVAL=1;' },
  if (freq === RRule.DAILY && interval === 1) {
    return RepeatType.Dayly;
  }
  //   { name: '每3天', value: 'RRULE:FREQ=DAILY;INTERVAL=3;' },
  if (freq === RRule.DAILY && interval === 3) {
    return RepeatType.ThreeDays;
  }
  //   { name: '每周', value: 'RRULE:FREQ=WEEKLY;INTERVAL=1;' },
  if (freq === RRule.WEEKLY && interval === 1) {
    return RepeatType.Weekly;
  }
  //   { name: '每双周', value: 'RRULE:FREQ=WEEKLY;INTERVAL=2;' },
  if (freq === RRule.WEEKLY && interval === 2) {
    return RepeatType.Biweekly;
  }
  //   { name: '每月', value: 'RRULE:FREQ=MONTHLY;INTERVAL=1;' },
  if (freq === RRule.MONTHLY && interval === 1) {
    return RepeatType.Monthly;
  }
  //   { name: '每年', value: 'RRULE:FREQ=YEARLY;INTERVAL=1;' },
  if (freq === RRule.YEARLY && interval === 1) {
    return RepeatType.Yearly;
  }
  return undefined;
};

export const getRuleStrByRepeatType = (type: RepeatType, dayjs: Dayjs) => {
  if (type === RepeatType.WorkDay) {
    return getWorkDayRRule();
  }
  // 每天
  else if (type === RepeatType.Dayly) {
    return getEveryDayRRule();
  }
  // 每3天
  else if (type === RepeatType.ThreeDays) {
    return getEvery3DayRRule();
  }
  //
  else if (type === RepeatType.Weekly) {
    //rrule 周一 周日
    const num = dayjs.day() === 0 ? 6 : dayjs.day() - 1;
    const byweekday = [num] as unknown as Weekday[];
    return getEveryWeeklyRRule(byweekday);
  }
  //
  else if (type === RepeatType.Biweekly) {
    const num = dayjs.day() === 0 ? 6 : dayjs.day() - 1;
    const byweekday = [num] as unknown as Weekday[];
    return getEveryTwoWeeklyRRule(byweekday);
  }
  //
  else if (type === RepeatType.Monthly) {
    const bymonthday = [dayjs.date()];
    return getEveryMonthlyRRule(bymonthday);
  }
  //
  else if (type === RepeatType.Yearly) {
    const bymonthday = [dayjs.date()];
    const bymonth = [dayjs.month() + 1]; //rrule 月1-12
    return getEveryYearlyRRule(bymonth, bymonthday);
  }
  return undefined;
};

export const isAllowRemind = (alarmCreateTime: Dayjs | number, deadlineTime: TimeValue) => {
  let createTime = dayjs(alarmCreateTime);
  const { time, timeFormat } = deadlineTime || {};
  if (time) {
    let _deadlineTime = dayjs(time);
    if (timeFormat === 1) {
      //如果没有具体的时间值 取今天23:59:59
      _deadlineTime = _deadlineTime.endOf('date');
    }
    if (
      deadlineTime &&
      createTime.isAfter(dayjs().startOf('date').add(20, 'hours')) &&
      createTime.isBefore(dayjs().add(1, 'days').startOf('date')) &&
      _deadlineTime.isAfter(dayjs().startOf('date').add(20, 'hours')) &&
      _deadlineTime.isBefore(dayjs().add(1, 'days').startOf('date'))
    ) {
      return false;
    }
  }

  return true;
};

export const getTimePickerFormat = (opt: { time: Dayjs; rrule?: string; hasHHmm?: boolean }) => {
  const { time, rrule, hasHHmm } = opt;
  if (rrule) {
    const repeatType = getRepeatTypeByRuleStr(rrule);
    const repeatName = getRepeatNameByRRule(rrule);
    if (
      repeatType === RepeatType.WorkDay ||
      repeatType === RepeatType.Dayly ||
      repeatType === RepeatType.ThreeDays
    ) {
      return `${repeatName} ${time.format('HH:mm')}`;
    }
    if (repeatType === RepeatType.Weekly || repeatType === RepeatType.Biweekly) {
      return `${repeatName} ${time.format('dddd HH:mm')}`;
    }
    if (repeatType === RepeatType.Monthly) {
      return `${repeatName} ${time.format('Do HH:mm')}`;
    }
    if (repeatType === RepeatType.Yearly) {
      return `${repeatName} ${time.format('M/D HH:mm')}`;
    }
    return `${time.format('YYYY M/D HH:mm')}`;
  } else {
    return time.calendar(
      null,
      getCalendarFormat({
        time: time,
        hasHHmm,
        todayStr: I18N.auto.today,
        tomorrowStr: I18N.auto.tomorrow,
        yearStr: I18N.auto.year,
      })
    );
  }
};

export const getTimePickerFormat2 = (opt: { time: Dayjs; hasHHmm?: boolean }) => {
  const { time, hasHHmm } = opt;
  let str: string = time.calendar(
    null,
    getCalendarFormat({
      time: time,
      hasHHmm,
      todayStr: I18N.auto.today,
      tomorrowStr: I18N.auto.tomorrow,
      yearStr: I18N.auto.year,
    })
  );
  // if (rrule) {
  //   const repeatName = getRepeatNameByRRule(rrule);
  //   str += repeatName;
  // }
  return str;
};
