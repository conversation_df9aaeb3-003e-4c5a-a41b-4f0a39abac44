import classNames from 'classnames';
import React, { FC, useMemo } from 'react';

import { Icon } from '@/components/basic';
import { Order } from '@/utils/const';
import I18N from '@/utils/I18N';

import Container from '../../overlay-container';
import s from './index.less';
export type Props = {
  className?: string;
  activeKey?: string | undefined;
  onChange?: (v: string | undefined) => void;
  groupListData: { name: string; fieldName: string }[];
};

export interface Sorted {
  sortOrder: Order;
  key: string;
}

const GroupOverlay: React.FC<Props> = (props) => {
  const { activeKey = undefined, onChange, groupListData } = props;
  return (
    <Container title={I18N.auto.grouping_2}>
      <GroupList groupListData={groupListData} activeKey={activeKey} onChange={onChange} />
      {/* {groupListData.map((item, index) => {
        const active = activeKey === item.fieldName;
        return (
          <div
            key={index}
            className={classNames(s.item, { [s.active]: active })}
            onClick={() => {
              onChange?.(item.fieldName);
            }}
          >
            <span className={s.left}>{item.name}</span>
            <span className={s.right}>
              {active ? <Icon className={s.icon} name="icon-sys_check"></Icon> : null}
            </span>
          </div>
        );
      })} */}
    </Container>
  );
};

interface IGroupListProps {
  groupListData: { name: string; fieldName: string }[];
  activeKey?: string | string[];
  onChange?: (
    v: string | undefined,
    item: { name: string; fieldName: string },
    add?: boolean
  ) => void;
}

export const GroupList: FC<IGroupListProps> = ({ groupListData, activeKey, onChange }) => {
  const activeKeyMap = useMemo(() => {
    if (Array.isArray(activeKey)) {
      return activeKey.reduce((acc, cur) => {
        acc[cur.fieldName] = cur;
        return acc;
      }, {} as Record<string, string>);
    } else if (activeKey) {
      return { [activeKey]: activeKey };
    }
    return {};
  }, [activeKey]);

  return groupListData.map((item, index) => {
    const active = !!activeKeyMap[item.fieldName];
    return (
      <div
        key={index}
        className={classNames(s.item, { [s.active]: active })}
        onClick={() => {
          onChange?.(item.fieldName, item, !active);
        }}
      >
        <span className={s.left}>{item.name}</span>
        <span className={s.right}>
          {active ? <Icon className={s.icon} name="icon-sys_check"></Icon> : null}
        </span>
      </div>
    );
  });
};

export default GroupOverlay;
