.editorWrapper {
  border: 1px solid var(--Brand600);
  border-radius: 6px;
  background: var(--bgTop);
  cursor: auto;
}
.editor {
  border-radius: 6px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  overflow: visible;
  height: auto !important;
  &.noEditing {
    border: none;
    min-height: 0px;
    height: auto;
    border: 1px solid transparent;
    :global {
      .ProseMirror {
        padding: 6px 0px;
      }
    }
  }
  :global {
    --r-editor-content-background-color: var(--bgTop);
    --r-editor-toolbar-background-color: var(--bgTop);
    .rock-editor__toolbar {
      margin-top: 4px;
      height: auto;
      order: 3;
      border-radius: 0 0 6px 6px;
      padding: 0px 6px 6px 6px;
      .rock-editor__toolbar__inner {
        width: 100%;
        display: flex;
        position: relative;
        justify-content: normal;
        gap: 6px;
        .insert-image {
          position: absolute;
          left: 90px;
        }
      }
      .rock-divider {
        display: none;
      }
      .rock-editor__toolbar__btn {
        color: var(--IconPrimary);
        &:hover,
        &.active {
          color: var(--Brand500);
          background-color: var(--aBrand12);
        }
      }
      .rock-dropdown-trigger-default.rock-editor__toolbar__dropdown {
        display: none;
      }
      .rock-dropdown-trigger-default + .rock-editor__toolbar__btn {
        margin-left: 0px;
      }
      .rock-editor__toolbar__btn + .rock-editor__toolbar__btn.rock-editor__toolbar__btn {
        margin-left: 0px;
      }
    }

    .prosemirror-wrapper {
      order: 1;
      flex: 1;
      min-height: auto;

      .rock-tooltip-content {
        display: none;
      }
    }

    .rock-imageviewer {
      order: 2;
    }

    .rock-editor-ol,
    .rock-editor-ul {
      margin: 0 auto;
    }

    .ProseMirror {
      padding: 6px;
      font-size: 14px;
      color: var(--TextPrimary);
      min-height: 40px;
      height: auto;
      overflow-y: visible;
      overflow-x: hidden;
      ul li {
        // height: 22px;
        > .ProseMirror-li-placeholder {
          height: 22px;
          line-height: 22px;
        }
      }
      strong {
        font-weight: 600;
      }
      .rock-editor__image-view {
        .rock-editor__image-view-body-wrapper {
          .rock-editor__image-resize-box-tips__inner {
            display: none;
          }
          .rock-tooltip {
            display: none;
          }
        }
      }
    }
    .ProseMirror[placeholder]::before {
      margin: 0;
      padding: 0;
      font-size: 14px;
      line-height: 18px;
      margin-left: -1px;
      cursor: text;
    }

    .prosemirror-mention {
      color: var(--Brand600);
    }
    .prosemirror-emoji {
      display: inline-block;
      width: 22px;
      height: 22px;
      line-height: 22px;
      vertical-align: bottom;
    }
    .editor-popo-doc-span {
      display: inline;
      text-decoration-line: none;
      color: var(--Brand600);
      .editor-popo-doc-img {
        margin: 0 3px;
        width: 16px;
        height: 16px;
        vertical-align: sub;
      }
    }
    .rock-tooltip {
      .rock-tooltip-inner > .rock-tooltip-title > .desc {
        display: none !important;
      }
    }
  }
  &.comment {
    min-height: 0px;
    :global {
      .prosemirror-wrapper {
        max-height: 225px;
      }
      .rock-editor__toolbar {
        .rock-editor__toolbar__inner {
          .insert-image {
            position: absolute;
            left: 150px;
          }
        }
      }
    }
  }
  &.show {
    :global {
      .rock-editor__toolbar {
        margin-top: 30px;
      }
    }
  }
}
.otherCheckbox {
  display: none;
  &.show {
    display: flex;
    position: absolute;
    bottom: 40px;
    left: 6px;
  }
  :global {
    .rock-checkbox-wrapper {
      line-height: 20px;
      margin-right: 6px;
    }
  }
  .tip {
    font-size: 13px;
    color: var(--TextSecondary);
    line-height: 20px;
  }
}
