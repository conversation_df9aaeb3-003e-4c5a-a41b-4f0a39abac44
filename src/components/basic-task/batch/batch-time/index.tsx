import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTodoBatchUpdateDeadlinePost } from '@/api';
import POPODatePicker, { TimeValue } from '@/components/basic/popo-date-picker';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
  disabled?: boolean;
};
const BatchTime: React.FC<Props> = (props) => {
  const { disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { batchList } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onChange = (v: TimeValue) => {
    const taskIds = batchList.map((item) => item.taskId!);
    // 请求批量操作
    apiTodoBatchUpdateDeadlinePost({
      taskIds: taskIds,
      deadline: v.time,
      deadlineFormat: v.timeFormat,
      rrule: v.rrule,
    }).then(() => {
      //刷新左侧标签列表、标签数量、当前列表数据
      dispatch.viewSetting.refreshDataByDataChange({
        refreshCount: false,
        refreshList: true,
      });
      dispatch.batch.setBatchList([]);
      dispatch.batch.setBatchMode(false);
    });
  };
  const showDelete = useMemo(() => {
    return batchList.some((item) => item.deadline);
  }, [batchList]);
  return disabled ? (
    <BatchItem
      disabled={disabled}
      name={I18N.auto.deadline}
      iconName="icon-details_data_calendar"
    ></BatchItem>
  ) : (
    <POPODatePicker
      visible={visible}
      onVisible={setVisible}
      showDelete={showDelete}
      hasRRule
      onChange={(v: TimeValue) => {
        onChange(v);
      }}
      className={s.batchTime}
    >
      <BatchItem name={I18N.auto.deadline} iconName="icon-details_data_calendar"></BatchItem>
    </POPODatePicker>
  );
};

export default BatchTime;
