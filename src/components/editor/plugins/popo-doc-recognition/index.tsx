import { RockEditorPlugin, <PERSON><PERSON>in<PERSON>ey, RockEditorView, Transaction, EditorState } from '@bedrock/editor'
import { apiTodoCommentDocParsePost } from '@/api'
import * as _ from 'lodash'
import { DocsRegex } from '@/components/basic-task/comment/editor/utils'
import { DocParse } from '@/types'
import PopoDocSpecs from './popoDocSpecs'

async function transformDocLinks(view: RockEditorView, text) {
  // 1. 保存当前选择位置
  const { from, to } = view.state.selection
  try {
    const processedContent = await processContentAsync(view, text, from)
    const { node, fromIndex, toIndex } = processedContent
    const { state } = view
    let tr = state.tr
    const suffix = ' '
    tr = tr.replaceWith(fromIndex, toIndex, [node, state.schema.text(suffix)])

    // if (!tr.before.eq(state.doc)) {
    //   // 如果文档已变更，重新映射位置
    //   const newFrom = tr.mapping.map(from);
    //   const newTo = tr.mapping.map(to);
    //   tr = state.tr.replaceWith(newFrom, newTo, node);
    // }
    // 6. 应用事务
    view.dispatch(tr)
  } catch (error) {
    return false
  }
}

async function processContentAsync(view, text, from) {
  let returnInfo = {
    node: text,
    fromIndex: from,
    toIndex: 0,
  }
  const links = text.match(DocsRegex)
  if (links) {
    const parseData = await apiTodoCommentDocParsePost({
      docUrls: links,
    })
    const popoDocType = view.state.schema.nodes.popoDoc

    const DocMap = parseData.reduce((pre, cur) => {
      pre[cur.docUrl!] = cur
      return pre
    }, {} as Record<string, DocParse>)
    const textArray = text.split(DocsRegex)
    textArray.forEach((_text, k) => {
      if (links.length && links[0] === _text && DocMap[_text]) {
        links.shift()
        const item = DocMap[_text] || {}
        const node = popoDocType.create({
          src: item.docUrl,
          text: item.docName,
          docIcon: item.docIconUrl,
        })
        const fromIndex = returnInfo.fromIndex + text.indexOf(_text)
        // TODO:通过length计算可能不准确
        const toIndex = fromIndex + _text.length
        returnInfo.node = node
        returnInfo.fromIndex = fromIndex
        returnInfo.toIndex = toIndex
      } else {
        // index += text.length;
      }
    })
  }
  return returnInfo
}

export default function createPopoDocRecognitonPlugin(options = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('popoDocRecognitionPlugin'),
    // nodeViews: PopoDocRecognitonView,
    priority: 9999,
    props: {
      handlePaste(view: RockEditorView, event) {
        // 获取粘贴内容
        const clipboardData = event.clipboardData
        const text = clipboardData?.getData('text/plain')
        if (text?.match(DocsRegex)) {
          // event.preventDefault();
          transformDocLinks(view, text)
          return false
        } else {
          return false
        }
      },
    },
    nodeSpecs: PopoDocSpecs,
    view(editorView) {
      return {
        destroy() {},
      }
    },
  })
}
