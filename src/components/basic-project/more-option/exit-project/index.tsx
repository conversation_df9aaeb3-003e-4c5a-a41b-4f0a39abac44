import React, { PropsWithChildren } from 'react';

import { apiProjectQuitPost } from '@/api';
import Modal from '@/components/basic/modal';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
  onExitSuccess?: () => void;
}

const ExitProject: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, projectInfo, onExitSuccess } = props;
  const exit = () => {
    if (projectInfo?.projectId) {
      try {
        apiProjectQuitPost({
          projectId: projectInfo?.projectId,
        }).then(() => {
          onExitSuccess?.();
        });
      } catch (error) {
        console.log(error);
      }
    }
  };
  return React.cloneElement(children, {
    onClick: () => {
      Modal.confirm(
        {
          rootClassName: s.confirm,
          title: I18N.template(I18N.auto.confirmExitItem, { val1: projectInfo?.name }),
          width: 400,
          scrolled: false,
          centered: true,
          okText: I18N.auto.signOut,
          content: <div>{I18N.auto.afterExitingYouWill}</div>,
          onOk: exit,
        },
        'warning'
      );
    },
  });
};

export default ExitProject;
