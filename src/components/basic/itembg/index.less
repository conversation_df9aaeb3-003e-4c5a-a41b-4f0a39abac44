// 确保父容器有宽度限制
.itemBg {
  display: flex;
  min-width: 0; // 关键：允许子元素收缩
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-height: 30px;
  // margin-left: -4px;
  // margin-right: -4px;
  padding: 0 4px;
  border-radius: 4px;
  min-width: 0;

  &.showbg:hover {
    background-color: var(--aBlack6);
  }
  :global {
    .todo-base-label {
      line-height: 20px;
    }
    .picker-label-content {
      font-size: 13px;
      color: var(--TextPrimary);
      max-width: 100%;
    }
  }
}

.disabled {
  &:hover {
    background-color: transparent !important;
  }
  :global {
    .todo-base-label,
    .com-placeholder {
      color: var(--TextQuartus) !important;
    }
  }
}
