import I18N from '@/utils/I18N';
export type ILevelOption = {
  name: string;
  value: number;
  IconName: string;
  className: string;
  showSelect?: boolean;
  borderColor?: string;
};

export enum Level {
  /**
   * 紧急
   */
  urgent = 1,
  /**
   * 高
   */
  high = 2,
  /**
   * 中
   */
  medium = 3,
  /**
   * 低
   */
  low = 4,
  /**
   * 默认无级别
   */
  default = 0,
}

// 0 表示无优先级 服务端划分0-4
export const LevelSelectOptions: ILevelOption[] = [
  {
    name: I18N.auto.urgent,
    value: Level.urgent,
    IconName: 'icon-kit_priority_priority_urgent',
    className: 'com-level-urgent',
    showSelect: true,
    borderColor: '--TasksBorderIV',
  },
  {
    name: I18N.auto.high,
    value: Level.high,
    IconName: 'icon-kit_priority_priority_high',
    className: 'com-level-high',
    showSelect: true,
    borderColor: '--TasksBorderIII',
  },
  {
    name: I18N.auto.in,
    value: Level.medium,
    IconName: 'icon-kit_priority_priority_medium',
    className: 'com-level-medium',
    showSelect: true,
    borderColor: '--TasksBorderII',
  },
  {
    name: I18N.auto.low,
    value: Level.low,
    IconName: 'icon-kit_priority_priority_low',
    className: 'com-level-low',
    showSelect: true,
    borderColor: '--TasksBorderI',
  },
  {
    name: I18N.auto.nothing,
    value: Level.default,
    IconName: 'icon-kit_priority_priority_no',
    className: 'com-level-unset',
    showSelect: true,
    borderColor: '',
  },
];

export const LevelSelectOptionsMap = LevelSelectOptions.reduce((pre, cur) => {
  pre[cur.value] = cur;
  return pre;
}, {} as Record<number, ILevelOption>);
