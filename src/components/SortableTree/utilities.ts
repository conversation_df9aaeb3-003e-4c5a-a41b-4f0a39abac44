import type { UniqueIdentifier } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';

import type { FlattenedItem, TreeItem, TreeItems } from './types';

export const iOS = /iPad|iPhone|iPod/.test(navigator.platform);

function getDragDepth(offset: number, indentationWidth: number) {
  return Math.round(offset / indentationWidth);
}

export function getProjection(
  items: FlattenedItem[],
  activeId: UniqueIdentifier,
  overId: UniqueIdentifier,
  dragOffset: number,
  indentationWidth: number,
  collisionDetectionData: any
) {
  let overItemIndex = -1;
  let activeItemIndex = -1;
  let activeItem: FlattenedItem | null = null;

  for (let i = 0; i < items.length; i++) {
    if (items[i].id === overId) {
      overItemIndex = i;
    }
    if (items[i].id === activeId) {
      activeItemIndex = i;
      activeItem = items[i];
    }
    // 如果两个索引都找到了，就可以提前退出循环
    if (overItemIndex !== -1 && activeItemIndex !== -1) {
      break;
    }
  }

  // const newItems = arrayMove(items, activeItemIndex, overItemIndex);
  const previousItem = items[overItemIndex - 1];
  const overItem = items[overItemIndex];

  const nextItem = items[overItemIndex + 1];
  const dragDepth = getDragDepth(dragOffset, indentationWidth);
  const projectedDepth = (activeItem?.depth || 0) + dragDepth;
  const maxDepth = !!activeItem?.isGroup
    ? 0
    : getMaxDepth({
        previousItem,
        overItem,
        activeItem,
        collisionDetectionData,
      });

  const minDepth = getMinDepth({ nextItem, overItem, activeItem, collisionDetectionData });
  let depth = projectedDepth;
  if (projectedDepth > maxDepth) {
    depth = maxDepth;
  } else if (projectedDepth < minDepth) {
    depth = minDepth;
  }
  return { depth, maxDepth, minDepth, parentId: getParentId() };

  function getParentId() {
    if (depth === 0 || !previousItem) {
      return null;
    }

    if (depth === previousItem.depth) {
      return previousItem.parentId;
    }

    if (depth > previousItem.depth) {
      return previousItem.id;
    }

    const newParent = items
      .slice(0, overItemIndex)
      .reverse()
      .find((item) => item.depth === depth)?.parentId;

    return newParent ?? null;
  }
}

function getMaxDepth({
  previousItem,
  overItem,
  activeItem,
  collisionDetectionData,
}: {
  previousItem: FlattenedItem;
  overItem: FlattenedItem;
  activeItem: FlattenedItem;
  collisionDetectionData: any;
}) {
  if (overItem?.parentId) {
    return Math.max(previousItem?.depth, overItem?.depth);
  }
  if (overItem?.isGroup && !activeItem?.isGroup) {
    if (collisionDetectionData?.position === 'below') {
      return overItem.depth + 1;
    }
  }
  if (!overItem?.isGroup && !overItem?.parentId) {
    return 0;
  }

  return 0;
}

function getMinDepth({
  activeItem,
  overItem,
  collisionDetectionData,
}: {
  activeItem: FlattenedItem;
  overItem: FlattenedItem;
  collisionDetectionData: any;
}) {
  if (overItem) {
    if (overItem.isGroup && !activeItem?.isGroup) {
      if (collisionDetectionData?.position === 'below') {
        return overItem.depth + 1;
      }
    }
    return overItem.depth;
  }

  return 0;
}

function flatten(params: {
  items: TreeItems;
  parentId?: UniqueIdentifier | null;
  depth?: number;
  // 限制flatten的深度, 从0开始为第一层，没有代表无限制
  flattenDepth?: number | undefined;
  // 内部使用的全局索引计数器
  _globalIndexRef?: { current: number };
}): FlattenedItem[] {
  const { items, parentId = null, depth = 0, flattenDepth, _globalIndexRef } = params;

  if (!items?.length) {
    return [];
  }

  // 初始化或使用传入的全局索引引用
  const globalIndexRef = _globalIndexRef || { current: 0 };
  const childrenDepth = depth + 1;

  return items.reduce<FlattenedItem[]>((acc, item, index) => {
    // 为当前节点分配全局索引
    const currentGlobalIndex = globalIndexRef.current++;

    let children: FlattenedItem[] = [];

    if (typeof flattenDepth !== 'number' || childrenDepth <= flattenDepth) {
      children = flatten({
        items: item.children,
        parentId: item.id || item.projectId,
        depth: childrenDepth,
        flattenDepth,
        _globalIndexRef: globalIndexRef, // 传递全局索引引用
      });
    }

    return [
      ...acc,
      {
        ...item,
        parentId,
        depth,
        index,
        globalIndex: currentGlobalIndex,
        id: item.id || item.projectId,
      },
      ...children,
    ];
  }, []);
}

export function flattenTree(items: TreeItems, flattenDepth?: number): FlattenedItem[] {
  return flatten({ items, flattenDepth });
}

export function buildTree(flattenedItems: FlattenedItem[]): TreeItems {
  const root: TreeItem = { id: 'root', children: [] };
  const nodes: Record<string, TreeItem> = { [root.id]: root };
  const itemIdsMap: any = {};
  const items = flattenedItems.map((item) => {
    const { id } = item;
    itemIdsMap[id] = item;
    return { ...item, children: [] };
  });

  for (const item of items) {
    const { id, children } = item;
    const parentId = item.parentId ?? root.id;
    const parent = nodes[parentId] ?? itemIdsMap[parentId];

    nodes[id] = { id, children };
    parent.children?.push(item);
  }

  return root.children;
}

export function findItem(items: TreeItem[], itemId: UniqueIdentifier) {
  return items.find(({ id }) => id === itemId);
}

export function findItemDeep(items: TreeItems, itemId: UniqueIdentifier): TreeItem | undefined {
  for (const item of items) {
    const { id, children } = item;

    if (id === itemId) {
      return item;
    }

    if (children?.length) {
      const child = findItemDeep(children, itemId);

      if (child) {
        return child;
      }
    }
  }

  return undefined;
}

export function removeItem(items: TreeItems, id: UniqueIdentifier) {
  const newItems = [];

  for (const item of items) {
    if (item.id === id) {
      continue;
    }

    if (item.children?.length) {
      item.children = removeItem(item.children, id);
    }

    newItems.push(item);
  }

  return newItems;
}

export function setProperty<T extends keyof TreeItem>(
  items: TreeItems,
  id: UniqueIdentifier,
  property: T,
  setter: (value: TreeItem[T]) => TreeItem[T]
) {
  for (const item of items) {
    if (item.id === id) {
      item[property] = setter(item[property]);
      continue;
    }

    if (item.children?.length) {
      item.children = setProperty(item.children, id, property, setter);
    }
  }

  return [...items];
}

function countChildren(items: TreeItem[], count = 0): number {
  return items?.reduce((acc, { children }) => {
    if (children?.length) {
      return countChildren(children, acc + 1);
    }

    return acc + 1;
  }, count);
}

export function getChildCount(items: TreeItems, id: UniqueIdentifier) {
  const item = findItemDeep(items, id);

  return item?.children ? countChildren(item.children) : 0;
}

export function removeChildrenOf(items: FlattenedItem[], ids: UniqueIdentifier[]) {
  const excludeParentIds = [...ids];
  return items.filter((item) => {
    if (item.parentId && excludeParentIds.includes(item.parentId)) {
      if (item.children?.length) {
        excludeParentIds.push(item.id);
      }
      return false;
    }

    return true;
  });
}
