import { ViewType } from '@/utils/const';
import { ViewGantt } from '@babylon/popo-icons';
import { FC } from 'react';
import { Icon } from '../basic';

const viewIconNameMap = {
  [ViewType.list]: 'icon-view_list_line',
  [ViewType.kanban]: 'icon-view_kanban_line',
  [ViewType.calendar]: 'icon-view_calendar_line',
  [ViewType.timeline]: <ViewGantt />,
};

interface IViewIconProps {
  viewType: ViewType;
  className?: string;
}

const ViewIcon: FC<IViewIconProps> = ({ viewType, className }) => {
  return <Icon className={className} name={viewIconNameMap[viewType]} />;
};

export default ViewIcon;
