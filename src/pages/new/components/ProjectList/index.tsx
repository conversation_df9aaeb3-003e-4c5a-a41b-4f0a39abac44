import DnDTree from '@/components/DnDTree';
import { TreeProps } from '@bedrock/components/lib/Tree';
import s from './index.less';
import { apiProjectGroupResortPost } from '@/api';
import { ProjectInfo } from '@/types';
import { EnumEmitter, ProjectListMenuId } from '@/utils/const';
import { useMemoizedFn } from 'ahooks';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, RootState } from '@/models/store';
import classNames from 'classnames';
import { useEffect, useRef } from 'react';
import { debounce } from 'lodash';
import { POPOBridgeEmitter } from '@popo-bridge/web';

interface IProjectListProps extends TreeProps {
  className?: string;
  isPin?: boolean;
  loadMore?: () => void;
  indicatorIndent?: number;
}

const ProjectList: React.FC<IProjectListProps> = (props) => {
  const { treeData = [], className, isPin = false, loadMore, indicatorIndent = 0, ...rest } = props;
  const dragNodeRef = useRef<any>(null);

  const dispatch = useDispatch<Dispatch>();

  const navigatorId = useSelector((state: RootState) => state.viewSetting.navigatorId);

  const handleDragStart = useMemoizedFn((info) => {
    console.log('handleDragStart', info);
    const { node } = info;
    dragNodeRef.current = node;
    if (node?.isGroup && node?.expanded) {
      POPOBridgeEmitter.emit(EnumEmitter.TreeExpandById, { id: node.id });
    }
  });

  const handleDragOver = useMemoizedFn((info) => {
    const { node } = info;
    if (node?.isGroup && node?.expanded && dragNodeRef.current?.isGroup) {
      POPOBridgeEmitter.emit(EnumEmitter.TreeExpandById, { id: node.id });
    }
  });

  const handleDragEnd = useMemoizedFn((info) => {
    console.log('rest', info);
    const { dragNode: active, node: over, dropToGap, dropPosition } = info;
    if (active.id !== over?.id) {
      // let active: ProjectInfo = active.data.current.item;
      // let over: ProjectInfo = over.data.current.item;
      let activeIndex = active?.index;
      let overIndex = over?.index;

      const params: any = {
        currentId: active.projectId as number,
        preId: undefined,
        tailId: undefined,
        overGroupId: over?.groupId,
        // oldGroupId: 0,
        // newGroupId: 0,
      };

      const overIndexes = overIndex?.split('.') || [];
      const lastOverIndex = Number(overIndexes[overIndexes.length - 1]);
      if (overIndexes?.length === 1) {
        params.newGroupId = undefined;
      }
      const group = dispatch.project.getGroupByIndexes({
        indexes: overIndexes,
        list: treeData,
      });

      // 代表放到over的下面一个元素
      if (dropToGap) {
        // --1. 拖到分组下面--
        // 2. 从A分组拖到B分组
        // 3. 从分组拖到非分组
        // 4. 同级排序
        params.preId = over?.projectId;
        if (over.isGroup) {
          params.tailId = treeData[Number(overIndex) + 1]?.projectId;
        } else {
          params.tailId =
            overIndexes.length > 1
              ? group?.groupProjects?.[lastOverIndex + 1]?.projectId
              : group?.projectId;
        }
      } else {
        if (over.isGroup) {
          params.tailId = over?.groupProjects?.[0]?.projectId;
          params.newGroupId = over.projectId;
        } else if (over.groupId) {
          params.preId = over?.projectId;
          params.newGroupId = over.groupId;
        }
      }

      if (active?.groupId !== over?.groupId || over?.isGroup) {
        if (!active?.isGroup) {
          params.oldGroupId = active.groupId;
        }
        if (!over?.isGroup) {
          params.newGroupId = over.groupId;
        }
      }
      Object.keys(params).forEach((key) => {
        if (typeof params[key] !== 'number') {
          delete params[key];
        }
      });
      dispatch.project.projectSort({
        isPin,
        activeItem: active,
        // overItem: over,
        ...params,
      });

      apiProjectGroupResortPost(params).then(() => {
        //当前如果是全部任务导航
        if (navigatorId === ProjectListMenuId) {
          dispatch.project.getProjectList({ page: 1 });
        }
        dispatch.project.getPingProjectList();
      });
    }
  });

  useEffect(() => {
    // 监听 .rc-tree-treenode-leaf-last 是否出现在视口，如果是，加载下一页数据
    let observer: MutationObserver | null = null;
    if (loadMore) {
      const target = document.querySelector(`.project__tree.${s.menu__tree}`);
      if (target) {
        observer = new MutationObserver(
          debounce(() => {
            const targetRect = target.getBoundingClientRect();
            const lastNode = target.querySelector('.rc-tree-treenode-leaf-last');
            if (lastNode) {
              const rect = lastNode.getBoundingClientRect();
              if (rect.top > targetRect.bottom - 100) {
                loadMore?.();
              }
            }
          }, 160)
        );
        observer.observe(document.querySelector(`.project__tree.${s.menu__tree}`)!, {
          attributes: true,
          childList: true,
          subtree: true,
        });
      }
    }
    return () => {
      observer?.disconnect();
    };
  }, []);

  const renderDropIndicator = (props: {
    dropPosition: 0 | 1 | -1;
    dropLevelOffset: number;
    indent: number;
    prefixCls: string;
    direction: any;
  }) => {
    const { indent, prefixCls, dropPosition, dropLevelOffset } = props;
    let newIndent = indent + indicatorIndent;
    const style: React.CSSProperties = {};
    switch (dropPosition) {
      case -1:
        style.top = 0;
        style.left = -dropLevelOffset * newIndent;
        break;
      case 1:
        style.bottom = 0;
        style.left = -dropLevelOffset * newIndent;
        break;
      case 0:
        style.bottom = 0;
        style.left = newIndent;
        break;
    }

    return <div className={s.indicator} style={style}></div>;
  };

  return (
    <DnDTree
      treeData={treeData}
      className={classNames(s.menu__tree, className)}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDrop={handleDragEnd}
      draggable={(node: any) => {
        return !node.status || node.status === 'normal';
      }}
      isPin={isPin}
      dropIndicatorRender={renderDropIndicator}
      // renderTreeNodeContent={(nodes: {
      //   indent: React.ReactNode;
      //   switcher: React.ReactNode;
      //   checkbox: React.ReactNode;
      //   selector: React.ReactNode;
      // }) => {
      //   const { indent, switcher, checkbox, selector } = nodes;
      //   return (
      //     <div>
      //       {indent}
      //       {switcher}
      //       {checkbox}
      //       {selector}
      //     </div>
      //   );
      // }}
      allowDrop={(options) => {
        const { dropNode, dropPosition } = options;
        console.log('allowDrop', options);
        // if (dropNode.isGroup && dropPosition <= 0) {
        //   return false;
        // }
        // 拖拽元素如果是分组，不可以放置在其他分组下面
        if (dropNode.isGroup && dropPosition <= 0 && dragNodeRef.current?.isGroup) {
          return false;
        }
        if ((!dropNode.isGroup && !dropNode.groupId && dropPosition <= 0) || !!dropNode.status) {
          return false;
        }
        if (!dropNode.isGroup && dropNode.groupId && dropPosition > 0) {
          return false;
        }
        return true;
      }}
      {...rest}
    />
  );
};

export default ProjectList;
