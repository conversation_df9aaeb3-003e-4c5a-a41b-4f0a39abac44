.renderTaskName {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: calc(100% - 2px);
  // top: 1px;
  right: -1px;
  min-width: 0;
  flex: 1;
  will-change: 'padding-left';
  // cursor: pointer;
  :global {
    .rock-checkbox-wrapper {
      margin: 0;
    }
    .rock-checkbox-inner {
      background-color: transparent;
      border-color: var(--IconQuartus);
    }
    .rock-checkbox-wrapper:not(.rock-checkbox-clicked):hover .rock-checkbox-inner {
      background-color: transparent;
    }
    .rock-checkbox-checked .rock-checkbox-inner {
      background-color: var(--Brand500);
    }
  }
}

.titleItem {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-left: 18px;
}
