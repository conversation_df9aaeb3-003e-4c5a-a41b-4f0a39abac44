import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { taskUpdateAlarm, taskUpdateDeadline } from '@/api-common';
import { IconBtn, POPOConvergenceDatePicker } from '@/components/basic';
import { PopoConvergenceDatePickerContext } from '@/components/basic/popo-convergence-date-picker/context';
import { TimeInputType } from '@/components/basic/popo-convergence-date-picker/convergence-date-picker';
import RemindStr from '@/components/basic/popo-convergence-date-picker/remind-item/remind-label';
import {
  getRepeatTypeByRuleStr,
  getTimePickerFormat,
  PPTimeFormat,
  repeatOptions,
} from '@/components/basic/popo-date-picker/utils';
import { Dispatch } from '@/models/store';
import { TaskTime, TodoInfo } from '@/types';
import { isUpdateTime } from '@/utils';
import { EnumTimePickerType, TaskTableRowType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Params {
  deadline: number;
  deadlineFormat: PPTimeFormat | undefined;
  rrule: string;
}

interface Props {
  onChange?: (v: Params) => void;
  taskInfo: TodoInfo;
}

const RenderDeadline: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const {
    taskId,
    _rowType: rowType,
    memoAlarm: alarm,
    deadline,
    rrule,
    deadlineFormat: timeFormat,
    startTime,
    isExpired,
    isToday,
  } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();

  const permissions = useGetPermissions({ taskId });

  const memoDeadlineTime = useMemo(() => {
    return {
      startTime: startTime,
      deadline: deadline,
      rrule: rrule,
      timeFormat: timeFormat,
      alarm: alarm,
    };
  }, [startTime, deadline, timeFormat, rrule, alarm]);

  const change = async (v: TaskTime) => {
    const { startTime, deadline, timeFormat, rrule, alarm } = v;
    const isUpdate = isUpdateTime(v, memoDeadlineTime!);
    if (!isUpdate) {
      return;
    }
    const params = {
      taskId: taskId,
      startTime: startTime || 0,
      deadline: deadline || 0,
      deadlineFormat: timeFormat,
      rrule: rrule,
      memoAlarm: alarm,
      alarm: alarm,
    };
    if (rowType === TaskTableRowType.add) {
      dispatch.task.updateTodoList(Object.assign(taskInfo, params));
      return null;
    }
    if (isUpdate) {
      await taskUpdateDeadline(params);
      await taskUpdateAlarm({
        taskId: taskId,
        alarm: {
          alarmTimestamp: alarm?.time || 0,
          alarmRrule: alarm?.rrule,
          selectedOption: alarm?.selectedOption,
        },
      });
      dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
    }
  };

  const changeVisible = (v: boolean) => {
    setVisible(v);
  };

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    });
    return !CAN_EDIT && rowType !== TaskTableRowType.add;
  }, [permissions, rowType]);
  const hasData = useMemo(() => {
    return !!memoDeadlineTime.deadline || !!memoDeadlineTime.alarm?.time;
  }, [memoDeadlineTime]);

  return (
    <AddRowBox
      iconName="icon-Soft_qk_td"
      tipTitle={hasData ? '' : I18N.auto.addDeadline_2}
      taskId={taskInfo.taskId}
      disabled={disabled}
      hasDelete={hasData}
      focused={visible}
      onClick={(e) => {
        changeVisible(!visible);
      }}
      onClear={() => {
        change({
          deadline: 0,
          timeFormat: PPTimeFormat.noDate,
          rrule: '',
          startTime: 0,
          selectedOption: 'NONE',
        });
      }}
    >
      <PopoConvergenceDatePickerContext.Provider
        value={{
          timePickerType: EnumTimePickerType.deadline,
        }}
      >
        <POPOConvergenceDatePicker
          disabled={disabled}
          timeInputType={TimeInputType.end}
          value={memoDeadlineTime}
          placeholder=" "
          onChange={change}
          visible={visible}
          onVisible={changeVisible}
          showCircle={!taskInfo.parentId}
          labelClassName={classNames({
            'time-today': isToday, // 今天未过期,未完成
            'time-expired': isExpired, //已过期 未完成
          })}
          hasArrow={false}
          showLabelRRuleIcon={false}
          renderLabel={(time: Dayjs, value?: TaskTime) => {
            const { deadline, timeFormat, rrule } = value!;
            const node = [];
            if (deadline) {
              let hasHHmm = false;
              if (timeFormat === PPTimeFormat.dateAndTime) {
                hasHHmm = true;
              }
              const timeStr = getTimePickerFormat({
                time: dayjs(time),
                hasHHmm,
              });
              node.push(<div className="mr-6">{timeStr}</div>);
            }
            if (rrule) {
              const type = getRepeatTypeByRuleStr(rrule);
              const repeat = repeatOptions.filter((item) => item.value === type)[0];
              if (repeat.name) {
                node.push(
                  <IconBtn
                    placement="top"
                    key={1}
                    title={I18N.template(I18N.auto.repeat_2, {
                      val1: repeat.name,
                    })}
                    className={s.icon}
                    iconName="icon-quick_round"
                    fontSize={16}
                  // onClick={(e) => {
                  //   e.stopPropagation();
                  // }}
                  ></IconBtn>
                );
              }
            }
            if (alarm?.time) {
              node.push(
                <IconBtn
                  placement="top"
                  key={2}
                  title={
                    <RemindStr
                      time={dayjs(alarm.time)}
                      timeFormat={alarm.timeFormat}
                      rrule={alarm.rrule}
                    ></RemindStr>
                  }
                  className={s.icon}
                  iconName="icon-quick_remind"
                  fontSize={16}
                // onClick={(e) => {
                //   e.stopPropagation();
                // }}
                ></IconBtn>
              );
            }
            return node;
          }}
        ></POPOConvergenceDatePicker>
      </PopoConvergenceDatePickerContext.Provider>
    </AddRowBox>
  );
};

export default RenderDeadline;
