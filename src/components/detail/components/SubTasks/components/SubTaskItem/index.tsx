import { RenderTaskName } from '@/pages/new/components/view/list/render';
import { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import ConciseOperation from '@/components/CreateTaskLine/components/ConciseOperation';
import s from './index.less';
import { useDispatch } from 'react-redux';
import { Dispatch, store } from '@/models/store';
import { TodoInfo } from '@/types';
import { Icon } from '@/components/basic';
import { SortableItem } from '@/components/dndkit';
import useGetPermissions from '@/hooks/useGetPermissions';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

interface ISubTaskItemProps {
  taskInfo: TodoInfo;
  onContextMenu?: (e: React.MouseEvent<HTMLDivElement>) => void;
  content?: ReactNode;
}
const SubTaskItem: FC<ISubTaskItemProps> = ({ taskInfo: srcTaskInfo, onContextMenu, content }) => {
  const [taskInfo, setTaskInfo] = useState(srcTaskInfo);

  const dispatch = useDispatch<Dispatch>();

  const permissions = useGetPermissions({ taskId: srcTaskInfo.taskId });

  const [CAN_DELETE] = useMemo(() => {
    const [CAN_DELETE] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_DELETE],
    }) as boolean[];
    return [CAN_DELETE];
  }, [permissions]);

  const handleContextMenu = (e: React.MouseEvent<HTMLDivElement>) => {
    const currentViewTab = store.getState().viewSetting.currentViewTab;

    if (CAN_DELETE && currentViewTab?.type) {
      if (onContextMenu) {
        onContextMenu(e);
      }
    } else {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  /**
   *
   * @param newTaskInfo
   * @param type local 只更新本地
   */
  const handleTaskInfoChange = (newTaskInfo, type) => {
    const { taskId, ...rest } = taskInfo;
    const info = { ...rest, ...newTaskInfo, taskId };
    setTaskInfo(info);
    // 1. 更新详情中该条子任务；会顺便更新列表中的该条数据
    dispatch.detail.replaceTask({ taskInfo: info, updateType: 'local' });
    // 2. 更新列表中该条子任务
    dispatch.viewSetting.refreshDataByDataChange({
      refreshCount: false,
      refreshList: true,
      detail: info,
    });
  };

  const handleSuccess = () => {
    // 1. 获取详情
    dispatch.detail.getTodoDetail(null).then((res) => {
      dispatch.viewSetting.refreshDataByDataChange({
        refreshCount: false,
        refreshList: true,
        detail: res,
      });
    });
  };

  useEffect(() => {
    setTaskInfo(srcTaskInfo);
  }, [srcTaskInfo]);

  const renderContent = () => {
    if (content) {
      return content;
    }
    return (
      <>
        <RenderTaskName showViewText={false} from="detail" taskInfo={taskInfo} />
        <ConciseOperation
          taskInfo={taskInfo}
          onChange={handleTaskInfoChange}
          onSuccess={handleSuccess}
        />
      </>
    );
  };

  return (
    <div
      data-task-id={taskInfo.taskId}
      data-task-title={taskInfo.title}
      onContextMenu={handleContextMenu}
      className={`${s.subtask__item} subtask__item flex-y-center`}
    >
      {renderContent()}
    </div>
  );
};

export const SortableSubTaskItem: FC<ISubTaskItemProps> = (props) => {
  return (
    <SortableItem
      data={props.taskInfo}
      hasHandle={false}
      handler={<Icon className={s.icon} fontSize={16} name="icon-pc_plane_move"></Icon>}
      className={`${s.sortable__subtask} flex-y-center`}
    >
      <SubTaskItem {...props} />
    </SortableItem>
  );
};
export default SubTaskItem;
