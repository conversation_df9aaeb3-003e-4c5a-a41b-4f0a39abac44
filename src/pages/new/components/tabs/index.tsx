import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Icon, Tooltip } from '@/components/basic';
import { RootState } from '@/models/store';
import I18N from '@/utils/I18N';
import {
  ProjectPermissionEnum,
  validatesPermission,
  ViewPermissionEnum,
  ViewTypeEnum,
} from '@/utils/permission';

import AddViewDropdown from './add-item/add-dropdown';
import DraggableTabs from './draggable-tabs';
import s from './index.less';
interface TabsProps {
  defaultKey?: string | number;
}

const Tabs: React.FC<TabsProps> = (props) => {
  const { permissions, viewTabList } = useSelector((state: RootState) => ({
    permissions: state.viewSetting.permissions!,
    viewTabList: state.viewSetting.viewTabList,
  }));

  const [canCreateCommonView, canCreatePersonView] = validatesPermission({
    permissions,
    key: [
      ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
      ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW,
    ],
  }) as boolean[];

  const [commonViewExceed, personalViewExceed] = useMemo(() => {
    const commonViewCount = viewTabList.filter(
      (view) => view.viewDistribution !== ViewTypeEnum.Personal
    ).length;
    const personalViewCount = viewTabList.length - commonViewCount;
    return [commonViewCount >= 20, personalViewCount >= 20];
  }, [viewTabList]);

  const addDisabledTip = useMemo(() => {
    if (canCreateCommonView && canCreatePersonView) {
      if (commonViewExceed && personalViewExceed) {
        return I18N.auto.publicView_3;
      }
    } else if (canCreatePersonView) {
      if (personalViewExceed) {
        return I18N.auto.numberOfPersonalViews;
      }
    }
    return '';
  }, [commonViewExceed, personalViewExceed, canCreateCommonView, canCreatePersonView]);

  return (
    <div className={s.tabBox} id="viewTabWrapper">
      <div className={s.tabs}>
        <DraggableTabs />
      </div>
      {(canCreateCommonView || canCreatePersonView) && (
        <AddViewDropdown addDisabledTip={addDisabledTip}>
          <Tooltip title={I18N.auto.addView}>
            <div className={s.addView}>
              <Icon name="icon-sys_add" fontSize={16} />
            </div>
          </Tooltip>
        </AddViewDropdown>
      )}
    </div>
  );
};
export default Tabs;
