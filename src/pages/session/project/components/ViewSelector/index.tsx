import { Select, Tooltip } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { ViewTab } from '@/types';
import { SelectValue } from '@bedrock/components/lib/Select';
import { useDispatch, useSelector } from 'react-redux';
import s from './index.less';
import ViewIcon from '@/components/ViewIcon';

const ViewSelector = () => {
  const dispatch = useDispatch<Dispatch>();

  const { viewTabList, currentViewTab } = useSelector((state: RootState) => ({
    viewTabList: state.viewSetting.viewTabList,
    currentViewTab: state.viewSetting.currentViewTab!,
  }));

  const changeCurrentViewTab = (viewId: SelectValue, { option }: any) => {
    setTimeout(() => {
      dispatch.viewSetting.toggeleView(option);
    }, 0);
  };

  const renderSelectedItem = (item?: ViewTab) => {
    if (!item) return null;
    return (
      <div className={`${s.selected__item} flex-y-center`}>
        <ViewIcon className="fs-16" viewType={item.viewType as any} />
        <div className="ellipsis">{item.name}</div>
      </div>
    );
  };

  const renderLabel = (item?: ViewTab) => {
    if (!item) return null;
    return (
      <div className={`${s.label__item} flex-y-center`}>
        <ViewIcon className={`${s['label__item--icon']} fs-16`} viewType={item.viewType as any} />
        <Tooltip title={item.name} onlyEllipsis>
          <div className={`${s.view__selector_item_name} ellipsis`}>{item.name}</div>
        </Tooltip>
      </div>
    );
  };

  return (
    <Select
      className={`${s.view__selector} mr-8`}
      // @ts-ignore
      options={viewTabList}
      value={currentViewTab?.viewId}
      fieldNames={{
        value: 'viewId',
      }}
      size="small"
      autoWidth
      autoHideTitle={true}
      dropdownClassName={s.view__dropdown}
      onChange={changeCurrentViewTab}
      renderSelectedItem={renderSelectedItem}
      renderLabel={renderLabel}
    />
  );
};

export default ViewSelector;
