import classNames from 'classnames';
import React, { PropsWithChildren, ReactElement, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Tooltip } from '@/components/basic';
import Icon from '@/components/basic/Icon';
import Modal from '@/components/basic/modal';
import { RootState } from '@/models/store';
import { Member } from '@/types';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission, ViewTypeEnum } from '@/utils/permission';

import NewView from '../new-view';
import s from './index.less';
import SortList from './sort-list';

interface Props {
  disabled?: boolean;
  className?: string;
  onChange?: (v: Member[]) => void;
  projectId?: number;
}

const ManageView: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, projectId } = props;

  const [visible, setVisible] = useState<boolean>(false);
  const { permissions, viewTabList } = useSelector((state: RootState) => ({
    permissions: state.viewSetting.permissions!,
    viewTabList: state.viewSetting.viewTabList,
  }));

  const [canCreateCommonView, canCreatePersonalView] = validatesPermission({
    permissions,
    key: [
      ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
      ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW,
    ],
  }) as boolean[];

  const [commonViewExceed, personalViewExceed] = useMemo(() => {
    const len = viewTabList?.filter(
      (view) => view.viewDistribution !== ViewTypeEnum.Personal
    ).length;
    return [len >= 20, (viewTabList?.length ?? 0) - len >= 20];
  }, [viewTabList]);

  const [canCreateCommon, canCreatePersonal] = useMemo(() => {
    return [!commonViewExceed && canCreateCommonView, !personalViewExceed && canCreatePersonalView];
  }, [canCreateCommonView, canCreatePersonalView, commonViewExceed, personalViewExceed]);

  const disableTip = useMemo(() => {
    if (canCreateCommonView && canCreatePersonalView) {
      if (commonViewExceed && personalViewExceed) {
        return I18N.auto.publicView;
      }
      return '';
    } else if (canCreatePersonalView) {
      if (personalViewExceed) {
        return I18N.auto.numberOfPersonalViews;
      }
      return '';
    }
    return I18N.auto.noPermissionToAdd;
  }, [canCreateCommonView, canCreatePersonalView, commonViewExceed, personalViewExceed]);

  return (
    <>
      {React.cloneElement(children as unknown as ReactElement, {
        onClick: () => {
          setVisible(!visible);
        },
      })}
      <Modal
        visible={visible}
        width={480}
        title={I18N.template(I18N.auto.managementView, { val1: viewTabList?.length ?? 0 })}
        okText={I18N.auto.determine}
        closable
        centered
        onCancel={() => {
          setVisible(false);
        }}
        footer={null}
        scrolled={false}
        wrapClassName={s.modal}
      >
        <div className={s.viewWrapper}>
          <div className={s.viewTabList}>
            <SortList />
          </div>
          <NewView disabled={!canCreateCommon && !canCreatePersonal}>
            <Tooltip title={disableTip}>
              <span
                className={classNames(s.addViewBtn, {
                  [s.disabledBtn]: !canCreateCommon && !canCreatePersonal,
                })}
              >
                <Icon name="icon-sys_add" fontSize={16} />
                <span className={s.tip}>{I18N.auto.addView}</span>
              </span>
            </Tooltip>
          </NewView>
        </div>
      </Modal>
    </>
  );
};

export default ManageView;
