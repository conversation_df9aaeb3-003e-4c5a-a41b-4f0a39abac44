.box__item {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  cursor: pointer;
  outline: 2px solid transparent;

  &.active {
    outline-color: var(--Brand500);
  }
}

.box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 19px;
  height: 19px;
  border-radius: 6px;
  cursor: pointer;
}

.label {
  font-size: 12px;
  line-height: 22px;
  color: var(--TextTertiary);
}

.icon {
  font-size: 24px;
  color: var(--absWhite);
}

.grid {
  display: grid;
  grid-template-columns: repeat(5, 24px);
  justify-items: center; // 水平居中
  align-items: center;
  gap: 8px 4px;
  // width: 126px;
  margin-top: 4px;
  padding: 2px;
}

.popover {
  :global {
    .rock-popover-inner {
      border-radius: 8px;
    }
    .rock-popover-inner-content {
      padding: 12px 16px;
    }
  }
}
