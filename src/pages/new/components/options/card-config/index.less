.dropdown {
  height: unset;
}

.config__pane {
  min-width: 260px;

  .config__option--left {
    white-space: nowrap;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .config__option--right {
    display: flex;
    align-items: center;
    white-space: nowrap;
    min-width: 0;
  }
}

.sub__title {
  margin-top: 1.4px;
}

.config__popover {
  min-width: 236px;

  .config__popover--item--right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    column-gap: 8px;
    margin-left: 8px;
  }

  .inactive {
    opacity: 0;
  }

  .check__icon {
    color: var(--Brand600);
  }

  :global {
    .config__popover--item {
      .operate__icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        opacity: 0;
        transition: all 0.2s;

        & > .babylon-popo-icon {
          font-size: 16px;
        }
      }

      &:hover {
        .operate__icon {
          opacity: 1;

          &:hover {
            background-color: var(--aBlack6);
          }
        }
      }
    }

    .rock-popover-inner-content {
      padding: 4px;
    }
  }
}
