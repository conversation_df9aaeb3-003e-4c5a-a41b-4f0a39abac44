import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import classNames from 'classnames';
import React, { useMemo } from 'react';

import { IconBtn } from '@/components/basic';

import s from './index.less';
interface SortableItemData extends Record<string, any> {
  id: number | string;
}

export interface SortableItemProps {
  data: SortableItemData;
  wholeRowHandle?: boolean;
  children: (v: React.ReactNode) => React.ReactNode;
}

const SortableItem: React.FC<SortableItemProps> = (props) => {
  const { data, children, wholeRowHandle = false } = props;
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: data.id,
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };
  const handleProps = useMemo(() => {
    return { ...attributes, ...listeners };
  }, [attributes, listeners]);
  return (
    <div
      style={style}
      className={classNames(s.sortable, 'todo-drag-tr', { [s.canDrag]: wholeRowHandle })}
      ref={setNodeRef}
      {...(wholeRowHandle ? handleProps : {})}
    >
      {children(
        <div
          className={classNames(s.drag, 'todo-drag', { [s.canDrag]: !wholeRowHandle })}
          {...(!wholeRowHandle ? handleProps : {})}
        >
          <div className={s.dragBtn}>
            <IconBtn
              className={s.IconBtn}
              iconClassName={s.icon}
              iconName="icon-pc_plane_move"
            ></IconBtn>
          </div>
        </div>
      )}
    </div>
  );
};

export default SortableItem;
