import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { history, useParams } from 'umi';

import Detail from '@/components/detail';
import { Dispatch } from '@/models/store';
import { ListType } from '@/utils/const';

import s from './index.less';

const HomePage: React.FC = () => {
  const params = useParams();
  const { taskId } = params as { taskId: string };
  const dispatch = useDispatch<Dispatch>();

  useEffect(() => {
    dispatch.detail.getTodoDetail(Number(taskId));
  }, [taskId]);

  const onClose = () => {
    history.go(-1);
  };
  return (
    <div className={s.page}>
      <Detail taskId={Number(taskId)} onClose={onClose}></Detail>
    </div>
  );
};
export default HomePage;
