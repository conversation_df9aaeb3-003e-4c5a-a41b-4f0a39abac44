import { IconBtn, Message } from '@/components/basic';
import I18N from '@/utils/I18N';
import { validatesVersionAndTip } from '@/utils/validate-version';
import { RockEditorPlugin, PluginKey } from '@bedrock/editor';
import { pp } from '@popo-bridge/web';
import { Image } from '@bedrock/icons-react';
import axios from 'axios';

const ImageUpload = (props) => {
  const handleClick = () => {
    if (validatesVersionAndTip('openTodoLocalDocPicker')) {
      pp.openTodoLocalDocPicker({
        maxFileCount: 5,
        maxFileSize: 200 * 1024 * 1024,
        attachmentsMd5: [],
        onComplete: (e) => {
          const attachments: any[] = [];
          // e.forEach((item) => {
          //   const index = list.findIndex((v) => v.taskId === item.taskId);
          //   if (index > -1) {
          //     const attachment = list[index];
          //     //@ts-ignore
          //     attachment.info = {
          //       ...attachment.info,
          //       ...item,
          //     };
          //     attachments.push(attachment);
          //   }
          // });
          console.log('ImageUpload', e);
          // uploadFile2Todo(attachments);
        },
      }).then(({ cancelLocalDocUpload, list: dataList, status }) => {
        if (status === 3) {
          Message.error(I18N.auto.thereAreMoreThanM);
        }
        if (status === 2) {
          Message.error(I18N.auto.atMostOnce);
          return;
        }

        console.log('dataList', dataList);
        // dataList.forEach((item) => {
        //   list.push({
        //     ...item,
        //     icon: '',
        //     loading: true,
        //   });
        // });
        // setList([...list]);
      });
    }
  };

  return <IconBtn icon={<Image />} title={'bibala'} onClick={handleClick} />;
};

export default function createImageUploadPlugin(options = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('imageUploadPlugin'),
    getToolbarConfig: (plugins) => {
      return [...plugins, [ImageUpload]];
    },
  });
}
