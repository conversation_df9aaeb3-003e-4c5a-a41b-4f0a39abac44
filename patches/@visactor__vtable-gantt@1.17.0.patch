diff --git a/es/Gantt.js b/es/Gantt.js
index 00271d73a72f248c18dfe3f83484c11e19ca3bc3..0d43521ddd15d317a018ce6a84769aac6e98c7a4 100644
--- a/es/Gantt.js
+++ b/es/Gantt.js
@@ -331,7 +331,9 @@ export class Gantt extends EventTarget {
     }
     _syncPropsFromTable() {
         this.itemCount = this.taskListTableInstance ? this.taskListTableInstance.rowCount - this.taskListTableInstance.columnHeaderLevelCount : this.records.length, 
-        this.headerHeight = this.getAllHeaderRowsHeight(), this.drawHeight = Math.min(this.getAllRowsHeight(), this.tableNoFrameHeight), 
+        this.headerHeight = this.getAllHeaderRowsHeight();
+        // POPO: 空态高度能撑满
+        this.drawHeight = this.tableNoFrameHeight; 
         this.gridHeight = this.drawHeight - this.headerHeight;
     }
     getContext() {
diff --git a/es/scenegraph/grid.js b/es/scenegraph/grid.js
index bb0ccaa8b447a0d795e008d286591dafb5559b31..81fdac77e28d5d780fcf7b1c585217763cc94afa 100644
--- a/es/scenegraph/grid.js
+++ b/es/scenegraph/grid.js
@@ -7,8 +7,10 @@ export class Grid {
         var _a;
         this._scene = scene, this.scrollLeft = 0, this.scrollTop = 0, this.x = 0, this.y = scene._gantt.getAllHeaderRowsHeight(), 
         this.width = scene.ganttGroup.attribute.width, this.height = scene.ganttGroup.attribute.height - scene.timelineHeader.group.attribute.height, 
-        this.rowHeight = scene._gantt.parsedOptions.rowHeight, this.rowCount = scene._gantt.itemCount, 
-        this.allGridWidth = scene._gantt.getAllDateColsWidth(), this.allGridHeight = scene._gantt.getAllTaskBarsHeight(), 
+        this.rowHeight = scene._gantt.parsedOptions.rowHeight, this.rowCount = scene._gantt.itemCount;
+        this.allGridWidth = scene._gantt.getAllDateColsWidth();
+        // POPO: 空态高度能撑满
+        this.allGridHeight = Math.max(scene._gantt.getAllTaskBarsHeight(), scene._gantt.tableNoFrameHeight)
         this.group = new Group({
             x: this.x,
             y: this.y,
@@ -205,7 +207,8 @@ export class Grid {
             height: this.height,
             y: this._scene._gantt.getAllHeaderRowsHeight()
         }), this.rowCount = this._scene._gantt.itemCount, this.allGridWidth = this._scene._gantt.getAllDateColsWidth(), 
-        this.allGridHeight = this._scene._gantt.getAllTaskBarsHeight(), this.group.removeAllChild(), 
+        // POPO: 空态高度能撑满
+        this.allGridHeight = Math.max(this._scene._gantt.getAllTaskBarsHeight(), this._scene._gantt.tableNoFrameHeight), this.group.removeAllChild(), 
         this.createVerticalBackgroundRects(), this.createHorizontalBackgroundRects(), this.createVerticalLines(), 
         this.createHorizontalLines(), this.createTimeLineHeaderBottomLine();
     }
diff --git a/es/scenegraph/mark-line.js b/es/scenegraph/mark-line.js
index 97dcfca77c3471274d397f3d6361074556e88d19..5c4c860ce5cd6a338f8f8664f7247c37df12d8a4 100644
--- a/es/scenegraph/mark-line.js
+++ b/es/scenegraph/mark-line.js
@@ -11,7 +11,8 @@ export class MarkLine {
             width: scene._gantt.tableNoFrameWidth,
             height: this.height,
             pickable: !1,
-            clip: !0
+            // POPO: 保证年月视图切换后当前线条不被裁剪
+            clip: !1
         }), this.group.name = "mark-line-container", scene.ganttGroup.addChild(this.group), 
         this.markLIneContainer = new Group({
             x: 0,
@@ -19,7 +20,8 @@ export class MarkLine {
             width: this._scene._gantt.getAllDateColsWidth(),
             height: this.height,
             pickable: !1,
-            clip: !0
+            // POPO: 保证年月视图切换后当前线条不被裁剪
+            clip: !1
         }), this.group.appendChild(this.markLIneContainer), this.initMarkLines();
     }
     initMarkLines() {
diff --git a/es/scenegraph/scroll-bar.js b/es/scenegraph/scroll-bar.js
index 84ddb80640de09b4963589777f852869fa5c842d..270c5a8d079c1f9d6e96fe9ab4ebba82afee3cd3 100644
--- a/es/scenegraph/scroll-bar.js
+++ b/es/scenegraph/scroll-bar.js
@@ -99,7 +99,9 @@ export class ScrollBarComponent {
             attrY = scrollStyle.barToSide ? this._gantt.tableNoFrameHeight - (hoverOn ? width : -this._gantt.scenegraph.ganttGroup.attribute.y) + this._gantt.tableY : y - (hoverOn ? width : -this._gantt.scenegraph.ganttGroup.attribute.y) + this._gantt.tableY, 
             this.hScrollBar.setAttributes({
                 x: this._gantt.scenegraph.ganttGroup.attribute.x,
-                y: attrY,
+                // POPO: 空态高度能撑满
+                // 先强制写死 drawHeight - 8，后面再仔细看为什么 AABBBounds 为什么没跟随 grid 高度
+                y: this._gantt.drawHeight - 8,
                 width: tableWidth,
                 range: [ 0, rangeEnd ],
                 visible: "always" === visible
diff --git a/es/scenegraph/task-bar.js b/es/scenegraph/task-bar.js
index 3d0ec3db1dc1badea152785746be72049ab1aa2e..c60b1bd06897af8574a8c18cd273395286e6a7c6 100644
--- a/es/scenegraph/task-bar.js
+++ b/es/scenegraph/task-bar.js
@@ -96,6 +96,12 @@ export class TaskBar {
                     ganttInstance: this._scene._gantt
                 });
             } else customLayoutObj = taskBarCustomLayout;
+
+          // bug-fix: 允许子元素溢出
+          if (customLayoutObj.allowOverflow) {
+            barGroup.attribute.clip = false
+          }
+
             customLayoutObj && (rootContainer = customLayoutObj.rootContainer, renderDefaultBar = null !== (_b = customLayoutObj.renderDefaultBar) && void 0 !== _b && _b, 
             renderDefaultText = null !== (_c = customLayoutObj.renderDefaultText) && void 0 !== _c && _c, 
             rootContainer && (rootContainer.name = "task-bar-custom-render"));
