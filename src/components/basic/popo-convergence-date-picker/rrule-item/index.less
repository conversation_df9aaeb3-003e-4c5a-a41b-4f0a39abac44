.clear {
  margin-left: 4px;
  i {
    color: var(--IconTertiary);
  }
}
.ruleItemWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;

  .left {
    column-gap: 8px;
    color: var(--TextPrimary);
    margin-right: 8px;
  }

  :global {
    .todo-item-bg {
      &:hover {
        .picker-label-arrow {
          display: block;
        }
      }
    }
  }
}
