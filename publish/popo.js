const axios = require('axios');
const url = 'http://notify.nie.netease.com/api/v1/messages';

const sendToPopo = ({ content, at_list, reciever_list }) => {
  axios
    .post(
      url,
      {
        message_type: 'popo',
        sender: '<EMAIL>',
        reciever_list,
        at_list,
        content,
      },
      {
        headers: { 'X-Notify-AccessKey': 'ce2d7fd979ed42bebef212070dd8ac38' },
      }
    )
    .then((res) => {
      console.log('发送成功', res);
    })
    .catch((err) => {
      console.log('发送失败', err);
    });
};

const publishSuccessToSend = (options) => {
  const { cimmit_user, env_tag, grayscale_tag, version_tag, ci_pipeline_url } = options;
  //   const recieverList = ['6093042'];
  //   sendToPopo({
  //     at_list: ['<EMAIL>', cimmit_user],
  //     reciever_list: recieverList,
  //     //@i18n-ignore
  //     content: `待办web发布成功：
  // 发布环境：${env_tag === 'production' ? '正式' : env_tag},
  // 是否灰度：${grayscale_tag ? '是' : '否'},
  // versiontag: ${version_tag},
  // ci流水地址：${ci_pipeline_url},
  // 发布人：${cimmit_user},`,
  //   });
};

module.exports = {
  sendToPopo,
  publishSuccessToSend,
};
