import classNames from 'classnames';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@/components/basic';
import { RootState } from '@/models/store';
import I18N from '@/utils/I18N';
import { CorlorMode } from '@/utils/platform';

import s from './index.less';

interface FollowGuideProps {
  onHideGuide: React.MouseEventHandler<HTMLDivElement>;
}

const FollowGuide: React.FC<FollowGuideProps> = ({ onHideGuide }) => {
  const { corlorMode, language } = useSelector((state: RootState) => ({
    corlorMode: state.user.corlorMode,
    language: state.user.language,
  }));
  const imgSrc = useMemo(() => {
    return `https://popo.res.netease.com/popo-assets/todo/gzbg-${
      corlorMode === CorlorMode.light ? 'light' : 'dark'
    }-${language}.png`;
  }, [corlorMode, language]);
  return (
    <div className={s.pageRightBox}>
      <div className={s.tip}>
        <div className={classNames(s.title, 'mb-6')}>{I18N.auto.focusOnTasks}</div>
        <div className={s.title}>{I18N.auto.realTimeMonitoringOfAnyTask}</div>
        <div className={s.desc}>{I18N.auto.afterFollowingTheTask}</div>
        <Button className={s.btn} type="checked" size="small" onClick={onHideGuide}>
          {I18N.auto.immediateMedicalExamination}
        </Button>
      </div>
      <div className={s.imgWrapper}>
        <img className={s.bg} src={imgSrc}></img>
      </div>
    </div>
  );
};
export default FollowGuide;
