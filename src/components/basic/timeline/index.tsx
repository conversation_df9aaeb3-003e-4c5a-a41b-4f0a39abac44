import { Timeline as BedrockTimeline } from '@bedrock/components';
import { TimelineProps } from '@bedrock/components/lib/Timeline/Timeline';
import classNames from 'classnames';
import React from 'react';

import s from './index.less';
interface Props extends TimelineProps {
  children: React.ReactNode;
}
const Timeline = (props: Props) => {
  const { className, children } = props;
  return (
    <BedrockTimeline className={classNames(s.timeLine, className)}>{children}</BedrockTimeline>
  );
};

Timeline.Item = BedrockTimeline.Item;

export default Timeline;
