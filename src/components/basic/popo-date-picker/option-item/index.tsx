import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useState } from 'react';

import Icon from '@/components/basic/Icon';

import s from './index.less';

interface Props {
  title: string;
  onClose?: () => void;
  showSelect?: boolean;
}

const OptionItem: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, title, onClose, showSelect } = props;
  const [show, setShow] = useState<boolean>(false);
  const openSelect = () => {
    if (!show) {
      setShow(true);
    }
  };
  useEffect(() => {
    if (showSelect) {
      setShow(true);
    }
  }, [showSelect]);
  return (
    <div className={classNames(s.item, { [s.showSelect]: show })}>
      <div className={classNames(s.label)} onClick={openSelect}>
        <Icon name="icon-add" fontSize={14} className={s.plus}></Icon>
        {title}
      </div>
      {show ? (
        <>
          <div className={s.select}>{children}</div>
          <Icon
            name="icon-close"
            className={s.clear}
            fontSize={14}
            onClick={() => {
              onClose?.();
              setShow(false);
            }}
          ></Icon>
        </>
      ) : null}
    </div>
  );
};

export default OptionItem;
