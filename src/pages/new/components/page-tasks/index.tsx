import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { IconBtn } from '@/components/basic';
import Button from '@/components/basic/button';
import Divider from '@/components/basic/divider';
import { ModeOptions } from '@/components/gantt/utils';
import { ModeBtn } from '@/components/mode-btn';
import { Dispatch, RootState } from '@/models/store';
import { setStorage, StorageType } from '@/utils';
import Const, { ViewType } from '@/utils/const';

import DetailDrawer from '../detail-drawer';
import Options from '../options';
import Tabs from '../tabs';
import View from '../view';
import { planView2ScaleType } from '../view/timeline/utils';
import s from './index.less';
import I18N from '@/utils/I18N';
import classNames from 'classnames';

interface IPageRightProps {
  prefixOption?: React.ReactNode;
  options?: React.ReactNode;
  showTabs?: boolean;
  className?: string;
}

const PageRight: React.FC<IPageRightProps> = ({
  prefixOption,
  options,
  showTabs = true,
  className,
}) => {
  const { currentViewTab } = useSelector((state: RootState) => {
    return {
      currentViewTab: state.viewSetting.currentViewTab,
    };
  });

  useEffect(() => {
    setStorage(Const.RetryReloadPageTimes, 0, StorageType.local);
  }, []);

  return (
    <div className={classNames(s.taskView, className)}>
      <DetailDrawerContainer />
      {showTabs && (
        <div className={s.tabBox}>
          <Tabs />
        </div>
      )}
      <div className={s.toolbar}>
        {options || <Options prefixOption={prefixOption} className={s.leftToolbar} />}
        {currentViewTab.viewType === ViewType.timeline && (
          <div className={`${s.rightToolbar} rightToolbar`}>
            <div className={s.shadow}></div>
            <div className={s.right}>
              <ModeBtn
                value={planView2ScaleType(currentViewTab.planViewMode)}
                options={ModeOptions}
                onChange={(scaleType) => {
                  window.gantt.onToggleView(scaleType);
                }}
              />
              <Divider />
              <Button
                type="checked-neutral"
                onClick={() => {
                  window.gantt.onBackToToday();
                }}
                className={s.backTodayBtn}
              >
                {I18N.auto.today}
              </Button>
              <IconBtn
                title={I18N.auto.previousScreen}
                iconName="icon-pc_plane_day_before"
                className={s.pre}
                onClick={() => {
                  window.gantt.onSwipe('prev');
                }}
              ></IconBtn>
              <IconBtn
                title={<span style={{ whiteSpace: 'nowrap' }}>{I18N.auto.nextScreen}</span>}
                iconName="icon-pc_plane_day_after"
                className={s.next}
                onClick={() => {
                  window.gantt.onSwipe('next');
                }}
              ></IconBtn>
            </div>
          </div>
        )}
      </div>
      <div className={s.view}>
        <View />
      </div>
    </div>
  );
};

export const DetailDrawerContainer: React.FC = () => {
  const { visibleDetail } = useSelector((state: RootState) => ({
    visibleDetail: state.detail.visibleDetail,
    // taskId: state.detail.taskId,
  }));

  useEffect(() => {
    const tableBody = document.querySelector('#tanstack-table__body') as HTMLElement;
    function handleClick(e: MouseEvent) {
      const target = e.target as HTMLElement;
      const classList = target?.classList;

      if (classList?.contains('table__cell')) {
        if (visibleDetail) {
          dispatch.detail.closeDetail({});
        }
        const rowBoxEl = target.querySelector('.row__box') as HTMLDivElement;
        if (rowBoxEl) {
          rowBoxEl.click();
        }
      }
    }

    if (tableBody) {
      tableBody.classList.toggle('detail-drawer-open', visibleDetail);
      tableBody.addEventListener('click', handleClick);
    }

    return () => {
      tableBody?.removeEventListener('click', handleClick);
    };
  }, [visibleDetail]);

  const dispatch = useDispatch<Dispatch>();
  return (
    <DetailDrawer
      // taskId={taskId}
      visible={visibleDetail}
      onClose={() => {
        dispatch.detail.closeDetail({});
      }}
      afterVisibleChange={() => {
        //lockRef.current = false;
      }}
    ></DetailDrawer>
  );
};

export default PageRight;
