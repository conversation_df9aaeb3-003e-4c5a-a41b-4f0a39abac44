import { <PERSON><PERSON><PERSON>, EventClickArg, EventDropArg } from '@fullcalendar/core'
// import esLocale from '@fullcalendar/core/locales/es';
import jaLocale from '@fullcalendar/core/locales/ja'
import zhLocale from '@fullcalendar/core/locales/zh-cn'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin, { EventDragStopArg, EventResizeDoneArg, ThirdPartyDraggable } from '@fullcalendar/interaction'
import FullCalendar from '@fullcalendar/react'
import { useUpdateEffect } from 'ahooks'
import classNames from 'classnames'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { ApiTodoUpdateDeadlinePostRequest } from '@/api'
import { taskUpdateDeadline } from '@/api-common'
import { IconBtn } from '@/components/basic'
import { getDayStart } from '@/components/basic/popo-date-picker/utils'
import { Dispatch, RootState } from '@/models/store'
import Filter from '@/pages/new/components/options/filter'
import AddDropdown from '@/pages/new/components/view/calendar/add-dropdown'
import ToggleUnplanned, { FromType } from '@/pages/new/components/view/calendar/toggle-unplanned'
import { TaskInfo } from '@/types'
import { CalendarModeType, EnumCalendarAddSource, EnumEmitter, PPTimeFormat, TaskNavigatorType } from '@/utils/const'
import I18N from '@/utils/I18N'
import { ProjectPermissionEnum, TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import { CalendarSaveViewBtn } from './calendar-save-view-btn'
import Card, { CardType } from './card'
import s from './index.less'
import ListErrorBoundary from './list-error-boundary'
import Setting from './setting'
import UnplannedList from './unplanned-list'
import { getPermissionsByTaskId } from '@/hooks/useGetPermissions'
import { getIsInSession } from '@/models/utils'
import { POPOBridgeEmitter } from '@popo-bridge/web'
import CalendarMode from './calendar-mode'
import { ConfigProvider } from '@bedrock/components'

enum RelativeDate {
  pre = 'pre',
  toDay = 'toDay',
  next = 'next',
}

const getCalendarTitle = (language?: string, time?: Dayjs | Date | string | number) => {
  return language === 'en-US' ? dayjs(time).format(`MMM YYYY`) : dayjs(time).format(`YYYY[${I18N.auto.year}]MMM`)
}
const sortBypriority = (a: number, b: number) => {
  if (a === 0) {
    a = 99
  }
  if (b === 0) {
    b = 99
  }
  return a - b
}

export interface CalendarProps {}

const CalendarList: React.FC<CalendarProps> = () => {
  const calendarRef = useRef()
  const refMoveIdRef = useRef<number>()
  const {
    calendarList,
    showWeekend,
    visibleDetail,
    planViewMode = CalendarModeType.week,
    showUnplanned,
    language,
    calendarKey,
    navigatorId,
    userInfo,
    permissions,
    currentViewTab,
    permissionsMap,
  } = useSelector((state: RootState) => ({
    calendarList: state.todoCalendar.calendarList,
    showWeekend: state.viewSetting.currentViewTab.showWeekend,
    visibleDetail: state.detail.visibleDetail,
    planViewMode: state.viewSetting.currentViewTab.planViewMode || CalendarModeType.week,
    showUnplanned: state.user.showUnplanned,
    language: state.user.language,
    calendarKey: state.todoCalendar.calendarKey,
    navigatorId: state.viewSetting.navigatorId,
    userInfo: state.user.userInfo,
    permissions: state.viewSetting.permissions,
    currentViewTab: state.viewSetting.currentViewTab,
    permissionsMap: state.permissions.permissionsMap,
  }))

  const dispatch = useDispatch<Dispatch>()
  const [title, setTitle] = useState<string>(getCalendarTitle(language))

  const showAddBtn = useMemo(() => {
    return validatesPermission({
      permissions: permissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    })
  }, [permissions])

  const locales = useMemo(() => {
    return [zhLocale, jaLocale]
  }, [])

  const events = useMemo<any[]>(() => {
    return calendarList

      .sort((a, b) => {
        let levela = a.priority!
        let levelb = b.priority!
        return sortBypriority(levela, levelb)
      })
      .map(item => {
        let start = item.startTime
        if (!start || start <= 0) {
          start = item.deadline
        }
        //截止时间
        const deadline = getDayStart(dayjs(item.deadline).add(1, 'day')).valueOf()
        const permissions = getPermissionsByTaskId(item.taskId)
        const [CAN_EDIT] = validatesPermission({
          permissions: permissions || item.permissions,
          key: [TaskPermissionEnum.CAN_EDIT],
        }) as boolean[]

        return {
          id: String(item.taskId),
          title: item.title,
          start: dayjs(start).format('YYYY-MM-DD'),
          end: dayjs(deadline).format('YYYY-MM-DD'), // 开始时间包含  结束时间时排除的
          //allDay: true,
          editable: CAN_EDIT,
          extendedProps: {
            data: item,
            className: 'xxxxx',
            level: item.priority,
            done: item.selfFinished === 1, //已完成
            //time: item.deadline,
            deadline: item.deadline,
            deadlineFormat: item.deadlineFormat,
            startTime: item.startTime,
            taskId: item.taskId,
            isExpired: item.isExpired,
            isToday: item?.isToday,
            timeFormat: item.deadlineFormat,
          },
        }
      })
  }, [calendarList, permissionsMap])
  const getCalendarTodoList = () => {
    //@ts-ignore
    const calendar = calendarRef.current?.getApi?.() as CalendarApi
    if (calendar) {
      dispatch.todoCalendar.getCalendarTodoList({
        start: calendar.view.activeStart,
        end: calendar.view.activeEnd,
      })
    } else {
      dispatch.todoCalendar.getCalendarTodoList({})
    }
  }

  function switchingMode(v: CalendarModeType) {
    dispatch.viewSetting.updateCurrentViewAndUpdateServer({
      planViewMode: v,
    })
    //@ts-ignore
    const calendar = calendarRef.current?.getApi?.() as CalendarApi
    if (calendar) {
      if (v === CalendarModeType.week) {
        calendar.changeView('dayGridWeek')
      } else {
        calendar.changeView('dayGridMonth')
      }
    }
    getCalendarTodoList()
    setTitle(getCalendarTitle(language, dayjs()))
  }

  const changeRelativeDate = (v: RelativeDate) => {
    //@ts-ignore
    const calendar = calendarRef.current?.getApi?.() as CalendarApi
    if (calendar) {
      if (v === RelativeDate.pre) {
        calendar.prev()
      } else if (v === RelativeDate.next) {
        calendar.next()
      } else {
        calendar.today()
      }
      getCalendarTodoList()
      setTitle(getCalendarTitle(language, calendar.getDate()))
    }
  }
  const eventClick: (arg: EventClickArg) => void = e => {
    dispatch.detail.openDetail(Number(e.event.id))
  }
  const updateTime = (param: ApiTodoUpdateDeadlinePostRequest) => {
    return taskUpdateDeadline(param)
      .then(() => {
        //更新时间
        dispatch.todoCalendar.updateTodoList(param)
      })
      .catch(error => {
        console.log(error)
        //如果更新失败了将原来的对象重新赋值刷新一遍列表
        dispatch.todoCalendar.setCalendarTodoList([...calendarList])
      })
  }
  const eventDrop: (arg: EventDropArg) => void = e => {
    console.log('eventDrop triggered:', e) // 调试日志

    // 验证事件是否可编辑
    if (!e.event.extendedProps?.data) {
      console.warn('Event drop not allowed')
      e.revert() // 恢复到原位置
      return
    }

    let { deadline, deadlineFormat, startTime, taskId } = e.event.extendedProps
    const { days } = e.delta
    deadline = dayjs(deadline).add(days, 'days').valueOf()
    if (startTime) {
      startTime = dayjs(startTime).add(days, 'days').valueOf()
    }
    const param: ApiTodoUpdateDeadlinePostRequest = {
      taskId,
      deadline: deadline,
      deadlineFormat: deadlineFormat,
      startTime: startTime ? startTime : undefined,
    }
    updateTime(param)
  }

  /**
   * 该事件用来替换eventDrop事件, 因为eventDrop在windows下，偶现会出现拖拽结束后，事件drop事件没有触发的情况，原因位置
   * @param e
   */
  const handleDragStop: (arg: EventDragStopArg) => void = e => {
    console.log('handleDragStop triggered:', e) // 调试日志

    let { taskId, startTime, deadline: prevDeadline, deadlineFormat } = e.event.extendedProps
    setTimeout(() => {
      const dropEl = document.querySelector(`.fc-day .calendar-task-item[id='${taskId}']`) as HTMLElement
      if (dropEl) {
        const deadlineStr = Number(dropEl.dataset.deadline)
        if (deadlineStr) {
          const deadline = dayjs(deadlineStr)
          const deltaDays = deadline.diff(dayjs(prevDeadline), 'day')
          if (startTime) {
            startTime = dayjs(startTime).add(deltaDays, 'days').valueOf()
          }
          const param: ApiTodoUpdateDeadlinePostRequest = {
            taskId,
            deadline: deadlineStr,
            deadlineFormat: deadlineFormat,
            startTime: startTime ? startTime : undefined,
          }
          updateTime(param)
        }
      }
    }, 160)
  }

  const eventResize: (arg: EventResizeDoneArg) => void = e => {
    let { deadline, deadlineFormat, startTime, taskId } = e.event.extendedProps as TaskInfo
    const { days: startDay } = e.startDelta
    if (startDay) {
      if (!startTime) {
        startTime = deadline
      }
      startTime = getDayStart(dayjs(startTime).add(startDay, 'days')).valueOf()
    }
    const { days: endDay } = e.endDelta
    if (endDay) {
      // 修改截止时间的时候没有开始时间 赋予当前截止时间
      if (!startTime) {
        startTime = getDayStart(dayjs(deadline)).valueOf()
      }
      deadline = dayjs(deadline).add(endDay, 'days').valueOf()
    }
    const param: ApiTodoUpdateDeadlinePostRequest = {
      taskId,
      deadline: deadline,
      deadlineFormat: deadlineFormat,
      startTime: startTime ? startTime : undefined,
    }
    updateTime(param)
  }

  useEffect(() => {
    const containerEl = document.getElementById('external-events-list')!
    new ThirdPartyDraggable(containerEl, {
      itemSelector: '.dndDraggableEl',
      mirrorSelector: '.dndDraggableEl', // the dragging element that dragula renders
      eventData: function (eventEl) {
        const id = eventEl.children[0].getAttribute('taskId')
        return {
          title: eventEl.innerText,
          id: id,
        }
      },
    })
    //TODO 此处的请求需要调整时机, 按照约定由viewSetting内统一触发所有视图的请求,但是计划视图又需要依赖视图能力的开始和截止时间
    if (currentViewTab.viewId) {
      dispatch.todoCalendar.initData()
      getCalendarTodoList()
    }
  }, [currentViewTab.viewId])

  useUpdateEffect(() => {
    //@ts-ignore
    const calendar = calendarRef.current?.getApi?.() as CalendarApi
    if (calendar) {
      setTimeout(() => {
        calendar.updateSize() // hack 动画结束之后在重新渲染日历
      }, 300)
    }
  }, [showUnplanned])

  useEffect(() => {
    POPOBridgeEmitter.addListener(EnumEmitter.CalendarModeTypeChange, switchingMode)

    return () => {
      POPOBridgeEmitter.removeListener(EnumEmitter.CalendarModeTypeChange, switchingMode)
    }
  }, [])

  const memoAssignees = useMemo(() => {
    if (navigatorId === TaskNavigatorType.assignToMe) {
      return [userInfo!]
    }
    return []
  }, [navigatorId, userInfo])

  return (
    <ConfigProvider
      getPopupContainer={() => {
        return document.querySelector(`.${s.calendarList}`) as HTMLElement
      }}
    >
      <div className={`${s.calendarList} calendarList`}>
        <div className={s.calendarListLeft}>
          <div className={s.todoContainer}>
            <div className={`${s.headerToolbar} header__toolbar`}>
              <div className={s.toolbarLeft}>
                <div className={`${s.dateTime} date__time`}>{title}</div>
                <div className={s.toggleDate}>
                  <IconBtn
                    iconName="icon-pc_plane_day_before"
                    className={s.pre}
                    onClick={() => {
                      changeRelativeDate(RelativeDate.pre)
                    }}
                  ></IconBtn>
                  <div
                    className={s.today}
                    onClick={() => {
                      changeRelativeDate(RelativeDate.toDay)
                    }}
                  >
                    {I18N.auto.today}
                  </div>
                  <IconBtn
                    iconName="icon-pc_plane_day_after"
                    className={s.next}
                    onClick={() => {
                      changeRelativeDate(RelativeDate.next)
                    }}
                  ></IconBtn>
                </div>
              </div>
              {getIsInSession() ? (
                <div className={s.toolbarRight}>
                  <CalendarMode />
                  <ToggleUnplanned from={FromType.calendar}></ToggleUnplanned>
                </div>
              ) : (
                <div className={s.toolbarRight}>
                  <Filter />
                  <CalendarMode />
                  {/* <div className={s.mode}>
                  <div
                    className={classNames(s.modeBtn, {
                      [s.modeBtnActive]: planViewMode === CalendarModeType.week,
                    })}
                    onClick={() => {
                      switchingMode(CalendarModeType.week);
                    }}
                  >
                    {I18N.auto.week}
                  </div>
                  <div
                    className={classNames(s.modeBtn, {
                      [s.modeBtnActive]: planViewMode === CalendarModeType.month,
                    })}
                    onClick={() => {
                      switchingMode(CalendarModeType.month);
                    }}
                  >
                    {I18N.auto.month}
                  </div>
                </div> */}
                  <Setting>
                    <IconBtn
                      fontSize={16}
                      className={s.settingBtn}
                      iconName="icon-pc_plane_more"
                      title={I18N.auto.viewLayoutWord}
                      placement="top"
                    ></IconBtn>
                  </Setting>
                  <ToggleUnplanned from={FromType.calendar}></ToggleUnplanned>
                  <CalendarSaveViewBtn />
                </div>
              )}
            </div>
            <div className={s.fullCalendarContainer}>
              <ListErrorBoundary key={planViewMode}>
                <FullCalendar
                  key={calendarKey}
                  //@ts-ignore
                  ref={calendarRef}
                  events={events}
                  locales={locales}
                  locale={language}
                  firstDay={1}
                  plugins={[dayGridPlugin, interactionPlugin]}
                  initialView={planViewMode === CalendarModeType.month ? 'dayGridMonth' : 'dayGridWeek'}
                  height={'100%'}
                  editable={!visibleDetail} // 处于“我关注的”时禁止操作
                  eventStartEditable={true} // 明确启用开始时间编辑
                  eventDurationEditable={true} // 明确启用持续时间编辑
                  eventResizableFromStart={true}
                  droppable={true}
                  headerToolbar={false}
                  footerToolbar={false}
                  weekends={showWeekend}
                  eventClick={eventClick}
                  // eventDrop={eventDrop}
                  eventDrop={console.log}
                  eventDragStop={handleDragStop}
                  eventResize={eventResize}
                  moreLinkContent={props => {
                    return <div>{I18N.template(I18N.auto.other, { val1: props.num })}</div>
                  }}
                  eventReceive={info => {
                    //  未计划任务加入到日历视图
                    const id = info.draggedEl.getAttribute('taskId')
                    if (id) {
                      refMoveIdRef.current = Number(id)
                      const param: ApiTodoUpdateDeadlinePostRequest = {
                        taskId: Number(id),
                        deadline: dayjs(info.event.start).valueOf(),
                        deadlineFormat: PPTimeFormat.olayDay,
                        startTime: undefined,
                      }
                      updateTime(param).then(() => {
                        //TODO 更新未计划任务 现在是直接刷新列表
                        dispatch.todoCalendar.getCalendarTodoList({})
                        dispatch.todoCalendar.getUnplannedList({ page: 1 })
                      })
                    }
                  }}
                  eventContent={v => {
                    const { level, done, deadline, taskId, isExpired, isToday, timeFormat, data } =
                      v.event.extendedProps
                    return (
                      <Card
                        title={v.event.title}
                        level={level}
                        mode={planViewMode!}
                        laneTime={v.event.end}
                        time={deadline ? dayjs(deadline) : undefined}
                        done={done}
                        taskId={taskId}
                        isExpired={isExpired}
                        isToday={!!isToday}
                        timeFormat={timeFormat}
                        //type={CardType.onlyShow} //todo
                        data={data}
                      ></Card>
                    )
                  }}
                  dayCellContent={props => {
                    return (
                      <div className={'fc-daygrid-day-todo-cell'}>
                        <div className={props.isToday ? 'fc-daygrid-day-todo-cell-tody' : undefined}>
                          {dayjs(props.date).get('D')}
                        </div>
                        <div
                          className={classNames('fc-daygrid-day-todo-add', {
                            ['show']: showAddBtn,
                          })}
                        >
                          <AddDropdown
                            assignees={memoAssignees}
                            date={dayjs(props.date)}
                            onAddSuccess={() => {
                              dispatch.todoCalendar.getCalendarTodoList({})
                            }}
                            source={EnumCalendarAddSource.calendar}
                          >
                            <IconBtn
                              title={I18N.auto.newTask}
                              iconName="icon-sys_add"
                              fontSize={12}
                              className={s.addIcon}
                            ></IconBtn>
                          </AddDropdown>
                        </div>
                      </div>
                    )
                  }}
                  dayHeaderContent={props => {
                    return <div>{dayjs(props.date).format('dddd')}</div>
                  }}
                ></FullCalendar>
              </ListErrorBoundary>
            </div>
          </div>
        </div>
        <div className={classNames(s.calendarListRight, { [s.hiddenUnplanned]: !showUnplanned })}>
          <UnplannedList showAddBtn={showAddBtn}></UnplannedList>
        </div>
      </div>
    </ConfigProvider>
  )
}
export default CalendarList
