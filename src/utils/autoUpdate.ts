/**
 * 页面重新被激活的时候自动更新方案
 */
import axios from 'axios';

import { onPageVisibleChange } from '@/utils';
const localVersionId = process.env.POPO_VERSION!;

console.log('##localVersion:', localVersionId);

function getLastestVersion() {
  return axios.get(`/version.txt?t=${Date.now()}`).then((res) => {
    return res.data;
  });
}

let isWaitingToUpdate = false;

export const checkAppVersion = async (refreshNow?: boolean) => {
  let latestV = Number(await getLastestVersion());
  //时间戳比较
  console.log('latestV----', latestV);
  console.log('localVersionId----', localVersionId);
  if (latestV > Number(localVersionId)) {
    isWaitingToUpdate = true;
    console.log('New version detected and ready for refresh');
    if (refreshNow) {
      location.replace(`${location.origin}${location.pathname}${location.search}`);
    }
  }
};

export default function autoUpdate() {
  onPageVisibleChange(async (isShow) => {
    console.log('autoUpdate start');
    if (isShow) {
      checkAppVersion();
    } else {
      if (isWaitingToUpdate) {
        setTimeout(() => {
          location.replace(`${location.origin}${location.pathname}${location.search}`);
        }, 10);
      }
    }
  });
}
