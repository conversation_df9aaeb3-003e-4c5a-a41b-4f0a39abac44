import { OperateDelete, OperateRename } from '@babylon/popo-icons';
import { ActionType } from '@bedrock/components/lib/Dropdown/interface';
import classNames from 'classnames';
import { PropsWithChildren } from 'react';

import { Dropdown, Icon, IconBtn, Menu } from '@/components/basic';
import I18N from '@/utils/I18N';

import { EditType } from '../interface';
import s from './index.less';

interface ViewTabMoreOptionProps {
  onChange?: (type: EditType) => void;
  iconClassName?: string;
  trigger?: ActionType | ActionType[];
  canDelete?: boolean;
  canEdit?: boolean;
}
const ViewTabMoreOption = (props: PropsWithChildren<ViewTabMoreOptionProps>) => {
  const { onChange, iconClassName, children, trigger = 'click', canDelete, canEdit } = props;
  const overlayBasic = () => {
    return (
      <Menu
        onClick={(value) => {
          if (value.key === 'rename') {
            onChange?.(EditType.rename);
          } else {
            onChange?.(EditType.remove);
          }
        }}
      >
        {canEdit && (
          <Menu.Item key={'rename'}>
            <OperateRename className={classNames(s.icon, 'fs-16')} />
            <span>{I18N.auto.rename}</span>
          </Menu.Item>
        )}
        {canDelete && (
          <Menu.Item key={'remove'}>
            <OperateDelete className={classNames(s.icon, s.removeIcon, 'fs-16')} />
            <span className={s.removeTip}> {I18N.auto.delete}</span>
          </Menu.Item>
        )}
      </Menu>
    );
  };
  return (
    <>
      {!canDelete && !canEdit ? (
        children
      ) : (
        <Dropdown
          overlay={overlayBasic()}
          trigger={trigger}
          overlayClassName={s.viewTabMoreDropdown}
        >
          {children ? (
            children
          ) : (
            <span>
              <IconBtn
                iconClassName={iconClassName}
                className={s.more}
                iconName="icon-pc_plane_more"
              ></IconBtn>
            </span>
          )}
        </Dropdown>
      )}
    </>
  );
};

export default ViewTabMoreOption;
