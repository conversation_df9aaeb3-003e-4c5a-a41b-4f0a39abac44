import classNames from 'classnames';
import { ReactNode } from 'react';

import s from './index.less';

interface PlaceholderProps {
  text?: ReactNode;
  className?: string;
}

const Placeholder: React.FC<PlaceholderProps> = (props) => {
  const { text, className } = props;
  return <div className={classNames(s.placeholder, 'com-placeholder', className)}>{text}</div>;
};

export default Placeholder;
