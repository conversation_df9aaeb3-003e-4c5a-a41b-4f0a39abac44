// 创建通用的 todoList 服务

import { TodoInfo, TaskInfo } from '@/types'
import { cancelRequest } from '@/utils/request'
import dayjs from 'dayjs'
import { isProjectId, handleSeverList, getExpandedIndexMap } from '../utils'
import { REQUEST_ID } from '@/utils/const'
import { apiGetTaskList } from '@/api-common'
import { getGroupedList, normalizeTimestamp } from '@/hooks/useGetGroupedList'
import { store } from '../store'

// src/services/todoListService.ts
interface TodoListOptions {
  page?: number
  scrollId?: string
  size?: number
  onlyGetData?: boolean
}

interface TodoListConfig {
  // 请求相关
  requestId: string
  requestAPI: any

  // 数据处理
  dataSelector: (state: any) => TodoInfo[]
  userInfoSelector: (state: any) => any
  navigatorIdSelector: (state: any) => string

  // 状态更新
  setRefreshing: (refreshing: boolean) => void
  setData: (data: any) => void
  setPage: (page: any) => void
  processData?: (data: { dataList: TodoInfo[] }) => void
  getTreeList?: (data: { srcList: TodoInfo[] }) => void

  // 参数构建
  getComParams: () => any

  // 递归调用
  getTodoList: (options: TodoListOptions) => Promise<any>
}

const DefaultPagination = {
  size: 200,
  more: false,
  order: undefined,
  orderBy: undefined,
}

export const getTreeList = ({
  srcList,
  dispatch,
  modelName,
}: {
  srcList: TodoInfo[]
  dispatch: any
  modelName: string
}) => {
  let newList = srcList
  if (!newList) {
    newList = store.getState()[modelName].dataList
  }
  const { queryGroupBys = [] } = store.getState().viewSetting.currentViewTab
  const treeList = getGroupedList(newList, queryGroupBys, {
    fieldsName: { children: modelName === 'gantt' ? 'children' : 'subtask' },
  }) as TodoInfo[]

  console.log('treeList', treeList)
  dispatch[modelName].setData({
    treeList: [...treeList],
  })
}

export const fetchTaskList = async ({
  options,
  dispatch,
  modelName,
  handleItem,
}: {
  options: TodoListOptions
  dispatch: any
  modelName: string
  handleItem?: (item: TodoInfo) => TodoInfo
}): Promise<{
  list: TodoInfo[]
  hasMore?: boolean
  totalTaskCount?: number
}> => {
  // 取消之前的请求
  cancelRequest(REQUEST_ID.GET_TASK_LIST, 'user canceled')

  const { page = 1, scrollId, size = DefaultPagination.size, onlyGetData } = options || {}
  const dataList = store.getState()[modelName]?.dataList

  const { viewSetting, user } = store.getState()
  const { userInfo } = user

  // 构建请求参数
  const _param = dispatch.viewSetting.getComParams({})
  const param = {
    ..._param,
    // @ts-ignore
    timezone: dayjs.tz?.guess?.(),
    size: size < DefaultPagination.size ? DefaultPagination.size : size,
    scrollId: scrollId,
  }

  try {
    // 设置加载状态
    if (page === 1) {
      dispatch[modelName].setData({
        refreshing: true,
      })
    }

    // 处理项目相关参数
    const isProject = isProjectId(param.navigatorId)
    if (isProject) {
      param.projectId = param.navigatorId
    }

    // 初始化数据跟踪
    // let existingTaskIds = new Set<number>()

    // 收集已有的任务ID
    // if (Math.ceil(size / DefaultPagination.size) > 0) {
    //   dataList?.forEach(handleItem)
    // }

    // 执行请求
    param.size = DefaultPagination.size
    const response = await apiGetTaskList(param, {
      errorSilent: true,
      requestId: REQUEST_ID.GET_TASK_LIST,
    })

    const { list: thisList, ...retData } = response || {}
    let _list: TaskInfo[] = []

    // 处理返回数据
    if (thisList && param.navigatorId === viewSetting.navigatorId) {
      if (page === 1) {
        _list = _list.concat(handleSeverList({ list: thisList, userInfo, handleItem }))
      } else {
        _list = dataList?.concat(
          handleSeverList({
            list: thisList,
            userInfo,
            handleItem,
            traverseFunc: 'filter',
          }) || [],
        )
      }
    }

    // 更新滚动ID
    if (thisList?.length) {
      param.scrollId = thisList[thisList.length - 1].taskId
    }

    // 处理树形结构
    getTreeList({ srcList: _list, dispatch, modelName })

    // 更新状态
    dispatch[modelName].setData({
      hasData: !!_list.length,
      refreshing: false,
      // taskIds: [...existingTaskIds],
      expandedKeysMap: getExpandedIndexMap(),
      more: retData.hasMore,
      total: retData.totalTaskCount,
      dataList: _list,
    })
    // 处理数据
    if (!onlyGetData) {
      dispatch[modelName].processData({ dataList: _list as TodoInfo[] })
    }

    // 递归加载更多数据
    if (retData.hasMore) {
      await fetchTaskList({
        options: {
          ...options,
          ...param,
          page: page + 1,
        },
        dispatch,
        modelName,
      })
    }

    return { list: _list, ...retData }
  } catch (err) {
    console.log('TodoList fetch error:', err)
    dispatch[modelName].setData({
      refreshing: false,
    })
    throw err
  }
}
