import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import classNames from 'classnames'
import React, { useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { Icon } from '@/components/basic'
import { Dispatch, RootState } from '@/models/store'
import { EnumCalendarAddSource, TaskNavigatorType } from '@/utils/const'
import I18N from '@/utils/I18N'

import AddDropdown from '../add-dropdown'
import ToggleUnplanned, { FromType } from '../toggle-unplanned'
import Droppable from './droppable'
import s from './index.less'
import RenderItem, { UnplannedItemProps } from './item'

export type Props = {
  showAddBtn?: boolean
}
const UnplannedList: React.FC<Props> = props => {
  const { showAddBtn } = props
  const [activeId, setActiveId] = useState<string | null>(null)
  const { unplannedList, more, isFollower, userInfo, navigatorId } = useSelector((state: RootState) => ({
    unplannedList: state.todoCalendar.unplannedList,
    more: state.todoCalendar.more,
    isFollower: state.viewSetting.navigatorId === TaskNavigatorType.myFollow,
    navigatorId: state.viewSetting.navigatorId,
    userInfo: state.user.userInfo,
  }))

  const dispatch = useDispatch<Dispatch>()
  const handleDragStart = (v: DragStartEvent) => {
    setActiveId(v.active.id as string)
  }

  const handleDragCancel = () => setActiveId(null)
  const handleDragOver = (v: DragOverEvent) => {
    // console.log('handleDragOver---', v);
  }
  const getActiveItem = (id: UniqueIdentifier) => unplannedList.find(item => item.taskId === id)
  const activeItem = activeId ? getActiveItem(activeId) : ({} as UnplannedItemProps)
  const handleDragEnd = (v: DragEndEvent) => {
    const dragOverlayEL = document.getElementsByClassName('dnd-DragOverlay')[0]
    dragOverlayEL.classList.add(s.hidden)
    setActiveId(null)
  }
  const atBottomStateChange = (atBottom: boolean) => {
    if (more && atBottom) {
      dispatch.todoCalendar.getUnplannedList({})
    }
  }
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 2,
      },
    }),
  )
  const memoAssignees = useMemo(() => {
    if (navigatorId === TaskNavigatorType.assignToMe) {
      return [userInfo!]
    }
    return []
  }, [navigatorId, userInfo])
  return (
    <div className={s.unplannedList}>
      <div className={s.head}>
        <div className={s.title}>{I18N.auto.unplannedAppointment}</div>
        <ToggleUnplanned from={FromType.unplanned}></ToggleUnplanned>
      </div>
      <div className={classNames(s.addbox, { [s.hidden]: !showAddBtn })}>
        <AddDropdown
          assignees={memoAssignees}
          onAddSuccess={opt => {
            if (opt.hasDeadline) {
              dispatch.todoCalendar.getCalendarTodoList({})
            } else {
              dispatch.todoCalendar.getUnplannedList({ page: 1 })
            }
          }}
          source={EnumCalendarAddSource.unplanned}
        >
          <div className={s.add}>
            <Icon className={s.icon} name="icon-add" fontSize={12}></Icon>
            <div className={s.addStr}>{I18N.auto.newTask}</div>
          </div>
        </AddDropdown>
      </div>
      <div className={s.list} id="external-events-list">
        <DndContext
          onDragStart={handleDragStart}
          onDragCancel={handleDragCancel}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          sensors={isFollower ? [] : sensors} // 处于“我关注的”时禁止操作
        >
          <Droppable id="xxx" items={unplannedList} atBottomStateChange={atBottomStateChange} />
          <DragOverlay className={'dnd-DragOverlay'}>
            {activeId ? (
              <RenderItem
                className={s.dragging}
                data={activeItem!}
                taskId={activeItem!.taskId!}
                title={activeItem?.title || ''}
              ></RenderItem>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>
    </div>
  )
}

export default UnplannedList
