image: ncr.nie.netease.com/mirror_docker.io/library/node:16.14.0

stages:
  - build
  - deploy
  - publish_image

before_script:
  - publish_script_path=/home/<USER>/scripts/publish_to_symphony
  # 测试走玉夫座，预发和正式走豺狼
  - pro_publish_cl_region=cld_hz_fcy_lupus # 豺狼集群
  - publish_production_name_space=ncc-open-fe-production
  - publish_staging_name_space=ncc-open-fe-staging

  - pro_publish_yfz_region=cld_hz_dg_sculptor # 玉夫座集群
  - publish_test_name_space=test03-ncc-open-fe

  - publish_service_name=popo-tobedone-fe

job_build:
  stage: build
  script:
    - npm i -g pnpm@7.29.0
    - pnpm -v
    - pnpm i
    - pnpm "build:$CI_COMMIT_REF_NAME"
  only:
    - production
    - staging
    - test
  artifacts:
    paths:
      - dist/
  tags:
    - ncc-fe-runner-saas-office

job_deploy:
  stage: deploy
  script:
    - cd $publish_script_path
    - ruby login_harbor.rb
    - cd -
    - chmod +x ./docker/deploy.sh
    - sh ./docker/deploy.sh

  only:
    - test
    - staging
    - production
  tags:
    # - gitlab-builder
    - gitlab-builder-office
  artifacts:
    when: always
    paths:
      - publish_version

job_publish:
  stage: publish_image
  script:
    - |
      name_space=$publish_test_name_space
      region=$pro_publish_yfz_region
      if [ $CI_COMMIT_REF_NAME == "staging" ]; then
        name_space=$publish_staging_name_space
        region=$pro_publish_cl_region
      else
        name_space=$publish_test_name_space
        region=$pro_publish_yfz_region
      fi
      echo 'name_space-'$name_space
      publish_version=$(cat publish_version)
      echo 'publish_version-'$publish_version
      echo "cd $publish_script_path;ruby run.rb $name_space $publish_service_name $publish_version --region=$region"
      cd $publish_script_path;ruby run.rb $name_space $publish_service_name $publish_version --region=$region
  only:
    - test
    - staging

  tags:
    #- gitlab-builder
    - gitlab-builder-office
  dependencies:
    - job_deploy

job_publish_production:
  stage: publish_image
  when: manual
  script:
    - |
      name_space=$publish_production_name_space
      echo 'name_space-'$name_space
      publish_version=$(cat publish_version)
      echo 'publish_version-'$publish_version
      echo "cd $publish_script_path;ruby run.rb $name_space $publish_service_name $publish_version --region=$pro_publish_cl_region"
      cd $publish_script_path;ruby run.rb $name_space $publish_service_name $publish_version --region=$pro_publish_cl_region --skip_shrink_replicas_to_one=true
  only:
    - production
  tags:
    #- gitlab-builder
    - gitlab-builder-office
  dependencies:
    - job_deploy
