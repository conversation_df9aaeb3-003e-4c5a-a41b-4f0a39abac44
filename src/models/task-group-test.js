// 测试分组任务编辑行重复问题的修复
// 这个文件用于验证修复是否有效

// 模拟分组任务数据
const mockGroupedTask = {
  taskId: 123,
  title: '测试任务',
  groupId: 'user-1',
  groupedIds: [['user-1'], ['user-2']], // 任务出现在多个分组中
  subtask: []
}

// 模拟不同分组中的相同任务
const mockTableRowData1 = {
  id: '0.1.2', // 第一个分组中的行ID
  parentId: 'parent-1'
}

const mockTableRowData2 = {
  id: '1.1.2', // 第二个分组中的行ID  
  parentId: 'parent-2'
}

// 测试唯一ID生成逻辑
function testUniqueIdGeneration() {
  const TaskTableRowTypeAdd = 'add'
  const TaskTableRowType = { add: 'add' }
  const now = Date.now()
  
  // 对于分组任务
  const isGroupedTask = true
  
  // 第一个分组中的任务
  const currentRowId1 = mockTableRowData1.id
  const uniqueId1 = isGroupedTask 
    ? `${TaskTableRowTypeAdd}-${currentRowId1}-${TaskTableRowType.add}-${now}`
    : `${TaskTableRowTypeAdd}-${mockTableRowData1.id}-${TaskTableRowType.add}-${now}`
    
  // 第二个分组中的任务
  const currentRowId2 = mockTableRowData2.id
  const uniqueId2 = isGroupedTask 
    ? `${TaskTableRowTypeAdd}-${currentRowId2}-${TaskTableRowType.add}-${now}`
    : `${TaskTableRowTypeAdd}-${mockTableRowData2.id}-${TaskTableRowType.add}-${now}`
  
  console.log('第一个分组中的编辑行ID:', uniqueId1)
  console.log('第二个分组中的编辑行ID:', uniqueId2)
  console.log('ID是否唯一:', uniqueId1 !== uniqueId2)
  
  return {
    uniqueId1,
    uniqueId2,
    isUnique: uniqueId1 !== uniqueId2
  }
}

// 测试分组信息继承
function testGroupInfoInheritance() {
  const addTarget = {
    ...mockGroupedTask,
    groupId: 'user-1',
    groupedIds: [['user-1']]
  }
  
  const addItem = {
    id: 'test-id',
    taskId: 'test-id',
    groupId: addTarget.groupId,
    groupedIds: addTarget.groupedIds,
    title: ''
  }
  
  console.log('父任务分组信息:', {
    groupId: addTarget.groupId,
    groupedIds: addTarget.groupedIds
  })
  
  console.log('编辑行继承的分组信息:', {
    groupId: addItem.groupId,
    groupedIds: addItem.groupedIds
  })
  
  return {
    parentGroupInfo: {
      groupId: addTarget.groupId,
      groupedIds: addTarget.groupedIds
    },
    childGroupInfo: {
      groupId: addItem.groupId,
      groupedIds: addItem.groupedIds
    },
    isInherited: addTarget.groupId === addItem.groupId && 
                 JSON.stringify(addTarget.groupedIds) === JSON.stringify(addItem.groupedIds)
  }
}

// 运行测试
console.log('=== 测试分组任务编辑行重复问题修复 ===')
console.log()

console.log('1. 测试唯一ID生成:')
const idTest = testUniqueIdGeneration()
console.log('结果:', idTest.isUnique ? '✅ 通过' : '❌ 失败')
console.log()

console.log('2. 测试分组信息继承:')
const inheritanceTest = testGroupInfoInheritance()
console.log('结果:', inheritanceTest.isInherited ? '✅ 通过' : '❌ 失败')
console.log()

console.log('=== 测试完成 ===')

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testUniqueIdGeneration,
    testGroupInfoInheritance
  }
}
