.page {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  // background-color: var(--bgApplication);
  background-color: var(--bgBottom);
  overflow: hidden;
  user-select: none;
  &.pageBorderTop {
    border-top: 1px solid var(--aBlack12);
  }

  &__resizer {
    height: 100%;
    transition: width 0.2s, min-width 0.2s;
  }
}
.pageLeft {
  width: 100%;
  height: 100%;

  .headTitleIcon {
    color: var(--TextPrimary);
    font-size: 18px;
    font-weight: 600;
    height: 24px;
    line-height: 24px;
  }
  &:global(.hidden-menu) {
    width: 0;
  }
}

.closeMenu {
  width: 40px;
  transition: 0.3s all;
  padding: 18px 0;
}

.pageRight {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  //padding: 0 12px 16px;
  margin-bottom: 2px;
  overflow-x: hidden;
  overflow-y: hidden;
  background-color: var(--bgBottom);
  transition: 0.3s all;
}

.resizing {
  transition: none;
  user-select: none;
  -webkit-user-select: none;
}

.resizer__handle {
  position: absolute;
  width: 10px;
  height: 100%;
  bottom: 0;
  right: 0;
  cursor: col-resize;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 100%;
    left: 50%;
    transform: translateX(-50%);
    transition: width 0.2s, background-color 0.2s;
  }

  &:hover::after,
  &:active::after {
    background: var(--Brand600);
  }
}
