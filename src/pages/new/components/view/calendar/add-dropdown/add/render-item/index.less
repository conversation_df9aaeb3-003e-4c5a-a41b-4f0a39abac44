.item {
  display: flex;
  align-items: flex-start;
  margin-top: 12px;
  min-height: 30px;
  .left {
    height: 30px;
    padding: 4px 0 4px 4px;
    min-width: 80px;
  }
  .addItembg {
    width: 100%;
    padding-top: 0;
    padding-bottom: 0;
  }
  .infoItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    padding-left: 20px;
    //padding: 8px 0 8px 14px;
    overflow: hidden;
    height: 100%;
    :global {
      .com-placeholder {
        font-size: 13px;
        color: var(--TextTertiary);
      }
      .rock-dropdown-trigger-default {
        // width: auto;
        min-height: 30px;

        // &:hover {
        //   background-color: var(--aBlack10);
        // }
      }
    }
    .opt {
      flex: 1;
      width: 100%;
    }
    .icon {
      width: 22px;
      height: 22px;
      flex-shrink: 0;
    }
  }
}
