.detailDrawer {
  //background-color: red;
  display: flex;
  :global {
    .rock-drawer-content-wrapper {
      // width: calc(100vw / 2 - 100px) !important;
      min-width: 420px;
      max-width: 600px;
    }
    .rock-drawer-title {
      display: none;
    }
    .rock-drawer-body {
      display: flex;
      flex-direction: column;
      padding: 0;
      overflow: hidden;
    }
    .com-placeholder {
      font-size: 14px !important;
      font-weight: 400 !important;
      color: var(--TextTertiary);
      height: 30px;
      line-height: 30px;
      &:hover {
        // background-color: var(--aBlack6);
      }
    }
  }
}
