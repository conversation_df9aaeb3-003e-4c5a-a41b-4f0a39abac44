import { UserInfo } from '@/types'
import { EnumFieldNONE, Priority } from '@/utils/fields'
import I18N from '@/utils/I18N'
import { GroupConfig, GroupConfigMap } from './types'
import { UserGroupOrderManager } from './UserGroupOrderManager'

// 用户分组管理器
export class UserGroupManager {
  static userGroupMap: Record<string, Record<string, GroupConfig>> = {}
  static orderManager = UserGroupOrderManager.getInstance()

  static updateUserGroup(params: {
    groupBy: string
    userIds?: string[]
    users?: UserInfo[]
    groupLevel: number
  }): void {
    const { groupBy, userIds, users, groupLevel } = params
    const groupId = userIds?.join(',')

    if (!groupId) return

    if (!this.userGroupMap[groupBy]) {
      this.userGroupMap[groupBy] = {}
    }

    const currGroup = this.userGroupMap[groupBy]

    if (!currGroup[groupId]) {
      currGroup[groupId] = {
        title: '',
        groupBy,
        groupId,
        users,
        groupLevel,
        sortOrder: groupId,
        compareFunc: (val: string[]) => val?.join(',') === groupId,
        defaultParams: {},
        totalCount: 0,
        finishedCount: 0,
        value: userIds,
      }
    } else {
      currGroup[groupId].users = users
      currGroup[groupId].groupLevel = groupLevel
      currGroup[groupId].value = userIds
    }
  }

  static getUserConfigs(groupBy: string, groupLevel: number): GroupConfigMap {
    const configs: GroupConfigMap = {}

    // 添加用户分组
    Object.values(this.userGroupMap[groupBy] || {}).forEach(config => {
      configs[config.groupId] = { ...config, groupLevel }
    })

    // 添加默认空分组
    configs[EnumFieldNONE.NONE] = {
      title: I18N.auto.nothing,
      compareFunc: (val: any[]) => !val?.length,
      groupBy,
      groupId: EnumFieldNONE.NONE,
      sortOrder: 'zzzzzzz',
      groupLevel,
      defaultParams: { priority: Priority.Unset },
      totalCount: 0,
      finishedCount: 0,
      value: null,
    }

    return configs
  }

  static clearUserGroups(groupBy: string): void {
    if (this.userGroupMap[groupBy]) {
      this.userGroupMap[groupBy] = {}
    }
  }
}