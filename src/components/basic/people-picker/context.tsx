import { createContext } from 'react';

import { UserInfo } from '@/types';
import { PeoplePickerType } from '@/utils/const';

interface ContextShape {
  pickerType?: PeoplePickerType;
  assignerUid?: string;
  assigneeUids?: string[];
  followerUids?: string[];
  currentUser?: UserInfo;
  canEditCompleteCondition?: boolean;
  /** 执行人是否修改了 */
  isAssigneeUpdated?: boolean;
  onCancel?: () => Promise<void>;
  onConfirm?: (userList: UserInfo[]) => Promise<void>;
}

export const PeoplePickerContext = createContext<ContextShape>({
  pickerType: undefined,
  assignerUid: '',
  assigneeUids: [],
  followerUids: [],
  currentUser: {},
  canEditCompleteCondition: false,
});
