import classNames from 'classnames';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTodoBatchCancelPost } from '@/api';
import { Message, Modal } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
  disabled?: boolean;
};
const BatchDelete: React.FC<Props> = (props) => {
  const { disabled } = props;
  const { batchList, userInfo } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
    userInfo: state.user.userInfo,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onClick = () => {
    if (disabled) {
      return;
    }
    const taskIds = batchList
      .filter((item) => item.assigner?.assignerUid === userInfo?.uid)
      .map((item) => item.taskId!);
    const hasOtherTodo = batchList.length !== taskIds.length;
    if (taskIds.length === 0) {
      Message.error(I18N.auto.canOnlyDeleteMe);
    } else {
      Modal.confirm(
        {
          width: 400,
          content: (
            <div className={classNames(s.deleteTitle)}>
              {hasOtherTodo
                ? I18N.template(I18N.auto.canOnlyDeleteMe_2, { val1: taskIds.length })
                : I18N.template(I18N.auto.deleteToDoItems, { val1: taskIds.length })}
            </div>
          ),

          centered: true,
          cancelText: I18N.auto.cancel,
          okText: I18N.auto.delete,
          onOk: () => {
            const taskIds = batchList.map((item) => item.taskId!);
            // 请求批量操作
            apiTodoBatchCancelPost({ taskIds }).then(() => {
              //刷新左侧标签列表、标签数量、当前列表数据
              dispatch.viewSetting.refreshDataByDataChange({
                refreshList: true,
                refreshCount: true,
              });
              dispatch.batch.setBatchList([]);
              dispatch.batch.setBatchMode(false);
            });
          },
          zIndex: 1100,
        },
        'warning'
      );
    }
  };
  return (
    <BatchItem
      disabled={disabled}
      className={s.batchDelete}
      name={I18N.auto.delete}
      iconName="icon-details_nav_delete1"
      onClick={onClick}
    ></BatchItem>
  );
};

export default BatchDelete;
