import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Button } from '@/components/basic';
import PeopleItem from '@/components/basic/people-picker/components/people-item';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  avatarUrl?: string;
  name?: string;
  onTransfer?: () => void;
};
const AssignorPanel: React.FC<PropsWithChildren<Props>> = (props) => {
  const { className, avatarUrl, name, onTransfer } = props;
  return (
    <div className={classNames(s.search, className)}>
      <div className={s.d}>{I18N.auto.assignedBy}</div>
      <PeopleItem className={s.assignorItem} avatarUrl={avatarUrl} name={name}></PeopleItem>
      <Button type="primary" size="small" className={s.btn} onClick={onTransfer}>
        {I18N.auto.transferAssignor}
      </Button>
    </div>
  );
};

export default AssignorPanel;
