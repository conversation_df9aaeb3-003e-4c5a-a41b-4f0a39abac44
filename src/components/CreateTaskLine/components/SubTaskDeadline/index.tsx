import { Icon, IconBtn, POPOConvergenceDatePicker, RenderAlarm } from '@/components/basic';
import { PopoConvergenceDatePickerContext } from '@/components/basic/popo-convergence-date-picker/context';
import { EnumTimePickerType, PPTimeFormat, TaskTableRowTypeAdd } from '@/utils/const';
import { Data16Timestop, ImCloseSolid2 } from '@babylon/popo-icons';
import { FC, useMemo, useState } from 'react';
import { TimeInputType } from '@/components/basic/popo-convergence-date-picker/convergence-date-picker';
import { TaskTime } from '@/types';
import { getTimePickerFormat } from '@/components/basic/popo-date-picker/utils';
import dayjs from 'dayjs';
import s from './index.less';
import I18N from '@/utils/I18N';
import { isUpdateTime } from '@/utils';
import { taskUpdateAlarm, taskUpdateDeadline } from '@/api-common';
import { Overlay } from '@/components/basic/popo-date-picker';

interface ICreateTaskLineProps {
  onChange: (value: TaskTime) => void;
  onSuccess?: () => void;
  taskInfo?: TaskTime;
  renderContent?: () => React.ReactNode;
  panelType?: Overlay;
}
const SubTaskDeadline: FC<ICreateTaskLineProps> = ({
  onChange,
  onSuccess,
  taskInfo = { startTime: 0 },
  renderContent,
  panelType,
}) => {
  const [visible, setVisible] = useState(false);

  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      // setSelectedValue(value);
    }
    setVisible(visible);
  };
  const { startTime, deadline, timeFormat, rrule, memoAlarm: alarm } = taskInfo;
  const memoDeadlineTime = useMemo(() => {
    return {
      startTime: startTime,
      deadline: deadline,
      rrule: rrule,
      timeFormat: timeFormat || 0,
      alarm: alarm,
    };
  }, [startTime, deadline, timeFormat, rrule, alarm]);

  const handleChange = async (value: TaskTime) => {
    onChange(value);
    const { taskId } = taskInfo || {};
    // 新建行 不处理
    if (!taskId || `${taskId}`.includes(TaskTableRowTypeAdd)) {
      return;
    }

    const { startTime, deadline, timeFormat, rrule, alarm } = value;
    const isUpdate = isUpdateTime(value, memoDeadlineTime!);
    if (!isUpdate) {
      return;
    }

    const params = {
      taskId: taskId,
      startTime: startTime || 0,
      deadline: deadline || 0,
      deadlineFormat: timeFormat,
      rrule: rrule,
    };
    // if (rowType === TaskTableRowType.add) {
    //   dispatch.task.updateTodoList(params);
    //   return null;
    // }
    if (isUpdate) {
      await taskUpdateDeadline(params);
      await taskUpdateAlarm({
        taskId: taskId,
        alarm: {
          alarmTimestamp: alarm?.time,
          alarmRrule: alarm?.rrule,
          selectedOption: alarm?.selectedOption,
        },
      });
      onSuccess?.();
    }
  };

  const handleClearTime = (e: React.MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation();
    const defaultTime = {
      alarm: {
        time: undefined,
        timeFormat: undefined,
        rrule: undefined,
        selectedOption: undefined,
      },
      deadline: 0,
      rrule: '',
      startTime: 0,
      timeFormat: 0,
    };
    handleChange(defaultTime);
  };

  const _renderContent = () => {
    if (taskInfo.deadline) {
      return null;
    }
    return renderContent ? (
      renderContent()
    ) : (
      <IconBtn
        title={I18N.auto.addDeadline_2}
        className={s.icon}
        icon={<Data16Timestop className={`${s.date__icon} fs-16`} />}
      />
    );
  };

  const renderLabel = (time: any) => {
    const timeStr = getTimePickerFormat({
      time: dayjs(time),
      hasHHmm: false,
    });

    return (
      <div className={`${s.time__label} flex-y-center add-row-time-label`}>
        <div className={s.time}>{I18N.template(I18N.auto.end, { val1: timeStr })}</div>
        <RenderAlarm
          renderContent={() => <Icon name="icon-quick_remind" fontSize={16} />}
          alarm={alarm}
          className="ml-2"
        ></RenderAlarm>
        <div className={`${s.clear__icon} flex-y-center add-row-clear-icon`}>
          <ImCloseSolid2 className={s.clear} onClick={handleClearTime} />
        </div>
      </div>
    );
  };

  return (
    <PopoConvergenceDatePickerContext.Provider
      value={{
        timePickerType: EnumTimePickerType.deadline,
      }}
    >
      <POPOConvergenceDatePicker
        timeInputType={TimeInputType.end}
        visible={visible}
        onVisible={handleVisibleChange}
        hasArrow={false}
        onChange={handleChange}
        value={memoDeadlineTime}
        renderLabel={renderLabel}
        className={s.date__picker}
        showCircle={!taskInfo.parentId}
        panelType={panelType || Overlay.datePanel}
      >
        {_renderContent()}
      </POPOConvergenceDatePicker>
    </PopoConvergenceDatePickerContext.Provider>
  );
};

export default SubTaskDeadline;
