import React, { useContext, useMemo } from 'react';

import { Checkbox } from '@/components/basic';

import { TableContext } from '../../context';

interface Data {
  id: number | string;
  [k: string]: any;
}

export type Props = {
  data: Data[];
  itemData: Data;
};

export type AllProps = {
  className?: string;
  data: Data[];
};
const TableAllCheckbox: React.FC<AllProps> = (props) => {
  const { data } = props;
  const { selectedRowKeys, onSelect } = useContext(TableContext);
  const checked = useMemo(() => {
    return data.length === selectedRowKeys?.length && !!selectedRowKeys.length;
  }, [data, selectedRowKeys]);
  const indeterminate = useMemo(() => {
    return data.length !== selectedRowKeys?.length && !!selectedRowKeys?.length;
  }, [data, selectedRowKeys]);
  return (
    <Checkbox
      checked={checked}
      indeterminate={indeterminate}
      onChange={(e) => {
        if (e.target.checked) {
          const keys = data?.map((item) => item.id);
          onSelect?.(keys, data);
        } else {
          onSelect?.([], []);
        }
      }}
    ></Checkbox>
  );
};

const TableCheckbox: React.FC<Props> = (props) => {
  const { data, itemData } = props;
  const { selectedRowKeys, onSelect } = useContext(TableContext);
  const checked = useMemo(() => {
    return selectedRowKeys?.includes(itemData.id);
  }, [itemData, selectedRowKeys]);
  return (
    <Checkbox
      checked={checked}
      onChange={(e) => {
        let keys = [];
        let rows = [];
        if (e.target.checked) {
          // TODO 没去是按照操作顺序返回的数据  可以改成按照列表顺序返回
          selectedRowKeys?.push(itemData.id);
          keys = [...(selectedRowKeys || [])];
        } else {
          keys = selectedRowKeys?.filter((item) => item !== itemData.id);
        }
        rows = data.filter((item) => keys.includes(item.id));
        onSelect?.(keys, rows);
      }}
    ></Checkbox>
  );
};

export { TableAllCheckbox, TableCheckbox };
