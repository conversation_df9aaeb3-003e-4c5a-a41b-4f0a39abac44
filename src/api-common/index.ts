import {
  apiNavigatorViewListGet,
  apiNavigatorViewUpdateSettingPost,
  ApiNavigatorViewUpdateSettingPostRequest,
  apiPermissionBatchCheckPost,
  apiProjectTaskCalendarViewPost,
  apiProjectTaskCalendarViewUnplannedPost,
  apiProjectTaskKanbanViewGroupsPost,
  apiProjectTaskListViewGroupedGroupsPost,
  apiProjectViewListGet,
  apiTaskCalendarViewPost,
  ApiTaskCalendarViewPostRequest,
  apiTaskCalendarViewUnplannedPost,
  ApiTaskCalendarViewUnplannedPostRequest,
  apiTaskCreatePost,
  ApiTaskCreatePostRequest,
  apiTaskKanbanViewGroupsPost,
  ApiTaskKanbanViewGroupsPostRequest,
  ApiTaskKanbanViewGroupsPostResponse,
  ApiTaskKanbanViewGroupsScrollQueryPostRequest,
  apiTaskListViewGroupedGroupsPost,
  ApiTaskListViewGroupedGroupsPostRequest,
  ApiTaskListViewGroupedScrollQueryPostRequest,
  ApiTaskListViewPostRequest,
  apiTodoUpdateAlarmPost,
  ApiTodoUpdateAlarmPostRequest,
  apiTodoUpdateDeadlinePost,
  ApiTodoUpdateDeadlinePostRequest,
  apiV3ProjectTaskKanbanViewGroupsPost,
  apiV3ProjectTaskKanbanViewGroupsScrollQueryPost,
  apiV3ProjectTaskListViewGroupedGroupsPost,
  apiV3ProjectTaskListViewGroupedScrollQueryPost,
  apiV3ProjectTaskListViewPost,
  apiV3TaskKanbanViewGroupsPost,
  apiV3TaskKanbanViewGroupsScrollQueryPost,
  apiV3TaskListViewGroupedGroupsPost,
  apiV3TaskListViewGroupedScrollQueryPost,
  apiV3TaskListViewPost,
} from '@/api';
import { getIsInSession, isProjectId, isTaskMenuId } from '@/models/utils';
import { TaskInfo, TaskPermission, UserInfo, ViewInfo, ViewPermission } from '@/types';
import { TaskNavigatorType, TaskTableRowTypeAdd, UNKNOW_GROUP_BY } from '@/utils/const';
import {
  PermissionObjectType,
  ProjectPermissionEnum,
  TaskPermissionEnum,
  ViewPermissionEnum,
  ViewTypeEnum,
} from '@/utils/permission';
import { RequestOptions } from '@/utils/request';
import { RequestQueue } from '@/utils/requestQueue';
import dayjs from 'dayjs';
import request from '../utils/request';
import { Method } from 'yapi-to-typescript';

const requestQueue = new RequestQueue(4);

export const taskUpdateDeadline = async (params: ApiTodoUpdateDeadlinePostRequest) => {
  return apiTodoUpdateDeadlinePost(params, { errorSilent: true });
};

export const taskUpdateAlarm = async (params: ApiTodoUpdateAlarmPostRequest) => {
  return apiTodoUpdateAlarmPost(params, { errorSilent: true });
};

/**
 * 查询视图列表  列表、看板、计划
 * 返回视图类型、字段、缓存参数等
 * @param menu
 * @returns
 */
export const apiGetViewList = async (menu: TaskNavigatorType | string | number) => {
  let request = undefined;
  if (isTaskMenuId(menu)) {
    request = apiNavigatorViewListGet({
      navigatorId: menu as TaskNavigatorType,
    });
  } else {
    request = apiProjectViewListGet({
      projectId: menu + '',
    });
  }
  return request.then((res) => {
    return res.map((item) => {
      let queryGroupBy = item.queryGroupBy;
      //@ts-ignore
      if (!item.queryGroupBy?.fieldName || item.queryGroupBy?.fieldName === UNKNOW_GROUP_BY) {
        queryGroupBy = {};
      }
      return { ...item, queryGroupBy: queryGroupBy };
    });
  });
};

/**
 * 查询视图列表 批量视图编辑删除权限
 * 返回不同视图的各权限列表
 * @param batchCheckParams
 * @returns
 */
export const apiBatchGetViewPermissionPost = async (
  batchCheckParams: {
    objectType: PermissionObjectType;
    objectIds?: string[];
    operateType?: string;
  }[]
) => {
  return (
    batchCheckParams?.length &&
    apiPermissionBatchCheckPost({
      perms: batchCheckParams,
    })
  );
};

/**
 * 更新视图信息, 筛选、字段设置、排序等
 * @param params
 * @returns
 */
export const apiNavigatorViewUpdateSetting = async (
  params: ApiNavigatorViewUpdateSettingPostRequest
) => {
  return apiNavigatorViewUpdateSettingPost(params).catch((err) => {
    console.error(err);
  });
};

/**
 * 创建任务
 * @param params
 * @returns
 */
export const apiCreateTask = async (params: ApiTaskCreatePostRequest) => {
  return apiTaskCreatePost({
    ...params,
  });
};

/**
 * 待办独立模块，列表视图分组列表查询
 * @param params
 * @returns
 */
export const apiGetTaskListViewGroups = async (
  params: ApiTaskListViewGroupedGroupsPostRequest,
  options?: RequestOptions
) => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiV3TaskListViewGroupedGroupsPost(
      {
        ...params,
        navigatorId: params.navigatorId,
        projectId: undefined,
      },
      options
    );
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiV3ProjectTaskListViewGroupedGroupsPost(
        {
          ...params,
          projectId: Number(params.navigatorId),
          navigatorId: undefined,
        },
        options
      );
    }
    return { groups: [] };
  }
};

/**
 * 待办 查询列表数据
 * @param params
 * @returns
 */
export const apiGetTaskList = async (
  params: ApiTaskListViewPostRequest,
  options?: RequestOptions
) => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiV3TaskListViewPost(
      {
        ...params,
        navigatorId: params.navigatorId,
      },
      options
    );
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiV3ProjectTaskListViewPost(
        {
          ...params,
          projectId: Number(params.navigatorId),
        },
        options
      );
    }
    return {
      hasMore: false,
      list: [],
    };
  }
};

/**
 * 待办独立模块，列表视图分组列表查询
 * @param params
 * @returns
 */
export const apiGetTaskGroupsList = async (
  params: ApiTaskListViewGroupedScrollQueryPostRequest,
  options?: RequestOptions
) => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiV3TaskListViewGroupedScrollQueryPost(
      {
        ...params,
        navigatorId: params.navigatorId,
      },
      options
    );
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiV3ProjectTaskListViewGroupedScrollQueryPost(
        {
          ...params,
          projectId: Number(params.navigatorId),
        },
        options
      );
    }
    return {
      hasMore: false,
      list: [],
    };
  }
};

/**
 * 看板视图，获取所有分组（泳道）
 * @param params
 * @returns
 */
export const apiGetKanbanViewGroups = async (
  params: ApiTaskKanbanViewGroupsPostRequest,
  options?: RequestOptions
): Promise<ApiTaskKanbanViewGroupsPostResponse> => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiV3TaskKanbanViewGroupsPost(
      {
        ...params,
        navigatorId: params.navigatorId,
      },
      options
    ).catch((e) => {
      console.error('拉取看板分组数据失败', e);
    }) as ApiTaskKanbanViewGroupsPostResponse;
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiV3ProjectTaskKanbanViewGroupsPost(
        {
          ...params,
          projectId: Number(params.navigatorId),
        },
        options
      ).catch((e) => {
        console.error('拉取看板分组数据失败', e);
      }) as ApiTaskKanbanViewGroupsPostResponse;
    }
    return { groups: [] };
  }
};

/**
 * 看板视图，单个分组（泳道）内滚动查询
 * @param params
 * @returns
 */
export const apiGetKanbanViewGroupsScrollQuery = async (
  params: ApiTaskKanbanViewGroupsScrollQueryPostRequest,
  options?: RequestOptions
) => {
  if (isTaskMenuId(params.navigatorId!)) {
    // return apiTaskKanbanViewGroupsScrollQueryPost({
    //   ...params,
    //   navigatorId: params.navigatorId,
    // });
    return requestQueue.addRequest(() =>
      apiV3TaskKanbanViewGroupsScrollQueryPost(
        {
          ...params,
          navigatorId: params.navigatorId,
        },
        options
      )
    );
  } else {
    if (isProjectId(params.navigatorId)) {
      // return apiProjectTaskKanbanViewGroupsScrollQueryPost({
      //   ...params,
      //   projectId: Number(params.navigatorId),
      // });
      return requestQueue.addRequest(() =>
        apiV3ProjectTaskKanbanViewGroupsScrollQueryPost(
          {
            ...params,
            projectId: Number(params.navigatorId),
          },
          options
        )
      );
    }
    return {
      hasMore: false,
      list: [],
    };
  }
};

/**
 * 查询日历视图数据
 * @param params
 * @returns
 */
export const apiGetCalendarView = async (params: ApiTaskCalendarViewPostRequest) => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiTaskCalendarViewPost({
      ...params,
      navigatorId: params.navigatorId,
    });
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiProjectTaskCalendarViewPost({
        ...params,
        projectId: Number(params.navigatorId),
      });
    }
    return [];
  }
};

/**
 * 查询日历视图数据
 * @param params
 * @returns
 */
export const apiGetCalendarViewUnplanned = async (
  params: ApiTaskCalendarViewUnplannedPostRequest
) => {
  if (isTaskMenuId(params.navigatorId!)) {
    return apiTaskCalendarViewUnplannedPost({
      ...params,
      navigatorId: params.navigatorId,
    });
  } else {
    if (isProjectId(params.navigatorId)) {
      return apiProjectTaskCalendarViewUnplannedPost({
        ...params,
        projectId: Number(params.navigatorId),
      });
    }
    return {
      hasMore: false,
      list: [],
    };
  }
};

// 一些公共的请求
export const getBatchPermission = <T>(
  perms: {
    objectType: PermissionObjectType;
    objectIds?: string[];
    operateType?: TaskPermissionEnum | ProjectPermissionEnum | ViewPermissionEnum;
  }[]
) => {
  if (!perms.length) {
    return {};
  }
  return apiPermissionBatchCheckPost(
    {
      perms: perms,
    },
    {
      errorSilent: getIsInSession(),
    }
  ).then((res) => {
    const obj: Record<
      string,
      {
        permissions: T[];
      }
    > = {};
    res.perms?.forEach((item) => {
      const { checkResult = {}, operateType } = item;
      Object.keys(checkResult).forEach((id) => {
        if (obj[id]) {
          obj[id].permissions.push({
            name: operateType!,
            //@ts-ignore
            value: !!checkResult[id],
          } as T);
        } else {
          obj[id] = {
            permissions: [
              {
                name: operateType!,
                //@ts-ignore
                value: !!checkResult[id],
              } as T,
            ],
          };
        }
      });
    });
    return obj;
  });
};

export const getPermissionsByTaskList = async (list: TaskInfo[], userInfo?: UserInfo) => {
  //不要删除此代码, 如果空数组直接返回即可, 报错之后会影响调用方逻辑
  if (!list.length) {
    return {};
  }
  const _ids = list
    .map((item) => String(item.taskId!))
    .filter((taskid) => !taskid.includes(TaskTableRowTypeAdd));
  //去重复
  const ids = [...new Set(_ids)];
  if (!ids.length) {
    return {};
  }
  let myFollowerIds = [];
  // 获取有项目的id
  // const hasProjectIds = list
  //   .filter((item) => item.project?.projectId)
  //   .map((item) => String(item.taskId!));
  const perms = [
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_COMPLETE,
    },
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_EDIT,
    },
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_VIEW,
    },
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_ADD_TO_PROJECT,
    },
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_REMOVE_FROM_PROJECT,
    },
    {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
    },
  ];
  if (userInfo) {
    myFollowerIds = list
      .filter((item) => item.followerUids?.includes(userInfo.uid!))
      .map((item) => String(item.taskId!));
    if (myFollowerIds.length) {
      perms.push({
        objectIds: myFollowerIds,
        objectType: PermissionObjectType.TASK,
        operateType: TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      });
    }
  }
  return getBatchPermission<TaskPermission>(perms);
};

export const getPermissionsByTaskIds = async ({
  noPermissionsTaskIds,
  myFollowedTaskIds,
  extraPermissionList = [],
}: {
  noPermissionsTaskIds: number[];
  myFollowedTaskIds: number[];
  extraPermissionList?: string[];
}) => {
  //不要删除此代码, 如果空数组直接返回即可, 报错之后会影响调用方逻辑
  if (!noPermissionsTaskIds.length) {
    return {};
  }
  //去重复
  const ids = [...new Set(noPermissionsTaskIds)].filter(
    (id) => !`${id}`.includes(TaskTableRowTypeAdd)
  );
  const _followedIds = [...new Set(myFollowedTaskIds)];
  const permissionKeys = [
    TaskPermissionEnum.CAN_COMPLETE,
    TaskPermissionEnum.CAN_EDIT,
    // TaskPermissionEnum.CAN_TRANSFER,
    TaskPermissionEnum.CAN_VIEW,
    TaskPermissionEnum.CAN_ADD_TO_PROJECT,
    TaskPermissionEnum.CAN_REMOVE_FROM_PROJECT,
    TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
    TaskPermissionEnum.CAN_DELETE,
    ...extraPermissionList,
  ];
  const perms = permissionKeys.map((item) => {
    return {
      objectIds: ids,
      objectType: PermissionObjectType.TASK,
      operateType: item,
    };
  });
  if (_followedIds.length) {
    perms.push({
      objectIds: _followedIds,
      objectType: PermissionObjectType.TASK,
      operateType: TaskPermissionEnum.CAN_CANCEL_OBSERVER,
    });
  }
  return getBatchPermission<TaskPermission>(perms);
};

export const getBatchViewCheckParams = async (viewTabList: ViewInfo[]) => {
  const viewCommonIds = viewTabList
    .filter((item) => item.viewDistribution !== ViewTypeEnum.Personal)
    .map((item) => item.viewId);
  const viewPersonalIds = viewTabList
    .filter((item) => item.viewDistribution === ViewTypeEnum.Personal)
    .map((item) => item.viewId);
  const batchCheckParams = [
    {
      objectType: PermissionObjectType.VIEW,
      operateType: ViewPermissionEnum.CAN_EDIT_COMMON_VIEW,
      objectIds: viewCommonIds,
    },
    {
      objectType: PermissionObjectType.VIEW,
      operateType: ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
      objectIds: viewPersonalIds,
    },
    {
      objectType: PermissionObjectType.VIEW,
      operateType: ViewPermissionEnum.CAN_DELETE_PERSON_VIEW,
      objectIds: viewPersonalIds,
    },
    {
      objectType: PermissionObjectType.VIEW,
      operateType: ViewPermissionEnum.CAN_DELETE_COMMON_VIEW,
      objectIds: viewCommonIds,
    },
  ].filter((item) => !!item.objectIds.length);
  let viewPermission = await getBatchPermission<ViewPermission>(batchCheckParams);
  return viewPermission;
};

export const getGroupsData = async ({ state, dispatch }: { state: any; dispatch: any }) => {
  const _param: ApiTaskListViewGroupedGroupsPostRequest = dispatch.viewSetting.getComParams({});
  const params = {
    ..._param,
    //@ts-ignore
    timezone: dayjs.tz?.guess?.(),
  };
  if (isProjectId(params.navigatorId) && params.queryGroupBy?.customFieldId) {
    //服务端要求在自定义单选字段分组时传递customFieldOptions
    const { customFields } = state.viewSetting;
    const field = customFields.find((item) => item.fieldId === params.queryGroupBy?.customFieldId);
    params.queryGroupBy.customFieldOptions = field?.options?.map((item) => item.value) || [];
  }
  let { groups = [] } = await apiGetTaskListViewGroups(params, {
    errorSilent: true,
  });
  return { groups, params };
};

// 获取fp上传地址
export const getFpUploadUrl = async () => {
  const data = await request<{ uploadUrl: string; token: string }>({
    path: '/api/bs-user/v1/file/fp/upload_url2?videoCover=vframe',
    method: Method.GET,
  } as any);

  return data;
};
