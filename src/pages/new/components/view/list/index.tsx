import { OperateAdd } from '@babylon/popo-icons'
import classNames from 'classnames'
import React, { useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { apiProjectResortTaskPost, apiTaskResortPost } from '@/api'
import { IconBtn } from '@/components/basic'
import CustomFieldDropdown from '@/components/custom-field-dropdown'
import { CustomFieldCreateWrapper } from '@/components/field-edit-popover'
import { RenderAddRow, RenderGroupRow } from '@/components/table/components'
import { Dispatch, RootState, store } from '@/models/store'
import { isProjectId, isTaskMenuId } from '@/models/utils'
import { ActionType, TableNoDataProp, TaskGroupItem, TodoInfo } from '@/types'
import { FieldTypeEnum } from '@/types/custom-field'
import {
  DrawerClickOrignKey,
  DrawerOrigin,
  EnumListAddSource,
  OneAndMoreServerParamsType,
  OneAndMoreType,
  OrderCustom,
  TaskNavigatorType,
  TaskNoDataDark,
  TaskNoDataLight,
  TaskSearchNoDataDark,
  TaskSearchNoDataLight,
  TaskTableRowType,
  ViewAddSource,
} from '@/utils/const'
import {
  attrAssignee,
  attrAssigner,
  attrCreatTime,
  attrDeadline,
  attrFollower,
  attrPriority,
  attrProject,
  attrStartTime,
  attrTaskName,
  combineDisplayFields,
  DisplayField,
  EnumField,
} from '@/utils/fields'
import I18N from '@/utils/I18N'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'
import { EnumTrackeKey } from '@/utils/skyline'

import s from './index.less'
import {
  RenderAssignor,
  RenderCreatTime,
  RenderCustomField,
  RenderDeadline,
  RenderExecutor,
  RenderLevel,
  RenderProject,
  RenderStartTime,
  RenderTaskName,
  RenderTaskNameAdd,
  RenderWatcher,
} from './render'
import TanstackTable from '@/components/TanstackTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { ConfigProvider } from '@bedrock/components'
import useGetGroupedList from '@/hooks/useGetGroupedList'
import { stopClickWhenDropdownPending } from './utils'

interface Props {}

const ViewTable: React.FC<Props> = props => {
  const {
    sortOrder,
    displayFields,
    pagination,
    queryGroupBys,
    dataList,
    treeList = [],
    navigatorId,
    // visibleDetail,
    customFields,
    permissions,
    refreshing,
    expandedKeysMap,
    totalTaskCount,
    conditions,
  } = useSelector((state: RootState) => {
    let currentView = state.viewSetting.currentViewTab
    const { querySort } = currentView
    return {
      // visibleDetail: state.detail.visibleDetail,
      tableAddTask: state.task.tableAddTask,
      sortOrder: {
        order: querySort?.order,
        orderBy: querySort?.customFieldId ? querySort.customFieldId : querySort?.fieldName,
      },
      displayFields: currentView.displays || [],
      pagination: state.task.pagination,
      refreshing: state.task.refreshing,
      queryGroupBys: currentView.queryGroupBys,
      dataList: state.task.dataList,
      treeList: state.task.treeList,
      navigatorId: state.viewSetting.navigatorId,
      customFields: state.viewSetting.customFields,
      permissions: state.viewSetting.permissions,
      expandedKeysMap: state.task.expandedKeysMap,
      totalTaskCount: state.task.pagination.total,
      conditions: currentView.conditions,
    }
  })
  const dispatch = useDispatch<Dispatch>()

  const canEditCustomField = validatesPermission({
    key: ProjectPermissionEnum.CAN_MANAGE_FIELD,
    permissions,
  })

  const isGroup = useMemo(() => {
    return !!queryGroupBys?.length
  }, [queryGroupBys])

  const tableData = isGroup ? treeList : dataList

  const memoColumns = useMemo(() => {
    const taskNameColumn: ColumnDef<any> = {
      header: () => <div className={s.headTaskName}>{I18N.auto.taskName}</div>,
      accessorKey: attrTaskName.value,
      minSize: 320,
      enableResizing: true,
      cell: ({ row, getValue }) => {
        const original = row.original
        const canExpand = row.getCanExpand()
        const isGroup = original.groupBy
        const renderContent = () => {
          if (original._rowType === TaskTableRowType.addBtn) {
            return (
              <RenderAddRow
                itemData={original}
                onAddBtnClick={() => {
                  onAddBtnClick(original.taskId, original.groupId, row)
                }}
              />
            )
          }
          if (original._rowType === TaskTableRowType.add) {
            const parentRow = row.getParentRow()
            return (
              <div className="task-tr-add" style={{ width: '100%' }}>
                <RenderTaskNameAdd
                  tableData={tableData}
                  tableRowData={row}
                  groupId={original.groupId}
                  taskInfo={original}
                  onAddCreatingLine={() =>
                    onAddBtnClick(original.taskId, original.groupId, row, {
                      actionType: ActionType.enter,
                    })
                  }
                  parentRow={parentRow}
                ></RenderTaskNameAdd>
              </div>
            )
          }
          if (canExpand || isGroup) {
            if (!isGroup) {
              return (
                <RenderTaskName
                  key={original.title}
                  taskInfo={original}
                  tableRowData={row}
                  // visibleDetail={visibleDetail}
                />
              )
            }
            return (
              <RenderGroupRow
                groupInfo={original}
                // onCollapse={onCollapse}
                assignees={original.users}
              ></RenderGroupRow>
            )
          }
          return (
            <RenderTaskName
              key={original.title}
              taskInfo={original}
              tableRowData={row}
              // visibleDetail={visibleDetail}
            />
          )
        }

        return (
          <div
            className={`${s.task__name} flex-y-center`}
            onClickCapture={stopClickWhenDropdownPending}
            style={{ paddingLeft: `${row.depth * 26}px` }}
          >
            <div className={s.expand__container}>
              {canExpand && (
                <IconBtn
                  className={classNames(s.arrow, 'expanded__icon')}
                  onClick={row.getToggleExpandedHandler()}
                  fontSize={16}
                  iconName="icon-sys_Expand"
                />
              )}
            </div>

            {renderContent()}
          </div>
        )
      },
      meta: {
        sortable: true,
      },
    }
    const assignerColumn = {
      header: () => I18N.auto.assignedBy,
      accessorKey: attrAssigner.value,
      align: 'left',
      size: 120,
      minSize: 100,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderAssignor taskInfo={original}></RenderAssignor>
      },
    }
    const assigneeColumn = {
      header: () => I18N.auto.assignedTo,
      accessorKey: attrAssignee.value,
      align: 'left',
      size: 120,
      minSize: 100,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return (
          <RenderExecutor
            oneAndMoreType={
              original.completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one
            }
            taskInfo={original}
          ></RenderExecutor>
        )
      },
    }
    const followerColumn = {
      header: () => I18N.auto.followPeople,
      accessorKey: attrFollower.value,
      align: 'left',
      size: 120,
      minSize: 120,
      cell: info => {
        const original = info.row.original
        return <RenderWatcher taskInfo={original}></RenderWatcher>
      },
    }
    const deadlineColumn = {
      header: () => I18N.auto.deadline_2,
      accessorKey: attrDeadline.value,
      align: 'left',
      size: 220,
      minSize: 130,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderDeadline taskInfo={original}></RenderDeadline>
      },
    }
    const startTimeColumn = {
      header: () => I18N.auto.startTime,
      accessorKey: attrStartTime.value,
      align: 'left',
      size: 180,
      minSize: 120,
      cell: info => {
        const original = info.row.original
        return <RenderStartTime taskInfo={original}></RenderStartTime>
      },
    }
    const creatTimeColumn = {
      header: () => I18N.auto.creationTime,
      accessorKey: attrCreatTime.value,
      align: 'left',
      size: 180,
      minSize: 120,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderCreatTime taskInfo={original}></RenderCreatTime>
      },
    }
    const priorityTimeColumn = {
      header: () => I18N.auto.priority,
      accessorKey: attrPriority.value,
      align: 'left',
      size: 120,
      minSize: 120,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderLevel taskInfo={original}></RenderLevel>
      },
    }
    const projectColumn = {
      header: () => I18N.auto.project,
      accessorKey: attrProject.value,
      align: 'left',
      size: 180,
      minSize: 120,
      meta: {
        sortable: true,
      },
      cell: info => {
        const original = info.row.original
        return <RenderProject taskInfo={original}></RenderProject>
      },
    }
    const addCustomFieldColum = {
      header: () => (
        <CustomFieldCreateWrapper>
          <IconBtn icon={<OperateAdd className="fs-16"></OperateAdd>}></IconBtn>
        </CustomFieldCreateWrapper>
      ),

      tooltip: I18N.auto.newField,
      sortable: false,
      align: 'left',
      fixed: 'right',
      accessorKey: 'add-icon',
      size: 40,
      minSize: 40,
      cell: () => {
        return null
      },
    }
    const columnList =
      combineDisplayFields(displayFields as DisplayField[], customFields, navigatorId)
        ?.filter(item => !!item.visible)
        .map(item => {
          if (item.fieldName === EnumField.assigner) {
            return assignerColumn
          } else if (item.fieldName === EnumField.assignee) {
            return assigneeColumn
          } else if (item.fieldName === EnumField.follower) {
            return followerColumn
          } else if (item.fieldName === EnumField.deadline) {
            return deadlineColumn
          } else if (item.fieldName === EnumField.startTime) {
            return startTimeColumn
          } else if (item.fieldName === EnumField.createTime) {
            return creatTimeColumn
          } else if (item.fieldName === EnumField.priority) {
            return priorityTimeColumn
          } else if (item.fieldName === EnumField.project) {
            return projectColumn
          } else if (item.customFieldId) {
            const filedInfo = customFields.find(v => v.fieldId === item.customFieldId)

            const customFieldColumn = {
              header: () => filedInfo?.name,
              accessorKey: filedInfo?.fieldId,
              align: 'left',
              size: 180,
              minSize: [FieldTypeEnum.text, FieldTypeEnum.number].includes(filedInfo?.type) ? 80 : 120,
              meta: {
                className: s.columnbox,
                suffix: canEditCustomField ? (
                  <div
                    onClick={e => {
                      e.stopPropagation()
                    }}
                    className="suffix"
                  >
                    <CustomFieldDropdown fieldId={filedInfo?.fieldId} name={filedInfo?.name}>
                      <IconBtn iconName="icon-sys_open" iconClassName={s.customSetting}></IconBtn>
                    </CustomFieldDropdown>
                  </div>
                ) : null,
                sortable: filedInfo
                  ? [FieldTypeEnum.text, FieldTypeEnum.datetime, FieldTypeEnum.number].includes(filedInfo?.type)
                  : false,
              },

              cell: info => {
                const original = info.row.original
                //根据类型匹配自定义组件 filedInfo?.type
                return filedInfo ? (
                  <RenderCustomField filedInfo={filedInfo} taskInfo={original}></RenderCustomField>
                ) : null
              },
            }
            return customFieldColumn
          }
          return undefined
        })
        .filter(item => !!item) || []
    columnList.unshift(taskNameColumn)
    if (!isTaskMenuId(navigatorId) && canEditCustomField) {
      columnList.push(addCustomFieldColum)
    }
    return columnList
  }, [displayFields, customFields, canEditCustomField, navigatorId])

  const onAddBtnClick = (id: number | string, groupId?: string, tableRowData?: Row<TodoInfo>, params = {}) => {
    // 标注新建来源
    if (groupId) {
      ViewAddSource.list = EnumListAddSource.groupBottomCreate
    } else {
      ViewAddSource.list = EnumListAddSource.bottomCreate
    }

    dispatch.detail.closeDetail({})

    const { original } = tableRowData
    if (original.parentId || original.groupedIds?.length) {
      dispatch.task.addSubLine({
        taskInfo: original,
        tableRowData,
        isSubTask: false,
        // addItemData: {
        //   placeholder: I18N.auto.subTaskPlaceholder_2,
        // },
        ...params,
      })
    } else {
      // 在新增按钮前面追加一个编辑态
      dispatch.task.tableAddTaskItem({
        taskId: Number(id),
        isHeader: original.isHeader,
        groupId,
        tableRowData,
        isSubtask: false,
        ...params,
      })
    }
  }

  const loadMore = (atBottom: boolean) => {
    if (pagination.more && atBottom) {
      dispatch.task.loadMore({})
    }
  }

  const onCollapse = (unfold: boolean, groupInfo: TaskGroupItem) => {
    dispatch.task.collapse({
      unfold: unfold,
      groupId: groupInfo.groupId!,
    })
  }

  const onRowClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd
  }
  const onRowMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd
  }

  const noData = !tableData?.length || (tableData.length === 1 && tableData[0]?._rowType === TaskTableRowType.addBtn)

  const memoNoSearchDataProps = useMemo(() => {
    return {
      isNoData: noData && navigatorId !== TaskNavigatorType.myFollow && conditions?.length,
      descriptive: I18N.auto.thereIsCurrentlyNoScreeningThatMeetsTheCriteria,
      bgUrl: {
        dark: TaskSearchNoDataDark,
        light: TaskSearchNoDataLight,
      },
    }
  }, [noData, dataList, navigatorId])

  const memoNoDataProps: TableNoDataProp = useMemo(() => {
    console.log('tableData', tableData)
    return {
      isNoData: noData && navigatorId !== TaskNavigatorType.myFollow && !refreshing,
      descriptive: I18N.auto.noTasksAvailableAtTheMoment,
      bgUrl: {
        dark: TaskNoDataDark,
        light: TaskNoDataLight,
      },
    }
  }, [tableData, navigatorId, refreshing])

  const handleDragEnd = ({ list, activeIndex, overIndex, preTaskId, tailTaskId, activeTaskId, activeParent }) => {
    if (activeParent) {
      dispatch.task.updateTaskInfo({
        taskInfo: activeParent,
      })
      // if (visibleDetail && activeParent.taskId === detailTaskId) {
      //   dispatch.detail.setData({ taskInfo: activeParent });
      // }
    } else {
      dispatch.task.setData({
        treeList: list,
      })
    }

    // 项目下的任务拖拽(非子任务)
    if (navigatorId && isProjectId(navigatorId) && !activeParent) {
      // 进行拖拽后触发hasViewChanged,然后将排序的操作进行收集
      // 只有在保存视图或者另存为新视图时才触发接口
      dispatch.viewSetting.updateProjectTaskOrder({
        orderParam: {
          preTaskId,
          tailTaskId,
          currentTaskId: activeTaskId,
        },
        hasViewChanged: true,
      })
    } else if (isTaskMenuId(navigatorId) || activeParent) {
      apiTaskResortPost({
        currentTaskId: activeTaskId,
        preTaskId,
        tailTaskId,
      }).then(() => {
        // 如果拖拽的是子任务，更新父任务
        const { visibleDetail, taskId: detailTaskId } = store.getState().detail
        if (visibleDetail && activeParent?.taskId === detailTaskId) {
          dispatch.detail.getDetailInfo({ taskId: activeParent?.taskId })
        }
      })
    }

    // dispatch.task.getTodoList({
    //   size: list.length,
    // });
    // dispatch.task.setData({
    //   showDataList: list
    // })

    // 埋点: 列表自定义排序  拖拽
    dispatch.user.tracking({ key: EnumTrackeKey.ListDrag })
  }
  return (
    <div className={classNames(s.tableView)}>
      <ConfigProvider
        getPopupContainer={triggerNode => {
          if (['rock-select', 'rock-dropdown-trigger-default'].some(item => triggerNode?.classList.contains(item))) {
            return document.querySelector('#tanstack-table') as HTMLElement
          }
          return document.body
        }}
      >
        <TanstackTable
          sortOrder={sortOrder!}
          onSortChange={(orderBy, sortOrder) => {
            dispatch.viewSetting.sort({
              order: sortOrder,
              orderBy: orderBy,
            })
          }}
          data={treeList}
          columns={memoColumns}
          expandedKeysMap={expandedKeysMap}
          // onExpandedChange={(expanded) => {
          //   dispatch.task.setData({
          //     expandedKeysMap: expanded as Record<number, boolean>,
          //   });
          // }}
          navigatorId={navigatorId}
          onDragEnd={handleDragEnd}
          rowsDraggable={sortOrder.orderBy === OrderCustom && !isGroup}
          emptyNodeProps={memoNoDataProps}
          emptySearchDataProps={memoNoSearchDataProps}
          total={totalTaskCount}
          onRowClick={onRowClick}
          onRowMouseDown={onRowMouseDown}
        />
      </ConfigProvider>
      {/* <SubtaskGuide user={userInfo} /> */}
      {/* <TodoTable
        rowKey="id"
        columns={memoColumns}
        data={memoDataList}
        groupData={groupList}
        onAddBtnClick={onAddBtnClick}
        sortOrder={sortOrder!}
        onSortChange={(orderBy, sortOrder) => {
          dispatch.viewSetting.sort({
            order: sortOrder,
            orderBy: orderBy,
          });
        }}
        atBottomStateChange={loadMore}
        isGroup={isGroup}
        drag={sortOrder.orderBy === OrderCustom && !isGroup}
        // dark={corlorMode === CorlorMode.dark}
        noDataProps={memoNoDataProps}
        // noSearchDataProps={memoNoSearchDataProps}
        groupRender={(index: number) => {
          const groupInfo = groupList[index];
          const { assignees } = groupInfo;
          return (
            <RenderGroupRow
              groupInfo={groupInfo}
              onCollapse={onCollapse}
              assignees={assignees}
            ></RenderGroupRow>
          );
        }}
        onRowClick={(v, e) => {
          onRowClick(e);
        }}
        onRowMouseDown={onRowMouseDown}
        onDragEnd={({ active, over }) => {
          if (active.id !== over?.id) {
            //1-1 //1->组 //组->组 //组->1
            //首先找到active的数据 并保存下来, 将原来的删除掉,
            //找到over的位置,并在这个地方插入新的数据
            const activeIndex = dataList.findIndex((item) => item.id === active.id);
            const overIndex = dataList.findIndex((item) => item.id === over?.id);
            const _list = arrayMove(dataList, activeIndex, overIndex);
            //排除

            let preId = undefined;
            let nextId = undefined;
            for (let index = 0; index < _list.length; index++) {
              const item = _list[index];
              if (item.taskId === active.id) {
                const pre = _list[index - 1];
                const next = _list[index + 1];
                if (pre?.taskId && validateTask(pre)) {
                  preId = pre.taskId;
                }
                if (next?.taskId && validateTask(next)) {
                  nextId = next.taskId;
                }
              }
            }
            //拖拽之后的数据 获取上一个和下一个的taskId 传递给服务端
            apiTaskResortPost({
              currentTaskId: active.id as number,
              preTaskId: preId,
              tailTaskId: nextId,
            });
            dispatch.task.calculatedTodoList({
              dataList: [..._list],
            });
            // 埋点: 列表自定义排序  拖拽
            dispatch.user.tracking({ key: EnumTrackeKey.ListDrag });
          }
        }}
      ></TodoTable> */}
    </div>
  )
}

export default ViewTable
