import { POPOBridgeEmitter } from '@popo-bridge/web';
import { createModel } from '@rematch/core';
import dayjs from 'dayjs';

import { ApiTaskKanbanViewGroupsScrollQueryPostResponse } from '@/api';
import {
  apiGetKanbanViewGroups,
  apiGetKanbanViewGroupsScrollQuery,
  getPermissionsByTaskIds,
  getPermissionsByTaskList,
} from '@/api-common';
import {
  AddTaskInfoItem,
  ILaneMapData,
  PermissionsTasksMap,
  TaskGroupItem,
  TaskInfo,
  TodoInfo,
} from '@/types';
import {
  EnumEmitter,
  TaskNavigatorType,
  TaskTableRowType,
  TaskTableRowTypeAdd,
  ViewType,
} from '@/utils/const';
import {
  EnumDeadlineGroup,
  EnumFieldNONE,
  EnumGroupBy,
  FinishedStatus,
  getFinishedGroupMap,
  getPriorityGroupMap,
  getTimeGroupMap,
  Priority,
} from '@/utils/fields';

import type { RootModel } from '.';
import {
  getAddItem,
  getNoPermissionList,
  handleSeverData,
  init_page_size,
  setNewAddItem,
  tfGroups,
} from './utils';

interface ILaneParams {
  hasMore: boolean;
}

interface IlaneOptionsMapData {
  [k: string]: ILaneParams;
}
interface KanbanState {
  groupList?: TaskGroupItem[]; //看板分组数据 和每一组的数据存储分开存, 如果合并到一个树当中,每次都会全量更新
  containerIds?: string[];
  laneMapData?: ILaneMapData;
  laneOptionsMapData?: IlaneOptionsMapData;
  permissionsMap: PermissionsTasksMap;
  dissociatedGroupListMap: Record<string, any>;
}

interface IAddTaskInfoItem extends AddTaskInfoItem {
  groupId: string | number;
  id: number;
}

const IDKey = 'taskId';

const addId = (list: any[], key: string) => {
  const _list = list?.map((item) => {
    return { ...item, id: item[key] };
  });
  return _list;
};

const initData = {
  containerIds: [] as string[],
  groupList: [] as TaskGroupItem[],
  laneMapData: {} as ILaneMapData,
  laneOptionsMapData: {} as IlaneOptionsMapData,
  permissionsMap: {} as PermissionsTasksMap,
  dissociatedGroupListMap: { list: [] } as Record<string, any>,
};

const DefaultPaginatio = {
  size: init_page_size,
  maxSize: 100,
};

export const kanban = createModel<RootModel>()({
  state: initData,
  reducers: {
    initData() {
      return {
        ...initData,
      };
    },
    setData(state, payload: Partial<KanbanState>) {
      return { ...state, ...payload };
    },
    setGroupList(state, payload: TaskGroupItem[]) {
      return { ...state, groupList: payload };
    },
    setLaneMapData(state, payload: ILaneMapData) {
      return { ...state, laneMapData: payload };
    },
  },
  effects: (dispatch) => ({
    async getTaskForGroupId(
      opt: { page?: number; groupId: string; scrollId?: string; size?: number },
      state
    ) {
      const { scrollId, groupId, size = 20 } = opt;
      const _param = dispatch.viewSetting.getComParams({});
      const param = {
        ..._param,
        navigatorId: _param.navigatorId + '',
        viewId: Number(_param.viewId!),
        size: size,
        scrollId: scrollId,
        groupId: groupId,
      };
      const times = Math.ceil(size / DefaultPaginatio.size);
      let oneList: TaskInfo[] = [];
      let _retData: ApiTaskKanbanViewGroupsScrollQueryPostResponse = {};
      for (let index = 0; index < times; index++) {
        param.size = DefaultPaginatio.size;
        const { list: thisList = [], ...retData } = await apiGetKanbanViewGroupsScrollQuery(param, {
          errorSilent: true,
        });
        _retData = retData;
        oneList = oneList.concat(thisList as TaskInfo[]);
        if (thisList.length) {
          param.scrollId = String(thisList[thisList.length - 1].taskId);
        }
      }
      const { userInfo } = state.user;
      const { permissionsMap } = state.permissions;
      oneList = oneList
        ?.map((item) => handleSeverData(item, userInfo!))
        .map((item) => {
          //如果权限有,则用之前的, 不然会导致拖拽的时候 刷新的泳道数据都无权限了
          if (permissionsMap[item.taskId!]?.permissions?.length) {
            return {
              ...item,
              permissions: permissionsMap[item.taskId!].permissions,
            };
          }
          return item;
        });
      //更新当前list的权限列表
      return {
        list: oneList,
        groupId,
        ..._retData,
      };
    },
    updatePermissionsList(opt: any, state) {
      const { laneMapData } = state.kanban;
      let taskList: TaskInfo[] = [];
      Object.keys(laneMapData).forEach((groupId) => {
        taskList = taskList.concat(laneMapData[groupId] || []);
      });

      // dispatch.permissions.setPermissionsMap({
      //   list: taskList,
      //   widthSubtasks: false,
      //   limit: taskList.length,
      // });

      // const { userInfo } = state.user;
      // const noPermissionsData = getNoPermissionList({ list: taskList, permissionsMap, userInfo });
      // getPermissionsByTaskIds(noPermissionsData).then((mapPermissions) => {
      //   const newPermissionsMap = {
      //     ...permissionsMap,
      //     ...mapPermissions,
      //   } as PermissionsTasksMap;
      //   //缓存看板权限数据
      //   Object.keys(laneMapData).forEach((groupId) => {
      //     if (laneMapData[groupId].length) {
      //       laneMapData[groupId] = laneMapData[groupId].map((item) => {
      //         if (!item.permissions && newPermissionsMap[item.taskId!]) {
      //           return {
      //             ...item,
      //             permissions: newPermissionsMap[item.taskId!].permissions,
      //           };
      //         }
      //         return item;
      //       });
      //     }
      //   });
      //   dispatch.kanban.setData({
      //     permissionsMap: newPermissionsMap,
      //     laneMapData: Object.assign({}, laneMapData),
      //   });
      // });
    },
    /**
     * 刷新看板分组
     * @param opt
     * @param state
     * @returns
     */
    async getKanbanGroupList(opt: { page?: number; isCurrentSize?: boolean }, state) {
      const { isCurrentSize } = opt;
      const param = dispatch.viewSetting.getComParams({});
      const res = await apiGetKanbanViewGroups(
        {
          ...param,
          navigatorId: param.navigatorId + '',
        },
        {
          errorSilent: true,
        }
      );
      const containerIds: string[] = [];
      const hasDataGroupIds: string[] = [];
      const noDataGroupIds: string[] = [];
      const groupList = tfGroups({
        fieldName: param.queryGroupBy?.fieldName,
        customFieldId: param.queryGroupBy?.customFieldId,
        customFields: state.viewSetting.customFields,
        projectId: param?.navigatorId,
        groups: res?.groups ?? [],
        viewType: ViewType.kanban,
      });
      const _laneMapData: ILaneMapData = {};
      const _laneOptionsMapData: IlaneOptionsMapData = {};
      const _dissociatedGroupListMap = { list: [] };
      const newGroupList = [];
      groupList?.forEach((item) => {
        containerIds.push(item.groupId!);
        if (item.isDissociated) {
          _dissociatedGroupListMap[item.groupId!] = item;
        } else {
          newGroupList.push(item);
        }
        if (item.count) {
          hasDataGroupIds.push(item.groupId!);
        } else {
          noDataGroupIds.push(item.groupId!);
        }
      });
      if (hasDataGroupIds.length) {
        let size = init_page_size;
        const requests = hasDataGroupIds.map((groupId) => {
          if (isCurrentSize) {
            const { laneMapData } = state.kanban;
            const length = laneMapData[groupId]?.length;
            size = length > init_page_size ? length : init_page_size;
          }
          return dispatch.kanban.getTaskForGroupId({
            groupId: groupId,
            size: size,
          });
        });
        const rets = await Promise.all(requests);
        let totalList: TaskInfo[] = [];
        rets.forEach((item, index) => {
          //@ts-ignore
          item.list = addId(item.list, IDKey);
          totalList = totalList.concat(item.list);
          if (!_dissociatedGroupListMap[item.groupId]) {
            _laneMapData[hasDataGroupIds[index]] = item.list || [];
            _laneOptionsMapData[hasDataGroupIds[index]] = { hasMore: !!item.hasMore };
          } else {
            const srcList = _dissociatedGroupListMap.list;
            _dissociatedGroupListMap.list = srcList.concat(item.list);
          }
        });

        if (_dissociatedGroupListMap.list?.length) {
          const srcList = _laneMapData[EnumFieldNONE.NONE] || [];
          _laneMapData[EnumFieldNONE.NONE] = srcList.concat(_dissociatedGroupListMap.list);
        }

        if (noDataGroupIds.length) {
          // 激活空数据泳道;
          noDataGroupIds.forEach((groupId) => {
            if (groupId !== EnumFieldNONE.NONE || !_dissociatedGroupListMap.list?.length) {
              _laneMapData[groupId] = [];
              _laneOptionsMapData[groupId] = { hasMore: false };
            }
          });
        }
      } else {
        // 全部为空  需要激活泳道
        if (noDataGroupIds.length) {
          noDataGroupIds.forEach((groupId) => {
            _laneMapData[groupId] = [];
            _laneOptionsMapData[groupId] = { hasMore: false };
          });
        }
      }
      dispatch.kanban.setData({
        laneMapData: _laneMapData,
        laneOptionsMapData: _laneOptionsMapData,
        groupList: newGroupList,
        containerIds,
        dissociatedGroupListMap: _dissociatedGroupListMap,
      });

      //更新整个分组内获取到的所有任务的权限列表
      //首屏幕不放在单个泳道请求, 循环泳道归并权限的时候 无法获取最新值
      dispatch.kanban.updatePermissionsList({});
    },

    async refreshListByGroups(
      opt: { groupIds: string[]; isCurrentSize?: boolean; viewId?: number },
      state
    ) {
      const { groupIds, isCurrentSize, viewId } = opt;
      const { currentViewTab } = state.viewSetting;

      if (viewId) {
        if (viewId !== currentViewTab.viewId) {
          return;
        }
      }
      const { laneMapData, laneOptionsMapData, dissociatedGroupListMap } = state.kanban;
      const { list, ...dissociatedGroup } = dissociatedGroupListMap;
      let { groupList } = state.kanban;
      let size = init_page_size;

      let requestIds = groupIds;
      if (requestIds.includes(EnumFieldNONE.NONE)) {
        requestIds = requestIds.concat(Object.keys(dissociatedGroup));
      }

      const requests = requestIds.map((groupId) => {
        if (isCurrentSize) {
          const { laneMapData } = state.kanban;
          const length = laneMapData[groupId]?.length;
          size = length > init_page_size ? length : init_page_size;
        }
        return dispatch.kanban.getTaskForGroupId({
          groupId: groupId,
          size: size,
        });
      });
      const rets = await Promise.all(requests);
      let totalList: TaskInfo[] = [];
      // const { currentViewTab } = state.viewSetting;
      const queryGroupBy = currentViewTab.queryGroupBy;
      let groupBy = queryGroupBy?.customFieldId || queryGroupBy?.fieldName;
      dissociatedGroupListMap.list = [];
      rets.forEach((item, index) => {
        if (item.total === 0 && groupBy === EnumGroupBy.ASSIGNEE) {
          delete laneMapData[groupIds[index]];
          delete laneOptionsMapData[groupIds[index]];
          groupList = groupList.filter((v) => v.groupId !== groupIds[index]);
        } else {
          totalList = totalList.concat(item.list);
          //@ts-ignore
          item.list = addId(item.list, IDKey);
          if (!dissociatedGroup[item.groupId]) {
            laneMapData[groupIds[index]] = item.list || [];
            laneOptionsMapData[groupIds[index]] = { hasMore: !!item.hasMore };
          } else {
            dissociatedGroupListMap.list = dissociatedGroupListMap.list.concat(item.list);
          }

          if (dissociatedGroupListMap.list?.length) {
            const srcList = laneMapData[EnumFieldNONE.NONE] || [];
            laneMapData[EnumFieldNONE.NONE] = srcList.concat(dissociatedGroupListMap.list);
          }

          const _index = groupList.findIndex((v) => v.groupId === groupIds[index]);

          if (groupList[_index]) {
            groupList[_index] = {
              ...groupList[_index],
              count: item.total || 0,
              groupTaskCount: item.total || 0,
            };
          }
        }
        //更新分组数据
      });
      //合并所有数据更新权限
      dispatch.kanban.setData({
        laneMapData: laneMapData,
        laneOptionsMapData: laneOptionsMapData,
        dissociatedGroupListMap,
        groupList: [...groupList],
      });
      dispatch.kanban.updatePermissionsList({});
    },

    // 更新当前泳道数据
    async loadMoreTaskForGroupId(opt: { page?: number; groupId: string }, state) {
      const { groupId } = opt;
      const { laneMapData, laneOptionsMapData } = state.kanban;
      const length = laneMapData[groupId]?.length;
      if (!length || !laneOptionsMapData[groupId]?.hasMore) {
        return;
      }
      let scrollId = '';
      if (length) {
        scrollId = laneMapData[groupId][length - 1].taskId as unknown as string;
      }
      return dispatch.kanban
        .getTaskForGroupId({
          groupId: groupId,
          scrollId: scrollId,
        })
        .then((res) => {
          const { list = [], hasMore } = res;
          let _list = addId(list, IDKey);
          laneMapData[groupId].push(..._list);
          laneOptionsMapData[groupId].hasMore = !!hasMore;
          dispatch.kanban.setData({
            laneOptionsMapData: Object.assign({}, laneOptionsMapData),
          });
          dispatch.kanban.setLaneMapData(Object.assign({}, laneMapData));
          dispatch.kanban.updatePermissionsList({});
          return res;
        });
    },
    tableAddTaskItem(opt: { groupId?: string; isHeader: boolean; defaultParams: any }, state) {
      let { groupId, isHeader, defaultParams } = opt;
      const { laneMapData } = state.kanban;
      const { userInfo } = state.user;
      const { currentViewTab, navigatorId } = state.viewSetting;
      const queryGroupBy = currentViewTab.queryGroupBy;
      if (!groupId) {
        if (queryGroupBy?.customFieldId) {
          POPOBridgeEmitter.emit(EnumEmitter.AddKanbanAndScrollRight);
          groupId = EnumFieldNONE.NONE;
          defaultParams = {
            fieldId: queryGroupBy.customFieldId,
            fieldVersion: queryGroupBy.customFieldVersion,
            value: undefined,
          };
        } else {
          //截止时间 今天
          if (queryGroupBy?.fieldName === EnumGroupBy.DEADLINE) {
            POPOBridgeEmitter.emit(EnumEmitter.AddKanbanAndScrollRight, 240);
            groupId = EnumDeadlineGroup.TODAY;
            const timeParams = getTimeGroupMap(dayjs())[EnumDeadlineGroup.TODAY].defaultParams;
            defaultParams = {
              deadline: timeParams.time,
              deadlineFormat: timeParams.format,
            };
          }
          //执行人 无
          if (queryGroupBy?.fieldName === EnumGroupBy.ASSIGNEE) {
            POPOBridgeEmitter.emit(EnumEmitter.AddKanbanAndScrollRight);
            groupId = EnumFieldNONE.NONE;
            defaultParams = {
              assignees: [],
            };
          }
          //优先级 无
          if (queryGroupBy?.fieldName === EnumGroupBy.PRIORITY) {
            POPOBridgeEmitter.emit(EnumEmitter.AddKanbanAndScrollRight);
            groupId = Priority.Unset as unknown as string;
            defaultParams = getPriorityGroupMap()[Priority.Unset].defaultParams;
          }
          //已完成分组 未完成
          if (queryGroupBy?.fieldName === EnumGroupBy.FINISHED) {
            groupId = FinishedStatus.UnFinished as unknown as string;
            defaultParams = getFinishedGroupMap()[FinishedStatus.UnFinished].defaultParams;
          }
        }
      }
      const dataList = laneMapData[groupId!] || [];
      if (isHeader) {
        POPOBridgeEmitter.emit(EnumEmitter.AddKanbanLaneAndScrollTop, {
          groupId: groupId,
          index: 0,
        });
      } else {
        POPOBridgeEmitter.emit(EnumEmitter.AddKanbanLaneAndScrollTop, {
          groupId: groupId,
          index: dataList.length + 1,
        });
      }
      const hasadd =
        dataList.findIndex((item: TodoInfo) => item._rowType === TaskTableRowType.add) > -1;
      if (hasadd) {
        return;
      }
      if (dataList === undefined) {
        return;
      }

      const addOption = {
        _rowType: TaskTableRowType.add,
        id: `${TaskTableRowTypeAdd}-2`,
        assigner: userInfo,
        defaultParams: {
          ...defaultParams,
        },
      };
      if (navigatorId === TaskNavigatorType.assignToMe) {
        //分配给我新建 我是执行人
        addOption.defaultParams = {
          ...addOption.defaultParams,
          assignees: [userInfo],
          assigneeUids: [userInfo?.uid],
          assigneeCount: 1,
        };
      }
      const addItem = getAddItem(addOption);
      const _list = setNewAddItem({
        dataList: dataList,
        isHeader: isHeader,
        //@ts-ignore
        item: addItem,
      });
      //
      Object.keys(laneMapData).forEach((_groupId) => {
        laneMapData[_groupId] = laneMapData[_groupId].filter(
          (item) => item._rowType !== TaskTableRowType.add
        );
      });
      laneMapData[groupId!] = _list;
      dispatch.kanban.setLaneMapData(Object.assign({}, laneMapData));
    },
    async addTask(opt: IAddTaskInfoItem, state) {
      const {
        title,
        priority,
        deadline,
        isFinished,
        deadlineFormat,
        assignees,
        groupId,
        customFieldValues,
      } = opt;
      return dispatch.task
        .addTask({
          title: title,
          priority: priority || Priority.Unset,
          deadline: deadline || 0,
          deadlineFormat: deadlineFormat || 0,
          assignees: assignees,
          isFinished: isFinished,
          customFieldValues: customFieldValues,
        })
        .then((res) => {
          //看板如果是点击其他视图触发失焦创建, 当前视图可能发生变化, 携带viewId判断是否要继续更新
          const { currentViewTab } = state.viewSetting;
          dispatch.kanban.refreshListByGroups({
            groupIds: [String(groupId)],
            isCurrentSize: true,
            viewId: currentViewTab.viewId,
          });
          dispatch.viewSetting.refreshDataByDataChange({
            refreshCount: true,
            refreshList: false,
          });
          return res;
        });
    },
    updateTodoList(taskInfo: TaskInfo, state) {
      const { laneMapData } = state.kanban;
      const list = laneMapData?.[taskInfo.groupId!];
      if (list.length) {
        laneMapData[taskInfo.groupId!] = list.map((item) => {
          if (taskInfo.taskId === item.taskId) {
            return {
              ...item,
              ...taskInfo,
            };
          }
          return item;
        });
        dispatch.kanban.setData({
          laneMapData: laneMapData,
        });
      }
    },
    // 上面那个方法的进阶版
    // updateTaskInfo(
    //   { taskInfo, actionType }: { taskInfo: TaskInfo; actionType?: 'delete' | 'replace' },
    //   state
    // ) {
    //   const { parentId } = taskInfo;
    //   if (parentId) {

    //   }

    //   const { laneMapData } = state.kanban;
    //   const list = laneMapData?.[taskInfo.groupId!];
    //   if (list.length) {
    //     const index = list.findIndex((item) => item.taskId === taskInfo.taskId);
    //     if (actionType === 'delete') {
    //       list.splice(index, 1);
    //     } else {
    //       list[index] = taskInfo;
    //     }
    //     laneMapData[taskInfo.groupId!] = [...list];
    //     dispatch.kanban.setData({
    //       laneMapData: laneMapData,
    //     });
    //   }
    // },
  }),
});
