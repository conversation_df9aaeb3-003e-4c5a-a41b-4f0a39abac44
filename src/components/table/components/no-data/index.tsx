import React, { FC, useContext, useMemo } from 'react';

import { TableContext } from '../../context';
import s from './index.less';

export type Props = {
  className?: string;
};
const NoData: React.FC<Props> = ({ className = '' }: Props) => {
  const { dark, noDataProps, noSearchDataProps } = useContext(TableContext);
  return (
    <ListTableNoData
      dark={dark}
      noDataProps={noDataProps}
      noSearchDataProps={noSearchDataProps}
      className={className}
    />
  );
};

interface ListTableNoDataProps {
  noDataProps?: any;
  noSearchDataProps?: any;
  dark?: boolean;
  className?: string;
}
export const ListTableNoData: FC<ListTableNoDataProps> = ({
  noDataProps,
  dark,
  noSearchDataProps,
  className = '',
}) => {
  const memoDesc = useMemo(() => {
    if (noSearchDataProps?.isNoData) {
      return {
        img: dark ? noSearchDataProps.bgUrl?.dark : noSearchDataProps.bgUrl?.light,
        title: noSearchDataProps.descriptive,
      };
    }
    if (noDataProps?.isNoData) {
      return {
        img: dark ? noDataProps.bgUrl?.dark : noDataProps.bgUrl?.light,
        title: noDataProps.descriptive,
      };
    }
  }, [noDataProps, dark, noSearchDataProps]);
  const hasData = useMemo(() => {
    return noDataProps?.isNoData || noSearchDataProps?.isNoData;
  }, [noDataProps, noSearchDataProps]);
  return hasData ? (
    <div className={`${s.nodata} ${className} no__data`}>
      <div>
        <img className={s.img} src={memoDesc?.img}></img>
      </div>
      <div className={s.desc}>{memoDesc?.title}</div>
    </div>
  ) : null;
};

export default NoData;
