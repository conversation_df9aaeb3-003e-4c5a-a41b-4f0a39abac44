import classNames from 'classnames';
import React, { useContext } from 'react';

import { TableContext } from '../../context';
import { Cell, Row } from '..';
import { TableCheckbox } from '../table-checkbox';
export type Props<RecordType = any> = {
  className?: string;
  data: RecordType[];
  itemData: RecordType;
  hasCheckbox?: boolean;
  onRowClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  onRowMouseDown?: React.MouseEventHandler<HTMLDivElement>;
  sortableDom: React.ReactNode;
};
const TableCell: React.FC<Props> = React.forwardRef((props, refs) => {
  const { data, itemData, hasCheckbox, onRowClick, onRowMouseDown, className, sortableDom } = props;
  const { onSelect, columns } = useContext(TableContext);
  return (
    <Row className={className} ref={refs} onRowClick={onRowClick} onRowMouseDown={onRowMouseDown}>
      {onSelect ? (
        <Cell>
          <div className="pl-8">
            {hasCheckbox ? <TableCheckbox data={data} itemData={itemData}></TableCheckbox> : null}
          </div>
        </Cell>
      ) : null}
      {columns?.map((item, key: number) => {
        let _className = '';
        if (item.fixed === 'left') {
          _className = 'todo-td-fixed-left';
        }
        if (item.fixed === 'right') {
          _className = 'todo-td-fixed-right';
        }
        //@ts-ignore
        return (
          <Cell
            key={key}
            className={classNames(
              {
                ['todo-td-right-no-border']: key === columns.length - 1,
              },
              _className
            )}
          >
            <>
              {sortableDom}
              {item.render(itemData[item.dataIndex], itemData)}
            </>
          </Cell>
        );
      })}
    </Row>
  );
});

export default TableCell;
