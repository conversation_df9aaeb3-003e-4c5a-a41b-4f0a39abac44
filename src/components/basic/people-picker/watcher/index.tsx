import { useLatest } from 'ahooks';
import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode, useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Dropdown, Placeholder } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { RootState } from '@/models/store';
import { UserInfo } from '@/types';

import { PeoplePickerContext } from '../context';
import { mergeArrays } from '../utils';
import s from './index.less';
import Overlay from './overlay';
export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  value: UserInfo[];
  hasMinimum?: boolean;
  assignerUid?: string;
  assigner?: UserInfo;
  onChange: (users: UserInfo[]) => void;
  hasArrow?: boolean;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  placeholder?: ReactNode;
  groupId?: string;
  taskId?: number;
  containerElement?: HTMLElement;
  canSelect?: boolean; //全部 通过select 选择和取消
  canDelete?: boolean; //单独的删除
  canDeleteSelf?: boolean; //能否单独删除自己
};

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    search,
    children,
    value,
    onChange,
    hasMinimum,
    hasArrow,
    disabled,
    visible = false,
    onVisible,
    placeholder,
    className,
    containerElement,
    canSelect,
    canDelete,
    canDeleteSelf,
  } = props;
  const { followerUids, assigneeUids, assignerUid } = useContext(PeoplePickerContext);
  const valueRef = useLatest(value);
  const [selectedPanelUsers, setSelectedPanelUsers] = useState<UserInfo[]>([]);
  const { recentContactsList, userInfo } = useSelector((state: RootState) => ({
    recentContactsList: state.user.recentContactsList,
    userInfo: state.user.userInfo,
  }));

  const changeselectedPanelUsers = (opt: { recentContacts: UserInfo[] }) => {
    const { recentContacts } = opt;
    let topUnSelect: UserInfo[] = [];
    let topSelect =
      [userInfo!]?.filter((item) => {
        if (valueRef.current.findIndex((v) => v.uid === item.uid) > -1) {
          return true;
        } else {
          // topUnSelect.push({
          //   ...item,
          // });
          return false;
        }
      }) || [];
    //已选人员value
    topSelect = topSelect.map((item) => ({
      ...item,
      selected: true,
    }));

    const selectValue = valueRef.current.map((item) => ({
      ...item,
      selected: true,
    }));
    const _selectedPanelUsers = disabled
      ? []
      : selectedPanelUsers.map((item) => ({
          ...item,
          selected: false,
        }));
    //我(选中) + 已选人人员 + 我(未选中) + 已选人员(取消选中的) + 最近联系人
    const list = [...topSelect, ...selectValue, ..._selectedPanelUsers];
    if (!list.length && !disabled) {
      //排除执行人和创建人assigneeUids和assignerUid
      list.push(
        ...[...topUnSelect, ...recentContacts].filter(
          (item) => !assigneeUids?.includes(item.uid!) && item.uid !== assignerUid
        )
      );
    }
    // 针对list 去重

    const data = mergeArrays(list) as UserInfo[];
    setSelectedPanelUsers(data);
  };

  useEffect(() => {
    // 打开面板用户不存在时获取最近联系人
    if (visible) {
      changeselectedPanelUsers({ recentContacts: disabled ? [] : recentContactsList || [] });
    }
  }, [visible, followerUids, disabled]);
  return (
    <Dropdown
      disabled={disabled}
      className={classNames(s.peoplePickerDropdown, className)}
      title={
        <DropdownIcon showArrow={visible} hasArrow={hasArrow}>
          {children ? (
            children
          ) : placeholder !== undefined ? (
            <Placeholder text={placeholder}></Placeholder>
          ) : null}
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      open={visible}
      onOpenChange={(open) => {
        onVisible?.(open);
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Overlay
          containerElement={containerElement}
          search={search}
          value={value}
          onChange={onChange}
          hasMinimum={hasMinimum}
          assignerUid={assignerUid}
          canSelect={canSelect}
          canDelete={canDelete}
          canDeleteSelf={canDeleteSelf}
          selectedPanelUsers={selectedPanelUsers}
        ></Overlay>
      }
    />
  );
};

export default PeoplePicker;
