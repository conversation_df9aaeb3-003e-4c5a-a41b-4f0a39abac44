{
  "editor.detectIndentation": false,
  "editor.tabSize": 2,
  "editor.formatOnPaste": false,
  "editor.renderWhitespace": "none", //空格变成......
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "files.eol": "auto",
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "prettier.prettierPath": "node_modules/prettier",
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.formatOnSave": true,
  "[xml]": {
    "editor.defaultFormatter": "redhat.vscode-xml"
  },
  "typescript.tsdk": "node_modules/typescript/lib"
}
