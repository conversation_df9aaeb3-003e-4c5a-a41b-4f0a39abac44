import classNames from 'classnames'
import React, { memo, useEffect, useMemo, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import {
  Message,
  RenderAlarm,
  RenderLevel,
  RenderPeoples,
  RenderRrule,
  RenderTime,
  RenderTimeStr,
  Tooltip,
} from '@/components/basic'
import { Variant } from '@/components/basic/input'
import { LevelSelectOptionsMap } from '@/components/basic/level-picker/utils'
import { CustomFieldSelect } from '@/components/basic-project'
import ComFieldText from '@/components/basic-project/com-field-text'
import { formatNumber, numberToPercent } from '@/components/basic-project/custom-field-number'
import ProjectTag from '@/components/basic-project/project-picker/tag-item'
import AddTaskMessage from '@/components/basic-task/add-task-message'
import TodoStatus from '@/components/basic-task/todo-status'
import {
  FieldNumberFormatEnum,
  FieldOptionColor,
  FieldTypeE<PERSON>,
  FiledTypeList,
  SelectFieldOption,
} from '@/components/field-edit-popover/const'
import { Dispatch, RootState } from '@/models/store'
import { TodoInfo } from '@/types'
import { EnumKanbanAddSource, FORMAT_DD_mm, OneAndMoreServerParamsType, ViewAddSource } from '@/utils/const'
import { getCustomColor } from '@/utils/customfields'
import {
  attrCreatTime,
  combineDisplayFields,
  defaultFieldOrder,
  DisplayField,
  EnumField,
  EnumGroupBy,
  Priority,
} from '@/utils/fields'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'
import { EnumTrackeKey } from '@/utils/skyline'

import { CardRenderTime } from './render'
import RenderItem from './render-item'
import CardSubTasks from './CardSubTasks'
import s from './index.less'
import './itemName.less'
import useGetPermissions from '@/hooks/useGetPermissions'

export enum CardType {
  onlyShow = 'onlyShow',
  add = 'add',
}
export interface CardProps {
  data: TodoInfo
  type: CardType
  groupId?: string | number
  className?: string
  style?: React.CSSProperties
}

const Card: React.FC<CardProps> = memo(props => {
  const { data: taskInfo, type, groupId, className, style = {} } = props

  const { title, taskId } = taskInfo

  const permissions = useGetPermissions({ taskId })

  const {
    laneMapData,
    currentOpenedTaskId,
    visibleDetail,
    groupBy,
    displayFields,
    customFields,
    navigatorId,
    parsedViewConfig,
  } = useSelector((state: RootState) => ({
    laneMapData: state.kanban.laneMapData,
    currentOpenedTaskId: state.detail?.taskId,
    visibleDetail: state.detail?.visibleDetail,
    groupBy: state.viewSetting.currentViewTab.queryGroupBy?.fieldName,
    displayFields: state.viewSetting.currentViewTab.displays || [],
    customFields: state.viewSetting.customFields,
    navigatorId: state.viewSetting.navigatorId,
    parsedViewConfig: state.viewSetting.currentViewTab.parsedViewConfig,
  }))
  const textRef = useRef<string>(title || '')

  const dispatch = useDispatch<Dispatch>()

  const cardConfig = parsedViewConfig?.cardConfig

  const onClose = () => {
    if (groupId) {
      const list = laneMapData[groupId]
      laneMapData[groupId] = list.filter(item => item.id !== taskInfo.id)
      // 调用接口计算位置
      dispatch.kanban.setLaneMapData(Object.assign({}, laneMapData))
    }
  }
  const onAdd = (title?: string) => {
    //大保底 如果title不存在 不允许新增
    if (!title || !groupId) {
      onClose()
      return
    }
    const { id, priority, deadline, deadlineFormat, assignees, customFieldValues, finished } = taskInfo
    dispatch.kanban
      .addTask({
        id: id!,
        groupId: groupId,
        title: title,
        priority: priority || Priority.Unset,
        deadline: deadline || 0,
        deadlineFormat: deadlineFormat,
        assignees: assignees?.map(item => item.uid!),
        customFieldValues: customFieldValues,
        isFinished: !!finished,
      })
      .then(res => {
        //埋点当前是谁新建 根据数据链路太长, 缓存最新的点击事件
        let key = EnumTrackeKey.KanbanNewGlobalCreate
        if (ViewAddSource.kanban === EnumKanbanAddSource.laneTopCreate) {
          key = EnumTrackeKey.KanbanNewLaneTopCreate
        } else if (ViewAddSource.kanban === EnumKanbanAddSource.laneBottomCreate) {
          key = EnumTrackeKey.KanbanNewLaneBottomCreate
        }
        dispatch.user.trackingByView({
          key: key,
          taskId: res.taskId,
          groupId: groupId!,
        })
        Message.success({
          content: (
            <AddTaskMessage
              onOpenDetail={() => {
                dispatch.detail.openDetail(res.taskId as unknown as number)
              }}
            ></AddTaskMessage>
          ),
          duration: 2,
          className: s.successMessage,
        })
      })
  }
  const onTextChange = v => {
    textRef.current = v
  }

  const disabledComplete = useMemo(() => {
    const [CAN_COMPLETE] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_COMPLETE],
    }) as boolean[]
    return !CAN_COMPLETE || type === CardType.add
  }, [permissions, type])

  const disabledView = useMemo(() => {
    const CAN_VIEW = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_VIEW,
    })
    return !CAN_VIEW
  }, [permissions])

  useEffect(() => {
    return () => {
      if (type === CardType.add && groupId && taskId) {
        dispatch.kanban.updateTodoList({
          taskId: taskId,
          title: textRef.current,
          groupId: groupId,
        })
      }
    }
  }, [])

  const renderAllField = useMemo(() => {
    const fields = combineDisplayFields(displayFields as DisplayField[], customFields, navigatorId)?.filter(
      item => !!item.visible,
    )

    const getNameNode = ({ icon, name }) => {
      return (
        <div className="card__item--name ellipsis">
          <span className="card__item--name--icon">{icon}</span>
          <span className="card__item--name--text">{name}</span>
        </div>
      )
    }

    const render: React.ReactNode =
      fields
        .map(item => {
          let Icon = <></>
          if (item.customFieldId) {
            const field = customFields?.find(v => v.fieldId === item.customFieldId)
            // FIXME: 遗留 循环优化
            const filedType = FiledTypeList.find(v => v.value === field?.type)
            const nameNode = getNameNode({
              icon: filedType?.icon,
              name: field?.name,
            })
            if (filedType?.icon) {
              Icon = (
                <Tooltip onlyEllipsis title={field?.name || filedType.name} placement="left">
                  {React.cloneElement(nameNode, {
                    className: `${filedType.icon.props.className || ''} ${s.attrIcon} ${nameNode.props.className}`,
                  })}
                </Tooltip>
              )
            }
          } else {
            const filedType = [...defaultFieldOrder, attrCreatTime].find(v => v.value === item.fieldName)
            const nameNode = getNameNode({
              icon: filedType?.icon,
              name: filedType?.name,
            })
            if (filedType?.icon) {
              Icon = (
                <Tooltip onlyEllipsis title={filedType.name} placement="left">
                  {React.cloneElement(nameNode, {
                    className: `${filedType.icon.props.className || ''} ${s.attrIcon} ${nameNode.props.className}`,
                  })}
                </Tooltip>
              )
            }
          }
          if (item.fieldName === EnumField.assigner) {
            const _list = [
              {
                ...taskInfo.assigner,
                avatarUrl: taskInfo.assigner?.assignerAvatarUrl,
                name: taskInfo.assigner?.assignerName,
                uid: taskInfo.assigner?.assignerUid,
              },
            ]
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderPeoples
                  list={_list}
                  count={1}
                  maxShowCount={1}
                  avatarClassName={s.avatar}
                  canOpenUserProfile={true}
                  size="small"
                ></RenderPeoples>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.assignee && taskInfo.assignees?.length) {
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderPeoples
                  showFinishedIcon={taskInfo.completeCondition === OneAndMoreServerParamsType.all}
                  list={taskInfo.assignees}
                  count={taskInfo.assigneeCount || taskInfo.assignees?.length}
                  maxShowCount={5}
                  avatarClassName={s.avatar}
                  canOpenUserProfile={true}
                  size="small"
                ></RenderPeoples>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.follower && taskInfo.followers?.length) {
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderPeoples
                  showFinishedIcon
                  list={taskInfo.followers}
                  count={taskInfo.followerCount || taskInfo.followers?.length}
                  maxShowCount={6}
                  avatarClassName={s.avatar}
                  size="small"
                ></RenderPeoples>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.deadline && taskInfo.deadline) {
            //判断收否显示开始时间
            const showStartTime = fields.findIndex(v => v.fieldName === EnumField.startTime && !!v.visible) > -1
            return (
              <RenderItem
                key={item.id}
                leftNode={Icon}
                className={s.deadlineItem}
                //  leftClassName={s.deadlineItemLeft}
              >
                <RenderTimeStr
                  startTime={showStartTime ? taskInfo.startTime : undefined}
                  timeFormat={taskInfo.deadlineFormat}
                  deadline={taskInfo.deadline}
                  isExpired={taskInfo.isExpired}
                  isToday={true}
                  className={classNames('mr-6')}
                ></RenderTimeStr>
                <span>
                  <RenderRrule
                    rrule={taskInfo.rrule}
                    className={classNames('mr-6', s.iconBtn, s.rulesIcon)}
                  ></RenderRrule>
                  <RenderAlarm
                    alarm={taskInfo.memoAlarm}
                    className={classNames('mr-6', s.iconBtn, s.rulesIcon)}
                  ></RenderAlarm>
                </span>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.startTime && taskInfo.startTime) {
            //判断是否显示截止时间  如果显示截止时间 开始时间不在此列渲染
            if (fields.findIndex(v => v.fieldName === EnumField.deadline) > -1) {
              return null
            }
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderTimeStr
                  onlyShowStart
                  startTime={taskInfo.startTime}
                  timeFormat={taskInfo.deadlineFormat}
                  isExpired={taskInfo.isExpired}
                  isToday={taskInfo.isToday}
                  className={classNames('mr-6')}
                ></RenderTimeStr>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.createTime) {
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderTime time={taskInfo.taskCreateTime} format={FORMAT_DD_mm}></RenderTime>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.priority && taskInfo.priority) {
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <RenderLevel value={taskInfo.priority}></RenderLevel>
              </RenderItem>
            )
          } else if (item.fieldName === EnumField.project && taskInfo.project?.projectId) {
            return (
              <RenderItem key={item.id} leftNode={Icon}>
                <ProjectTag item={taskInfo.project}></ProjectTag>
              </RenderItem>
            )
          } else if (item.customFieldId) {
            const filedInfo = customFields.find(v => v.fieldId === item.customFieldId)
            let defaultValue = undefined
            if (filedInfo?.type === FieldTypeEnum.user) {
              defaultValue = []
            }

            const _value =
              //@ts-ignore
              taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.value || defaultValue

            if (filedInfo?.type === FieldTypeEnum.datetime && _value) {
              return (
                <RenderItem key={item.id} leftNode={Icon}>
                  <CardRenderTime value={_value} timeFormat={filedInfo.format}></CardRenderTime>
                </RenderItem>
              )
            }
            if (filedInfo?.type === FieldTypeEnum.user && _value.length) {
              const users =
                //@ts-ignore
                taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.users || defaultValue
              return (
                <RenderItem key={item.id} leftNode={Icon}>
                  <RenderPeoples
                    showFinishedIcon
                    list={users || []}
                    count={_value.length || users?.length}
                    maxShowCount={6}
                    avatarClassName={s.avatar}
                    size="small"
                  ></RenderPeoples>
                </RenderItem>
              )
            }
            if ((filedInfo?.type === FieldTypeEnum.multiOption || filedInfo?.type === FieldTypeEnum.option) && _value) {
              // @ts-ignore
              if (!filedInfo.optionsMap) {
                // @ts-ignore
                filedInfo.optionsMap = filedInfo.options!.reduce((pre, cur) => {
                  pre[cur.value] = cur
                  return pre
                }, {} as Record<string, SelectFieldOption>)
              }
              // @ts-ignore
              const valueExit = filedInfo.optionsMap[_value]
              if (!valueExit) {
                return null
              }

              return (
                <RenderItem key={item.id} leftNode={Icon}>
                  <CustomFieldSelect
                    disabled
                    value={_value}
                    options={filedInfo.options!}
                    className={classNames(s.customSelect)}
                    multiple={filedInfo.type === FieldTypeEnum.multiOption}
                    maxTagCount={'responsive'}
                    tagCloseable={false}
                    noRadius
                  ></CustomFieldSelect>
                </RenderItem>
              )
            }
            if (filedInfo?.type === FieldTypeEnum.text && _value) {
              return (
                <RenderItem key={item.id} leftNode={Icon}>
                  <div className="ellipsis">{_value}</div>
                </RenderItem>
              )
            }
            if (filedInfo?.type === FieldTypeEnum.number && _value !== undefined) {
              let value = _value || _value === 0 ? formatNumber(_value, filedInfo.format) : _value
              if (
                filedInfo.format === FieldNumberFormatEnum.percent ||
                filedInfo.format === FieldNumberFormatEnum.percent2
              ) {
                value = `${numberToPercent(value)}%`
              }
              return (
                <RenderItem key={item.id} leftNode={Icon}>
                  <div>{value}</div>
                </RenderItem>
              )
            }
          }
          return ''
        })
        .filter(item => !!item) || []
    return render
  }, [displayFields, customFields, navigatorId, taskInfo])

  const siderBorderColor = useMemo(() => {
    if (cardConfig?.siderColor) {
      if (cardConfig.siderColor === EnumField.priority) {
        const color = LevelSelectOptionsMap[taskInfo.priority || Priority.Unset].borderColor
        return color ? `var(${color})` : ''
      }

      const customField = customFields.find(item => item.fieldId === cardConfig.siderColor)
      if (customField) {
        const options = customField.options || []
        const option = options.find(
          item => item.value === taskInfo.customFieldValues?.values?.[customField.fieldId]?.value,
        )
        if (option) {
          return getCustomColor(FieldOptionColor[option.color], 'default')
        }
      }
      return ''
    }
    return ''
  }, [cardConfig?.siderColor, taskInfo, customFields])

  return (
    <div
      className={classNames(
        s.card,
        {
          [s.cardFinished]: taskInfo.selfFinished || taskInfo.finished,
          [s.cardAdd]: type === CardType.add || (visibleDetail && currentOpenedTaskId === taskId),
          ['cursor']: !disabledView,
          [s.sideBorderColor]: !!siderBorderColor,
        },
        className,
      )}
      style={{ '--sideBorderColor': siderBorderColor, ...style } as React.CSSProperties}
      onClick={e => {
        e.stopPropagation() // 不要触发详情的outclick事件
        if (type !== CardType.add) {
          dispatch.detail.openDetail(taskId!)
        }
      }}
    >
      <RenderItem
        className={s.titleItem}
        leftClassName={s.titleItemLeft}
        leftNode={
          cardConfig?.showCompleteIcon && (
            <TodoStatus
              className={classNames(s.status)}
              onChange={() => {
                //否者刷新当前泳道数据
                if (groupBy === EnumGroupBy.FINISHED) {
                  // 刷新已完成和未完成两个泳道
                  dispatch.kanban.getKanbanGroupList({
                    isCurrentSize: true,
                  })
                } else {
                  dispatch.kanban.refreshListByGroups({
                    groupIds: [groupId as string],
                    isCurrentSize: true,
                  })
                }
              }}
              disabled={disabledComplete}
              taskInfo={taskInfo}
            ></TodoStatus>
          )
        }
      >
        <ComFieldText
          value={title}
          contentClassName={s.input}
          editable={type === CardType.add}
          onChange={onAdd}
          onTextChange={onTextChange}
          onClose={onClose}
          editding={type === CardType.add}
          blurChangeValue
          onBlur={() => {
            onClose()
          }}
          variant={Variant.borderless}
        ></ComFieldText>
      </RenderItem>
      {renderAllField}
      <CardSubTasks taskInfo={taskInfo} />
    </div>
  )
})

export default Card
