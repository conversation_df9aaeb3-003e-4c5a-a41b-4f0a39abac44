// eslint-disable-next-line simple-import-sort/imports
import { Select as BasicSelect } from '@bedrock/components';
import { Option, SelectProps as BasicProps } from '@bedrock/components/lib/Select/index';

import './index.less';

export interface SelectProps extends BasicProps {}
export type { Option };

export default function Select(props: SelectProps) {
  return <BasicSelect {...props}></BasicSelect>;
}
