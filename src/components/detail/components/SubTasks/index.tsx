import { Data16Sub, ImRetry1, ImStatusWarningSolid2, ImTrash1 } from '@babylon/popo-icons';
import s from './index.less';
import I18N from '@/utils/I18N';
import { FC, forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import CreateTaskLine from '@/components/CreateTaskLine';
import SubTaskItem, { SortableSubTaskItem } from './components/SubTaskItem';
import { Checkbox, Dropdown, Menu, Space } from '@bedrock/components';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, RootState } from '@/models/store';
import useDelTaskConfirm from '@/hooks/useDelTaskConfirm';
import DnDList from '@/components/dndkit/dnd-list';
import { DetailTodoInfo, TodoInfo } from '@/types';
import { Button, Icon } from '@/components/basic';
import { apiTaskAgentGuessTaskPost, apiTaskResortPost } from '@/api';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import { CheckHeavy } from '@bedrock/icons-react';
import { SUBTASK_MAX_COUNT } from '@/utils/const';
import { getCreateParams } from '@/models/utils';
import { checkSubtaskOverLimit } from '@/pages/new/components/view/list/utils';
import { EnumTrackeKey } from '@/utils/skyline';

interface ISubTasksProps {
  subTasks?: TodoInfo[];
  canEdit?: boolean;
  taskInfo?: DetailTodoInfo;
}
const SubTasks: FC<ISubTasksProps> = (props) => {
  const { canEdit } = props;
  const guessTaskRef = useRef<any>(null);
  const dispatch = useDispatch<Dispatch>();
  const { taskInfo, subTasks } = useSelector((state: RootState) => ({
    taskInfo: state.detail.taskInfo,
    subTasks: state.detail.subTasks,
  }));

  const subTaskIds = useMemo(() => {
    return (subTasks || []).map((item) => item.taskId);
  }, [subTasks]);

  const delTargetRef = useRef<TodoInfo>({});

  const { open } = useDelTaskConfirm();

  const handleItemContentMenu = (e) => {
    if (!canEdit) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    const currentTarget = e.currentTarget;
    const { taskId } = currentTarget.dataset;
    delTargetRef.current = subTasks?.find((item) => item.taskId === Number(taskId)) || {};
  };

  const handleDel = () => {
    open({
      taskId: delTargetRef.current.taskId,
      taskInfo: delTargetRef.current,
      from: 'detail_inner',
    });
  };

  const handleOpenChange = (open) => {
    if (!open) {
      delTargetRef.current = {};
    }
  };

  const toggleTasksContainerVisible = (visible: boolean) => {
    if (!subTasks?.length) {
      const taskContainer = document.querySelector(`.${s.tasks}`) as HTMLDivElement;
      if (taskContainer) {
        taskContainer.style.display = visible ? 'block' : 'none';
      }
    }
  };

  const handleGuess = () => {
    if (checkSubtaskOverLimit(subTasks)) {
      return;
    }

    dispatch.user.tracking({
      key: EnumTrackeKey.SubtaskSplit,
    });

    guessTaskRef.current?.guess();
    toggleTasksContainerVisible(false);
  };

  const handleGuessOperated = () => {
    toggleTasksContainerVisible(true);
  };

  return (
    <div id="subtasks" className={s.subtasks}>
      <SubTasksTitle
        canEdit={canEdit}
        taskInfo={taskInfo}
        subTasks={subTasks}
        onGuess={handleGuess}
      />
      <div className={s.tasks}>
        <Dropdown
          trigger={['contextMenu']}
          disabled={!canEdit}
          onOpenChange={handleOpenChange}
          overlay={
            <Menu className={s.context__menu}>
              <Menu.Item
                onClick={handleDel}
                key="C"
                className={s.item}
                icon={<ImTrash1 className={`${s.danger} fs-14`} />}
              >
                <span className={`${s.danger} ${s.text}`}>{I18N.auto.delSubTask}</span>
              </Menu.Item>
            </Menu>
          }
          primary
        >
          <div>
            <DnDList<TodoInfo>
              wholeRowHandle={false}
              list={subTasks || []}
              ids={subTaskIds}
              onDragEnd={(item, list: TodoInfo[], { activeIndex, overIndex }) => {
                const { over, active } = item;
                if (!over?.id || !active.id) {
                  return;
                }
                let preIndex;
                let nextIndex;
                if (activeIndex > overIndex) {
                  preIndex = overIndex - 1;
                  nextIndex = overIndex;
                } else {
                  preIndex = overIndex;
                  nextIndex = overIndex + 1;
                }

                // 2. 重新排序子任务
                apiTaskResortPost({
                  currentTaskId: active.id as number,
                  preTaskId: subTasks![preIndex]?.taskId,
                  tailTaskId: subTasks![nextIndex]?.taskId,
                }).then(() => {
                  dispatch.viewSetting.updateItemByDetail({
                    taskId: taskInfo?.taskId,
                  });
                });
                // 1. 本地更新子任务列表
                dispatch.detail.updateSubtasks({
                  subTasks: list,
                });
              }}
              customRenderItem={(item: TodoInfo) => {
                return (
                  <SortableSubTaskItem onContextMenu={handleItemContentMenu} taskInfo={item} />
                );
              }}
              renderOverlayItem={(item: TodoInfo) => {
                return (
                  <div style={{ background: 'var(--bgTop)' }}>
                    <SubTaskItem taskInfo={item || {}} key={item?.taskId} />
                  </div>
                );
              }}
            />
          </div>
        </Dropdown>
        <CreateTaskLine subTasks={subTasks} canEdit={canEdit} />
      </div>

      <GuessTasks taskInfo={taskInfo} ref={guessTaskRef} onGuessOperated={handleGuessOperated} />
    </div>
  );
};

interface ISubTasksTitleProps extends ISubTasksProps {
  onGuess?: () => void;
}
const SubTasksTitle: FC<ISubTasksTitleProps> = ({ onGuess, canEdit, taskInfo, subTasks }) => {
  const { theme } = useSelector((state: RootState) => ({
    theme: state.user.corlorMode,
  }));

  const subLength = subTasks?.length || 0;
  return (
    <div className={`${s.title} flex-y-center`}>
      <div className="flex-y-center">
        <div className={`${s.label} flex-y-center`}>
          <Data16Sub className="fs-16 mr-6" />
          {/* @ts-ignore */}
          {I18N.auto.subTask}
        </div>
        {subLength > 0 && (
          <div className="flex-y-center ml-12">
            <div className={`${s.sub__process} mr-8`}>
              {taskInfo?.subtaskFinishedCount || 0}/{subLength}
            </div>
            <div
              className={s['sub__process--bar']}
              style={{
                '--percent': subLength
                  ? `-${(1 - (taskInfo?.subtaskFinishedCount || 0) / subLength) * 100}%`
                  : '-100%',
              }}
            ></div>
          </div>
        )}
      </div>
      {canEdit && (
        <div className={`${s.option} flex-y-center`} onClick={onGuess}>
          <img
            src={`https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/subtask/ai_sub_${theme}.png`}
          />
          <div>{I18N.auto.AISplitSubtask}</div>
        </div>
      )}
    </div>
  );
};

interface IGuessTasksProps extends ISubTasksProps {
  onGuessOperated?: () => void;
}

/**
 * AI拆解的子任务
 */
const GuessTasks = forwardRef<any, IGuessTasksProps>(({ taskInfo, onGuessOperated }, ref) => {
  const { taskId, title, subtask } = taskInfo || {};
  const [value, setValue] = useState<string[]>([]);
  const [error, setError] = useState(false);
  const [inseting, setInseting] = useState(false);

  const { theme } = useSelector((state: RootState) => ({
    theme: state.user.corlorMode,
  }));

  const dispatch = useDispatch<Dispatch>();

  const guessCount = Math.min(SUBTASK_MAX_COUNT - (subtask?.length || 0), 5);

  const {
    run: guess,
    data: guessData,
    mutate: updateGuess,
    cancel: cancelGuess,
    loading: guessLoading,
  } = useRequest(
    () =>
      apiTaskAgentGuessTaskPost({
        taskId,
        title,
        maxCount: guessCount,
        sessionId: `${Date.now()}`,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.agentError) {
          setError(true);
        } else {
          setValue(res.guessTaskTitles || []);
        }
      },
    }
  );

  const handleGuess = () => {
    cancelGuess?.();
    guess();
    setTimeout(() => {
      const operationEls = document.querySelector('.guess_operation--btns');
      if (operationEls) {
        operationEls.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }, 160);
  };

  useImperativeHandle(
    ref,
    () => ({
      guess: handleGuess,
    }),
    [subtask]
  );

  if (!error && (inseting || (!guessLoading && !guessData?.guessTaskTitles?.length))) {
    return null;
  }

  const handleCancel = () => {
    cancelGuess();
    onGuessOperated?.();
    updateGuess({});
    setError(false);
  };

  const handleConfirm = () => {
    if (checkSubtaskOverLimit(taskInfo?.subtask, (taskInfo?.subtask?.length || 0) + value.length)) {
      return;
    }
    setInseting(true);
    onGuessOperated?.();
    Promise.all(
      value.map((item, index) => {
        return new Promise((resolve) => {
          setTimeout(() => {
            const taskParams = getCreateParams({ title: item }, taskId);
            dispatch.task
              .addTask(taskParams)
              .then((createdTask) => {
                // 更新子任务列表，将乐观更新的数据替换为createdTask
                dispatch.detail.insertSubTasks(createdTask);
                resolve(createdTask);
              })
              .catch(() => {
                resolve(null);
              });
          }, index * 100);
        });
      })
    )
      .then((details) => {
        dispatch.task.updatePermissionsList(details);
        dispatch.detail.getTodoDetail(null).then((detail) => {
          dispatch.viewSetting.refreshDataByDataChange({
            detail: detail,
            refreshList: true,
            refreshCount: true,
          });
        });
      })
      .finally(() => {
        setInseting(false);
        updateGuess({});
        dispatch.user.tracking({
          key: EnumTrackeKey.SubtaskSplitComfirm,
        });
      });
  };

  let content = null;
  if (guessLoading) {
    content = Array.from({ length: guessCount }).map((item, index) => {
      return (
        <div className={`${s.guess__item} flex-y-center`} key={index}>
          <div className="flex-y-center">
            <div className={`${s.skeleton__circle} mr-10`}></div>
            <div className={s.skeleton__rectangle}></div>
          </div>
        </div>
      );
    });
  } else if (guessData?.guessTaskTitles?.length) {
    content = (
      <Checkbox.Group value={value} onChange={(value) => setValue(value)}>
        {guessData?.guessTaskTitles?.map((item) => {
          return (
            <Checkbox key={item} value={item}>
              {({ checked }) => {
                return (
                  <div className={`${s.guess__item} guess__item flex-y-center`}>
                    <span
                      className={classNames('rock-checkbox', 'mr-10', {
                        'rock-checkbox-checked': checked,
                      })}
                    >
                      <span className="rock-checkbox-inner">{checked && <CheckHeavy />}</span>
                    </span>
                    <div className={s['guess__item--title']}>{item}</div>
                  </div>
                );
              }}
            </Checkbox>
          );
        })}
      </Checkbox.Group>
    );
  }

  if (error) {
    content = (
      <div className={`${s.guess__item} guess__item flex-y-center`}>
        <div className="flex-y-center">
          <Icon name={<ImStatusWarningSolid2 />} className="fs-16 mr-10" />
          <div className={s.guess__item__title}>{I18N.auto.splitSubtaskFail}</div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className={`${s.tasks} mt-12`}>{content}</div>
      <div
        className="guess_operation--btns mt-8 flex-y-center"
        style={{ justifyContent: 'space-between' }}
      >
        <div>
          {!!guessData?.guessTaskTitles?.length && (
            <Button type="link" icon={<ImRetry1 />} disabled={guessLoading} onClick={handleGuess}>
              {I18N.auto.regenerate}
            </Button>
          )}
        </div>
        <Space size={12}>
          <Button onClick={handleCancel} size="small" type="checked-neutral">
            {I18N.auto.cancel}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!error && (!value.length || guessLoading)}
            size="small"
            className="origin__primary__btn"
            type="primary"
          >
            {error ? I18N.auto.regenerate : I18N.auto.addTo}
          </Button>
        </Space>
      </div>
    </div>
  );
});

export default SubTasks;
