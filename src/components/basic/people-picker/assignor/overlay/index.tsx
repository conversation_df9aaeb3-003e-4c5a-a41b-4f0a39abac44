import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { apiTodoParticipantCandidateSearchGet } from '@/api';
import { CommonSearch, Divider } from '@/components/basic';
import { RootState } from '@/models/store';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import AssignorPanel from '../../components/assignor-panel';
import SearchList from '../../components/search-list';
import s from './index.less';

export const enum PanelType {
  init = 'init',
  search = 'searching',
}

export type Props = {
  className?: string;
  value?: UserInfo;
  onChange?: (user: UserInfo) => void;
  hasArrow?: boolean;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  placeholder?: ReactNode;
  executorList?: UserInfo[]; //执行人列表
  fromCreate?: boolean;
  assignerUid?: string;
  initPanelType: PanelType;
};

const Overlay: React.FC<PropsWithChildren<Props>> = (props) => {
  const { recentContactsList, currentUser } = useSelector((state: RootState) => ({
    recentContactsList: state.user.recentContactsList,
    currentUser: state.user.userInfo,
  }));
  const { value, onChange, assignerUid, initPanelType, visible } = props;
  const [panelType, setPanelType] = useState<PanelType>(initPanelType);
  const [showRecentContacts, setShowRecentContacts] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchList, setSearchList] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const commonSearchRef = useRef<any>();
  const searchPeople = (v: string) => {
    setLoading(true);
    apiTodoParticipantCandidateSearchGet({ keyword: v })
      .then((res) => {
        const tempRes = res.filter((item) => item.uid !== assignerUid);
        setSearchList(tempRes);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const onChangeUser = async (userList: UserInfo[]) => {
    const user = userList[0];
    if (user) {
      //TODO 转让的人无执行人身份
      onChange?.(user);
    }
  };
  const onTransfer = () => {
    setPanelType(PanelType.search);
    setTimeout(() => {
      commonSearchRef?.current?.focus?.();
    }, 100);
  };
  const getRecentContactsList = () => {
    setLoading(false);
    setShowRecentContacts(true);
    setSearchList(recentContactsList || []);
  };
  useEffect(() => {
    if (visible && recentContactsList?.length) {
      getRecentContactsList();
    }
  }, [visible]);

  return (
    <div
      className={classNames('com-dropdown-select', s.panel, {
        [s.searchingPanel]: panelType === PanelType.search,
      })}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {panelType === PanelType.search ? (
        <>
          <CommonSearch
            autoFocus
            ref={commonSearchRef}
            className={classNames(s.input)}
            placeholder={I18N.auto.transferAssignor}
            onSearch={(v) => {
              if (!v) {
                getRecentContactsList();
                return;
              } else {
                setShowRecentContacts(false);
                searchPeople(v);
              }
            }}
            value={searchValue}
            onChange={(v) => {
              setLoading(true);
              setSearchValue(v);
            }}
            fill={false}
            border={false}
            round={false}
            debounceTime={350}
          ></CommonSearch>
          <Divider type="horizontal" className={s.verticalLine}></Divider>
          {searchValue || showRecentContacts ? (
            <SearchList
              loading={loading}
              list={searchList}
              onChange={onChangeUser}
              className={s.list}
              multiple={false}
              currentUser={currentUser}
            ></SearchList>
          ) : null}
        </>
      ) : null}
      {panelType === PanelType.init ? (
        <AssignorPanel
          avatarUrl={value?.avatarUrl}
          name={value?.name}
          onTransfer={onTransfer}
        ></AssignorPanel>
      ) : null}
    </div>
  );
};

export default Overlay;
