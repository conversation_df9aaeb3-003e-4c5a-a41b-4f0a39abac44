import { ScaleType } from '@/components/gantt/utils';
import { CalendarModeType } from '@/utils/const';

const popup = document.createElement('div');

export function showTooltip(content, x, y) {
  popup.innerHTML = '';
  popup.id = 'popup';
  popup.style.left = x + 'px';
  popup.style.top = y + 'px';
  popup.style.transform = 'translateX(-50%)';
  popup.classList.add('rock-tooltip', 'rock-tooltip-dark', 'rock-tooltip-placement-top');
  const tooltipContent = `
      <div class="rock-tooltip-arrow"></div>
      <div class="rock-tooltip-inner" role="tooltip">
        <div class="rock-tooltip-title">${content}</div>
      </div>
  `;

  popup.innerHTML = tooltipContent; // Set the tooltip content

  // Append the tooltip to the document body
  document.body.appendChild(popup);

  // Append the tooltip to the document body
  document.body.appendChild(popup);

  // Collision detection
  const popupRect = popup.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const arrow = popup.querySelector('.rock-tooltip-arrow') as HTMLElement;

  if (popupRect.left < 0) {
    popup.style.left = '0px';
    popup.style.transform = 'translateX(0)';
    if (arrow) {
      arrow.style.left = `${popupRect.width / 2 + popupRect.left}px`;
    }
  } else if (popupRect.right > viewportWidth) {
    popup.style.left = `${viewportWidth - popupRect.width}px`;
    popup.style.transform = 'translateX(0)';
  }

  if (popupRect.top < 0) {
    popup.style.top = '0px';
  } else if (popupRect.bottom > viewportHeight) {
    popup.style.top = `${viewportHeight - popupRect.height}px`;
  }
}

export function hideTooltip() {
  if (document.body.contains(popup)) {
    document.body.removeChild(popup);
  }
}

export function getTreeIndent(col: number, row: number, instance) {
  const showIndex = instance.getRecordIndexByCell(col, row);
  let indent = 0;
  if (Array.isArray(showIndex)) {
    indent = (showIndex.length - 1) * 20;
  }
  return indent;
}

export const planView2ScaleType = (
  planView: CalendarModeType | `${CalendarModeType}` | undefined
) => {
  if (planView === CalendarModeType.year) {
    return ScaleType.year;
  }

  return ScaleType.month;
};

export const scaleType2PlanView = (scaleType: ScaleType) => {
  if (scaleType === ScaleType.year) {
    return CalendarModeType.year;
  }

  return CalendarModeType.month;
};

/**
 * 依照 100px - 165px为基准, 计算0 - 30对应比例的数值
 * popo 中最小值为86px, 最大值为165px
 * @param x 字体大小 html 中的 px 值
 * @returns 
 */
export const mapValue = (x: number) => {
  return 6 / 13 * (x - 100)
}
