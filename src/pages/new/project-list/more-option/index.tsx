import classNames from 'classnames';
import { PropsWithChildren, useState } from 'react';

import { apiProjectCheckRolePost } from '@/api';
import { Dropdown } from '@/components/basic';
import { Permission, ProjectInfo } from '@/types';
import { ProjectListPermissions } from '@/utils/permission';

import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
  onChangeVisible?: (v: boolean) => void;
}

const MoreOption: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled, className, projectInfo, onChangeVisible } = props;

  const [visible, setVisible] = useState<boolean>(false);
  const [permissionList, setPermissionList] = useState<Permission[]>([]);
  const getPermissionList = async () => {
    apiProjectCheckRolePost({
      roles: ProjectListPermissions,
      projectId: projectInfo?.projectId,
    }).then((_permissionList) => {
      setPermissionList(_permissionList as Permission[]);
    });
  };

  return (
    <div className={classNames(s.iconSelect, className)}>
      <Dropdown
        className={s.dropdown}
        title={children}
        trigger="click"
        arrow={false}
        defaultOpen={visible}
        disabled={disabled}
        open={visible}
        onOpenChange={async (v) => {
          if (v && projectInfo?.projectId && !projectInfo.isGroup) {
            await getPermissionList();
          }
          setVisible(v);
          onChangeVisible?.(v);
        }}
        destroyPopupOnHide={false}
        // minOverlayWidthMatchTrigger={false}
        overlay={<Overlay projectInfo={projectInfo} permissions={permissionList}></Overlay>}
      />
    </div>
  );
};

export default MoreOption;
