import classNames from 'classnames';
import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { Dispatch } from '@/models/store';
import { DetailTodoInfo, TaskPermission } from '@/types';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import { Calendar, Close, Remove, Share, TransformSubtask } from '../operation';
import Follow from '../operation/follow';
import TaskStatus from '../operation/task-status';
import TransferCreator from '../operation/transfer-creator';
import s from './index.less';
import TransferIndependent from '../operation/TransferIndependent';
import useGetPermissions from '@/hooks/useGetPermissions';
import * as _ from 'lodash';
export interface Props {
  containerElement: HTMLElement;
  className?: string;
  onClose: () => void;
  onFollowChange?: (isFollower: boolean) => void;
  onRemove?: () => void;
  taskInfo?: DetailTodoInfo;
  permissions?: TaskPermission[];
}

const DetailHeaderWeb: React.FC<Props> = (props) => {
  const { onClose, onRemove, onFollowChange, containerElement, taskInfo, permissions } = props;
  const { taskId, isFollower, calendarSyncStatus, assigneeUids, assigner } = taskInfo || {};
  const { assignerUid } = assigner || {};
  const dispatch = useDispatch<Dispatch>();

  const memoPermissions = useMemo(() => {
    const [
      CAN_COMPLETE,
      CAN_EDIT,
      CAN_DELETE,
      CAN_TRANSFER,
      CAN_SHARE,
      CAN_SYNC_TO_CALENDAR,
      CAN_NOT_OBSERVER,
      CAN_CANCEL_OBSERVER,
    ] = validatesPermission({
      permissions: permissions,
      key: [
        TaskPermissionEnum.CAN_COMPLETE,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_DELETE,
        TaskPermissionEnum.CAN_TRANSFER,
        TaskPermissionEnum.CAN_SHARE,
        TaskPermissionEnum.CAN_SYNC_TO_CALENDAR,
        TaskPermissionEnum.CAN_NOT_OBSERVER,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[];
    return {
      CAN_EDIT,
      CAN_COMPLETE,
      CAN_DELETE,
      CAN_SHARE,
      CAN_TRANSFER,
      CAN_SYNC_TO_CALENDAR,
      CAN_NOT_OBSERVER,
      CAN_CANCEL_OBSERVER,
    };
  }, [permissions]);

  const renderTransfer = () => {
    if (memoPermissions.CAN_TRANSFER) {
      if (taskInfo.parentId) {
        return <TransferIndependent taskInfo={taskInfo}></TransferIndependent>;
      }
    }
    if (memoPermissions.CAN_TRANSFER) {
      return (
        <TransferCreator
          taskId={taskId!}
          assignerUid={assignerUid}
          iconBtnClass={s.icon}
          assigneeUids={assigneeUids}
          containerElement={containerElement}
          onTransferAndExit={() => {
            dispatch.detail.closeDetail({});
          }}
        ></TransferCreator>
      );
    }
    return null;
  };

  return (
    <div className={s.head}>
      <TaskStatus taskInfo={taskInfo} disabled={!memoPermissions.CAN_COMPLETE} />
      <div className="flex flex-align-center">
        {memoPermissions.CAN_SHARE ? <Share taskId={taskId!} iconBtnClass={s.icon}></Share> : null}
        {memoPermissions.CAN_SYNC_TO_CALENDAR ? (
          <Calendar
            taskId={taskId!}
            iconBtnClass={s.icon}
            calendarSyncStatus={calendarSyncStatus}
          ></Calendar>
        ) : null}
        {renderTransfer()}
        {memoPermissions.CAN_EDIT && !taskInfo?.parentId && _.isEmpty(taskInfo?.subtask) ? (
          <TransformSubtask />
        ) : null}
        {memoPermissions.CAN_DELETE ? (
          <Remove iconBtnClass={classNames(s.icon)} onRemove={onRemove}></Remove>
        ) : null}
        {!memoPermissions.CAN_NOT_OBSERVER ? (
          <Follow
            isFollower={isFollower}
            onFollow={(v) => {
              if (!v) {
                if (memoPermissions.CAN_CANCEL_OBSERVER) {
                  onFollowChange?.(v);
                }
              } else {
                onFollowChange?.(v);
              }
            }}
            containerElement={containerElement}
          />
        ) : null}
        <Close iconBtnClass={classNames(s.icon)} onClose={onClose}></Close>
      </div>
    </div>
  );
};

export default DetailHeaderWeb;
