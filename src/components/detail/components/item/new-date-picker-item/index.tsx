import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import { taskUpdateAlarm, taskUpdateDeadline } from '@/api-common';
import { IconBtn, POPOConvergenceDatePicker } from '@/components/basic';
import { PopoConvergenceDatePickerContext } from '@/components/basic/popo-convergence-date-picker/context';
import { TimeInputType } from '@/components/basic/popo-convergence-date-picker/convergence-date-picker';
import { Dispatch, RootState } from '@/models/store';
import { TaskTime, TodoInfo } from '@/types';
import { isUpdateTime } from '@/utils';
import { EnumTimePickerType, PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { PickerRenderRangeTime } from './render-range-time';

export type Props = {
  className?: string;
  value: TaskTime;
  disabled?: boolean;
  taskId?: number;
  loopInducedDisabled?: boolean;
  isExpired?: boolean;
  isToday?: boolean;
  taskInfo?: TodoInfo;
};

const DataPickItem: React.FC<Props> = (props) => {
  const { value, disabled, taskId, loopInducedDisabled, isExpired, isToday, taskInfo } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { currentViewTab } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onChange = async (v: TaskTime) => {
    const { startTime, deadline, timeFormat, rrule, alarm } = v;
    const params = {
      taskId: taskId,
      startTime: startTime || 0,
      deadline: deadline || 0,
      deadlineFormat: timeFormat,
      rrule: rrule,
    };
    const isUpdate = isUpdateTime(v, value!);
    if (isUpdate) {
      await taskUpdateDeadline(params);
      await taskUpdateAlarm({
        taskId: taskId,
        alarm: {
          alarmTimestamp: alarm?.time,
          alarmRrule: alarm?.rrule,
          selectedOption: alarm?.selectedOption,
        },
      });
      dispatch.viewSetting.updateItemByDetail({ taskId });
    }
  };

  useEffect(() => {
    // taskId变更时重置visible状态
    setVisible(false);
  }, [taskId]);

  return (
    <PopoConvergenceDatePickerContext.Provider
      value={{
        timePickerType: EnumTimePickerType.deadline,
      }}
    >
      <POPOConvergenceDatePicker
        disabled={disabled}
        visible={visible}
        onVisible={setVisible}
        value={value}
        loopInducedDisabled={loopInducedDisabled}
        onChange={onChange}
        hasQuick={false}
        timeInputType={TimeInputType.end}
        showCircle={!taskInfo?.parentId}
      >
        <div
          className="flex flex-align-center"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <PickerRenderRangeTime
            value={value}
            onChange={onChange}
            onClickOther={() => {
              setVisible(!visible);
            }}
            disabled={disabled}
            isExpired={isExpired}
            isToday={isToday}
          />
          {Boolean(!disabled && (value?.deadline || value?.alarm?.time)) && (
            <IconBtn
              className={classNames(s.clear, 'detail-com-delete', 'ml-4')}
              fontSize={16}
              title={I18N.auto.delete}
              iconName="icon-sys_close"
              onClick={() => {
                onChange({
                  deadline: 0,
                  startTime: 0,
                  timeFormat: PPTimeFormat.noDate,
                  rrule: '',
                  alarm: undefined,
                });
              }}
            ></IconBtn>
          )}
        </div>
      </POPOConvergenceDatePicker>
    </PopoConvergenceDatePickerContext.Provider>
  );
};

export default DataPickItem;
