.addFollowerWrapper {
  height: 28px;
  display: inline-flex;
  align-items: center;
  padding: 4px 12px 4px 8px;
  cursor: pointer;
  color: var(--TextSecondary);
  width: auto;
  line-height: 20px;
  i {
    margin-right: 2px;
    color: var(--IconPrimary);
  }
  &:hover {
    background-color: var(--aBlack6);
    border-radius: 4px;
  }
}

.watcherPickerWrapper {
  height: 28px !important;
  margin-top: 14px;
  width: auto !important;
}

.hasValueWatcherPickerWrapper {
  :global {
    .picker-label-content {
      &:hover {
        padding-right: 12px;
        background-color: var(--aBlack6);
        border-radius: 56px;
      }
    }
  }
}

.showFollowCount {
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 28px;
}
