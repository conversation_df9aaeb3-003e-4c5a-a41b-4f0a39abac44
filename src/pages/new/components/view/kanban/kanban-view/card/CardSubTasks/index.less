.card__subtask {
  .right {
    font-size: 12px;
    line-height: 20px;
    color: var(--TextSecondary-ongrey);

    &__icon {
      color: var(--IconTertiary);
      margin-left: 2px;
      transition: transform 0.2s ease-in-out;
      &.open {
        transform: rotate(90deg);
      }
    }
  }

  .sub__item {
    position: relative;
    padding: 4px 6px;
    border-radius: 6px;
    height: 32px;
    transition: background-color 0.2s ease-in-out;
    cursor: pointer;

    &--title {
      color: var(--TextPrimary-strong);
      font-size: 13px;
      line-height: 20px;
      padding-left: 24px;

      &.finished {
        color: var(--TextSecondary);
      }
    }

    &:hover {
      background-color: var(--aBlack4);
    }

    &:not(:last-child):after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: calc(100% - 12px);
      height: 1px;
      background-color: var(--aBlack8);
    }
  }

  :global {
    .render__item {
      border-radius: 4px;
      &:hover {
        background-color: var(--aBlack4);
      }
    }
  }
}
