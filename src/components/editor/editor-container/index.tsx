import React, {
  useState,
  useEffect,
  useRef,
  ReactNode,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { BedrockEditor } from '..';
import s from './index.less';
import classNames from 'classnames';
import { DetailTodoInfo } from '@/types';
import { TaskPermissionEnum } from '@/utils/permission';
import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';
import { ImEdit2 } from '@babylon/popo-icons';
import { setRemarkDefaultHeight } from '../bedrock-editor';

interface IEditorContainerProps {
  taskId: number;
  disabled?: boolean; // 控制编辑器的启用或禁用状态
  value?: string; // 传递给编辑器的值
  placeholder?: ReactNode; // 编辑器的占位文本
  type: 'comment' | 'remark';
  taskInfo?: DetailTodoInfo;
  reply?: React.ReactNode;
  permissions?: { name: TaskPermissionEnum; value: boolean }[];
  onChange?: (newValue: string) => void; // 处理编辑器值的变化
  onSubmit?: (value: string, inviteJoinTaskWhenAt: boolean, atFollowers) => Promise<void>;
}
//  该组件来判断是否需要渲染编辑器或者文本
const EditorContainer: React.FC<IEditorContainerProps> = forwardRef(
  (
    { taskId, value = '', disabled, type, reply, taskInfo, permissions, placeholder, onSubmit },
    ref
  ) => {
    const [isEditing, setIsEditing] = useState(false);
    const bedrockEditorRef = useRef<any>(null); // 用于访问编辑器实例的引用

    useEffect(() => {
      if (reply) {
        setIsEditing(true);
      }
      bedrockEditorRef?.current?.onFocus();
    }, [reply]);

    const onOpenBtnClick = () => {
      setIsEditing(true);
    };

    useImperativeHandle(
      ref,
      () => ({
        setEditing: (isEditing: boolean) => {
          setIsEditing(isEditing);
        },
      }),
      [isEditing]
    );

    return (
      <div className={s.editor}>
        {isEditing || value.length > 0 ? (
          <BedrockEditor
            value={value}
            taskId={taskId}
            type={type}
            disabled={disabled}
            reply={reply}
            isEditing={isEditing}
            onIsEditingChange={setIsEditing}
            onSubmit={onSubmit}
            taskInfo={taskInfo}
            permissions={permissions}
          />
        ) : (
          <div
            className={classNames(s.placeholder, 'com-placeholder', {
              [s.comment]: type === 'comment',
            })}
            onClick={() => {
              if (disabled) return;
              setIsEditing(true);
            }}
          >
            {placeholder}

            {type === 'comment' && (
              <div className={s.submitWrapper}>
                <IconBtn
                  iconName="icon-input_solid"
                  iconClassName={classNames(s.commentSubmit)}
                  onClick={onOpenBtnClick}
                />
              </div>
            )}
          </div>
        )}
        {type === 'remark' && !isEditing && value.length > 0 && !disabled && (
          <IconBtn
            icon={<ImEdit2 />}
            iconClassName={classNames(s.editIcon)}
            onClick={() => {
              setRemarkDefaultHeight('remark');
              setIsEditing(true);
            }}
          >
            <span className={classNames(s.innerText)}>{I18N.auto.edit}</span>
          </IconBtn>
        )}
      </div>
    );
  }
);

export default EditorContainer;
