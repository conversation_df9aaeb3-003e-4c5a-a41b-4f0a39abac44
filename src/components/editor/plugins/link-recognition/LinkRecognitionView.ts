import { NodeSelection, TextSelection } from 'prosemirror-state';
import applyMark from './applyMark';
// import findNodesWithSameMark from "../../utils/findNodesWithSameMark"
// import lookUpElement from "../../utils/lookUpElement"
// import LinkTooltip from "./LinkTooltip"
// import LinkModal from "../../Toolbar/LinkModal"
// import createPopup from "../../utils/createPopup"
// import { MARK_LINK } from "../../Marks/MarkNames"
// import './index.less';
import { Fragment, RockEditorView } from '@bedrock/editor';
// import findNodesWithSameMark from './findNodesWithSameMark'; // 如果不再需要，可以移除

const MARK_LINK = 'link'; // 使用ProseMirror基础的link mark

class LinkRecognitionView {
  _anchorEl: Element | null = null;
  _popup: any = null;
  _editor: any = null;
  _view: RockEditorView | null = null;

  constructor(editorView: RockEditorView) {
    this._view = editorView;
    this.init(editorView);
    this.update(editorView);
  }
  // 在处理粘贴时调用
  handlePaste(event: ClipboardEvent): void {
    const clipboardData = event.clipboardData;
    if (!clipboardData) return;

    const pastedText = clipboardData.getData('text/plain');
    const LinkRegex = /((?:https?|ftp):\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]+)/g;

    const links = [...pastedText.matchAll(LinkRegex)];

    if (links.length > 0) {
      console.log(`检测到 ${links.length} 个链接，准备替换为 linkRecognitionNode`);

      // 阻止默认粘贴行为
      event.preventDefault();

      // 替换链接为自定义节点
      this.replacePastedTextWithLinksAdvanced(pastedText, links);

      // 可选：异步获取真实标题
      // const linkNodes = links.map((match, index) => ({
      //   pos: /* 计算位置 */,
      //   url: match[0]
      // }));
      // this.fetchAndUpdateLinkTitles(linkNodes);
    }
  }

  replacePastedTextWithLinksAdvanced(text: string, links: RegExpMatchArray[]): void {
    if (!this._view) return;

    const { state, dispatch } = this._view;
    const { schema, selection } = state;

    const linkRecognitionNode = schema.nodes.linkRecognition;

    if (!linkRecognitionNode) {
      console.warn('linkRecognitionNode not found in schema');
      return;
    }

    let { tr } = state;
    const { from, to } = selection;

    // 删除原始选区
    tr = tr.delete(from, to);

    // 插入普通文本
    tr = tr.insertText(text, from);

    // 按照链接在文本中的位置排序（从后往前处理避免位置偏移）
    const sortedLinks = [...links].sort((a, b) => (b.index || 0) - (a.index || 0));

    // 从后向前处理链接，避免位置变化
    for (let i = sortedLinks.length - 1; i >= 0; i--) {
      const match = sortedLinks[i];
      const linkUrl = match[0];
      const matchIndex = match.index!;

      try {
        // 生成标题
        const linkTitle = 'ttt';

        // 创建 linkRecognitionNode
        const linkNode = linkRecognitionNode.create({
          link: linkUrl,
          title: linkTitle,
        });

        // 计算在文档中的实际位置
        const linkEndPos = to;
        const linkStartPos = to - linkUrl.length;

        // 选择链接文本范围
        // FIXME: linkEndPos + linkUrl.length 这个计算方式存疑
        tr = tr.setSelection(
          TextSelection.create(tr.doc, linkStartPos, linkEndPos + linkUrl.length)
        );

        // 用 linkRecognitionNode 替换选中的链接文本
        tr = tr.replaceSelectionWith(linkNode);

        console.log(
          `替换链接: ${linkUrl} -> ${linkTitle} at position ${linkStartPos}-${linkEndPos}`
        );
      } catch (error) {
        console.error(`处理链接失败: ${linkUrl}`, error);
      }
    }

    // 应用所有变更
    if (tr.docChanged) {
      // 设置选区到内容末尾
      const finalPos = Math.min(tr.doc.content.size, from + text.length);
      tr = tr.setSelection(TextSelection.create(tr.doc, finalPos));

      dispatch(tr.scrollIntoView());
    }
  }
  /**
   * 异步获取链接标题并更新节点
   */
  async fetchAndUpdateLinkTitles(linkNodes: Array<{ pos: number; url: string }>): Promise<void> {
    if (!this._view) return;

    for (const { pos, url } of linkNodes) {
      try {
        // 异步获取真实标题
        const realTitle = await this.fetchLinkTitle(url);

        if (realTitle && this._view) {
          const { state, dispatch } = this._view;
          const linkRecognitionNode = state.schema.nodes.linkRecognition;

          if (linkRecognitionNode) {
            const tr = state.tr;
            const node = state.doc.nodeAt(pos);

            if (node && node.type === linkRecognitionNode) {
              // 更新节点属性
              const updatedNode = linkRecognitionNode.create({
                ...node.attrs,
                title: realTitle,
              });

              tr.setNodeMarkup(pos, undefined, updatedNode.attrs);

              if (tr.docChanged) {
                dispatch(tr);
              }
            }
          }
        }
      } catch (error) {
        console.warn(`获取链接标题失败: ${url}`, error);
      }
    }
  }

  /**
   * 获取链接的真实标题
   */
  async fetchLinkTitle(url: string): Promise<string | null> {
    try {
      // 这里可以调用后端API或使用其他方式获取标题
      const response = await fetch(`/api/link-title?url=${encodeURIComponent(url)}`);
      const data = await response.json();
      return data.title || null;
    } catch (error) {
      console.error('获取链接标题失败:', error);
      return null;
    }
  }
  init(editorView: RockEditorView): void {
    this._view = editorView;
    this.handlePaste = this.handlePaste.bind(this);
    editorView.dom.addEventListener('paste', this.handlePaste);
  }

  update(view: RockEditorView): void {
    // ... (update 方法的其余部分保持不变，但要注意 _view 的使用)
    // ... 您可能不需要在此方法中做特别的 link recognition 处理了，除非有其他需求
    if (!this._view) this._view = view;
    if ((view as any).readOnly) {
      this.destroy();
      return;
    }
    // ...
  }

  destroy() {
    if (this._view && this.handlePaste) {
      this._view.dom.removeEventListener('paste', this.handlePaste);
    }
    this._popup && this._popup.destroy();
    this._popup = null;
    this._anchorEl = null;
    // _editor 的销毁逻辑取决于其创建和用途，如果它与此插件相关也应在此销毁
  }

  _onClose = (): void => {
    this._anchorEl = null;
    this._editor = null;
    this._popup = null;
  };

  _onEdit = (view: RockEditorView): void => {
    if (this._editor) {
      return;
    }

    const { state } = view;
    const { schema, doc, selection } = state;
    const { from, to } = selection;
    const markType = schema.marks[MARK_LINK];
    const result = findNodesWithSameMark(doc, from, to, markType);
    if (!result) {
      return;
    }
    let { tr } = state;
    const linkSelection = TextSelection.create(tr.doc, result.from.pos, result.to.pos + 1);

    tr.setSelection(linkSelection);
    // tr = showSelectionPlaceholder(state, tr);
    view.dispatch(tr);
    const href = result ? result.mark?.attrs.href ?? null : null;
    if (this._editor) {
      // 先销毁，防止重复创建组件
      this._editor.destroy();
    }
    // this._editor = createPopup(LinkModal, {
    // 	editorView: view,
    // 	validator: (view as any).config?.validateLink,
    // 	needContent: true,
    // 	formValues: {
    // 		content: doc.textBetween(linkSelection.from, linkSelection.to),
    // 		href
    // 	},
    // 	onOk: (values: any) => {
    // 		this._editor.destroy()
    // 		this._editor = null
    // 		this.destroy()
    // 		this._onEditEnd(view, selection as TextSelection, values)
    // 	},
    // 	onCancel: () => {
    // 		this._editor.destroy()
    // 		this._editor = null
    // 		this.destroy()
    // 	}
    // })
  };

  _onRemove = (view: RockEditorView): void => {
    // @ts-ignore
    this._onEditEnd(view, view.state.selection, { href: null });
  };

  _onEditEnd = (
    view: RockEditorView,
    initialSelection: TextSelection,
    values: { content: string; href: string } | null
  ): void => {
    const { state, dispatch } = view;
    const { content, href } = values || {};
    const { tr, doc } = state;

    if (href !== undefined) {
      const { schema } = state;
      const markType = schema.marks[MARK_LINK];
      if (markType) {
        let result = findNodesWithSameMark(
          tr.doc,
          initialSelection.from,
          initialSelection.to,
          markType
        );
        if (result) {
          const selectedText = doc.textBetween(result.from.pos, result.to.pos + 1);
          let to = result.to.pos + 1;
          if (content && selectedText !== content) {
            tr.replaceRangeWith(result.from.pos, to, schema.text(content));
            to = result.from.pos + content.length;
          }

          const linkSelection = TextSelection.create(tr.doc, result.from.pos, to);
          tr.setSelection(linkSelection);
          const attrs = href ? { href } : null;
          applyMark(tr, schema, markType, attrs);
        }
      }
    }
    // todo: create一个新的selection
    // tr = tr.setSelection(initialSelection);
    dispatch(tr);
    view.focus();
  };
}

export default LinkRecognitionView;
