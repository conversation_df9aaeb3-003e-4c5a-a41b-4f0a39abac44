import { POPOBridgeEmitter } from '@popo-bridge/web'
import { createModel } from '@rematch/core'
import dayjs from 'dayjs'
import _ from 'lodash'

import { ApiTaskListViewGroupedGroupsPostRequest } from '@/api'
import { apiCreateTask, apiGetTaskListViewGroups, getPermissionsByTaskList } from '@/api-common'
import { validateTask } from '@/pages/new/components/view/list/utils'
import { Pagination, PermissionsTasksMap, TaskGroupItem, TaskInfo, TaskPermission, TodoInfo } from '@/types'
import { EnumEmitter, TaskNavigatorType, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import {
  EnumDeadlineGroup,
  EnumFieldNONE,
  EnumGroupBy,
  FinishedStatus,
  getFinishedGroupMap,
  getPriorityGroupMap,
  getTimeGroupMap,
  Priority,
} from '@/utils/fields'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'

import type { RootModel } from '.'
import {
  getAddItem,
  getExpandedIndexMap,
  getUnfoldedArray,
  getViewIsGroup,
  handleSeverData,
  isProjectId,
  isTaskMenuId,
  setNewAddItem,
  tfGroups,
  tfTasks,
} from './utils'
import { fetchTaskList, updateTaskInfo } from './utility'
import { getTreeList } from './utility/getTodoList'
import { normalizeTimestamp } from '@/hooks/useGetGroupedList'

const DefaultPagination = {
  size: 30,
  more: false,
  order: undefined,
  orderBy: undefined,
}

type PreAddParamsType = {
  taskId?: string | number
  groupId?: string
  isHeader: boolean
  defaultParams: any
}

type CacheCollapse = {
  [k: string]: TaskGroupItem[]
}

interface TaskState {
  filters?: string[]
  searchValue?: ''
  pagination?: Pagination
  newCreatetaskId?: undefined | number //刚改新建待办id
  tableAddTask?: boolean
  showDataList?: TodoInfo[]
  dataList?: TodoInfo[]
  treeList?: TaskInfo[]
  groupList?: TaskGroupItem[]
  showGroupList?: TaskGroupItem[]
  refreshing: boolean
  taskId?: number
  preAddParams?: PreAddParamsType
  cacheCollapse?: CacheCollapse
  hasData?: boolean
  permissionsMap: PermissionsTasksMap
}

const initData = {
  filters: [] as string[],
  searchValue: '', //搜索组件在value和onChange时用的值,保证输入正常显示
  pagination: Object.assign({}, DefaultPagination) as Pagination,
  newCreatetaskId: undefined as undefined | number, //刚改新建待办id
  tableAddTask: false as boolean,
  showDataList: [] as TodoInfo[],
  dataList: [] as TodoInfo[], // 有新建任务一行, 数组长度大于1也不一定是待办有数据
  treeList: [] as TaskInfo[],
  refreshing: true,
  hasData: false, //判断是否有数据
  groupList: [] as TaskGroupItem[],
  showGroupList: [] as TaskGroupItem[],
  preAddParams: {} as PreAddParamsType,
  cacheCollapse: {} as CacheCollapse,
  permissionsMap: {} as Record<string, { permissions: TaskPermission[] }>,
}

export const gantt = createModel<RootModel>()({
  state: {
    ...initData,
  },
  reducers: {
    initData(state) {
      return {
        ...initData,
        cacheCollapse: state.cacheCollapse,
      }
    },
    setData(state, payload: Partial<TaskState>) {
      return { ...state, ...payload }
    },
    setShowTitle(state, payload: string) {
      return { ...state, showTitle: payload }
    },
    setTableList(state, payload: TaskInfo[]) {
      return { ...state, tableList: payload }
    },

    /**
     * 切换页面
     * @param state
     * @param pageNo
     * @returns
     */
    setPage(state, payload: Pagination) {
      const { pagination } = state
      return {
        ...state,
        pagination: {
          ...pagination,
          ...payload,
        },
      }
    },
    setLoading(state, payload: boolean) {
      return {
        ...state,
        loading: payload,
      }
    },
    setFirstLoading(state, payload: boolean) {
      return {
        ...state,
        firstLoading: payload,
      }
    },

    setNewCreatetaskId(state, payload: number | undefined) {
      return {
        ...state,
        newCreatetaskId: payload,
      }
    },
    setSearchValue(state, payload: string) {
      return {
        ...state,
        searchValue: payload,
      }
    },
    setEditing(state, payload: boolean) {
      return {
        ...state,
        editing: payload,
      }
    },
  },
  effects: dispatch => ({
    async getGroupList(opt: any, state) {
      const { cacheCollapse } = state.gantt
      const { currentViewTab, customFields, navigatorId } = state.viewSetting
      const { groupBy } = currentViewTab
      const _param: ApiTaskListViewGroupedGroupsPostRequest = dispatch.viewSetting.getComParams({})
      const params = {
        ..._param,
        //@ts-ignore
        timezone: dayjs.tz?.guess?.(),
      }
      // FIXME: queryGroupBy mock: init queryGroupBy by queryGroupBys[0]
      params.queryGroupBy = params.queryGroupBys?.[0]
      if (isProjectId(params.navigatorId) && params.queryGroupBys?.length) {
        //服务端要求在自定义单选字段分组时传递customFieldOptions
        const { customFields } = state.viewSetting
        const field = customFields.find(item => item.fieldId === params.queryGroupBy?.customFieldId)
        if (params.queryGroupBy && field?.options?.length) {
          params.queryGroupBy.customFieldOptions = field.options.map(item => item.value)
        }
      }
      let { groups = [] } = await apiGetTaskListViewGroups(params, {
        errorSilent: true,
      })
      // 增加自定义字段数据
      let cacheGroupUnfoldMap = {} as Record<string, boolean>
      if (
        currentViewTab &&
        cacheCollapse[currentViewTab.viewId!]?.length &&
        cacheCollapse[currentViewTab.viewId!][0].groupBy === groupBy
      ) {
        cacheGroupUnfoldMap = cacheCollapse[currentViewTab.viewId!].reduce((pre, cur) => {
          //
          pre[cur.groupId!] = !!cur.unfold
          return pre
        }, {} as Record<string, boolean>)
      }
      const list = tfGroups({
        fieldName: params.queryGroupBy?.fieldName,
        customFieldId: params.queryGroupBy?.customFieldId,
        customFields: customFields,
        projectId: navigatorId,
        groups: groups,
      }).map(item => {
        return {
          ...item,
          unfold: _.isUndefined(cacheGroupUnfoldMap[item.groupId!]) ? true : cacheGroupUnfoldMap[item.groupId!],
        }
      })
      // 默认设置每个组都是默认打开的
      dispatch.gantt.setData({
        groupList: list,
      })
    },
    /**
     * @param state
     * @param payload
     * @returns
     */
    async getTodoList(options: { page?: number; scrollId?: string; size?: number; onlyGetData?: boolean }, state) {
      const expandedKeysMap = getExpandedIndexMap()

      console.log('expandedKeysMap', expandedKeysMap)

      fetchTaskList({
        options,
        dispatch,
        modelName: 'gantt',
        handleItem: item => {
          normalizeTimestamp(item)
          if (expandedKeysMap[item.groupId || item.taskId || '']) {
            item.hierarchyState = 'expand'
          } else {
            item.hierarchyState = 'collapse'
          }
          return item
        },
      })
    },

    updateTreeList({ srcList }: { srcList?: TaskInfo[] }, state) {
      getTreeList({ srcList, dispatch, modelName: 'gantt' })
    },

    async updatePermissionsList(list: TaskInfo[], state) {},
    /**
     * 计算分组数据
     * @param opt
     * @param state
     */
    calculatedGroupTodoList(opt: any, state) {
      //每一个分组下增加一个新增按钮
      const { permissions } = state.viewSetting
      const {
        groupList,
        dataList,
        pagination: { more },
      } = state.gantt
      const { userInfo } = state.user
      // 按照分组调整对应的数据
      let _list = tfTasks({ dataList: dataList, groups: groupList })
      // 深拷贝
      const _groupList = _.cloneDeep(groupList)
      // 排除掉已折叠数据
      _list = getUnfoldedArray(_list, _groupList)
      if (
        validatesPermission({
          permissions: permissions,
          key: ProjectPermissionEnum.CAN_CREATE_TASK,
        })
      ) {
        //每个组末尾追加新增按钮 数量+1  讲数组切割成 [3,8,16,28] 数字代表下标开始位
        let btnCount = 1
        const unfoldGroups: { groupId: string; count: number }[] = []
        let totalCount = 0
        _groupList.forEach(item => {
          if (item.unfold) {
            totalCount += item.count || 0
            unfoldGroups.push({
              count: totalCount + btnCount,
              groupId: item.groupId!,
            })
            btnCount += 1
          }
        }, [] as number[])
        //分组的尾部增加一个新增按钮
        unfoldGroups.forEach((item, index) => {
          if (_list[item.count - 2] || item.count - 2 < 0) {
            const _item = getAddItem({
              _rowType: TaskTableRowType.addBtn,
              id: `${TaskTableRowTypeAdd}-${index}`,
              defaultParams: groupList[index].defaultParams,
              assigner: userInfo,
              groupId: item.groupId,
            })
            _list.splice(item.count - 1, 0, _item)
            // 这个要根据非折叠计算
            const _index = _groupList.findIndex(v => v.groupId === item.groupId)
            _groupList[_index].count += 1
          }
        })
      }
      // 需要排除只有一条新增任务的的情况
      if (!_list.filter(item => !item.id?.toString().includes(TaskTableRowTypeAdd)).length && more) {
        dispatch.gantt.loadMore({})
        return
      }
      dispatch.gantt.setData({
        showDataList: _list,
        showGroupList: _groupList,
      })
      if (_list.length) {
        //检查没有权限的数据
        dispatch.gantt.updatePermissionsList(_list)
      }
    },
    /**
     *
     * @param opt
     * @param state
     */
    calculatedTodoList(opt: { dataList: TodoInfo[] }, state) {
      let { dataList: baseDataList = [] } = opt
      //去除按钮以及编辑台之后的真实数据 多处理一次数据不耗费多少时间 保稳点
      baseDataList = baseDataList.filter(item => validateTask(item))
      //如果传了数据 就默认存起来
      if (baseDataList && baseDataList.length) {
        dispatch.gantt.setData({
          dataList: baseDataList,
        })
      }
      const {
        dataList,
        pagination: { more },
      } = state.gantt
      const { permissions } = state.viewSetting
      let _list = baseDataList.length ? baseDataList : dataList
      if (
        validatesPermission({
          permissions: permissions,
          key: ProjectPermissionEnum.CAN_CREATE_TASK,
        })
      ) {
        // TODO: 全量模式可以删除
        //默认增加一个底部按钮 按钮加载时机要处理下
        if (!more) {
          const addItem = getAddItem({
            _rowType: TaskTableRowType.addBtn,
            id: `${TaskTableRowTypeAdd}-1`,
          })
          _list = setNewAddItem({
            dataList: [..._list],
            isHeader: false,
            item: addItem,
          })
        }
      }
      dispatch.gantt.setData({
        showDataList: _list,
      })
      if (_list.length) {
        //检查没有权限的数据
        dispatch.gantt.updatePermissionsList(_list)
      }
    },
    //TODO table基于react-virtuoso 分组数据、新增数据、新增任务按钮、分组新增任务按钮都要额外去处理数据源,非常的不友好
    //如果有一个支持children的树结构的table 且支持自定义组的head和组的footer就可以去除复杂的数据处理逻辑 数据处理难度直线下降
    processData(opt: { dataList: TodoInfo[] }, state) {
      const { queryGroupBys } = dispatch.viewSetting.getComParams({})
      const { dataList } = opt
      dispatch.gantt.setData({
        dataList: dataList,
      })
      // if (queryGroupBys?.length) {
      //   //计算分组后的内容
      //   // dispatch.gantt.calculatedGroupTodoList({});
      // } else {
      // }
      dispatch.gantt.setData({
        groupList: [],
        showGroupList: [],
      })
      // dispatch.gantt.calculatedTodoList({})
    },
    updateTodoList(payload: TodoInfo, state) {
      const { taskId } = payload
      const { dataList } = state.gantt
      const list = dataList.map(item => {
        if (item.taskId === taskId) {
          return {
            ...item,
            ...payload,
          }
        } else {
          return item
        }
      })
      dispatch.gantt.processData({
        dataList: list,
      })
    },
    async updatePermissionsWithTask(payload: TodoInfo, state) {
      const { taskId } = payload
      const { userInfo } = state.user
      const { permissionsMap } = state.gantt
      const mapPermissions = (await getPermissionsByTaskList([payload], userInfo)) as PermissionsTasksMap
      payload.permissions = mapPermissions[String(taskId)].permissions
      dispatch.gantt.setData({
        permissionsMap: {
          ...permissionsMap,
          ...mapPermissions,
        },
      })
      dispatch.gantt.updateTodoList(payload)
    },
    /**
     * 增加编辑的空任务  分组比较复杂
     * @param opt
     * @param state
     */
    async tableAddTaskItem(opt: { taskId?: number; groupId?: string; isHeader: boolean }, state) {
      let { groupId } = opt
      const { dataList } = state.gantt
      // 先判端下数据是否已处在新建数据
      const hasaddIndex = dataList.findIndex((item: TodoInfo) => item._rowType === TaskTableRowType.add)
      if (hasaddIndex > -1) {
        dispatch.gantt.tableRemoveTaskItem({
          taskId: dataList[hasaddIndex].taskId,
          groupId,
        })
      }
      // 抽离方法可以获取最新的值
      dispatch.gantt.tableAdd(opt)
    },
    async tableAdd(opt: { taskId?: number; groupId?: string | number; isHeader: boolean }, state) {
      let defaultParams = {}
      let { groupId, groupId: copyGroupId, isHeader } = opt
      const { queryGroupBy, navigatorId } = dispatch.viewSetting.getComParams({})
      const { userInfo } = state.user
      const { dataList, groupList, showDataList } = state.gantt
      //分组新增
      if (queryGroupBy?.fieldName) {
        //如果groupId不存在 说明点击的是顶部操作栏的数据 这个时候要根据当前分组去增加数据
        if (!groupId) {
          if (queryGroupBy?.customFieldId) {
            groupId = EnumFieldNONE.NONE
            defaultParams = {
              fieldId: queryGroupBy.customFieldId,
              fieldVersion: queryGroupBy.customFieldVersion,
              value: undefined,
            }
          } else {
            //截止时间 今天
            if (queryGroupBy?.fieldName === EnumGroupBy.DEADLINE) {
              groupId = EnumDeadlineGroup.TODAY
              const timeParams = getTimeGroupMap(dayjs())[EnumDeadlineGroup.TODAY].defaultParams
              defaultParams = {
                deadline: timeParams.time,
                deadlineFormat: timeParams.format,
              }
            }
            //执行人 无
            if (queryGroupBy?.fieldName === EnumGroupBy.ASSIGNEE) {
              groupId = EnumFieldNONE.NONE
              defaultParams = {
                assignees: [],
              }
            }
            //优先级 无
            if (queryGroupBy?.fieldName === EnumGroupBy.PRIORITY) {
              groupId = Priority.Unset
              defaultParams = getPriorityGroupMap()[Priority.Unset].defaultParams
            }
            //已完成分组 未完成
            if (queryGroupBy?.fieldName === EnumGroupBy.FINISHED) {
              groupId = FinishedStatus.UnFinished
              defaultParams = getFinishedGroupMap()[FinishedStatus.UnFinished].defaultParams
            }
          }
        }
        // groupList 当前组count+1
        // 在列表中增加一条数据
        const _groupList = groupList.map(item => {
          if (item.groupId === String(groupId)) {
            return {
              ...item,
              count: (item.count || 0) + 1,
              unfold: true,
            }
          }
          return item
        })
        let totalCount = 0 // 截止当前组 总共的数据量
        let showCount = 0
        for (let index = 0; index < _groupList.length; index++) {
          const element = _groupList[index]
          totalCount += element.groupTaskCount!
          if (element.unfold) {
            showCount += (element.groupTaskCount || 0) + 1
          }
          if (element.groupId === String(groupId)) {
            if (!copyGroupId) {
              //groupId不存在 说明点的是顶部按钮
              totalCount -= element.groupTaskCount!
              if (element.unfold) {
                showCount -= (element.groupTaskCount || 0) + 1
              }
            }
            defaultParams = element.defaultParams
            break
          }
        }
        if (dataList.length < totalCount) {
          //需要加载更多数据
          let scrollId = dataList[dataList.length - 1].taskId
          if (`${scrollId}`.includes(TaskTableRowTypeAdd)) {
            scrollId = dataList[dataList.length - 2].taskId
          }
          const { list } = await dispatch.gantt.getTodoList({
            scrollId: scrollId,
            size: totalCount - showDataList.length + 30,
            onlyGetData: true,
          })
          // 更新数据
          dataList.push(...list)
        }

        const addOption = {
          _rowType: TaskTableRowType.add,
          id: `${TaskTableRowTypeAdd}-2`,
          assigner: userInfo,
          groupId: groupId,
          defaultParams: {
            ...defaultParams,
          },
        }
        if (navigatorId === TaskNavigatorType.assignToMe) {
          //分配给我新建 我是执行人
          addOption.defaultParams = {
            ...addOption.defaultParams,
            assignees: [userInfo],
            assigneeUids: [userInfo?.uid],
            assigneeCount: 1,
          }
        }
        const addItem = getAddItem(addOption)
        // 记忆当前的创建参数,连续创建需要用
        dispatch.gantt.setData({
          preAddParams: {
            ...opt,
            taskId: addItem.taskId,
            groupId: groupId,
          },
        })
        //@ts-ignore
        dataList.splice(totalCount, 0, addItem)
        dispatch.gantt.setData({
          groupList: _groupList,
        })
        dispatch.gantt.processData({
          dataList: [...dataList],
        })
        if (isHeader) {
          POPOBridgeEmitter.emit(EnumEmitter.VirtuoSoscrollToIndex, showCount + 1)
        }
      } else {
        const addOption = {
          _rowType: TaskTableRowType.add,
          id: `${TaskTableRowTypeAdd}-2`,
          assigner: userInfo,
          groupId: groupId,
          defaultParams: {
            ...defaultParams,
          },
        }
        if (navigatorId === TaskNavigatorType.assignToMe) {
          //分配给我新建 我是执行人
          addOption.defaultParams = {
            ...addOption.defaultParams,
            assignees: [userInfo],
            assigneeUids: [userInfo?.uid],
            assigneeCount: 1,
          }
        }
        const addItem = getAddItem(addOption)
        const list = setNewAddItem({
          dataList: dataList,
          isHeader: isHeader,
          //@ts-ignore
          item: addItem,
        })
        // 记忆当前的创建参数,连续创建需要用
        dispatch.gantt.setData({
          preAddParams: {
            ...opt,
            taskId: addItem.taskId,
            groupId: groupId,
          },
        })
        dispatch.gantt.processData({
          dataList: list,
        })
        if (isHeader) {
          POPOBridgeEmitter.emit(EnumEmitter.VirtuoSoscrollToIndex, 0)
        }
      }
    },
    /**
     * 列表移除任务
     * @param opt
     * @param state
     */
    tableRemoveTaskItem(opt: TodoInfo, state) {
      const { taskId, groupId } = opt
      const { dataList } = state.gantt
      const _dataList = dataList.filter(v => v.id !== taskId)
      // 新增的时候会修改分组真实数据 移除的时候也要判断
      if (groupId) {
        const { groupList } = state.gantt
        const _groupList = groupList.map(item => {
          return {
            ...item,
            count: item.groupTaskCount,
          }
        })
        dispatch.gantt.setData({
          groupList: _groupList,
        })
      }
      dispatch.gantt.updateTreeList({ srcList: _dataList })
      dispatch.gantt.processData({
        dataList: [..._dataList],
      })
    },

    collapse(opt: { unfold: boolean; groupId: string }, state) {
      const { unfold, groupId } = opt
      const { groupList, cacheCollapse } = state.gantt
      const { currentViewTab } = state.viewSetting
      const _groupList = groupList.map(item => {
        if (item.groupId === groupId) {
          return { ...item, unfold: unfold }
        } else {
          return item
        }
      })
      dispatch.gantt.setData({
        groupList: _groupList,
        //记忆分组折叠
        cacheCollapse: {
          ...cacheCollapse,
          [currentViewTab.viewId!]: _groupList,
        },
      })
      dispatch.gantt.calculatedGroupTodoList({})
    },

    async loadMore(options: any, state) {
      const { dataList } = state.gantt
      if (!dataList.length) {
        return undefined
      }
      let scrollId = dataList[dataList.length - 1]?.taskId
      if (`${scrollId}`.includes(TaskTableRowTypeAdd)) {
        scrollId = dataList[dataList.length - 2]?.taskId
      }
      if (scrollId) {
        return dispatch.gantt.getTodoList({
          //@ts-ignore
          scrollId: scrollId,
        })
      }
      return undefined
    },

    async addTask(opt, state) {
      console.log('addTask', opt)
      const { showIndex, isHeader, ...rest } = opt
      const { userInfo } = state.user
      const { navigatorId, currentViewTab } = state.viewSetting
      let projectId
      if (!isTaskMenuId(navigatorId!)) {
        projectId = navigatorId
      }

      const param = {
        ...rest,
        assignees: opt?.assignees?.map(v => v.uid!) || [],
        assigner: userInfo?.uid,
        //@ts-ignore
        timezone: dayjs.tz.guess(),
        projectId,
      }

      const isGroup = getViewIsGroup()

      const listKey = 'treeList'

      const tableList = state.gantt[listKey]

      const newData = handleSeverData({ ...param, _rowType: TaskTableRowType.normal }, state.user.userInfo!)

      if (showIndex !== undefined) {
        const currIndexes = Array.isArray(showIndex) ? showIndex : [showIndex]
        // 通过currIndexes在dataList中插入newData
        let newList = tableList
        if (currIndexes.length > 1) {
          let i = 0
          while (i < currIndexes.length) {
            const currIndex = currIndexes[i]
            const children = newList[currIndex]?.children
            if (children?.length) {
              newList = children
            }
            i++
          }
        }
        if (isHeader) {
          newList[currIndexes[currIndexes.length - 1]] = newData
        } else {
          newList.splice(currIndexes[currIndexes.length - 1], 0, newData)
        }

        // if (!isGroup) {
        //   dispatch.gantt.setData({
        //     dataList: [...newList],
        //   })
        // } else {
        // }
        dispatch.gantt.setData({
          [listKey]: [...tableList],
        })

        // dispatch.viewSetting.updateItemByDetail({
        //   targetTaskId: taskInfo?.taskId,
        //   taskId: res.taskId,
        //   detailPayload: {
        //     groupedIds: item?.groupedIds,
        //   },
        // })

        // dispatch.gantt.updateTaskInfo({
        //   taskInfo: { ...param, _rowType: TaskTableRowType.creating },
        //   actionType: !param?.title ? 'delete' : 'update',
        // })

        // dispatch.gantt.updateTreeList({ srcList: tableList })
      }
      // dispatch.gantt.updateTreeList({ srcList: dataList })
      return apiCreateTask(param).then(res => {
        if (res.taskId) {
          dispatch.permissions.setPermissionsMap({ list: [{ taskId: res.taskId }] })
        }

        dispatch.viewSetting.updateItemByDetail({
          targetTaskId: param?.taskId,
          taskId: res.taskId,
          detailPayload: {
            groupedIds: opt?.groupedIds,
          },
        })
        // .then(detail => {
        //   if (!detail) {
        //     return
        //   }
        //   const isGroup = getViewIsGroup()
        //   if (isGroup) {
        //     const dataList = state.gantt.dataList
        //     dataList.unshift(handleSeverData(detail, state.user.userInfo!))
        //     dispatch.gantt.processData({
        //       dataList: [...dataList],
        //     })
        //   }
        // })

        return res
      })
    },
    updateTaskInfo(opt, state) {
      opt.taskInfo = normalizeTimestamp(opt.taskInfo)
      updateTaskInfo(opt, { modelName: 'gantt', dispatch })
    },
  }),
})
