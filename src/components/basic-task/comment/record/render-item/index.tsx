import classNames from 'classnames';
import React, { ReactNode } from 'react';

import I18N from '@/utils/I18N';

import s from './index.less';
interface ItemRenderProps {
  event: string;
  right?: ReactNode;
  operatorAvatarUrl?: string;
  // 操作文案前的 icon
  prefixIcon?: ReactNode;
  // 操作文案后的 icon
  suffixIcon?: ReactNode;
  // 是否需要强化显示
  strong?: boolean;
  // 操作是否换行显示
  linkBreak?: boolean;
  // 操作文案是否高亮
  highlight?: boolean;
  className?: string;
}

const ItemRender: React.FC<ItemRenderProps> = (props) => {
  const { event, right, className } = props;

  return (
    <span className={classNames(s.top, className)}>
      {I18N.templateNode(event, {
        val1: <span>{right}</span>,
      })}
    </span>
  );
};

export default ItemRender;
