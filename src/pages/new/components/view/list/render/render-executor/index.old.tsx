import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import {
  apiTaskUpdateCompleteConditionPost,
  apiTodoParticipantListGet,
  ApiTodoParticipantListGetResponse,
  apiTodoParticipantUpdatePost,
} from '@/api';
import { PeoplePicker, RenderPeoples } from '@/components/basic';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { useExecutorRemoveSelf } from '@/components/basic-task/use-executor-remove-self';
import { Dispatch, RootState } from '@/models/store';
import { TodoInfo, UserInfo } from '@/types';
import { onPageVisibleChange } from '@/utils';
import {
  OneAndMoreServerParamsType,
  OneAndMoreType,
  PeoplePickerType,
  TaskTableRowType,
  TaskTableRowTypeAdd,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  onChange?: (params: { assignees: UserInfo[] }) => void;
  oneAndMoreType?: OneAndMoreType;
  taskInfo: TodoInfo;
}

interface ILatestIds {
  /** 执行人是否已经更新 */
  updated: boolean;
  /** 执行人的原始数据生成的map，key是uid */
  srcAssigneeMap: Record<string, string> | null;
  /** 执行人原始数据，下拉打开时获取 */
  srcAssignees: UserInfo[];
  /** 执行人操作完成后要提交的uid */
  uids: string[];
}

const RenderExecutor: React.FC<Props> = (props) => {
  const { oneAndMoreType, taskInfo } = props;
  const {
    taskId,
    assigner,
    _rowType: rowType,
    assigneeUids,
    followerUids,
    assigneeCount,
    assignees,
    followers,
  } = taskInfo;

  const permissions = useGetPermissions({ taskId });

  const { assignerUid } = assigner!;

  const { userInfo, visibleDetail } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    visibleDetail: state.detail.visibleDetail,
  }));
  const [list, setList] = useState<UserInfo[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  const latestUidsRef = useRef<ILatestIds>({
    updated: false,
    srcAssigneeMap: null as Record<string, string> | null,
    srcAssignees: [] as UserInfo[],
    uids: [] as string[],
  });

  const { removeSelf } = useExecutorRemoveSelf({
    className: s.confirm,
    taskId: taskId,
  });

  const uidsSubmit = () => {
    if (latestUidsRef.current.updated) {
      const uids = latestUidsRef.current.uids;
      if (typeof taskId === 'number') {
        return apiTodoParticipantUpdatePost({
          taskId: taskId,
          uids: uids,
        })
          .then(() => {
            latestUidsRef.current.updated = false;
            if (visibleDetail) {
              return dispatch.detail.getTodoDetail(taskId!);
            }
            //更新列表权限
            return dispatch.task.updatePermissionsWithTask({ taskId });
          })
          .finally(() => {
            dispatch.viewSetting.refreshDataByDataChange({
              refreshList: true,
              refreshCount: true,
            });
            // // 更新项目成员数量
            // dispatch.project.getProject({
            //   id: String(taskInfo.projectId),
            //   hideLoading: true,
            // });
            // dispatch.task.updateTodoList({
            //   taskId,
            //   assignees: users,
            //   assigneeUids: uids,
            //   assigneeCount: users.length,
            // });
          });
      }
    }
    return Promise.resolve();
  };

  const handleCancelUpdate = () => {
    setVisible(false);
    return updateExecutor(latestUidsRef.current.srcAssignees);
  };

  const handleConfirmUpdate = () => {
    setVisible(false);
    return uidsSubmit();
  };

  const dispatch = useDispatch<Dispatch>();
  const isAdd = rowType === TaskTableRowType.add;
  const updateExecutor = (users: UserInfo[]) => {
    // if (!latestUidsRef.current.srcAssigneeMap) {
    // @ts-ignore
    latestUidsRef.current.srcAssigneeMap =
      latestUidsRef.current.srcAssignees?.reduce((pre, cur) => {
        if (cur.uid) {
          // @ts-ignore
          pre[cur.uid] = 1;
        }
        return pre;
      }, {}) || {};
    // }
    const newMap = { ...latestUidsRef.current.srcAssigneeMap };
    // 新增的用户
    let newAddedUids: string[] = [];
    const uids: string[] = users.map((item) => {
      if (item.uid) {
        if (!newMap[item.uid]) {
          // 新增
          newAddedUids.push(item.uid);
        } else {
          delete newMap[item.uid];
        }
      }
      return item.uid!;
    });
    // 删除的用户;
    const deletedUids = Object.keys(newMap);
    const _followers = followers?.filter((item) => !uids.includes(item.uid as string)) || [];
    if (rowType === TaskTableRowType.add) {
    } else {
      latestUidsRef.current.uids = uids;
      if (!users.length) {
        if (latestUidsRef.current.srcAssignees.length) {
          latestUidsRef.current.updated = true;
        } else {
          latestUidsRef.current.updated = false;
        }
      } else {
        latestUidsRef.current.updated = newAddedUids.length > 0 || deletedUids.length > 0;
      }
      // apiTodoParticipantUpdatePost({
      //   taskId: taskId,
      //   uids: uids,
      // })
      //   .then(() => {
      //     //更新列表权限
      //     return dispatch.task.updatePermissionsWithTask({ taskId });
      //   })
      //   .finally(() => {
      //     dispatch.viewSetting.refreshDataByDataChange({
      //       refreshList: true,
      //       refreshCount: true,
      //     });
      //     // dispatch.task.updateTodoList({
      //     //   taskId,
      //     //   assignees: users,
      //     //   assigneeUids: uids,
      //     //   assigneeCount: users.length,
      //     // });
      //   });
    }
    dispatch.task.updateTodoList({
      taskId: taskId,
      assignees: users,
      assigneeUids: uids,
      followers: _followers,
      followerUids: _followers.map((item) => item.uid!),
      followerCount: _followers.length,
    });
    return Promise.resolve();
  };

  const change = async (users: UserInfo[]) => {
    // 新增逻辑
    if (isAdd) {
      const uids = users.map((item) => item.uid!) || [];
      if (uids.length) {
        latestUidsRef.current.updated = true;
      } else {
        latestUidsRef.current.updated = false;
      }
      const _followers = followers?.filter((item) => !uids.includes(item.uid as string)) || [];
      dispatch.task.updateTodoList({
        taskId,
        assignees: users,
        assigneeUids: uids,
        followers: _followers,
        followerUids: _followers.map((item) => item.uid!),
        followerCount: _followers.length,
      });
      return null;
    }
    const oldSelf = list.findIndex((item) => item.uid === userInfo?.uid) > -1;
    const moveSelf = users.findIndex((item) => item.uid === userInfo?.uid) === -1;
    //移除自己
    if (moveSelf && oldSelf) {
      return removeSelf().then((persist) => {
        updateExecutor(users);
        if (!persist) {
          uidsSubmit().then(() => {
            setVisible(false);
          });
          dispatch.detail.closeDetail({});
        }
      });
    } else {
      updateExecutor(users);
    }
  };

  const onOneAndMoreChange = useMemoizedFn((val: number) => {
    let _completeCondition =
      val === OneAndMoreType.all ? OneAndMoreServerParamsType.all : OneAndMoreServerParamsType.one;
    if (isAdd) {
      dispatch.task.updateTodoList({
        taskId,
        completeCondition: _completeCondition,
      });
      return null;
    }
    // 调用更改任务完成方式接口逻辑
    taskId &&
      apiTaskUpdateCompleteConditionPost({
        taskId,
        completeCondition: _completeCondition,
      }).then(() => {
        dispatch.task.updateTodoList({
          taskId,
          completeCondition: _completeCondition,
        });
      });
  });

  useEffect(() => {
    if (taskId && visible) {
      // 判断当前是不是新增
      if (String(taskId).includes(TaskTableRowTypeAdd)) {
        return;
      }
      apiTodoParticipantListGet({ todoId: String(taskId) }).then((res) => {
        const list = [...(res || [])];
        latestUidsRef.current.srcAssignees = list;
        setList(list);
      });
    }
  }, [taskId, visible]);

  // 修改详情的时候会重新刷新列表
  useEffect(() => {
    setList(assignees || []);
  }, [assignees]);

  useEffect(() => {
    onPageVisibleChange((pageShow) => {
      if (!pageShow) {
        setVisible(false);
      }
    });
  }, []);

  useEffect(() => {
    const scrollEl = document.querySelector('#virtuoso-table .os-viewport');
    if (!scrollEl) {
      return;
    }
    const lastScrollTop = scrollEl.scrollTop;
    const handleScroll = () => {
      const diff = scrollEl.scrollTop - lastScrollTop;

      if (Math.abs(diff) > 80) {
        handleCancelUpdate();
      }
    };
    if (visible) {
      scrollEl.addEventListener('scroll', handleScroll);
    } else {
      scrollEl.removeEventListener('scroll', handleScroll);
    }

    return () => {
      scrollEl.removeEventListener('scroll', handleScroll);
    };
  }, [visible]);

  const changeVisible = (v: boolean) => {
    // 收起渲染列表时，如果内容已经更新，阻止收起并给确认按钮增加动画
    if (!v && latestUidsRef.current.updated) {
      const btn = document.body.querySelector(`.confirm__btn`);
      if (btn) {
        btn?.classList.add('blink__animation');
        setTimeout(() => {
          btn?.classList.remove('blink__animation');
        }, 200);
      }
    } else {
      setVisible(v);
    }
  };

  const memoPermissions = useMemo(() => {
    const [CAN_VIEW, CAN_EDIT, CAN_SET_COMPLETE_MODE, CAN_CANCEL_OBSERVER] = validatesPermission({
      permissions: permissions,
      key: [
        TaskPermissionEnum.CAN_VIEW,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[];
    return {
      CAN_VIEW: CAN_VIEW || rowType === TaskTableRowType.add,
      CAN_EDIT: CAN_EDIT || rowType === TaskTableRowType.add,
      CAN_SET_COMPLETE_MODE: CAN_SET_COMPLETE_MODE || rowType === TaskTableRowType.add,
      CAN_CANCEL_OBSERVER: CAN_CANCEL_OBSERVER || rowType === TaskTableRowType.add,
      disabled: !CAN_EDIT && rowType !== TaskTableRowType.add,
    };
  }, [permissions, rowType]);

  return (
    <PeoplePickerContext.Provider
      value={{
        assignerUid: assignerUid,
        assigneeUids: assigneeUids,
        followerUids: followerUids,
        pickerType: PeoplePickerType.executor,
        currentUser: userInfo,
        canEditCompleteCondition: memoPermissions.CAN_SET_COMPLETE_MODE,
        // isAssigneeUpdated: latestUidsRef.current.updated,
        // onCancel: handleCancelUpdate,
        // onConfirm: handleConfirmUpdate,
      }}
    >
      <AddRowBox
        className={visible ? '' : s.dropdown__hide}
        iconName="icon-kit_user_add"
        tipTitle={list.length ? '' : I18N.auto.addParticipants}
        taskId={taskInfo.taskId}
        hasDelete={!!list.length && memoPermissions.CAN_EDIT}
        disabled={memoPermissions.disabled}
        focused={visible}
        onClick={(e) => {
          changeVisible(!visible);
        }}
        onClear={(e) => {
          // 清空必定触发执行人更新
          latestUidsRef.current.updated = true;
          change([]);
        }}
      >
        <PeoplePicker
          taskId={taskId!}
          disabled={memoPermissions.disabled}
          assignerUid={assignerUid}
          search
          hasArrow={false}
          value={[...list]}
          onChange={change}
          onOneAndMoreChange={onOneAndMoreChange}
          hasMinimum
          visible={visible}
          onVisible={changeVisible}
          placeholder=" "
          oneAndMoreType={oneAndMoreType}
        >
          {list.length ? (
            <>
              <div className={s.listPeopleItem}></div>
              <RenderPeoples
                showFinishedIcon={oneAndMoreType === OneAndMoreType.all}
                list={list || []}
                count={latestUidsRef.current.uids?.length || assigneeCount || list.length}
                maxShowCount={3}
                avatarClassName="mr-4"
                canOpenUserProfile={true}
              />
            </>
          ) : null}
        </PeoplePicker>
      </AddRowBox>
    </PeoplePickerContext.Provider>
  );
};

export default RenderExecutor;
