.pageRightBox {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40PX 20PX 40PX 40PX;
  flex: 1;
  height: 100%;
  width: 100%;
}

.tip {
  width: 200PX;
  min-width: 200PX;
  margin-right: 12PX;
  .title {
    font-weight: 600;
    font-size: 24PX;
    line-height: 28PX;
  }
  .desc {
    color: var(--TextSecondary);
    font-size: 13PX;
    margin-top: 10PX;
    line-height: 20PX;
  }
  .btn {
    margin-top: 20PX;
    padding: 0 11PX;
    height: 28PX;
    font-size: 14PX;
    border-radius: 6PX;
  }
}
.imgWrapper {
  width: 100%;
  max-width: 524PX;
  min-width: 0;
  .bg {
    width: 100%;
  }
}

