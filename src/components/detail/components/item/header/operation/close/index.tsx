import React from 'react';

import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './web.less';
export interface Props {
  onClose: () => void;
  iconBtnClass?: string;
}

const Close: React.FC<Props> = (props) => {
  const { onClose, iconBtnClass } = props;
  return (
    <span className="ml-10">
      <IconBtn
        title={I18N.auto.close}
        className={iconBtnClass}
        iconName="icon-pc_details_close"
        onClick={onClose}
      ></IconBtn>
    </span>
  );
};

export default Close;
