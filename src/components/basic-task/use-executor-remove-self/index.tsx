import { useMemoizedFn } from 'ahooks'

import { getBatchPermission } from '@/api-common'
import Modal from '@/components/basic/modal'
import { TaskPermission } from '@/types'
import I18N from '@/utils/I18N'
import { PermissionObjectType, TaskPermissionEnum } from '@/utils/permission'

type Props = {
  taskId?: number
  className?: string
}
export function useExecutorRemoveSelf(props: Props) {
  const { className, taskId } = props
  const removeSelf = useMemoizedFn(async () => {
    const obj = await getBatchPermission<TaskPermission>([
      {
        objectType: PermissionObjectType.TASK,
        operateType: TaskPermissionEnum.CAN_EDIT_FROM_PROJECT,
        objectIds: [String(taskId)],
      },
      {
        objectType: PermissionObjectType.TASK,
        operateType: TaskPermissionEnum.CAN_VIEW_FROM_PROJECT,
        objectIds: [String(taskId)],
      },
    ])
    return new Promise<string>((resolve, reject) => {
      const permissions: TaskPermission[] = obj[taskId!].permissions
      //@ts-ignore
      if (permissions.some(item => !!item.value)) {
        resolve('persist')
      } else {
        Modal.confirm(
          {
            width: 400,
            title: I18N.auto.confirmToRemoveFrom,
            content: I18N.auto.afterDeletingOneself,
            centered: true,
            cancelText: I18N.auto.cancel,
            okText: I18N.auto.confirmDeletion,
            onOk: () => {
              resolve('')
            },
            onCancel: () => {
              reject(false)
            },
            zIndex: 110000,
          },
          'warning',
        )
      }
    })
  })
  return {
    removeSelf,
  }
}
