.editor {
  position: relative;
  width: 100%;
  min-height: 22px;
  outline: none;
  white-space: pre-line;
  cursor: pointer;
  p {
    margin: 0 auto;
    line-height: 22px;
    word-break: break-all;
  }
  :global {
    .rock-popover-inner-content {
      padding: 0px;
    }
  }
}

.placeholder {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  color: var(--TextTertiary);
  font-size: 13px;
  line-height: 22px;
  //padding-left: 8px;
}

.disabled {
  cursor: default;
  // pointer-events: none;
}

.barrier {
  width: 1px;
}

.editorTextArea {
  :global {
    textarea {
      // max-height: calc(100vh - 300px);
      // overflow: hidden auto !important;
    }
  }
}

.innerDiv {
  img {
    width: 22px;
    height: 22px;
  }

  ul,
  ol {
    margin: 0 auto;
  }
}

.comment {
  border-radius: 6px;
  border: 1px solid var(--aBlack6);
  padding: 0 8px;
  height: 40px !important;
  background-color: var(--bgTop);
}

.editIcon {
  color: var(--Primary);
}

.innerText {
  width: 32px;
  font-size: 14px;
  color: var(--TextTertiary);
  margin-left: 2px;
  text-align: center;
}

.submitWrapper {
  position: absolute;
  right: 14px;
  .commentSubmit {
    color: var(--IconQuartus);
  }
}
