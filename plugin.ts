import { IApi } from 'umi';

export default (api: IApi) => {
  api.modifyHTML(($) => {
    //TODO 默认记得改成light
    $('body').prepend([
      `<script>var match = navigator.userAgent.match(/POPOTheme\\/(\\S+)/);
var skinType = 'light'; 
if (match && match[1]) {
  skinType = match[1];
}
console.log('userAgent-----', navigator.userAgent, 'skinType---', skinType);
document.body.setAttribute('data-theme', skinType);
document.body.setAttribute('data-color-scheme', skinType);

window.POPOTaskTrackTimeMap = {
  firstScript: Date.now(), //进入第一个Script时间
};

window.onload = function () {
  window.POPOTaskTrackTimeMap = {
    ...(window.POPOTaskTrackTimeMap || {}),
    onload: Date.now(), //onload时间
  };
};</script>`,
    ]);
    return $;
  });
};
