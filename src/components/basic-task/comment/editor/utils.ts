import Delta from 'quill-delta';

import { getEmoji, getEmojisEnMap, getEmojisMap } from '@/components/basic-task/emoji/util';
import { UserInfo } from '@/types';

import { POPOAtProps, POPODocProps, POPOEmbedClass, POPOEmojiProps } from './types';

export const getEmptyDelta = () => {
  return new Delta({ ops: [{ insert: '\n' }] });
};
export const getInitEmptyDelta = () => {
  return new Delta();
};

export const isEmptyDelta = (delta: Delta | undefined) => {
  if (!delta) {
    return true;
  }
  if (delta.ops.length === 0) {
    return true;
  }
  if (delta.ops.length === 1 && delta.ops[0].insert === '\n') {
    return true;
  }
  //TODO 全部空格要不要判断为空呢?
  return false;
};

export const getPopoEmojiText = (opt: POPOEmojiProps) => {
  const { text, key } = opt;
  return `[popoEmoji]{"text":"${text}","key":"${key}"}[/popoEmoji]`;
};

export const getPopoAtText = (opt: POPOAtProps) => {
  const { uid, text, atType } = opt;
  return `[popoAt]{"text":"${text}","uid":"${uid}","atType":"${atType}"}[/popoAt]`;
};

export const getPopoDocText = (opt: POPODocProps) => {
  const { text, docUrl, docIcon } = opt;
  return `[popoDoc]{"text":"${text}","docUrl":"${docUrl}","docIcon":"${docIcon}"}[/popoDoc]`;
};

const POPOEmojiPickRegex = /\[popoEmoji\](\{.+?\})\[\/popoEmoji\]/;
const POPOAtPickRegex = /\[popoAt\](\{.+?\})\[\/popoAt\]/;
const POPODocPickRegex = /\[popoDoc\](\{.+?\})\[\/popoDoc\]/;

const POPOEmojiRegex = /(\[popoEmoji\]\{.+?\}\[\/popoEmoji\])/g;
const POPOAtRegex = /(\[popoAt\]\{.+?\}\[\/popoAt\])/g;
const POPODocRegex = /(\[popoDoc\]\{.+?\}\[\/popoDoc\])/g;

const POPORegex = new RegExp(
  POPOEmojiRegex.source + '|' + POPOAtRegex.source + '|' + POPODocRegex.source
);

const HrefRegex = /(https?:\/\/[^\s/$.?#].[^\s]*)/;

// 此正则不会匹配src、href属性、a标签中的链接
// const HrefRegex2 = /(?:src|href|data-src|data-mention-avatarurl|data-origin|data-docicon)\s*=\s*["'][^"']*["']|(https?:\/\/[^\s"'><]+)/g;
const HrefRegex2 = /(?:src|href|data-src|data-mention-avatarurl|data-origin|data-docicon)\s*=\s*["'][^"']*["']|(https?:\/\/[^\s"'><]+)(?!(?:[^<]*<\/a>)|[^<>]*>)/g;


const JSON2Data = <T>(text: string): T | string => {
  let obj: T | string = text;
  try {
    obj = JSON.parse(text) as T;
  } catch (error) {
    console.log(error);
  }

  return obj;
};

/**
 * 服务端存储的数据转换成quill需要的格式
 * @param str
 * @returns
 */
export const dataPOPOtag2Delta = (popoTagText: string) => {
  //兼容历史表情
  const emojisMap = getEmojisMap();
  const emojisENMap = getEmojisEnMap();
  popoTagText = popoTagText.replace(/\[(.+?)\]/g, (word, $1) => {
    const emoji = emojisMap[$1] || emojisENMap[$1];
    if (emoji) {
      return `[popoEmoji]{"text":"${emoji.name}","key":"${emoji.key}"}[/popoEmoji]`;
    }
    return `[${$1}]`;
  });
  const textArr = popoTagText.split(POPORegex);
  let delta = getInitEmptyDelta();
  if (textArr.length) {
    textArr.forEach((text: string) => {
      if (text === undefined) {
        return;
      }
      //表情
      const regexEmojiArr = text.match(POPOEmojiPickRegex);
      if (regexEmojiArr) {
        const str = regexEmojiArr[1];
        const data = JSON2Data<{ text: string; src: string; key: string }>(str);
        if (typeof data !== 'string') {
          delta = delta.insert({
            popoEmoji: {
              ...data,
              src: getEmoji(Number(data.key))?.dynamicPath,
            },
          });
          return;
        }
      }

      //@联系人
      const regexPOPOAtArr = text.match(POPOAtPickRegex);
      if (regexPOPOAtArr) {
        const str = regexPOPOAtArr[1];
        const data = JSON2Data<{ text: string; uid: string }>(str);
        if (typeof data !== 'string') {
          delta = delta.insert({
            popoAt: {
              text: data.text.replace(/^@/, ''),
              uid: data.uid,
            },
          });
          return;
        }
      }

      //@云文档
      const regexPOPODocArr = text.match(POPODocPickRegex);
      if (regexPOPODocArr) {
        const str = regexPOPODocArr[1];
        const data = JSON2Data<{ text: string; docUrl: string; docIcon: string }>(str);
        if (typeof data !== 'string') {
          delta = delta.insert({
            popoDoc: data,
          });
          return;
        }
      }
      delta = tfOldHref(text, delta);
    });
  } else {
    delta = tfOldHref(popoTagText, delta);
  }
  delta.insert('\n');
  return delta;
};

//TODO 判断Text 是不是含有超链接  如果是 则按照链接切短 分别插入
export const tfOldHref = (text: string, delta: Delta) => {
  const textArr = text.split(HrefRegex);
  textArr.forEach((str) => {
    if (str !== '') {
      if (str.match(HrefRegex)) {
        delta.insert(str, { link: str });
      } else {
        delta.insert(str);
      }
    }
  });
  return delta;
};

export const dataDelta2POPOtag = (delta: Delta): { text: string; length: number } => {
  let str = '';
  let index = 0;
  delta.ops.forEach((item) => {
    if (typeof item.insert === 'string') {
      str += item.insert;
      index += item.insert.length;
    } else if (item.insert?.popoEmoji) {
      const popoEmoji = item.insert?.popoEmoji as POPOEmojiProps;
      str += getPopoEmojiText(popoEmoji);
      index += popoEmoji.text.length;
    } else if (item.insert?.popoDoc) {
      const popoDoc = item.insert?.popoDoc as POPODocProps;
      str += getPopoDocText(popoDoc);
      index += popoDoc.docUrl.length;
    } else if (item.insert?.popoAt) {
      const popoAt = item.insert?.popoAt as POPOAtProps;
      str += getPopoAtText(popoAt);
      index += popoAt.text.length;
    } else {
      str += item.insert;
      index += 1;
    }
  });
  return {
    text: str.replace(/\n$/, ''),
    length: index - 1,
  };
};

export const MentionExtraData: {
  participants?: UserInfo[]; //包含了指派人的所有参与人
  assignor?: UserInfo;
  userInfo?: UserInfo;
} = {
  participants: [],
  assignor: {},
  userInfo: {},
};

/**
 * 判定元素的祖先元素是否含有className内的类名,在遇到stopAtClassName停止 如果有返回结果并返回具体的class和包含此class的元素
 * @param element
 * @param className
 * @param stopAtClassName
 * @returns
 */
export const isAncestorOrSelfWithClass = (
  element: HTMLElement,
  className: string | string[],
  stopAtClassName: string
) => {
  let currentElement = element;
  while (currentElement && !currentElement?.classList?.contains(stopAtClassName)) {
    if (Array.isArray(className)) {
      for (let i = 0; i < className.length; i++) {
        if (currentElement?.classList?.contains(className[i])) {
          return {
            v: true,
            className: className[i] as POPOEmbedClass,
            element: currentElement,
          };
        }
      }
    } else {
      if (currentElement?.classList?.contains(className)) {
        return {
          v: true,
          className: className as POPOEmbedClass,
          element: currentElement,
        };
      }
    }
    if (currentElement.parentElement) {
      currentElement = currentElement.parentElement;
    } else {
      return {
        v: false,
      };
    }
  }
  return {
    v: false,
  };
};

export const findIndexDescendantInArray = (
  container: ChildNode,
  nodesArray: NodeListOf<ChildNode>
) => {
  for (let i = 0; i < nodesArray.length; i++) {
    let currentNode = nodesArray[i];
    if (currentNode.contains(container)) {
      return i;
    }
  }
  return -1;
};

export const DocsRegex =
  /((?:https|http):\/\/(?:test-|pre-)?docs\.(?:popo|pm)\.netease\.com\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]+)/g;


export const dataPOPOtag2Es = (popoTagText: string) => {
  //兼容历史表情
  const emojisMap = getEmojisMap();
  const emojisENMap = getEmojisEnMap();
  popoTagText = popoTagText.replace(/\[(.+?)\]/g, (word, $1) => {
    const emoji = emojisMap[$1] || emojisENMap[$1];
    if (emoji) {
      return `[popoEmoji]{"text":"${emoji.name}","key":"${emoji.key}"}[/popoEmoji]`;
    }
    return `[${$1}]`;
  });
  let htmlContent = '';
  const textArr = popoTagText.split(POPORegex);
  if (textArr.length) {
    textArr.forEach((text: string) => {
      if (text === undefined) {
        return;
      }
      //表情
      const regexEmojiArr = text.match(POPOEmojiPickRegex);
      if (regexEmojiArr) {
        const str = regexEmojiArr[1];
        const data = JSON2Data<{ text: string; src: string; key: string }>(str);
        if (typeof data !== 'string') {
          // delta = delta.insert({
          //   popoEmoji: {
          //     ...data,
          //     src: getEmoji(Number(data.key))?.dynamicPath,
          //   },
          // });
          htmlContent += `<img class="prosemirror-emoji" src="${getEmoji(Number(data.key))?.dynamicPath}" alt="${data.text}" />`;
          return;
        }
      }

      //@联系人
      const regexPOPOAtArr = text.match(POPOAtPickRegex);
      if (regexPOPOAtArr) {
        const str = regexPOPOAtArr[1];
        const data = JSON2Data<{ text: string; uid: string }>(str);
        if (typeof data !== 'string') {
          // delta = delta.insert({
          //   popoAt: {
          //     text: data.text.replace(/^@/, ''),
          //     uid: data.uid,
          //   },
          // });
          htmlContent += `<span class="prosemirror-mention" data-uid="${data.uid}">${data.text}</span>`;
          return;
        }
      }

      //@云文档
      const regexPOPODocArr = text.match(POPODocPickRegex);
      if (regexPOPODocArr) {
        const str = regexPOPODocArr[1];
        const data = JSON2Data<{ text: string; docUrl: string; docIcon: string }>(str);
        if (typeof data !== 'string') {

          htmlContent += `<a class="prosemirror-popo-doc" href="${data.docUrl}" target="_blank">${data.text}</a>`;
          return;
        }
      }
      htmlContent += processExternalLinks(text, (url) => {
        return `<a class="prosemirror-link" href="${url}" target="_blank">${url}</a>`;
      });
    });
  } else {
    htmlContent += processExternalLinks(popoTagText, (url) => {
      return `<a class="prosemirror-link" href="${url}" target="_blank">${url}</a>`;
    });
  }
  return htmlContent;
}

export function processExternalLinks(
  input: string,
  processor: (url: string) => string
): string {
  let result = '';
  let lastIndex = 0;
  let match;

  while ((match = HrefRegex2.exec(input)) !== null) {
    if (match[1]) {
      // 添加匹配前的文本
      result += input.substring(lastIndex, match.index);

      // 处理匹配到的URL
      const processedUrl = processor(match[1]);

      // 添加处理后的URL
      result += processedUrl;

      // 更新最后索引位置
      lastIndex = match.index + match[1].length;
    }
  }
  // 添加剩余文本
  result += input.substring(lastIndex);

  return result;
}