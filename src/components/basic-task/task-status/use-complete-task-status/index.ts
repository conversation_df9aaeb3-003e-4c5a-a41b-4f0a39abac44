import { useMemoizedFn } from 'ahooks'
import { useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { apiTaskFinishPost, apiTaskRebuildPost } from '@/api'
import { Message, Modal } from '@/components/basic'
import { Dispatch, RootState, store } from '@/models/store'
import { UserInfo } from '@/types'
import { OneAndMoreType, TaskStatusCompleteType } from '@/utils/const'
import I18N from '@/utils/I18N'
import { getIsInSession } from '@/models/utils'

export enum TaskCompleteStatus {
  /**
   * 直接完成                    场景:
   *                            1、任意一人完成, 只要有完成权限
   *                            2、所有执行人完成, 我是执行人中的一个 我是最后一个
   *                            3、所有执行人完成, 我不是执行人中的一个 有项目设置完成方式权限(项目管理员和创建人)
   */
  InstantaneousFinished = 1,
  /**
   * 取消完成                    场景:
   *                            1、任意一人完成, 项目已完成, 只要有完成权限
   *                            2、所有执行人完成, 有项目设置完成方式权限(项目管理员和创建人)
   */
  InstantaneousUnfinished = 2,

  // 执行人 且有权限就会有框
  /**
   * 需要选择, 仅我完成、完成全部,  场景:
   *                            1、一定是执行人 有权限完成(项目管理员和创建人)  我未完成、任务未完成
   *
   */
  CompleteMeOrCompleteAll = 3,
  /**
   * 需要选择, 重启我的、完成全部   场景:
   *                            1、一定是执行人 有权限完成(项目管理员和创建人)  我完成了、任务未完成
   */
  RebuildMeOrCompleteAll = 4,
  /**
   * 需要选择, 重启我的、 重启全部  场景:
   *                            1、一定是执行人 有权限完成(项目管理员和创建人) 有权限完成 我完成了、任务也完成
   */
  RebuildMeOrRebuildAll = 5,
}

export interface TaskStatusProps {
  oneAndMoreType?: OneAndMoreType
  isFollower?: boolean
  inDetail?: boolean
  isManager?: boolean
  /**
   * 执行人
   */
  isCoordinator?: boolean
  selfFinished?: number
  finished?: number
  taskId: number
  taskState?: TaskCompleteStatus
  onChange?: (finished: boolean) => void
  onTaskStateChange?: (taskState: TaskCompleteStatus) => void
  assigneeUids?: string[]
  disabled?: boolean
  finishedParticipantCount?: number
  participantCount?: number
}

export interface ContextProps extends TaskStatusProps {
  type?: TaskStatusCompleteType // todo 暂时先这么写
  loadingRef?: React.MutableRefObject<boolean>
  dispatch: Dispatch
  visibleDetail?: boolean
  userInfo?: UserInfo
  isThatLastOne?: boolean
  inDetail?: boolean
  onCancel?: () => void
}

export enum TaskOperateStatus {
  /**
   * 仅完成自己
   */
  completeMyTask = 1,
  /**
   * 完成所有
   */
  completeAllTask,
  /**
   * 重启自己
   */
  resetMyTask,
  /**
   * 重启所有
   */
  resetAllTask,
}

export interface GetTaskStatusFnProps {
  oneAndMoreType?: OneAndMoreType
  /**
   * 执行人
   */
  isCoordinator?: boolean
  /**
   * 有管理权限, 任务创建人或项目管理员
   */
  isManager?: boolean
  selfFinished?: number
  finished?: number
  assigneeUids?: string[]
  userInfo: UserInfo
  isThatLastOne?: boolean
}

// 单独写 在hooks使用,在回调函数中也要使用
export const getTaskStatusFn = (props: GetTaskStatusFnProps) => {
  const { oneAndMoreType, isCoordinator, isManager, selfFinished, finished, assigneeUids, userInfo, isThatLastOne } =
    props
  if (oneAndMoreType === OneAndMoreType.all) {
    if (isCoordinator && !isManager) {
      return selfFinished ? TaskCompleteStatus.InstantaneousFinished : TaskCompleteStatus.InstantaneousUnfinished
    } else if (!isCoordinator && isManager) {
      return finished ? TaskCompleteStatus.InstantaneousFinished : TaskCompleteStatus.InstantaneousUnfinished
    } else if (isManager && isCoordinator) {
      if (assigneeUids?.length === 1 && userInfo?.uid === assigneeUids[0]) {
        return selfFinished ? TaskCompleteStatus.InstantaneousFinished : TaskCompleteStatus.InstantaneousUnfinished
      } else if (finished) {
        return TaskCompleteStatus.RebuildMeOrRebuildAll
      } else if (isThatLastOne) {
        //只剩一个人,且我未完成
        return TaskCompleteStatus.InstantaneousUnfinished
      } else {
        return selfFinished ? TaskCompleteStatus.RebuildMeOrCompleteAll : TaskCompleteStatus.CompleteMeOrCompleteAll
      }
    } else {
      return selfFinished ? TaskCompleteStatus.InstantaneousFinished : TaskCompleteStatus.InstantaneousUnfinished
    }
  } else {
    return finished ? TaskCompleteStatus.InstantaneousFinished : TaskCompleteStatus.InstantaneousUnfinished
  }
}

export interface IsThatLastOneFnProps {
  oneAndMoreType?: OneAndMoreType
  finishedParticipantCount: number
  participantCount: number
  selfFinished?: number
  finished?: number
}

export const isThatLastOneFn = (props: IsThatLastOneFnProps) => {
  const { oneAndMoreType, participantCount, finishedParticipantCount, selfFinished, finished } = props

  if (participantCount === 1 && finishedParticipantCount === 1 && selfFinished) {
    return true
  }
  //所有人完成,最后一个是我, 走单个按钮完成逻辑
  if (
    oneAndMoreType === OneAndMoreType.all &&
    !finished &&
    !selfFinished &&
    participantCount - finishedParticipantCount === 1
  ) {
    return true
  }
  return false
}

export const handleRefreshList = (props: ContextProps) => {
  const { dispatch, loadingRef, taskId, parentId } = props

  if (loadingRef) {
    loadingRef.current = false
  }

  dispatch.viewSetting
    .updateItemByDetail({
      taskId: parentId || taskId,
      detailPayload: {
        groupedIds: props.groupedIds,
      },
    })
    .finally(() => {
      if (loadingRef) {
        loadingRef.current = false
      }
    })
}
/**
 * 完成任务
 */
export const handleFinishTask = (props: ContextProps) => {
  const { dispatch, taskId, onChange, parentId, rrule, loadingRef, groupedIds, type, visibleDetail } = props

  apiTaskFinishPost({ finishType: type, taskId })
    .then(() => {
      Message.success(I18N.auto.taskCompleted)
      // 完成后重拉详情接口
      onChange?.(true)
    })
    .finally(() => {
      if (loadingRef) {
        loadingRef.current = false
      }
      if (rrule) {
        dispatch.detail.getDetailInfo({ taskId: parentId || taskId })
        dispatch.viewSetting.refreshDataByDataChange({ refreshList: true, refreshCount: true })
      } else {
        dispatch.viewSetting.updateItemByDetail({ taskId: parentId || taskId, detailPayload: { groupedIds } })
      }

      delete window.detailClosePending
    })
}
/**
 * 重启任务
 */
export const handleRebuildTask = (props: ContextProps) => {
  const { dispatch, taskId, onChange, visibleDetail, type, assigneeUids, userInfo, isManager } = props
  let _type = type
  if (assigneeUids?.length === 0 || (assigneeUids?.length === 1 && userInfo?.uid === assigneeUids[0])) {
    _type = TaskStatusCompleteType.all
  }
  window.detailClosePending = true
  apiTaskRebuildPost({ finishType: isManager ? _type : TaskStatusCompleteType.me, taskId })
    .then(() => {
      Message.success(I18N.auto.theTaskHasBeenRestarted)
      // 完成后重拉详情接口
      onChange?.(false)
    })
    .finally(() => {
      if (visibleDetail) {
        dispatch.detail.getTodoDetail(null)
      }
      handleRefreshList(props)
      delete window.detailClosePending
    })
}

/**
 * 判断完成任务是否要给提示
 */
export const handleConfirmFinishTask = (props: {
  type: TaskStatusCompleteType
  dispatch: Dispatch
  taskId: number
  onChange?: (finished: boolean) => void
  onCancel?: () => void
  visibleDetail?: boolean
  assigneeUids?: string[]
  userInfo?: UserInfo
  isManager?: boolean
  oneAndMoreType?: OneAndMoreType

  loadingRef?: React.MutableRefObject<boolean>
  isThatLastOne?: boolean
  inDetail?: boolean
}) => {
  const { type, assigneeUids, userInfo, oneAndMoreType, isThatLastOne, loadingRef, inDetail, onCancel } = props
  if (
    assigneeUids?.length === 0 ||
    (assigneeUids?.length === 1 && userInfo?.uid === assigneeUids[0]) ||
    oneAndMoreType === OneAndMoreType.one
  ) {
    handleFinishTask({
      ...props,
      type: TaskStatusCompleteType.all,
    })
    return
  }
  //排除我是最后一人的情况
  if (type === TaskStatusCompleteType.all && !isThatLastOne) {
    if (loadingRef) {
      loadingRef.current = false
    }
    window.detailClosePending = true
    Modal.confirm(
      {
        width: 420,
        centered: true,
        title: I18N.auto.completeTheTask,
        content: I18N.auto.andThePersonInCharge,
        okText: I18N.auto.confirmCompletion,
        mask: !getIsInSession(),
        onOk: () => {
          handleFinishTask({
            ...props,
            type,
          })
        },
        zIndex: Number.MAX_SAFE_INTEGER,
        onCancel: onCancel,
        getContainer: () =>
          inDetail ? document.getElementById('task-detail-wrapper')! : document.getElementsByTagName('body')[0],
      },
      'info',
    )
  } else {
    handleFinishTask({
      ...props,
      type,
    })
  }
}

/**
 * 判断重启任务是否要给提示
 */
export const handleConfirmRebuildTask = (props: ContextProps) => {
  const { type, isManager, assigneeUids, userInfo, oneAndMoreType, loadingRef, inDetail, onCancel } = props
  if (
    assigneeUids?.length === 0 ||
    (assigneeUids?.length === 1 && userInfo?.uid === assigneeUids[0]) ||
    oneAndMoreType === OneAndMoreType.one
  ) {
    handleRebuildTask({
      ...props,
      type: TaskStatusCompleteType.all,
    })
    return
  }
  if (type === TaskStatusCompleteType.all && isManager) {
    if (loadingRef) {
      loadingRef.current = false
    }
    Modal.confirm(
      {
        centered: true,
        width: 420,
        title: I18N.auto.restartTask,
        content: I18N.auto.afterRestartingTheTask,
        okText: I18N.auto.confirmRestart,
        mask: !getIsInSession(),
        zIndex: 99999,
        onOk: () => {
          handleRebuildTask({
            ...props,
            type: type,
          })
        },
        onCancel: onCancel,
        getContainer: () =>
          inDetail ? document.getElementById('task-detail-wrapper')! : document.getElementsByTagName('body')[0],
      },
      'info',
    )
  } else {
    handleRebuildTask({
      ...props,
      type: type,
    })
    handleRefreshList(props)
  }
}

interface SelectOperationMethodFnProps extends ContextProps {
  key?: TaskOperateStatus
}

export const selectOperationMethodFn = (props: SelectOperationMethodFnProps) => {
  const { loadingRef, assigneeUids, userInfo, isManager, isCoordinator, taskState, key } = props
  if (loadingRef) {
    loadingRef.current = true
  }
  // 针对不同任务状态及完成情况，执行调用接口或者弹窗等逻辑
  switch (taskState) {
    case TaskCompleteStatus.InstantaneousFinished:
      if (
        assigneeUids?.length === 0 ||
        (assigneeUids?.length === 1 && userInfo?.uid === assigneeUids[0]) ||
        (assigneeUids && assigneeUids?.length > 1 && !isManager)
      ) {
        handleRebuildTask({
          type: TaskStatusCompleteType.me,
          ...props,
        })
      } else {
        handleConfirmRebuildTask({
          type: TaskStatusCompleteType.all,
          ...props,
        })
      }
      break
    case TaskCompleteStatus.InstantaneousUnfinished:
      if (isCoordinator && !isManager) {
        handleFinishTask({
          type: TaskStatusCompleteType.me,
          ...props,
        })
      } else {
        handleConfirmFinishTask({
          type: TaskStatusCompleteType.all,
          ...props,
        })
      }
      break
    case TaskCompleteStatus.RebuildMeOrCompleteAll:
      if (key === TaskOperateStatus.resetMyTask) {
        handleRebuildTask({
          type: TaskStatusCompleteType.me,
          ...props,
        })
      } else {
        handleConfirmFinishTask({
          type: TaskStatusCompleteType.all,
          ...props,
        })
      }
      break
    case TaskCompleteStatus.CompleteMeOrCompleteAll:
      if (key === TaskOperateStatus.completeMyTask) {
        handleFinishTask({
          type: TaskStatusCompleteType.me,
          ...props,
        })
      } else {
        handleConfirmFinishTask({
          type: TaskStatusCompleteType.all,
          ...props,
        })
      }
      break
    case TaskCompleteStatus.RebuildMeOrRebuildAll:
      if (key === TaskOperateStatus.resetMyTask) {
        handleRebuildTask({
          type: TaskStatusCompleteType.me,
          ...props,
        })
      } else {
        handleConfirmRebuildTask({
          type: TaskStatusCompleteType.all,
          ...props,
        })
      }
      break
  }
}

export default function useCompleteTaskStatus(props: TaskStatusProps) {
  const {
    isManager,
    isCoordinator,
    oneAndMoreType,
    selfFinished,
    finished,
    assigneeUids, // 不能加默认值, 被监听了
    finishedParticipantCount = 0,
    participantCount = 0,
  } = props
  const loadingRef = useRef<boolean>(false)

  const { userInfo, visibleDetail } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    visibleDetail: state.detail.visibleDetail,
  }))

  const dispatch = useDispatch<Dispatch>()
  //刷新数据

  /**
   * 选择完成方式
   */
  const selectOperationMethod = useMemoizedFn((opt?: { key?: TaskOperateStatus }) => {
    const { key } = opt || {}
    loadingRef.current = true
    const isThatLastOne = isThatLastOneFn({
      finishedParticipantCount,
      participantCount,
      finished,
      selfFinished,
      oneAndMoreType,
    })
    selectOperationMethodFn({
      key: key,
      loadingRef,
      dispatch,
      isThatLastOne,
      userInfo: userInfo!,
      visibleDetail,
      ...props,
    })
  })

  // 获取当前最新任务完成方式的状态
  const getTaskState = useCallback(() => {
    const isThatLastOne = isThatLastOneFn({
      finishedParticipantCount,
      participantCount,
      finished,
      selfFinished,
      oneAndMoreType,
    })
    return getTaskStatusFn({
      oneAndMoreType,
      isCoordinator,
      isManager,
      selfFinished,
      finished,
      assigneeUids,
      userInfo: userInfo!,
      isThatLastOne,
    })
  }, [
    selfFinished,
    isCoordinator,
    isManager,
    finished,
    oneAndMoreType,
    assigneeUids,
    userInfo,
    finishedParticipantCount,
    participantCount,
  ])

  return {
    selectOperationMethod,
    getTaskState,
    loadingRef,
  }
}
