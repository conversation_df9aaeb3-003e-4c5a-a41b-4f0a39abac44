.renderPeoples {
  display: flex;
  align-items: center;

  &.default {
    --avatar-width: 24px;
    --avatar-height: 24px;
  }

  &.small {
    --avatar-width: 20px;
    --avatar-height: 20px;
  }
  min-height: var(--avatar-height, 24px);

  .avatarBox {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    height: var(--avatar-height);
    width: var(--avatar-width);
    .avatar {
      height: var(--avatar-height);
      width: var(--avatar-width);
      border-radius: 50%;
      background: url('~@/assets/images/people.png') center center no-repeat;
      background-size: 100%;
      :global {
        .rock-icon {
          font-size: var(--avatar-width);
        }
      }
    }
    .finishedIcon {
      display: inline-block;
      position: absolute;
      right: -2px;
      bottom: -1px;
      width: 10px;
      height: 10px;
      background: url('~@/assets/images/finished.png') center center no-repeat;
      background-size: 100%;
    }
  }

  .count {
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--avatar-height);
    width: var(--avatar-width);
    border-radius: 50%;
    font-weight: 500;
    font-size: 10px;
    color: var(--TextSecondary);
    background-color: var(--aBlack6);
    margin-right: 8px;
  }
  .fs11 {
    font-size: 11px;
  }
  .addIcon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--aBlack4);
  }
}

.finish {
  color: var(--TasksDone);
}

.renderPeoplesWrapper {
  display: flex;
  justify-content: space-between;
}

.finishPercentTip {
  display: flex;
  align-items: center;
  .divider {
    height: 12px;
    margin-right: 12px;
    width: 1px;
    background-color: var(--aBlack12);
  }
  .completeTip {
    color: var(--TextPrimary);
    font-size: 14px;
    line-height: 24px;
  }
}

.detailRenderPeopleWrapper {
  flex: 1;
}
