.editorBox {
  margin-top: 6px;
  // padding: 5px 14px;
  //padding-right: 12px;
  border: 1px solid var(--aBlack12);
  border-radius: 8px;
  background-color: var(--bgTop);
  user-select: auto !important;
  .btnBox {
    height: 38px;
  }
  &.close {
    display: flex;
    justify-content: space-between;
    .editor {
      flex: 1;
    }

    .btnBox {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      padding-right: 12px;
      .btnBoxLeft {
        display: none;
      }
    }
  }
  &.open {
    border-color: var(--Brand600);
    .btnBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px 0 12px;
      .btnBoxLeft {
        display: flex;
        .otherCheckbox {
          display: none;
          &.show {
            display: flex;
          }
          :global {
            .rock-checkbox-wrapper {
              line-height: 20px;
              margin-right: 6px;
            }
          }
          .tip {
            font-size: 13px;
            color: var(--TextSecondary);
            line-height: 20px;
          }
        }
      }
    }
  }
}

.editor {
  display: flex;
  align-items: center;

  :global {
    .rock-input-textarea-wrapper {
      flex: 1;
      width: 100%;
      padding: 0;
      margin: 0;
      border: none;
    }
    .rock-input-textarea::-webkit-resizer {
      background: unset;
    }
    .rock-input-textarea {
      padding: 0;
      margin: 0;
      max-height: 160px;
      //height: unset !important;
      min-height: 22px;
      line-height: 20px;
      padding: none;
    }
  }
  .areaIcon {
    color: var(--IconQuartus);
  }
}

.divider {
  color: var(--aBlack8);
  margin-top: 4px;
  margin-bottom: 4px;
}
.options {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .iconColor {
    color: var(--IconPrimary);
  }
  .icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
  }
  .send {
  }
  .iconSendColor {
    color: var(--IconQuartus);
  }
  .active {
    color: var(--Brand600) !important;
  }
}

.quill {
  width: 100%;
  min-height: 38px;
  :global {
    .ql-container.ql-snow {
      border: none;
    }
    .ql-editor.ql-blank::before {
      font-size: 14px;
    }
    .ql-editor.ql-blank::before {
      font-style: normal;
      color: var(--TextTertiary);
    }
  }
}
