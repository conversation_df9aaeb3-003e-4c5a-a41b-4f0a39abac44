import { useMemo } from 'react';

import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import { getRepeatTypeByRuleStr, repeatOptions } from '../utils';
export type Props = {
  rrule?: string;
  className?: string;
};

export default function RenderRrule(props: Props) {
  const { rrule, className } = props;
  const repeatName = useMemo(() => {
    if (rrule) {
      const type = getRepeatTypeByRuleStr(rrule);
      const repeat = repeatOptions.filter((item) => item.value === type)[0];
      return repeat.name;
    }
    return '';
  }, [rrule]);
  return rrule ? (
    <IconBtn
      placement="top"
      key={1}
      title={I18N.template(I18N.auto.repeat_2, {
        val1: repeatName,
      })}
      className={className}
      iconName="icon-quick_round"
      fontSize={16}
    ></IconBtn>
  ) : null;
}
