import { call } from '@popo-bridge/web'
import { createModel } from '@rematch/core'

import { apiTodoParticipantMeGet } from '@/api'
import { UserInfo } from '@/types'
import Const, { CalendarModeType, ListType, ViewType } from '@/utils/const'
import { devLogin } from '@/utils/login'
import { CorlorMode, setLanguageConf, setThemeConf } from '@/utils/platform'
import scout from '@/utils/scout'
import skyline, { EnumTrackeKey } from '@/utils/skyline'

import type { RootModel } from '.'

interface State {
  userInfo?: UserInfo
  corlorMode?: CorlorMode
  language?: string
  listType?: ListType
  calendarViewMode?: CalendarModeType
  showUnplanned?: boolean
  showMenu?: boolean
  /**
   * 是否显示独立窗口  true 表示在独立窗口
   */
  showSeparate?: boolean
  recentContactsList?: UserInfo[]
}

type TrackingOpt = {
  key: EnumTrackeKey
  navigatorId?: string | number
  viewType?: ViewType
  viewId?: string
  [k: string]: any
}

export const user = createModel<RootModel>()({
  state: {
    userInfo: {},
    corlorMode: CorlorMode.light,
    language: 'zh-CN',
    listType: undefined,
    calendarViewMode: CalendarModeType.week,
    showUnplanned: true,
    showSeparate: false,
    showMenu: true,
    recentContactsList: [], // 最近联系人列表
  } as State,
  reducers: {
    setData(state, payload: State) {
      return { ...state, ...payload }
    },
    setUser(state, payload: UserInfo) {
      return { ...state, userInfo: payload }
    },
    setCorlorMode(state, payload: CorlorMode) {
      setThemeConf(payload)
      return { ...state, corlorMode: payload }
    },
    setLanguage(state, payload: string) {
      setLanguageConf(payload)
      return { ...state, language: payload }
    },
    setListType(state, type: ListType) {
      return {
        ...state,
        listType: type,
      }
    },
    setShowSeparate(state, showSeparate: boolean) {
      return { ...state, showSeparate }
    },
  },
  effects: dispatch => ({
    async login() {
      return new Promise<any>((resolve, reject) => {
        if (process.env.NODE_ENV === Const.PRODUCTION) {
          call('getToken', {}).then(token => {
            localStorage.setItem(Const.LoginToken, token)
            resolve(false)
          })
        } else {
          //本地运行
          devLogin({
            // email: '<EMAIL>',
            email: '<EMAIL>',
            // email: '<EMAIL>',
            // email: '<EMAIL>',
            // email: '<EMAIL>',
            // email: '<EMAIL>',
            //email: '<EMAIL>',
            //email: '<EMAIL>',
          })
            .then((res: any) => {
              localStorage.setItem(Const.LoginToken, res.accessToken)
              resolve(res)
            })
            .catch(() => {
              reject(false)
            })
        }
      })
    },
    async getUser() {
      return apiTodoParticipantMeGet().then(data => {
        skyline.initLoginUser(data)
        scout.initLoginUser(data)
        dispatch.user.setUser(data)
      })
    },
    tracking(opt: TrackingOpt, state) {
      const { key, ...ret } = opt
      skyline.send(key, {
        ...ret,
      })
    },
    trackingByView(opt: TrackingOpt, state) {
      const { key, ...ret } = opt
      const {
        currentViewTab: { viewType, viewId },
        navigatorId,
      } = state.viewSetting
      skyline.send(key, {
        ...ret,
        navigatorId,
        viewType: viewType,
        viewId: viewId,
      })
    },
  }),
})
