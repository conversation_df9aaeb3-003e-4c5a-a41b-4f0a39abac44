import './index.less';

import Quill from 'quill';

import { POPOAtProps, POPOEmbedClass } from '@/components/basic-task/comment/editor/types';
import { AtType } from '@/utils/const';
import I18N from '@/utils/I18N';

import { MentionExtraData } from '../../../utils';
interface MentionBlotProps extends POPOAtProps {}

class MentionBlot extends Quill.import('blots/embed') {
  static create(value: MentionBlotProps) {
    let node = super.create();
    node.innerHTML = `@${value.text}`;
    node.setAttribute('data-text', `@${value.text}`);
    node.setAttribute('data-uid', value.uid);
    node.setAttribute('data-attype', value.atType ? value.atType : AtType.user);
    //node.contentEditable = false;
    if (MentionExtraData.userInfo?.uid === value.uid) {
      node.classList.add('ql-popo-mention-myself');
      node.innerHTML = I18N.auto.me;
    }
    return node;
  }
  static value(node: any) {
    return {
      text: node.getAttribute('data-text'),
      uid: node.getAttribute('data-uid'),
      atType: node.getAttribute('data-attype'),
    };
  }
}
MentionBlot.blotName = 'popoAt';
MentionBlot.tagName = 'span';

MentionBlot.className = POPOEmbedClass.mention;

Quill.register(MentionBlot);
