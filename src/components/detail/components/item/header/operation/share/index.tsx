import { pp } from '@popo-bridge/web';
import React from 'react';

import { apiTodoSharePost } from '@/api';
import { IconBtn } from '@/components/basic';
import { BridgeUserType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './web.less';
export interface Props {
  taskId: number;
  iconBtnClass?: string;
}

const DetailHeaderWeb: React.FC<Props> = (props) => {
  const { taskId, iconBtnClass } = props;

  const onShare = () => {
    pp.chooseIMContacts({
      groupCollapse: false,
      title: I18N.auto.forwardTo,
      checkTeamMsgForward: true,
    })
      .then((res) => {
        if (Array.isArray(res.data)) {
          const uids: string[] = [];
          const teamIds: string[] = [];
          res.data.forEach((item) => {
            if (item.type === BridgeUserType.p2p) {
              uids.push(item.id);
            } else if (item.type === BridgeUserType.team) {
              teamIds.push(item.id);
            }
          });
          if (uids.length || teamIds.length) {
            apiTodoSharePost({
              taskId: taskId,
              uids: uids,
              teamIds: teamIds,
            }).then(() => {
              console.log('share success');
            });
          }
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        });
      });
  };
  return (
    <span className="ml-10">
      <IconBtn
        title={I18N.auto.share}
        className={iconBtnClass}
        iconName="icon-details_nav_share"
        onClick={onShare}
      ></IconBtn>
    </span>
  );
};

export default DetailHeaderWeb;
