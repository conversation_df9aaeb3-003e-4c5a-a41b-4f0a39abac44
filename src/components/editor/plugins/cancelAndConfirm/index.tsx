
import { IconBtn } from "@/components/basic";
import { RockEditorPlugin, PluginKey, convertToHTML, EditorState } from '@bedrock/editor'
import s from './index.less';
import { ImCheck1, ImClose1 } from "@babylon/popo-icons";
import classNames from "classnames";
import { useMemoizedFn } from "ahooks";
import { cleanTagP } from "../../utils";

interface ICancelAndConfirmProps {
  type: 'remark' | 'comment'
  onCancel?: () => void;
  onConfirm?: () => void;
  editorState: EditorState;
}

const CancelAndConfirm: React.FC<ICancelAndConfirmProps> = ({
  type,
  onCancel,
  onConfirm,
  editorState,
}) => {

  const isEmptyEs = useMemoizedFn(() => {
    const newEs = editorState;
    const htmlValue = convertToHTML(newEs) || '';
    return cleanTagP(htmlValue).length === 0;
  });

  return type === 'remark' && (
    <div className={s.wrapper}>
      {/* <IconBtn
        icon={<ImClose1 />}
        iconClassName={s.cancelIcon}
        onClick={onCancel}
      /> */}
      <IconBtn
        icon={<ImCheck1 />}
        iconClassName={classNames(s.confirmIcon, { [s.active]: !isEmptyEs() })}
        onClick={onConfirm}
      />
    </div>
  )
}

interface PluginOptions {
  type: 'remark' | 'comment';
  onCancel?: () => void;
  onConfirm?: () => void;
}

export default function createCancelAndConfirmPlugin(options: PluginOptions = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('cancelAndConfirm'),
    getToolbarConfig: (plugins) => {
      return [...plugins, [(p) => CancelAndConfirm({ ...p, ...options })]];
    },
  });
}