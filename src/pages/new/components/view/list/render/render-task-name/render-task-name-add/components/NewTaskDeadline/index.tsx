import { Button } from '@/components/basic';
import { Data16Timestop } from '@babylon/popo-icons';
import { TaskTime } from '@/types';

import s from './index.less';
import I18N from '@/utils/I18N';
import { SubTaskDeadline } from '@/components/CreateTaskLine/components/index';
import { Overlay } from '@/components/basic/popo-date-picker';

interface INewTaskDeadlineProps {
  onChange: (value: TaskTime) => void;
  onSuccess?: () => void;
  taskInfo?: TaskTime;
}
const NewTaskDeadline: React.FC<INewTaskDeadlineProps> = ({
  onChange,
  onSuccess,
  taskInfo = { startTime: 0 },
}) => {

  const renderContent = () => {
    return (
      <Button
        size="xSmall"
        icon={<Data16Timestop className={`${s.date__icon} fs-16`} />}
        aria-label="取消"
        className={s.cancel__btn}
      >
        {I18N.auto.deadline_2}
      </Button>
    )
  }

  return (
    <SubTaskDeadline
      taskInfo={taskInfo}
      onChange={onChange}
      onSuccess={onSuccess}
      renderContent={renderContent}
      panelType={Overlay.quick}
    />
  )
};

export default NewTaskDeadline;
