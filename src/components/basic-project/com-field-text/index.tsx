import classNames from 'classnames';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import ContentEditableEditor from '@/components/basic/content-editable-editor';
import { Variant } from '@/components/basic/input';
import { isControlled } from '@/utils';
import { ActionType } from '@/types';

export type Props = {
  editding?: boolean;
  onChangeEditding?: (editding: boolean) => void;
  className?: string;
  contentClassName?: string;
  focusedClassName?: string;
  value?: string;
  onChange?: (v: string, type: ActionType) => void;
  onTextChange?: (v?: string) => void;
  onFocus?: React.FocusEventHandler;
  onBlur?: React.FocusEventHandler;
  editable?: boolean;
  placeholder?: string;
  onClose?: () => void;
  disabled?: boolean;
  blurChangeValue?: boolean;
  variant?: Variant;
  onKeyDown?: React.KeyboardEventHandler;
  switchInput?: boolean;
};

//支持回车 & 失去焦点 才修改数据
const ComFieldText = React.forwardRef<
  {
    getValue: () => string | undefined;
    setValue: (v: string) => void;
  },
  Props
>((props, refs) => {
  const {
    value: baseValue = '',
    className,
    onFocus,
    onBlur,
    editable,
    placeholder,
    contentClassName,
    focusedClassName,
    onClose,
    onChange,
    onTextChange,
    editding: propEditding,
    onChangeEditding,
    blurChangeValue,
    disabled,
    variant,
    onKeyDown,
    switchInput,
  } = props;
  const refValue = useRef<string>(baseValue);
  const [value, setValue] = useState<string>(baseValue);

  const [editding, setVisible] = useState<boolean>();

  const memoEditding = useMemo(() => {
    return isControlled(propEditding) ? propEditding : editding;
  }, [propEditding, editding]);

  const changeEditding = (v: boolean) => {
    if (disabled) {
      return;
    }
    if (isControlled(propEditding)) {
      onChangeEditding?.(v);
    } else {
      setVisible(v);
    }
  };

  const handleChange = (v: string, type: ActionType) => {
    changeEditding(false);
    onChange?.(v, type);
  };

  useImperativeHandle(refs, () => ({
    getValue: () => {
      return value;
    },
    setValue: (value: string) => {
      setValue(value);
    },
  }));

  useEffect(() => {
    setValue(baseValue);
  }, [baseValue]);

  useEffect(() => {
    if (memoEditding) {
      const listener = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose?.();
          changeEditding?.(false);
        }
      };
      document.addEventListener('keydown', listener);
      return () => {
        document.removeEventListener('keydown', listener);
      };
    }
  }, [memoEditding]);

  return (
    <ContentEditableEditor
      className={classNames(className)}
      contentClassName={contentClassName}
      focusedClassName={focusedClassName}
      variant={variant}
      value={value}
      onTextChange={(v) => {
        setValue(v);
        onTextChange?.(v);
        refValue.current = v;
      }}
      onChange={handleChange}
      maxLength={1500}
      id="todo-title-input-editor"
      allowedEmpty={false}
      disableSelect
      onFocus={(v) => {
        changeEditding?.(true);
        onFocus?.(v);
      }}
      onEditding={changeEditding}
      onBlur={(e) => {
        if (baseValue !== value && blurChangeValue) {
          onChange?.(value);
        }
        onBlur?.(e);
      }}
      editable={editable}
      editding={memoEditding}
      placeholder={placeholder}
      disabled={disabled}
      onKeyDown={onKeyDown}
      switchInput={switchInput}
    ></ContentEditableEditor>
  );
});

export default ComFieldText;
