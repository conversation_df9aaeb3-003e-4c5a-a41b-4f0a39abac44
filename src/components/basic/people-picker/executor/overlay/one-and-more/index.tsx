import classNames from 'classnames';
import React, { PropsWithChildren, useMemo, useState } from 'react';

import { DropdownSelect, Icon } from '@/components/basic';
import { OneAndMoreType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  oneAndMoreType?: OneAndMoreType;
  onChange?: (oneAndMoreType: OneAndMoreType) => void;
  disabled?: boolean;
};
const OneAndMore: React.FC<PropsWithChildren<Props>> = (props) => {
  const { className, oneAndMoreType, onChange, disabled } = props;
  const [visible, setVisible] = useState(false);
  const [menuList] = useState([
    {
      name: I18N.auto.everyoneNeedsTo,
      value: OneAndMoreType.all as number,
    },
    {
      name: I18N.auto.anyoneCanFinishIt,
      value: OneAndMoreType.one as number,
    },
  ]);
  const active = useMemo(() => {
    return menuList.find((item) => item.value === oneAndMoreType);
  }, [oneAndMoreType, menuList]);
  return (
    <div className={classNames(s.oneAndMore, className)}>
      {disabled ? (
        <div className={s.readonlyBtn}>{active?.name}</div>
      ) : (
        <DropdownSelect
          value={oneAndMoreType}
          data={menuList}
          onChange={(value) => {
            onChange?.(value as unknown as OneAndMoreType);
            setVisible(false);
          }}
          visible={visible}
          onVisibleChange={(v) => {
            setVisible(v);
          }}
          menuClassName={s.oneAndMoreDropdownSelect}
          getPopupContainer={(dom) => dom}
        >
          <div className={s.btn}>
            {active?.name}
            <Icon
              className={classNames(s.arrow, { [s.openArrow]: visible })}
              name="icon-pc_plane_day_before"
              fontSize={12}
            ></Icon>
          </div>
        </DropdownSelect>
      )}
    </div>
  );
};

export default OneAndMore;
