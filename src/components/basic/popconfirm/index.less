body {
  .rock-popover {
    &.rock-popover-confirm {
      &.noIcon {
        .rock-popover-title {
          padding: 16px 16px 0 16px;
        }
        .rock-popover-inner-content {
          padding: 0 16px 16px 16px;
        }
        .rock-popover-confirm-title {
          .rock-icon {
            display: none;
          }
        }
      }
      .rock-popover-title,
      .rock-popover-inner-content {
        min-width: 200px;
      }
      .rock-popover-confirm-title {
        color: var(--TextPrimary);
        word-break: break-all;
      }
      .rock-popover-content {
        border-radius: 8px;
      }
      .rock-popover-inner {
        background: var(--bgTop);
        border: 1px solid var(--aBlack12);
      }
      .rock-popover-confirm-footer {
        .rock-btn-secondary {
          border-radius: 6px;
          border: 1px solid var(--aBlack12);
          background: var(--bgTop) !important;
          color: var(--TextPrimary);
        }
        .rock-btn {
          line-height: 22px;
        }
      }
      .rock-popover-arrow {
        --popover-border-color: var(--aBlack12);
        --border-color-2: var(--bgTop);
        background: var(--bgTop) !important;
      }
    }
  }
}
