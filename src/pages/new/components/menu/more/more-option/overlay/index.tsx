import { OperateComin, OperateDelete, OperatePin3, OperateRemovepj } from '@babylon/popo-icons';
import { ChevronRight } from '@bedrock/icons-react';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import {
  apiProjectGetGet,
  apiProjectGroupPinPost,
  apiProjectPinPut,
  apiProjectStateRecordGet,
  apiProjectUpdateStatePost,
  apiProjectUpdateTitleIconPost,
} from '@/api';
import { Icon, Menu } from '@/components/basic';
import { ProjectStatusTag } from '@/components/basic-project';
import { DeleteProject, EditProject, InvitedMembers } from '@/components/basic-project/more-option';
import { EditProjectValue } from '@/components/basic-project/more-option/edit-project';
import useProjectShare from '@/components/basic-project/more-option/project-share/overlay/use-share-project';
import ProjectStatusRecord from '@/components/basic-project/project-status/project-status-record';
import useProjectStatus, {
  ProjectRecord,
} from '@/components/basic-project/project-status/use-project-status';
import { Dispatch, RootState, store } from '@/models/store';
import { Member, Permission, ProjectInfo } from '@/types';
import {
  EnumEmitter,
  EnumProjectStatus,
  ProjectListMenuId,
  TaskNavigatorType,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import { validatesVersion } from '@/utils/validate-version';

import s from './index.less';
import classNames from 'classnames';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import { getPageIsProjectGroup } from '@/models/utils';

export type Props = {
  className?: string;
  projectInfo?: ProjectInfo;
  permissions?: Permission[];
};

const Overlay: React.FC<Props> = (props) => {
  const { projectInfo, permissions } = props;
  const { navigatorId } = useSelector((state: RootState) => ({
    navigatorId: state.viewSetting.navigatorId,
  }));
  const [stateInfo, setStateInfo] = useState<ProjectRecord>();
  const { onShare, onCopy } = useProjectShare({ projectInfo });
  const dispatch = useDispatch<Dispatch>();
  const nameAndIcon = useMemo(() => {
    return {
      name: projectInfo?.name,
      icon: projectInfo?.icon,
      iconColor: projectInfo?.iconColor,
    };
  }, [projectInfo]);

  const updateList = async (param: Partial<ProjectInfo>, pin?: boolean) => {
    //判断当前有没有成员数据
    let _projectInfo = projectInfo;
    if (!projectInfo?.memberVos?.length) {
      _projectInfo = await apiProjectGetGet({ projectId: String(projectInfo?.projectId) });
    }
    dispatch.project.updateProjectList({
      ..._projectInfo,
      ...param,
    });
    if (pin) {
      dispatch.project.updatePingProjectList({
        ..._projectInfo,
        ...param,
      });
    }
    //快捷导航编辑 如果当前项目被打开要更新详情
    if (projectInfo?.projectId === navigatorId) {
      dispatch.project.getProject({
        id: String(navigatorId),
        hideLoading: true,
      });
    }
  };
  const changeNameAndIcon = (v: EditProjectValue) => {
    const param = {
      projectId: projectInfo?.projectId,
      name: v.name,
      icon: v.icon,
      iconColor: v.iconColor,
    };

    apiProjectUpdateTitleIconPost(param).then(() => {
      updateList(param, true);
      // 更新分组详情
      POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, { groupId: projectInfo?.groupId });
    });
  };
  const changeMembers = (v: Member[]) => {
    updateList({}, false);
    if (location.href.includes('/member')) {
      dispatch.project.getMemberList(String(projectInfo?.projectId));
    }
  };

  const { changeProjectStatus } = useProjectStatus({
    onChange: (v: EnumProjectStatus) => {
      const param = {
        projectId: projectInfo?.projectId,
        state: v,
      };
      apiProjectUpdateStatePost({ ...projectInfo, ...param })
        .then(() => {
          updateList(param);
        })
        .then(() => {
          dispatch.project.getPingProjectList({});
          if (getPageIsProjectGroup()) {
            POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, {
              groupId: projectInfo?.groupId,
            });
          } else {
            dispatch.project.updateProjectList({
              projectId: projectInfo?.projectId,
              isPin: false,
            });
          }
        });
    },
  });

  const onUnPing = () => {
    apiProjectPinPut({
      projectId: projectInfo?.projectId,
      isPin: false,
    }).then(() => {
      dispatch.project.getPingProjectList({});
      if (getPageIsProjectGroup()) {
        POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, { groupId: projectInfo?.groupId });
      } else {
        dispatch.project.updateProjectList({
          projectId: projectInfo?.projectId,
          isPin: false,
        });
      }
    });
  };

  const onUnPingGroup = () => {
    apiProjectGroupPinPost({
      groupId: projectInfo?.projectId,
      isPin: false,
    }).then(() => {
      dispatch.project.getPingProjectList({});
      if (getPageIsProjectGroup()) {
        POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, { groupId: projectInfo?.groupId });
      } else {
        dispatch.project.updateProjectList({
          projectId: projectInfo?.projectId,
          isPin: false,
        });
      }
    });
  };

  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_ADD_MANAGER, CAN_SET_STATE, CAN_ADD_EDITOR, CAN_ADD_VIEWER, CAN_SHARE] =
      validatesPermission({
        permissions: permissions,
        key: [
          ProjectPermissionEnum.CAN_EDIT,
          ProjectPermissionEnum.CAN_ADD_MANAGER,
          ProjectPermissionEnum.CAN_SET_STATE,
          ProjectPermissionEnum.CAN_ADD_EDITOR,
          ProjectPermissionEnum.CAN_ADD_VIEWER,
          ProjectPermissionEnum.CAN_SHARE,
          ProjectPermissionEnum.CAN_EXIT,
          ProjectPermissionEnum.CAN_DELETE,
        ],
      }) as boolean[];
    const CAN_EDIT_MEMBER = CAN_ADD_MANAGER || CAN_ADD_EDITOR || CAN_ADD_VIEWER;
    return {
      CAN_EDIT,
      CAN_SET_STATE,
      CAN_ADD_MANAGER,
      CAN_ADD_EDITOR,
      CAN_ADD_VIEWER,
      CAN_EDIT_MEMBER: CAN_EDIT_MEMBER,
      CAN_SHARE,
    };
  }, [permissions]);

  const getData = async () => {
    if (projectInfo?.projectId) {
      const data = await apiProjectStateRecordGet({
        projectId: projectInfo?.projectId + '',
      });
      if (!data) {
        return;
      }
      const { preState, state, operatorName, timestamp } = data;
      setStateInfo({
        preState: preState as EnumProjectStatus,
        state: state as EnumProjectStatus,
        userName: operatorName!,
        updateTime: timestamp,
      });
    }
  };

  const handleGroupRename = () => {
    dispatch.project.editGroup({ projectInfo, isPin: true });
  };

  if (projectInfo?.isGroup) {
    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
        className={s.overlay}
      >
        <Menu
          //triggerSubMenuAction="click"
          className={s.menu}
          onOpenChange={(v: string[]) => {
            if (v.includes('changeState')) {
              getData();
            }
          }}
        >
          <Menu.Item onClick={handleGroupRename} key="A" className={s.item}>
            <Icon name="icon-details_nav_edit" className="mr-7"></Icon>
            {I18N.auto.rename}
          </Menu.Item>
          <Menu.Item
            className={s.item}
            key="D"
            onClick={() => {
              onUnPingGroup();
            }}
          >
            <OperatePin3 className="mr-7 fs-16" />
            {I18N.auto.removeFromNavigation}
          </Menu.Item>
          <Menu.Item key="F" className={classNames(s.item, s.all)}>
            <DeleteProject
              projectInfo={projectInfo}
              onDeleteSuccess={() => {
                dispatch.project.getPingProjectList();
                if (projectInfo?.projectId) {
                  if (projectInfo?.projectId === navigatorId) {
                    dispatch.viewSetting.openNavigator({
                      navigatorId: ProjectListMenuId,
                    });
                  } else {
                    dispatch.project.getProjectList({
                      page: 1,
                      size: store.getState().project.projectList.length,
                    });
                  }
                }
              }}
              text={{
                title: I18N.auto.confirmDelProjectGroup,
                content: I18N.auto.afterProjectDelGroup,
                okText: I18N.auto.delete,
              }}
            >
              <div className={classNames(s.allBtn, s.delete)}>
                <OperateDelete className="mr-7 fs-16" />
                {I18N.auto.delGroup}
              </div>
            </DeleteProject>
          </Menu.Item>
        </Menu>
      </div>
    );
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
      // onMouseDown={(event) => event.stopPropagation()}
      className={s.overlay}
    >
      <Menu
        //triggerSubMenuAction="click"
        className={s.menu}
        onOpenChange={(v: string[]) => {
          if (v.includes('changeState')) {
            getData();
          }
        }}
        // subMenuCloseDelay={100000000}
      >
        {memoPermissions.CAN_EDIT ? (
          <EditProject value={nameAndIcon} onChange={changeNameAndIcon}>
            <Menu.Item key="A" className={s.item}>
              <Icon name="icon-details_nav_edit" className="mr-7"></Icon>
              {I18N.auto.editProject}
            </Menu.Item>
          </EditProject>
        ) : null}
        {memoPermissions.CAN_SET_STATE ? (
          <Menu.SubMenu
            popupClassName={s.stateSubMenu}
            className={s.item}
            key="changeState"
            title={
              <div>
                <Icon name="icon-details_nav_pjstate" className="mr-7"></Icon>
                {I18N.auto.changeProjectStatus}
              </div>
            }

            // expandIcon={<OperateComin size={16} />}
          >
            <Menu.Item
              className={s.item}
              key={EnumProjectStatus.ongoing}
              onClick={() => {
                changeProjectStatus(EnumProjectStatus.ongoing);
              }}
            >
              <ProjectStatusTag status={EnumProjectStatus.ongoing}></ProjectStatusTag>
            </Menu.Item>
            <Menu.Item
              className={s.item}
              key={EnumProjectStatus.pause}
              onClick={() => {
                changeProjectStatus(EnumProjectStatus.pause);
              }}
            >
              <ProjectStatusTag status={EnumProjectStatus.pause}></ProjectStatusTag>
            </Menu.Item>
            <Menu.Item
              className={s.item}
              key={EnumProjectStatus.risk}
              onClick={() => {
                changeProjectStatus(EnumProjectStatus.risk);
              }}
            >
              <ProjectStatusTag status={EnumProjectStatus.risk}></ProjectStatusTag>
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item
              className={s.item}
              key={EnumProjectStatus.done}
              onClick={() => {
                changeProjectStatus(EnumProjectStatus.done);
              }}
            >
              <ProjectStatusTag status={EnumProjectStatus.done}></ProjectStatusTag>
            </Menu.Item>
            <Menu.Item
              className={s.item}
              key={EnumProjectStatus.archive}
              onClick={() => {
                changeProjectStatus(EnumProjectStatus.archive);
              }}
            >
              <ProjectStatusTag status={EnumProjectStatus.archive}></ProjectStatusTag>
            </Menu.Item>
            {stateInfo?.state ? (
              <>
                <Menu.Divider />
                <ProjectStatusRecord value={stateInfo} key="other"></ProjectStatusRecord>
              </>
            ) : null}
          </Menu.SubMenu>
        ) : null}
        {/* {memoPermissions.CAN_EDIT_MEMBER ? (
          <InvitedMembers onChange={changeMembers} projectId={projectInfo?.projectId}>
            <Menu.Item key="C" className={s.item}>
              <Icon name="icon-kit_user_add" className="mr-7"></Icon>
              {I18N.auto.inviteMembers}
            </Menu.Item>
          </InvitedMembers>
        ) : null} */}

        {/* {memoPermissions.CAN_SHARE && showShare ? (
          <Menu.SubMenu
            className={s.item}
            key="D"
            title={
              <div>
                <Icon name="icon-details_nav_share" className="mr-7"></Icon>
                {I18N.auto.share}
              </div>
            }

            // expandIcon={<OperateComin size={16} />}
          >
            <Menu.Item
              className={s.item}
              key="D-1"
              onClick={() => {
                onShare();
              }}
            >
              <Icon name="icon-details_nav_share" className="mr-7"></Icon>
              {I18N.auto.shareToConversation}
            </Menu.Item>
            <Menu.Item
              className={s.item}
              key="D-2"
              onClick={() => {
                onCopy();
              }}
            >
              <Icon name="icon-details_data_file" className="mr-7"></Icon>
              {I18N.auto.copyLink}
            </Menu.Item>
          </Menu.SubMenu>
        ) : null} */}

        <Menu.Item
          className={s.item}
          key="D"
          onClick={() => {
            onUnPing();
          }}
        >
          <OperatePin3 className="mr-7 fs-16" />
          {I18N.auto.removeFromNavigation}
        </Menu.Item>
      </Menu>
    </div>
  );
};

export default Overlay;
