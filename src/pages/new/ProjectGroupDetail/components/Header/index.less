.header {
  height: 64px;
  padding: 0 16px 0 24px;
}

.title {
  font-size: 16px;
  line-height: 22px;
  column-gap: 2px;
  min-width: 0;

  .all__project {
    flex-shrink: 0;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: var(--Brand500);
    }
  }
  .secondary {
    color: var(--TextTertiary);
  }
  .primary {
    color: var(--TextPrimary);
    font-weight: 500;
  }
}

.add__modal {
  :global {
    .rock-modal-body {
      padding: 0 12px !important;
    }
  }
}

.action {
  column-gap: 12px;
  flex-shrink: 0;
}
