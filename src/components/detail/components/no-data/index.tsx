import React, { useMemo } from 'react';

import I18N from '@/utils/I18N';

import s from './index.less';
import { TodoDetailStatus } from '@/types';

const noExist = require('@/assets/images/not-exist.png');
const noPermission = require('@/assets/images/no-permission.png');
const networkErr = require('@/assets/images/network-err.png');

export type Props = {
  status: TodoDetailStatus;
  className?: string;
};

const NoData: React.FC<Props> = (props) => {
  const { status } = props;
  const info = useMemo(() => {
    if (status === TodoDetailStatus.notExist) {
      return {
        img: noExist,
        desc: I18N.auto.toDoHasBeenDeleted,
      };
    }
    if (status === TodoDetailStatus.noPermission) {
      return {
        img: noPermission,
        desc: I18N.auto.noPermissions,
      };
    }
    return {
      img: networkErr,
      desc: I18N.auto.networkRequestLoss,
    };
  }, [status]);
  return (
    <div className={s.todoNoData}>
      <img className={s.todoNoDataImg} src={info.img}></img>
      <div className={s.todoNoDataDesc}>{info.desc}</div>
    </div>
  );
};

export default NoData;
