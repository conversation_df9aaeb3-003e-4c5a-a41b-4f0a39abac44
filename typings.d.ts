import 'umi/typings'
declare module '*.css'
declare module '*.less'
declare module '*.png'
declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement
  const url: string
  export default url
}
declare module 'redux-logger'
declare module 'postcss-plugin-px2rem'

// declare const SentryDSN: string;
// declare const SentryRelease: string;
interface POPOTaskTrackTimeMap {
  firstScript?: number
  layout?: number
  beforeLogin?: number
  loginSuccess?: number
  pagesNew?: number
  onload?: number
}

declare global {
  interface Window {
    POPOTaskTrackTimeMap: POPOTaskTrackTimeMap
    gantt: any
    tanstackTable: any
    virtualizer: any
    expandNodeSetMap: {
      expandAll(): void
      collapseAll(): void
      isAllExpand(): boolean
      getExpandNodeSet(viewId: number): Set<string | number>
    }
    fontSizeScaleRatio: number
    popo_bridge_init_data: any
  }
}
