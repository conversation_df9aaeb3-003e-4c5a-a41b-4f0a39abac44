.batchBox {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 4px;
  background-color: var(--bgBottom);
  border: 1px solid var(--aBlack8);
  box-shadow: var(--ComBoxShadow);
  .selectCount {
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: var(--TextPrimary);
  }
  :global {
    .rock-dropdown-trigger-primary,
    .rock-dropdown-trigger-default {
      height: 24px !important;
    }
    .rock-dropdown-trigger-default {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
  }
  .line {
    width: 1px;
    height: 16px;
    margin: 0 12px;
    background-color: var(--aBlack10);
  }
}
.deleteIcon {
  color: var(--IconBlack);
  margin-right: 12px;
}
.hideLine {
  border-top: none;
}
.tip {
  position: absolute;
  top: 26px;
  left: 0px;
  right: 0px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 26px;
  background-color: var(--R50);
  color: var(--R500);
  font-size: 12px;
}
