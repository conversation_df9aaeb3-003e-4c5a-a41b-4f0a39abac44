import { VTable } from '@visactor/vtable-gantt'
import { ColumnIconOption, IconPosition } from '@visactor/vtable/es/ts-types'

export const dragReorderIcon: ColumnIconOption = {
  type: 'svg',
  svg: '<svg width="16" height="16" viewBox="0 0 7 16" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M2 11a1 1 0 1 1 0 2 1 1 0 0 1 0-2m3 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2M2 7a1 1 0 1 1 0 2 1 1 0 0 1 0-2m3 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2M2 3a1 1 0 1 1 0 2 1 1 0 0 1 0-2m3 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/></svg>',
  width: 16,
  height: 16,
  funcType: VTable.TYPES.IconFuncTypeEnum.dragReorder,
  name: 'dragReorder',
  positionType: IconPosition.left,
  visibleTime: 'mouseenter_cell',
  cursor: 'pointer',
  hover: {
    width: 16,
    height: 16,
    bgColor: 'rgba(0,0,0,0.06)',
  },
}
