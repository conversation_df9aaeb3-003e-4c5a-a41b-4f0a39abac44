import { DropdownProps } from '@bedrock/components/lib/Dropdown';
import { PropsWithChildren } from 'react';

import { Dropdown, MenuSelect, Tooltip } from '@/components/basic';

import { MenuselectItem } from '../menu-select';

interface TabsProps extends DropdownProps {
  onChange: (v: string) => void;
  tooltip?: string;
  value?: string | number;
  data: MenuselectItem[];
  className?: string;
  menuClassName?: string;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

const DropdownSelect: React.FC<PropsWithChildren<TabsProps>> = (props) => {
  const {
    children,
    data,
    onChange,
    tooltip,
    className,
    menuClassName,
    visible,
    onVisibleChange,
    value,
  } = props;
  return (
    <Dropdown
      {...props}
      className={className}
      open={visible}
      onOpenChange={onVisibleChange}
      overlay={
        <MenuSelect
          className={menuClassName}
          data={data}
          value={value}
          onChange={(value) => {
            onChange(value);
          }}
        ></MenuSelect>
      }
      trigger="click"
    >
      <Tooltip title={tooltip ? tooltip : undefined}>{children}</Tooltip>
    </Dropdown>
  );
};
export default DropdownSelect;
