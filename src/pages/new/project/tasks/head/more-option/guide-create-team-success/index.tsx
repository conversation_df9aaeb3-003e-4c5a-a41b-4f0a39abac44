import { TooltipPlacement } from '@bedrock/components/lib/Tooltip';
import { PropsWithChildren, useEffect, useMemo } from 'react';

import { Guide } from '@/components/basic';
import useGuide, { TaskGuide } from '@/components/guide/useGuide';
import { Permission } from '@/types';
import { GuideType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  className?: string;
  visible?: boolean;
  onOk?: () => void;
  placement?: TooltipPlacement;
  hasTeam?: boolean;
  permissions?: Permission[];
}

const GuideCreateTeamSuccess: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, onOk } = props;
  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.createTeamSuccess,
  });

  const steps = useMemo(() => {
    return [
      {
        title: I18N.auto.theProjectGroupHasBeenCreated,
        content: (
          <div>
            <div>{I18N.auto.clickJumpGTeam}</div>
            <div>{I18N.auto.setTaskNotify}</div>
          </div>
        ),
        selector: '.guide_create_team',
        placement: 'topRight',
      },
    ];
  }, []);

  useEffect(() => {
    TaskGuide.addListener(GuideType.createTeamSuccess, () => {
      getGuideData().then((ret) => {
        //接口判定不显示引导
        if (!ret) {
          TaskGuide.removeGuide(GuideType.createTeamSuccess, true);
        }
      });
    });
    TaskGuide.start(GuideType.createTeamSuccess);
  }, []);
  const end = () => {
    handleHideGuide(); //内部会调用removeGuide
    onOk?.();
  };

  return (
    <>
      <Guide
        visible={showGuide}
        scrollType="fixed"
        padding={2}
        beforeText={I18N.auto.iGotIt}
        nextText={I18N.auto.iGotIt}
        stepText=" "
        steps={steps}
        afterStepChange={(step: any, index: number) => {
          if (index >= 1) {
            document.body.style.overflow = '';
            end();
          }
        }}
        onClose={() => {}}
        beforeStepChange={(step: any, index: number) => {
          document.body.style.overflow = 'hidden';
        }}
        onMaskClick={() => {
          document.body.style.overflow = '';
          end();
        }}
        maskClassName={s.mask}
        popoverClassName={s.guide}
      />

      <div className="guide_create_team">{children}</div>
    </>
  );
};

export default GuideCreateTeamSuccess;
