import { FieldOptionColor } from '@/types/custom-field';

/**
 * 获取自定义颜色，传入的是颜色的原本色值（bedrock中的值），然后根据亮度，取对应的透明度，一般为20%、40%
 * @param color 原始的色值
 * @param bright 亮度的别名，default为40%，bright为20%，默认值为bright；黄色要特殊处理，bright为30%，default为50%
 * @returns 返回对应的颜色值
 */
export function getCustomColor(color: string, bright: 'default' | 'bright' = 'bright'): string {
  let alpha = bright === 'default' ? '66' : '33';
  if (color === FieldOptionColor.pibg_yellow) {
    alpha = bright === 'default' ? '80' : '4D';
  }
  return `${color}${alpha}`;
}
