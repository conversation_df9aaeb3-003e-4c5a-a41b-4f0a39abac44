import { ImAdd1, OperateSeet } from '@babylon/popo-icons';
import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Icon, Menu, POPODropdown, Tooltip } from '@/components/basic';
import ManageView from '@/components/basic-project/manage-view';
import NewView from '@/components/basic-project/new-view';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  className?: string;
  addDisabledTip?: string;
};

const AddViewDropdown: React.FC<PropsWithChildren<Props>> = (props) => {
  const { addDisabledTip } = props;

  const { children } = props;
  return (
    <POPODropdown
      title={children}
      className={s.sortDropdown}
      clickItemCloseDropdown
      placement="bottomLeft"
    >
      <div className={s.list}>
        {addDisabledTip ? (
          <Tooltip title={addDisabledTip}>
            <div
              className={classNames(s.item, {
                [s.disableItem]: !!addDisabledTip,
              })}
              key={I18N.auto.newView}
            >
              <Icon name="icon-sys_add" fontSize={16} />
              <div className={s.name}>{I18N.auto.newView}</div>
            </div>
          </Tooltip>
        ) : (
          <NewView key={I18N.auto.newView}>
            <div
              className={classNames(s.item, {
                [s.disableItem]: !!addDisabledTip,
              })}
              key={I18N.auto.newView}
            >
              <Icon name="icon-sys_add" fontSize={16} />
              <div className={s.name}>{I18N.auto.newView}</div>
            </div>
          </NewView>
        )}

        <ManageView key={I18N.auto.managementView_2}>
          <div className={s.item} key={I18N.auto.managementView_2}>
            <OperateSeet className="fs-16 mr-8" />
            <div className={s.name}>{I18N.auto.managementView_2}</div>
          </div>
        </ManageView>
      </div>
    </POPODropdown>
  );
};

export default AddViewDropdown;
