import classNames from 'classnames';
import { PropsWithChildren, useMemo, useState } from 'react';

import { Dropdown } from '@/components/basic';
import { Permission, ProjectInfo } from '@/types';
import { EnumRole } from '@/utils/const';
import {
  ProjectPermissionEnum,
  Role_Admin,
  Role_Members,
  Role_ViewOnly,
  RoleList,
  validatesPermission,
} from '@/utils/permission';

import s from './index.less';
import Overlay from './overlay';
import { ImTriangleDownSolid1 } from '@babylon/popo-icons';

interface Props {
  disabled?: boolean;
  className?: string;
  value?: EnumRole;
  onChange?: (v: EnumRole) => void;
  canAddManager?: boolean;
  canAddEditor?: boolean;
  canAddViewer?: boolean;
  showExit?: boolean;
  onExit?: () => void;
  showRemove?: boolean;
  onRemove?: () => void;
  isMe?: boolean;
  permissions?: Permission[];
  projectInfo?: ProjectInfo;
}

const SwitchPermission: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    disabled,
    className,
    value = EnumRole.members,
    onChange,
    showExit,
    onExit,
    showRemove,
    onRemove,
    isMe,
    permissions,
    projectInfo,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const showName = useMemo(() => {
    return RoleList.find((item) => item.value === value)?.name;
  }, [value]);
  const memoOptions = useMemo(() => {
    const [CAN_ADD_MANAGER, CAN_ADD_EDITOR, CAN_ADD_VIEWER] = validatesPermission({
      permissions: permissions,
      key: [
        ProjectPermissionEnum.CAN_ADD_MANAGER,
        ProjectPermissionEnum.CAN_ADD_EDITOR,
        ProjectPermissionEnum.CAN_ADD_VIEWER,
      ],
    }) as boolean[];
    const options = [];
    if (CAN_ADD_MANAGER) {
      if (isMe) {
        return [Role_Admin];
      } else {
        options.push(Role_Admin);
      }
    }
    if (CAN_ADD_EDITOR) {
      if (isMe) {
        return [Role_Members];
      } else {
        options.push(Role_Members);
      }
    }
    if (CAN_ADD_VIEWER) {
      if (isMe) {
        return [Role_ViewOnly];
      } else {
        options.push(Role_ViewOnly);
      }
    }
    if (isMe) {
      return [Role_ViewOnly];
    }
    return options;
  }, [isMe, permissions]);
  const memoDisabled = useMemo(() => {
    if (isMe) {
      return false;
    }
    const [CAN_REMOVE_MANAGER, CAN_REMOVE_EDITOR, CAN_REMOVE_VIEWER] = validatesPermission({
      permissions: permissions,
      key: [
        ProjectPermissionEnum.CAN_REMOVE_MANAGER,
        ProjectPermissionEnum.CAN_REMOVE_EDITOR,
        ProjectPermissionEnum.CAN_REMOVE_VIEWER,
      ],
    }) as boolean[];
    if (disabled || !memoOptions.length) {
      return true;
    }
    //判断当前角色
    //管理员
    if (value === EnumRole.admin) {
      return !CAN_REMOVE_MANAGER;
    }
    //编辑者
    if (value === EnumRole.members) {
      return !CAN_REMOVE_EDITOR;
    }
    //查看者
    if (value === EnumRole.viewOnly) {
      return !CAN_REMOVE_VIEWER;
    }
    return true;
  }, [disabled, memoOptions, value, permissions, isMe]);

  return (
    <div className={classNames(s.iconSelect, className)}>
      <Dropdown
        className={classNames(s.dropdown, {
          disabled: memoDisabled,
        })}
        title={<div className={s.btnValue}>{showName}</div>}
        trigger="click"
        arrow={
          <ImTriangleDownSolid1
            className={classNames(s.arrow, {
              [s.disabled]: memoDisabled,
            })}
          />
        }
        defaultOpen={visible}
        disabled={memoDisabled}
        open={visible}
        placement="bottomLeft"
        onOpenChange={(v) => {
          setVisible(v);
        }}
        destroyPopupOnHide
        minOverlayWidthMatchTrigger={false}
        overlay={
          <Overlay
            value={value}
            projectInfo={projectInfo}
            onChange={(v) => {
              if (value !== v) {
                onChange?.(v);
              }
              setVisible(false);
            }}
            options={memoOptions}
            showRemove={showRemove}
            onRemove={() => {
              onRemove?.();
              setVisible(false);
            }}
            onExit={() => {
              onExit?.();
              setVisible(false);
            }}
            showExit={showExit}
          ></Overlay>
        }
        onOverlayClick={() => {
          setVisible(false);
        }}
      />
    </div>
  );
};

export default SwitchPermission;
