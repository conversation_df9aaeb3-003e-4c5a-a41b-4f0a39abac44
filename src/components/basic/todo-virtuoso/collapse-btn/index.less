.groupHead {
  display: flex;
  align-items: center;
  height: 44px;
  border-bottom: 1px solid var(--aBlack8);
  cursor: pointer;
  &:hover {
    .addcalendar {
      display: flex;
    }
  }
}

.collapseItem {
  display: flex;
  align-items: center;
  line-height: 20px;
  color: var(--TextPrimary);
  font-size: 13px;
}

.collapseIcon {
  width: 20px;
  height: 20px;
  transform: rotate(90deg);
}
.collapse {
  transform: rotate(0deg);
}

.headDesc {
  display: flex;
  align-items: center;
  margin-left: 8px;
  margin-right: 16px;
}
.btn {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 4px;
  border-radius: 4px;
  &:hover {
    background-color: var(--aBlack10);
  }
}

.addcalendar {
  display: none;
  color: var(--TextSecondary);
}

.unaddcalendar {
  color: var(--TextTertiary);
}

.icon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}
