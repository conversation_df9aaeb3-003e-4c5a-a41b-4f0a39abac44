.overlay {
  min-width: 200px;
}
.stateSubMenu {
  min-width: 160px !important;
}
.menu {
  padding: 12px 20px 20px;
  background-color: var(--bgTop);
  border-radius: 8px;
  box-shadow: var(--ComBoxShadow);
}

.item {
  display: flex;
  align-items: center;
  color: var(--TextPrimary);
  font-weight: 400;
  max-width: 192px;
  :global {
    .rock-dropdown-menu-submenu-title {
      width: 100%;
      margin: 0 4px;
      color: var(--TextPrimary);
    }
  }
}
.groupSubMenu {
  :global {
    .rock-dropdown-menu {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
.group__content {
  width: 100%;

  .group__check {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--Brand600);
  }
}

.divider {
  margin-top: 4px !important;
  margin-bottom: 4px;
}

.all {
  padding: 0;
}
.allBtn {
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  padding: 5px 12px;
  padding-right: 40px;
}
.delete {
  color: var(--R600);
}
