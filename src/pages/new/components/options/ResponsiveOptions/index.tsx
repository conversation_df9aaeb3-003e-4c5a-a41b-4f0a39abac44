import React from 'react';

import { SoftZd } from '@babylon/popo-icons';
import classNames from 'classnames';
import { useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as _ from 'lodash';

import { Button, Icon, Popover } from '@/components/basic';
import { FilterGuide } from '@/components/guide/filter-guide';
import useGuide from '@/components/guide/useGuide';
import { Dispatch, RootState } from '@/models/store';
import { isProjectId, isTaskMenuId } from '@/models/utils';
import {
  EnumKanbanAddSource,
  EnumListAddSource,
  GuideType,
  ViewAddSource,
  ViewType,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import {
  ProjectPermissionEnum,
  validatesPermission,
  ViewPermissionEnum,
  ViewTypeEnum,
} from '@/utils/permission';

import CardConfig from '../card-config';
import FieldsSetting from '../fields-setting';
import Filter from '../filter';
import Group from '../group';
import s from '../index.less';
import Sort from '../sort';
import SaveViewBtn from '../save-view-btn';
import { useParams } from 'umi';
import MoreOptions from '../MoreOptions';

const ResponsiveOptions = () => {
  const dispatch = useDispatch<Dispatch>();
  const { currentViewTab, permissions, viewTabList, projectInfo, navigatorId } = useSelector(
    (state: RootState) => ({
      currentViewTab: state.viewSetting.currentViewTab,
      permissions: state.viewSetting.permissions,
      viewTabList: state.viewSetting.viewTabList,
      projectInfo: state.project.projectInfo,
      navigatorId: state.viewSetting.navigatorId,
    })
  );

  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.filter,
  });
  const ref = useRef<{ changeVisible: (v: boolean) => void }>({
    changeVisible: () => {},
  });

  const isTaskMenuTab = useMemo(() => {
    return isTaskMenuId(navigatorId as number);
  }, [navigatorId]);

  useEffect(() => {
    //筛选引导目前只放在任务快捷视图下
    if (isTaskMenuTab) {
      getGuideData();
    }
  }, [isTaskMenuTab]);

  const filter = (
    <Popover
      overlayClassName={s.guidePopover}
      arrowPointAtCenter
      placement={'bottomLeft'}
      trigger={'click'}
      visible={showGuide}
      content={
        <FilterGuide
          onClick={() => {
            handleHideGuide();
          }}
          onExpClick={() => {
            handleHideGuide().then(() => {
              ref.current.changeVisible(true);
            });
          }}
        />
      }
    >
      <Filter ref={ref}></Filter>
    </Popover>
  );

  const sort = <Sort />;

  const group = <Group />;

  const fieldSetting = currentViewTab.viewType !== ViewType.timeline && (
    <FieldsSetting>
      <Button
        className={classNames('mr-8', s.hidden, 'task-fields-setting')}
        type="text-subtle"
        icon={<SoftZd className="fs-16" />}
      >
        {I18N.auto.fieldConfiguration}
      </Button>
    </FieldsSetting>
  );

  // 看板-> 卡片配置
  const cardConfig = currentViewTab.type === ViewType.kanban && <CardConfig />;

  const shownOptions = [filter, sort, group, fieldSetting, cardConfig];

  return <div></div>;
};

export default ResponsiveOptions;
