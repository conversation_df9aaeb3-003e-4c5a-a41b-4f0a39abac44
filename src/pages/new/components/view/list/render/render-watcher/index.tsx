import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import {
  apiTaskFollowerAddPost,
  apiTaskFollowerDeletePost,
  apiTaskFollowerDeleteSelfPost,
  apiTaskFollowerListPost,
} from '@/api';
import { RenderPeoples, WatcherPicker } from '@/components/basic';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { Dispatch, RootState, store } from '@/models/store';
import { TodoInfo, UserInfo } from '@/types';
import { onPageVisibleChange } from '@/utils';
import {
  PeoplePickerType,
  TaskNavigatorType,
  TaskTableRowType,
  TaskTableRowTypeAdd,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  taskInfo: TodoInfo;
}

const RenderExecutor: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const {
    taskId,
    assigner,
    _rowType: rowType,
    followers,
    followerCount,
    assigneeUids,
    followerUids,
  } = taskInfo;
  const { assignerUid } = assigner || {};
  const { userInfo } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
  }));
  const updateRef = useRef<boolean>(false);
  const [list, setList] = useState<UserInfo[]>([]);
  const [active, setActive] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();
  const isAdd = rowType === TaskTableRowType.add;

  const permissions = useGetPermissions({ taskId });

  const updateExecutor = async (users: UserInfo[]) => {
    const uids: string[] = users.map((item) => item.uid!);
    if (rowType === TaskTableRowType.add) {
      dispatch.task.updateTodoList({
        taskId: taskId,
        ...taskInfo,
        followers: users,
        followerCount: users.length,
      });
    } else {
      //list
      const olduids = list.map((v) => v.uid);
      let reUids = olduids.filter((item) => !uids.includes(item!));
      let addedUids = uids.filter((item) => !olduids.includes(item));
      setList(users);
      if (addedUids.length) {
        updateRef.current = true;
        await apiTaskFollowerAddPost({
          taskId: taskId,
          uids: addedUids,
        });
        // 不更新列表 关闭弹框的时候更新
        if (reUids.includes(userInfo?.uid)) {
          dispatch.viewSetting.refreshDataByDataChange({
            refreshList: false,
            refreshCount: true,
          });
        }
        dispatch.task.updateTodoList({
          taskId,
          ...taskInfo,
          followers: users,
          followerUids: uids,
          followerCount: users.length,
        });
      }
      if (reUids.length) {
        updateRef.current = true;
        let removeIdsExcludeSelf = reUids;
        if (reUids.includes(userInfo?.uid)) {
          await apiTaskFollowerDeleteSelfPost({
            taskId: taskId,
          });
          removeIdsExcludeSelf = reUids.filter((item) => item !== userInfo?.uid);
        }

        if (removeIdsExcludeSelf.length) {
          await apiTaskFollowerDeletePost({
            taskId: taskId,
            uids: removeIdsExcludeSelf,
          });
        }
        // 不更新列表 关闭弹框的时候更新
        if (reUids.includes(userInfo?.uid)) {
          // 全部任务和我关注的需要刷新 现在是全部刷新
          const navigatorId = store.getState().viewSetting.navigatorId;
          if (
            navigatorId &&
            [TaskNavigatorType.allTask, TaskNavigatorType.myFollow].includes(
              navigatorId as TaskNavigatorType
            )
          ) {
            dispatch.viewSetting.refreshDataByDataChange({
              refreshList: true,
              refreshCount: true,
            });
          }
        } else {
          dispatch.task.updateTodoList({
            taskId,
            ...taskInfo,
            followers: users,
            followerUids: uids,
            followerCount: users.length,
          });
        }
      }
    }
  };

  const change = (users: UserInfo[]) => {
    // 新增逻辑
    if (isAdd) {
      setList(users);
      dispatch.task.updateTodoList({
        taskId,
        ...taskInfo,
        followers: users,
      });
      return null;
    }
    updateExecutor(users);
  };

  useEffect(() => {
    onPageVisibleChange((pageShow) => {
      if (!pageShow) {
        setVisible(false);
      }
    });
  }, []);

  useEffect(() => {
    if (taskId && active) {
      // 判断当前是不是新增
      if (String(taskId).includes(TaskTableRowTypeAdd)) {
        return;
      }
      apiTaskFollowerListPost({ taskId: taskId }).then((res) => {
        const _list = [...(res.followers || [])];
        setList(_list);
      });
    }
    // followerUids 变化需要重新刷一下列表
  }, [taskId, active]);

  // 修改详情的时候会重新刷新列表
  useEffect(() => {
    setList(followers || []);
  }, [followers]);

  const changeVisible = (v: boolean) => {
    if (memoPermissions.CAN_EDIT || memoPermissions.CAN_CANCEL_OBSERVER) {
      setVisible(v);
      if (!v && updateRef.current) {
        // dispatch.viewSetting.refreshDataByDataChange({
        //   refreshList: true,
        //   refreshCount: false,
        // });

        dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
      }
    }
  };

  const memoPermissions = useMemo(() => {
    const [CAN_VIEW, CAN_EDIT, CAN_CANCEL_OBSERVER] = validatesPermission({
      permissions: permissions,
      key: [
        TaskPermissionEnum.CAN_VIEW,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[];
    return {
      CAN_VIEW: CAN_VIEW || rowType === TaskTableRowType.add,
      CAN_EDIT: CAN_EDIT || rowType === TaskTableRowType.add,
      CAN_CANCEL_OBSERVER: CAN_CANCEL_OBSERVER || rowType === TaskTableRowType.add,
      disabled: !CAN_VIEW && rowType !== TaskTableRowType.add,
    };
  }, [permissions, rowType]);
  return (
    <AddRowBox
      iconName="icon-kit_user_add"
      tipTitle={list.length ? '' : I18N.auto.addAsFollow}
      taskId={taskInfo.taskId}
      hasDelete={!!list.length && memoPermissions.CAN_EDIT}
      focused={visible}
      onClick={() => {
        changeVisible(!visible);
      }}
      onClear={() => {
        change([]);
      }}
      disabled={memoPermissions.disabled}
    >
      <PeoplePickerContext.Provider
        value={{
          assignerUid: assignerUid,
          assigneeUids: assigneeUids,
          followerUids: followerUids,
          pickerType: PeoplePickerType.watcher,
          currentUser: userInfo,
        }}
      >
        <WatcherPicker
          taskId={taskId}
          disabled={memoPermissions.disabled}
          assignerUid={assignerUid!}
          search
          value={[...list]}
          onChange={change}
          hasMinimum
          visible={visible}
          onVisible={changeVisible}
          hasArrow={false}
          canDelete={false}
          canDeleteSelf={memoPermissions.CAN_CANCEL_OBSERVER && !memoPermissions.CAN_EDIT}
          canSelect={memoPermissions.CAN_EDIT}
          placeholder=" "
        >
          {list.length ? (
            <>
              <div className={s.listPeopleItem}></div>
              <RenderPeoples
                showFinishedIcon
                list={list || []}
                count={followerCount || list.length}
                maxShowCount={3}
                avatarClassName="mr-4"
              ></RenderPeoples>
            </>
          ) : null}
        </WatcherPicker>
      </PeoplePickerContext.Provider>
    </AddRowBox>
  );
};

export default RenderExecutor;
