.renderPeoples {
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden;
  // padding-left: 4px;
  // margin-left: -4px;
}

.avatarBox {
  display: flex;
  position: relative;
  flex-shrink: 0;
  height: 22px;
  width: 22px;
  margin-right: 8px;
  .avatar {
    height: 22px;
    width: 22px;
    border-radius: 22px;
    background: url('~@/assets/images/people.png') center center no-repeat;
    background-size: 100%;
    :glabol {
      .rock-icon {
        font-size: 22px;
      }
    }
  }
  .finishedIcon {
    display: inline-block;
    position: absolute;
    right: -2px;
    bottom: -1px;
    width: 10px;
    height: 10px;
    background: url('~@/assets/images/finished.png') center center no-repeat;
    background-size: 100%;
  }
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  margin-bottom: 2px;
  margin-left: -4px;
  margin-right: -4px;
  padding: 0 4px;
  font-size: 13px;
  border-radius: 4px;
  color: var(--TextPrimary);
  cursor: pointer;
  &:hover {
    background-color: var(--aBlack6);
    .right {
      display: flex;
      flex-shrink: 0;
    }
  }
}

.info {
  display: flex;
  flex: 1;
  min-width: 1px;
  overflow: hidden;
}
.infobox {
  width: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.right {
  display: none;
  align-items: center;
}
.delete {
  width: 22px;
  height: 22px;
  flex-shrink: 0;
  color: var(--IconTertiary);
}
