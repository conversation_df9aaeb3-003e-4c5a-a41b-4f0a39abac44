.detailTransferCreator {
  .transfer {
    .open {
      background-color: transparent !important;
    }
    .peoplePickerWrap {
      .label {
        background-color: transparent;
      }
    }
  }
}

.hoverIcon {
  background-color: var(--aBlack6);
  border-radius: 6px;
}

:global {
  .transferCreatorModal {
    .rock-modal-content {
      .rock-modal-confirm-header {
        display: flex !important;
      }
      .rock-radio-group {
        margin-top: 20px;
        .rock-radio-wrapper {
          font-size: 13px;
          color: var(--TextPrimary);
          &:first-child {
            margin-bottom: 10px;
          }
        }
      }
      // .rock-icon {
      //   display: none;
      // }
    }
    .rock-modal-body {
      padding: 16px 20px 20px 20px !important;
    }
  }
}

.transferRadioGroup {
  display: flex;
  flex-direction: column;
}
