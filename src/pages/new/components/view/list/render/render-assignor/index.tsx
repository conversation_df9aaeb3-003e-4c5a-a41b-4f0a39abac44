import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React from 'react';

import { AvatarPeople } from '@/components/basic';
import { TodoInfo } from '@/types';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  taskInfo: TodoInfo;
}

const RenderAssignor: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const { assigner } = taskInfo;

  return (
    <AddRowBox focused={false} taskId={taskInfo.taskId} className={s.assignerBox}>
      <div className={s.assigner}>
        <AvatarPeople
          className={classNames(s.avatar, { [s.disabled]: true })}
          avatarUrl={assigner?.assignerAvatarUrl}
          onClick={(e) => {
            e.stopPropagation();
            pp.openUserProfile({
              uid: assigner!.assignerUid!,
            });
          }}
        ></AvatarPeople>
      </div>
    </AddRowBox>
  );
};

export default RenderAssignor;
