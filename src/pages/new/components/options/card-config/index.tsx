import { <PERSON>16<PERSON>ntd, <PERSON><PERSON><PERSON>pcolor, OperateEdit, <PERSON>K<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@babylon/popo-icons';
import { ConfigProvider } from '@bedrock/components';
import classNames from 'classnames';
import { useContext, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, Dropdown, Icon, Popover, Switch } from '@/components/basic';
import { useFieldForm } from '@/components/field-edit-popover';
import { PopoverItem } from '@/components/field-edit-popover/popoverItem';
import { useGetShownFields } from '@/hooks';
import { Dispatch, RootState } from '@/models/store';
import { FieldTypeEnum } from '@/types/custom-field';
import { DisplayField, EnumField } from '@/utils/fields';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import Container from '../overlay-container';
import s from './index.less';
import I18N from '@/utils/I18N';

/**
 * 卡片配置
 * @returns
 */
const CardConfig = ({ className = '', ...rest }: { className?: string }) => {
  const dispatch = useDispatch<Dispatch>();
  const list = useGetShownFields();
  const [visible, setVisible] = useState(false);

  const { parsedViewConfig, perms } = useSelector((state: RootState) => {
    let view = state.viewSetting.currentViewTab;
    return {
      parsedViewConfig: view.parsedViewConfig,
      perms: state.viewSetting.permissions,
    };
  });

  const cardConfig = parsedViewConfig?.cardConfig;

  const { openModal } = useFieldForm({ dispatch });

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext);
  const zIndex = useMemo(() => getGlobalZIndex(), []);

  const handleSelect = (item: DisplayField) => {
    dispatch.viewSetting.updateViewConfig({
      cardConfig: { ...cardConfig, siderColor: item.id },
    });
  };

  const handleSwitchChange = (checked: boolean) => {
    dispatch.viewSetting.updateViewConfig({
      cardConfig: { ...cardConfig, showCompleteIcon: checked },
    });
  };

  const configList = useMemo(() => {
    // 单选 且 不是所属项目
    const filterList = list.filter(
      (item) => item.fieldType === FieldTypeEnum.option && item.fieldName !== EnumField.project
    );

    filterList.unshift({
      name: I18N.auto.nothing, // 显示名称
      fieldName: '', //服务端字段,或者自定义字段id
      visible: 1,
      id: undefined,
      customFieldId: undefined,
    });
    return filterList;
  }, [list]);

  const selectedField = useMemo(() => {
    if (!cardConfig?.siderColor) {
      return configList[0];
    }
    const field = configList.find((item) => item.id === cardConfig?.siderColor);
    return field || { name: I18N.auto.nothing };
  }, [configList, cardConfig]);

  const handleEditField = (e: React.MouseEvent, fieldItem: DisplayField) => {
    e.stopPropagation();
    openModal({ fieldId: fieldItem.id });
    setVisible(false);
  };

  const fieldEditable = useMemo(() => {
    return validatesPermission({
      key: ProjectPermissionEnum.CAN_MANAGE_FIELD,
      permissions: perms,
    });
  }, [perms]);

  return (
    <Dropdown
      className={s.dropdown}
      style={{ padding: 0 }}
      zIndex={zIndex}
      title={
        <Button
          className={classNames('mr-8', className, s.group)}
          type="text-subtle"
          icon={<SoftKp className="fs-16" />}
        >
          <div className={s.groupDsc}>
            {I18N.auto.cardConfig}: {selectedField?.name}
          </div>
        </Button>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      open={visible}
      onOpenChange={(v) => {
        setVisible(v);
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      {...rest}
      overlay={
        <Container className={s.config__pane} title={I18N.auto.cardConfig}>
          <Popover
            showArrow={false}
            placement="rightTop"
            trigger="hover"
            align={{ offset: [-2, 0] }}
            overlayClassName={s.config__popover}
            content={configList.map((item) => (
              <PopoverItem
                className="config__popover--item"
                key={item.fieldName}
                left={<span>{item.name}</span>}
                right={
                  <span className={s['config__popover--item--right']}>
                    {fieldEditable && item.customFieldId && (
                      <span
                        onClick={(e) => handleEditField(e, item)}
                        className="operate__icon flex-center"
                      >
                        <OperateEdit />
                      </span>
                    )}
                    <ToolCheck
                      className={classNames(s.check__icon, {
                        [s.inactive]: cardConfig?.siderColor !== item.id,
                      })}
                    />
                  </span>
                }
                level="secondary"
                interactive
                onClick={() => {
                  handleSelect(item);
                }}
              >
                {item.name}
              </PopoverItem>
            ))}
          >
            <PopoverItem
              style={{ cursor: 'pointer' }}
              left={
                <div className={`${s['config__option--left']} flex-y-center`}>
                  <Data16Kpcolor
                    className="fs-16 mr-4"
                    style={{
                      '--svgIconColor2': 'var(--aBlack)',
                      '--svgIconColor1': 'var(--TextSecondary)',
                    }}
                  />
                  <span className={s['config__item--label']}>{I18N.auto.cardSideColor}</span>
                </div>
              }
              right={
                <span className={s['config__option--right']}>
                  <span className="ellipsis">{selectedField?.name}</span>
                  <Icon
                    style={{ position: 'relative', top: '1px' }}
                    name="icon-sys_open"
                    fontSize={16}
                  />
                </span>
              }
            />
          </Popover>
          <PopoverItem
            left={
              <div className={`${s['config__option--left']} flex-y-center`}>
                <Data16Intd
                  className="fs-16 mr-4"
                  style={{
                    '--svgIconColor2': 'var(--aBlack)',
                    '--svgIconColor1': 'var(--TextSecondary)',
                  }}
                />
                <span className={s['config__item--label']}>{I18N.auto.completionIcon}</span>
              </div>
            }
            right={
              <span className={`${s.sub__title} flex-y-center ellipsis`}>
                <Switch checked={cardConfig?.showCompleteIcon} onChange={handleSwitchChange} />
              </span>
            }
          />
        </Container>
      }
    />
  );
};

export default CardConfig;
