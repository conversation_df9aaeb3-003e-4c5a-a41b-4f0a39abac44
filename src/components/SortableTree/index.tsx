import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragStartEvent,
  DragOverlay,
  DragMoveEvent,
  DragEndEvent,
  DragOverEvent,
  MeasuringStrategy,
  Modifier,
  UniqueIdentifier,
  DropAnimation,
  defaultDropAnimation,
  KeyboardSensor,
  CollisionDetection,
  Collision,
} from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'

import { SortableContext, arrayMove, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'

import { buildTree, flattenTree, getProjection, removeChildrenOf, setProperty } from './utilities'
import type { SensorContext } from './types'
import { SortableTreeItem } from './components'
import './index.less'
import { ProjectItemStatus } from '@/types'
import projectGroupFoldStatus from '@/utils/projectGroupFoldStatus'
import { POPOBridgeEmitter } from '@popo-bridge/web'
import { EnumEmitter } from '@/utils/const'
import { LoadMore } from '@bedrock/components'
import { useVirtualizer } from '@tanstack/react-virtual'
import { useLatest } from 'ahooks'

const measuring = {
  droppable: {
    strategy: MeasuringStrategy.WhileDragging,
  },
}

const dropAnimationConfig: DropAnimation = {
  keyframes({ transform }) {
    return [
      { opacity: 1, transform: CSS.Transform.toString(transform.initial) },
      {
        opacity: 0,
        transform: CSS.Transform.toString({
          ...transform.final,
          x: transform.final.x + 5,
          y: transform.final.y + 5,
        }),
      },
    ]
  },
  easing: 'ease-out',
  sideEffects({ active }) {
    active.node.animate([{ opacity: 0 }, { opacity: 1 }], {
      duration: defaultDropAnimation.duration,
      easing: defaultDropAnimation.easing,
    })
  },
}

interface Props<T> {
  collapsible?: boolean
  draggable?: boolean
  treeData?: T[]
  indentationWidth?: number
  indicator?: boolean
  removable?: boolean
  itemRender?: (item: T) => React.ReactNode
  handlerRender?: (item: T) => React.ReactNode
  isPin?: boolean
  canLoadMore?: boolean
  loadMore?: () => Promise<any>
  scrollContainer: HTMLElement | null
  showCollapse?: boolean
  selectedId?: UniqueIdentifier
  onDragEnd?: (
    items: T[],
    {
      activeIndex,
      overIndex,
      depth,
      newParentId,
      oldParentId,
      activeId,
      preId,
      nextId,
    }: {
      activeIndex: number
      overIndex: number
      depth: number
      newParentId: UniqueIdentifier | null
      oldParentId: UniqueIdentifier | null
      activeId: UniqueIdentifier
      preId: UniqueIdentifier | null
      nextId: UniqueIdentifier | null
    },
  ) => void
  overlayStyle?: React.CSSProperties
  overlayClassName?: string
  estimateSize?: (index: number, flattenedItems: any[]) => number
  rowOffset?: number
  flattenDepth?: number
}
window.fontSizeScaleRatio = window.fontSizeScaleRatio || 1
export const SortableTree = forwardRef(
  (
    {
      collapsible,
      treeData,
      indicator = false,
      indentationWidth = 35,
      itemRender,
      handlerRender,
      onDragEnd,
      isPin,
      canLoadMore,
      loadMore,
      scrollContainer,
      showCollapse,
      selectedId,
      overlayStyle,
      estimateSize,
      rowOffset = 0,
      flattenDepth,
      overlayClassName = '',
      draggable = true,
    }: Props<T>,
    ref,
  ) => {
    const [items, setItems] = useState(() => treeData || [])
    const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null)
    const activeItemRef = useRef<any>(null)
    const [overId, setOverId] = useState<UniqueIdentifier | null>(null)
    const overItemRef = useRef<any>(null)
    const [offsetLeft, setOffsetLeft] = useState(0)
    const collisionDetectionData = useRef({})
    const flattenedTreeRef = useRef<any>(null)

    const [foldKeys, setFoldKeys] = useState<(string | number)[]>([])

    useEffect(() => {
      setFoldKeys(projectGroupFoldStatus.getFoldStatus() || [])
    }, [])

    const flattenedItems = useMemo(() => {
      const flattenedTree = (flattenedTreeRef.current = flattenTree(items, flattenDepth))
      return removeChildrenOf(flattenedTree, activeId != null ? [activeId, ...foldKeys] : foldKeys)
    }, [activeId, items, foldKeys])

    const latestFlattenedItems = useLatest(flattenedItems)

    const projected = useMemo(() => {
      if (activeId && overId) {
        return getProjection(
          flattenedItems,
          activeId,
          overId,
          offsetLeft,
          indentationWidth,
          collisionDetectionData.current,
        )
      }
      return null
    }, [activeId, overId, offsetLeft, flattenedItems, indentationWidth])

    const sensorContext: SensorContext = useRef({
      items: flattenedItems,
      offset: offsetLeft,
    })

    const sensors = useSensors(
      useSensor(PointerSensor, {
        activationConstraint: {
          distance: 1,
        },
      }),
      useSensor(KeyboardSensor, {
        // Disable smooth scrolling in Cypress automated tests
        scrollBehavior: 'Cypress' in window ? 'auto' : undefined,
        coordinateGetter: sortableKeyboardCoordinates,
      }),
    )

    const sortedIds = useMemo(() => flattenedItems.map(({ id }) => id), [flattenedItems])

    useEffect(() => {
      sensorContext.current = {
        items: flattenedItems,
        offset: offsetLeft,
      }
    }, [flattenedItems, offsetLeft])

    useEffect(() => {
      setItems(treeData)
    }, [treeData])

    const handleFold = (id: string | number, expandOnly: boolean) => {
      const index = foldKeys.indexOf(id)
      let keys: any[] = []
      if (index === -1) {
        if (expandOnly) {
          return
        }
        keys = [...foldKeys, id]
      } else {
        keys = foldKeys.filter(key => key !== id)
      }
      projectGroupFoldStatus.setFoldStatus({ ids: keys as (string | number)[] })
      setFoldKeys([...keys])
    }

    useEffect(() => {
      function handleFoldById(
        { id, expandOnly = false }: { id: string | number; fromPin?: boolean; expandOnly?: boolean } = {} as any,
      ) {
        handleFold(id, expandOnly)
      }
      POPOBridgeEmitter.addListener(EnumEmitter.TreeExpandById, handleFoldById)

      return () => {
        POPOBridgeEmitter.removeListener(EnumEmitter.TreeExpandById, handleFoldById)
      }
    }, [foldKeys])

    const rowVirtualizer = useVirtualizer({
      count: flattenedItems.length,
      getScrollElement: () => scrollContainer,
      estimateSize: index => estimateSize?.(index, flattenedItems),
      overscan: 10,
    })

    const handleDragEnd = ({ active, over, collisions }: DragEndEvent) => {
      if (projected && over) {
        let activeIndex = flattenedItems.findIndex(item => item.id === active.id)
        let overIndex = flattenedItems.findIndex(item => item.id === over.id)
        const activeItemData = flattenedItems[activeIndex]
        const overItemData = flattenedItems[overIndex]

        if (!activeItemData || !overItemData) {
          return
        }

        const position = collisionDetectionData.current?.position
        let newParentId = overItemData.parentId
        const oldParentId = activeItemData.parentId

        if (!activeItemData.isGroup) {
          if (overItemData.isGroup) {
            if (position === 'below') {
              newParentId = overItemData.id
              overIndex += 1
              activeItemData.parentId = overItemData.id
              activeItemData.depth = projected.depth
            } else if (position === 'inside') {
              newParentId = overItemData.id
              activeItemData.parentId = overItemData.id
              activeItemData.depth = overItemData.depth + 1
              if (activeIndex > overIndex) {
                overIndex += 1
              }
            } else {
              if (activeIndex < overIndex) {
                overIndex -= 1
              }
              newParentId = activeItemData.parentId = overItemData.parentId
              activeItemData.depth = projected.depth
            }
          } else {
            if (activeIndex < overIndex) {
              if (position !== 'below') {
                overIndex -= 1
              }
            }
            if (activeIndex > overIndex) {
              if (position === 'below') {
                overIndex += 1
              }
            }
            activeItemData.parentId = overItemData.parentId
            activeItemData.depth = projected.depth
          }
        } else {
          if (activeIndex < overIndex) {
            if (position !== 'below') {
              overIndex -= 1
            } else {
              overIndex += 1
            }
          }
        }

        const sortedItems = arrayMove([...flattenedItems], activeIndex, overIndex)

        const newItems = buildTree(sortedItems)

        let preId, nextId

        if (!newParentId) {
          const index = newItems.findIndex(item => item.id === activeItemData.id)
          preId = index > 0 ? newItems[index - 1].id : null
          nextId = index < newItems.length - 1 ? newItems[index + 1].id : null
        } else {
          const parent = newItems.find(item => item.id === newParentId)
          if (parent) {
            const index = parent.children?.findIndex(item => item.id === activeItemData.id)
            preId = index > 0 ? parent.children[index - 1].id : null
            nextId = index < parent.children?.length - 1 ? parent.children[index + 1].id : null
          }
        }

        onDragEnd?.(newItems, {
          activeIndex,
          overIndex,
          newParentId,
          oldParentId,
          activeId: active.id,
          preId,
          nextId,
        })

        setItems(newItems)
        resetState()
      }
    }

    // 自定义碰撞检测算法
    const customCollisionDetection: CollisionDetection = ({ active, droppableContainers, pointerCoordinates }) => {
      if (!pointerCoordinates) {
        return []
      }

      const collisions: Collision[] = []

      // 获取拖拽元素的数据
      const activeItemData = active.data.current?.item
      const isActiveGroup = activeItemData?.isGroup
      const activeDepth = activeItemData?.depth

      for (const container of droppableContainers) {
        const { rect } = container
        if (!rect) continue

        const { top, bottom, left, right, height } = rect.current

        // 检查指针是否在元素的水平范围内
        if (
          pointerCoordinates.x >= left &&
          pointerCoordinates.x <= right &&
          pointerCoordinates.y >= top &&
          pointerCoordinates.y <= bottom
        ) {
          // 获取目标元素的数据
          const targetItemData = container.data.current?.item
          const targetDepth = targetItemData?.depth

          // 如果拖拽的是分组，检查同级限制
          if (isActiveGroup) {
            // 只允许在同一层级拖动
            if (targetDepth !== activeDepth) {
              continue // 跳过不同层级的元素
            }
          }

          // 计算碰撞数据
          const centerY = top + height / 2
          const distance = Math.abs(pointerCoordinates.y - centerY)

          // 将元素高度分为三等份
          const thirdHeight = height / 3
          const upperBound = top + thirdHeight
          const lowerBound = bottom - thirdHeight

          let position: 'above' | 'inside' | 'below'

          if (pointerCoordinates.y <= upperBound) {
            position = 'above'
          } else if (pointerCoordinates.y >= lowerBound) {
            position = 'below'
          } else {
            position = 'inside'
          }

          // 如果拖拽的是分组，不允许 inside 位置（分组不能被放入其他元素内部）
          if (isActiveGroup && position === 'inside') {
            // 根据指针位置调整为 above 或 below
            position = pointerCoordinates.y < centerY ? 'above' : 'below'
          }

          collisionDetectionData.current = {
            overId: container.id,
            position,
          }

          collisions.push({
            id: container.id,
            data: {
              ...container.data.current,
              droppableContainer: container,
              value: distance,
              position, // 添加位置信息
            },
          })
        }
      }

      // 按距离排序，最近的优先
      return collisions.sort((a, b) => a.data?.value - b.data?.value)
    }

    useImperativeHandle(
      ref,
      () => ({
        scrollToIndexById: (id: number) => {
          const itemData = flattenedTreeRef.current.find(item => item.id === id)
          if (!itemData?.isGroup && itemData?.groupId && foldKeys.includes(itemData.groupId)) {
            handleFold(itemData.groupId)
          }
          setTimeout(() => {
            const index = latestFlattenedItems.current.findIndex(item => item.id === id)
            rowVirtualizer.scrollToIndex?.(index, {
              align: 'auto',
            })
          }, 320)
        },
        scrollToIndex(index: number) {
          rowVirtualizer.scrollToIndex?.(index, {
            align: 'auto',
          })
        },
      }),
      [flattenedItems, foldKeys],
    )

    const virtualItems = rowVirtualizer.getVirtualItems()

    return (
      <DndContext
        accessibility={{ container: scrollContainer! }}
        sensors={sensors}
        collisionDetection={customCollisionDetection}
        measuring={measuring}
        onDragStart={handleDragStart}
        onDragMove={handleDragMove}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <div
          style={{
            height: rowVirtualizer.getTotalSize(),
            width: '100%',
            position: 'relative',
          }}
        >
          <SortableContext items={sortedIds} strategy={verticalListSortingStrategy}>
            {!!flattenedItems.length && (
              <div
                className="sortable-tree"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualItems[0]?.start ?? 0}px)`,
                }}
              >
                {virtualItems.map(virtualRow => {
                  const item = flattenedItems[virtualRow.index]
                  const { id, children, collapsed, depth, projectId } = item
                  const hasChildren = !!children?.length
                  const currId = projectId || id
                  const isPlaceholder =
                    typeof currId === 'string' || `${currId}`.includes(ProjectItemStatus.placeholder)
                  return (
                    <div
                      data-id={currId}
                      key={currId + '-' + virtualRow.index}
                      className="sortable-tree__row"
                      // style={{
                      //   position: 'absolute',
                      //   top: virtualRow.start,
                      //   left: 0,
                      //   width: '100%',
                      //   height: virtualRow.size,
                      // }}
                    >
                      <SortableTreeItem
                        key={currId}
                        id={currId}
                        value={currId}
                        // disabled={isPlaceholder}
                        draggable={!isPlaceholder && draggable}
                        projectedDepth={currId === activeId && projected ? projected.depth : depth}
                        depth={depth}
                        indentationWidth={indentationWidth}
                        indicator={indicator}
                        collapsible={collapsible && hasChildren}
                        foldKeys={foldKeys}
                        collapsed={Boolean(foldKeys.includes(currId))}
                        onCollapse={collapsible && hasChildren ? () => handleCollapse(currId) : undefined}
                        itemRnder={itemRender}
                        handlerRender={handlerRender}
                        showCollapse={showCollapse}
                        data={item as any}
                        selectedId={selectedId}
                        collisionDetectionData={collisionDetectionData.current}
                        scrollContainer={scrollContainer}
                      />
                    </div>
                  )
                })}
                {scrollContainer && loadMore && (
                  <LoadMore scrollContent={scrollContainer} canLoadMore={canLoadMore} moreFetcher={loadMore} />
                )}
              </div>
            )}
            {createPortal(
              <DragOverlay
                dropAnimation={dropAnimationConfig}
                modifiers={indicator ? [adjustTranslate] : undefined}
                style={{ zIndex: 1000 }}
              >
                {activeId && activeItemRef.current ? (
                  <SortableTreeItem
                    id={activeId}
                    depth={activeItemRef.current.depth}
                    clone
                    collapsible={collapsible && !!activeItemRef.current.children?.length}
                    collapsed={true}
                    value={activeId}
                    indentationWidth={indentationWidth}
                    itemRnder={itemRender}
                    handlerRender={handlerRender}
                    draggable={true}
                    showCollapse={showCollapse}
                    data={activeItemRef.current}
                    selectedId={selectedId}
                    style={overlayStyle}
                    className={overlayClassName}
                  />
                ) : null}
              </DragOverlay>,
              document.body,
            )}
          </SortableContext>
        </div>
      </DndContext>
    )

    function handleDragStart({ active }: DragStartEvent) {
      const { id: activeId } = active
      setActiveId(activeId)
      setOverId(activeId)

      activeItemRef.current = active.data.current?.item
      overItemRef.current = active.data.current?.item
    }

    function handleDragMove({ delta }: DragMoveEvent) {
      requestAnimationFrame(() => {
        setOffsetLeft(delta.x)
      })
    }

    function handleDragOver({ over, active }: DragOverEvent) {
      console.log('handleDragOver', over?.data.current?.item, active)
      if (over?.id === active.id) {
        return
      }
      // const activeItem = active?.data.current?.item;
      const overItem = over?.data.current?.item
      // if (!activeItem?.isGroup && overItem?.isGroup) {
      //   if (overItem?.isGroup) {
      //     // 展开分组
      //     if (foldKeys.includes(overItem.id)) {
      //       handleCollapse(overItem.id);
      //       return;
      //     }
      //   }
      // }
      // if (activeItem?.isGroup) {
      //   if (overItem?.isGroup) {
      //     // 关闭overItem的子节点
      //     if (!foldKeys.includes(overItem.id)) {
      //       handleCollapse(overItem.id);
      //     }
      //   } else if (overItem?.groupId) {
      //     // 关闭overItem的子节点
      //     if (!foldKeys.includes(overItem.groupId)) {
      //       handleCollapse(overItem.groupId);
      //     }
      //   }
      // }

      overItemRef.current = overItem

      setOverId(over?.id ?? null)
    }

    function handleDragCancel() {
      resetState()
    }

    function resetState() {
      setOverId(null)
      setActiveId(null)
      setOffsetLeft(0)

      activeItemRef.current = null
      overItemRef.current = null
      collisionDetectionData.current = {}

      document.body.style.setProperty('cursor', '')
    }

    function handleCollapse(id: UniqueIdentifier) {
      handleFold(id)

      setItems(items =>
        setProperty(items, id, 'collapsed', value => {
          return !value
        }),
      )
    }
  },
)

const adjustTranslate: Modifier = ({ transform }) => {
  return {
    ...transform,
    // x: transform.x,
    y: transform.y - 25,
  }
}
