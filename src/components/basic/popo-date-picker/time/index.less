.timeInput {
  border-style: none;
  &::before {
    border-style: none;
  }
  &:global(.rock-input-wrapper-inline) {
    width: 100%;
  }
  :global {
    .rock-input {
      font-size: 13px;
      color: var(--TextTertiary);
    }
  }
}
.timeSelect {
  height: 0;
  //width: 0 !important;
  opacity: 0;
  display: block !important;
  :global {
    .rock-select-selector {
      display: none;
    }
  }
}

.timeSelectDropDown {
  top: 15px !important;
  min-width: 100px;
  :global {
    .rock-scrollbar {
      max-height: 290px !important;
    }
    .rock-select-item-wrapper {
      padding-bottom: 0;
    }
  }
}
