import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';

import { apiProjectStateRecordGet } from '@/api';
import { Dropdown, Icon, Tooltip } from '@/components/basic';
import { ProjectInfo } from '@/types';
import { EnumProjectStatus } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import Overlay from './overlay';
import ProjectStatusTag from './project-status-tag';
import { ProjectRecord } from './use-project-status';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
  onChange?: (k: EnumProjectStatus) => void;
}

const MoreOption: React.FC<Props> = (props) => {
  const { disabled, className, projectInfo, onChange } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [stateInfo, setStateInfo] = useState<ProjectRecord>();
  const getData = async () => {
    if (projectInfo?.projectId) {
      const data = await apiProjectStateRecordGet({
        projectId: projectInfo?.projectId + '',
      });
      if (!data) {
        return;
      }
      const { preState, state, operatorName, timestamp } = data;
      setStateInfo({
        preState: preState as EnumProjectStatus,
        state: state as EnumProjectStatus,
        userName: operatorName!,
        updateTime: timestamp,
      });
    }
  };
  useEffect(() => {
    if (visible) {
      setStateInfo(undefined);
      getData();
    }
  }, [visible]);

  return (
    <div className={classNames(s.projectStatus, className)}>
      <Tooltip title={!disabled ? I18N.auto.updateStatus : ''}>
        <Dropdown
          className={s.dropdown}
          title={
            <div
              className={classNames(s.statusSelect, {
                [s.statusEdit]: !disabled,
                [s.open]: visible && !disabled,
              })}
            >
              <ProjectStatusTag status={projectInfo?.state}></ProjectStatusTag>
              <Icon name="icon-sys_open" className={`${s.icon} ${visible ? s.open : ''}`}></Icon>
            </div>
          }
          trigger="click"
          arrow={false}
          defaultOpen={visible}
          disabled={disabled}
          open={visible}
          onOpenChange={(v) => {
            setVisible(v);
          }}
          destroyPopupOnHide
          minOverlayWidthMatchTrigger={false}
          overlay={<Overlay value={stateInfo} onChange={onChange}></Overlay>}
        ></Dropdown>
      </Tooltip>
    </div>
  );
};

export default MoreOption;
