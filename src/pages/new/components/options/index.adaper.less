.options {
  position: relative;
  display: flex;
  padding: 14px 20px;
  flex: 1;
  flex-shrink: 0;
  width: 100%;

  .hidden {
    transition: none;
    opacity: 0;
    pointer-events: none;
  }

  :global {
    .rock-btn {
      font-size: 13px;
      padding: 0 10px 0 8px;
    }
    .option-active {
      background-color: var(--aBrand12) !important;
      color: var(--Brand600) !important;
    }
    .option-opened {
      background-color: var(--aBlack4);
    }

    .rock-dropdown-trigger-default {
      transition: background-color linear 0.3s;
    }
  }
}

.optionsWrapper {
  display: flex;
  flex-direction: row;
  // padding-right: 20px;
  width: 100%;
  min-width: 0;
}

.add {
  margin-right: 16px;
}

.saveViewModal {
  :global {
    .rock-icon-alert-circle-filled-colored {
      color: var(--Brand600) !important;
    }
    .rock-btn-primary {
      background-color: var(--Brand600);
    }
  }
}

.guidePopover {
  :global {
    --spacing-md: 24px;
    .rock-popover-arrow {
      background-color: var(--TaskGuideBg);
    }
    .rock-popover-inner {
      width: 380px;
      background: linear-gradient(180deg, var(--TaskGuideBg) 0%, var(--bgTop) 100%);
    }
  }
}
