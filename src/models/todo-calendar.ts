import { createModel } from '@rematch/core';
import dayjs from 'dayjs';

import {
  apiGetCalendarView,
  apiGetCalendarViewUnplanned,
  getPermissionsByTaskList,
} from '@/api-common';
import { TodoInfo, UnplannedTodoItem } from '@/types';

import type { RootModel } from '.';
import { handleSeverData } from './utils';

interface Params {
  begin?: number;
  end?: number;
}

type CalendarTodoParams = {
  start?: Date;
  end?: Date;
};

type Pagination = {
  page?: number;
  size?: number;
  more?: boolean;
};
const DefaultPaginatio = {
  page: 1,
  size: 30,
  more: false,
};

interface CalendarState {
  params: Params;
  loading: boolean;
  calendarList: TodoInfo[];
  unplannedList: UnplannedTodoItem[];
  more: boolean;
  pagination: Pagination;
  calendarKey: number;
}

const initData: CalendarState = {
  params: {
    begin: undefined,
    end: undefined,
  },
  loading: false,
  calendarList: [],
  unplannedList: [],
  more: false,
  pagination: DefaultPaginatio,
  calendarKey: dayjs().get('D'),
};
export const todoCalendar = createModel<RootModel>()({
  state: {
    ...initData,
  },
  reducers: {
    initData() {
      return {
        ...initData,
      };
    },
    setData(state, payload: Partial<CalendarState>) {
      return { ...state, ...payload };
    },
    initunplanned(state) {
      return {
        ...state,
        unplannedList: [],
        more: false,
      };
    },
    setCalendarKey(state, payload: number) {
      return {
        ...state,
        calendarKey: payload,
      };
    },
    setCalendarTodoList(state, payload: TodoInfo[]) {
      return {
        ...state,
        calendarList: payload,
      };
    },

    updateTodoList(state, payload: TodoInfo) {
      const { taskId } = payload;
      const { calendarList } = state;
      const index = calendarList.findIndex((item) => item.taskId === taskId);
      let list: TodoInfo[] = calendarList;
      if (index > -1) {
        list = calendarList
          .map((item) => {
            if (item.taskId === taskId) {
              let isExpired = item.isExpired;
              if (payload.deadline) {
                if (dayjs().isAfter(payload.deadline)) {
                  isExpired = true;
                } else {
                  isExpired = false;
                }
              }
              return {
                ...item,
                ...payload,
                isExpired,
              };
            } else {
              return item;
            }
          })
          .filter((item) => item.deadline);
      } else {
        // 如果不存在 就从未计划任务中找
        const { unplannedList } = state;
        const item = unplannedList.find((item) => item.taskId === taskId);
        if (item) {
          list.push({
            ...item,
            taskId,
            ...payload,
          });
        }
      }
      return {
        ...state,
        calendarList: [...list],
      };
    },
    setParams(
      state,
      payload: {
        begin?: number;
        end?: number;
      }
    ) {
      return {
        ...state,
        params: { ...state.params, ...payload },
      };
    },
    setFiltersState(state, payload: string[]) {
      return { ...state, filters: payload };
    },
    setUnplannedList(state, payload: { list: UnplannedTodoItem[]; more: boolean }) {
      const { more, list } = payload;
      return { ...state, unplannedList: list, more };
    },
  },
  effects: (dispatch) => ({
    /**
     * 当前操作下,刷新首页数据
     * @param opt
     * @param state
     * @returns
     */
    async getCalendarTodoList(opt: CalendarTodoParams, state) {
      let begin = undefined;
      let end = undefined;
      const param = dispatch.viewSetting.getComParams({});

      if (!opt.end) {
        begin = state.todoCalendar.params.begin;
        end = state.todoCalendar.params.end;
      } else {
        begin = opt.start?.getTime();
        end = opt.end.getTime();
      }
      if (!end) return;

      let data = await apiGetCalendarView({
        ...param,
        navigatorId: param.navigatorId + '',
        viewId: Number(param.viewId!),
        begin: begin!,
        end: end!,
      }).then((res) => {
        const { userInfo } = state.user;
        return res?.map((item) => handleSeverData(item, userInfo!));
      });
      dispatch.todoCalendar.setParams({
        begin: begin!,
        end: end!,
      });
      //更新这一次请求到任务的权限列表
      // if (data.length) {
      //   const obj = await getPermissionsByTaskList(data);
      //   data = data.map((item) => ({
      //     ...item,
      //     permissions: obj[item.taskId!]?.permissions || [],
      //   }));
      // }
      dispatch.todoCalendar.setCalendarTodoList(data as unknown as TodoInfo[]);
    },
    updateCalendarTodoListbyStatus(opt: { taskId: string; isFinished: boolean }, state) {
      const { taskId, isFinished } = opt;
      const { calendarList } = state.todoCalendar;
      const item = calendarList.find((item) => item.taskId === Number(taskId));
      if (item) {
        item.selfFinished = isFinished ? 1 : 0;
        dispatch.todoCalendar.updateTodoList(item);
      }
    },
    async getUnplannedList(opt: { page?: number; size?: number }, state) {
      const { page, size = 30 } = opt;
      const { unplannedList = [] } = state.todoCalendar;
      const { userInfo } = state.user;
      const param = dispatch.viewSetting.getComParams({});
      let scrollId: string | undefined = undefined;
      if (unplannedList.length && page !== 1) {
        scrollId = String(unplannedList[unplannedList.length - 1].taskId!);
      }
      const data = await apiGetCalendarViewUnplanned({
        ...param,
        navigatorId: param.navigatorId + '',
        viewId: Number(param.viewId!),
        size: size < DefaultPaginatio.size ? DefaultPaginatio.size : size,
        scrollId: scrollId,
      });
      data.list = data.list?.map((item) => handleSeverData(item, userInfo!));
      const { hasMore, list = [] } = data;
      let _unplannedList = [];
      if (page === 1) {
        _unplannedList = list;
      } else {
        _unplannedList = unplannedList.concat(list);
      }
      //更新这一次请求到任务的权限列表
      // if (_unplannedList.length) {
      //   const obj = await getPermissionsByTaskList(_unplannedList);
      //   _unplannedList = _unplannedList.map((item) => ({
      //     ...item,
      //     permissions: obj[item.taskId!]?.permissions || [],
      //   }));
      // }
      dispatch.todoCalendar.setUnplannedList({
        list: _unplannedList,
        more: !!hasMore,
      });
    },
  }),
});
