.dropdown {
  padding: 0;
  height: auto;
  &:hover {
    background-color: transparent;
  }
}
.statusSelect {
  padding: 2px 6px 2px 2px;
  display: flex;
  align-items: center;
  border-radius: 6px;
}
.statusEdit {
  &:hover,
  &.open {
    background-color: var(--aBlack6);
    .icon {
      display: block;
    }
  }
}
.icon {
  margin-left: 4px;
  color: var(--IconTertiary);
  font-size: 16px;
  transform: rotate(90deg);
  display: none;

  &.open {
    transform: rotate(-90deg);
  }
}
