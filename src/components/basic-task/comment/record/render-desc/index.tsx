import React, { ReactNode } from 'react';

import s from './index.less';
interface ItemRenderProps {
  operatorName?: string;
  desc?: string;
  other?: ReactNode;
}

const RenderDesc: React.FC<ItemRenderProps> = (props) => {
  const { operatorName, desc, other } = props;

  return (
    <span className={s.box}>
      <span className={s.left}>{operatorName}</span>
      <span className={s.right}>
        <span>{desc}</span>
        {other}
      </span>
    </span>
  );
};

export default RenderDesc;
