.listPeopleItem {
  // width: 100%;
  // height: 100%;
}

.deleteTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--TextPrimary);
}

.confirm {
  :global {
    .rock-btn-primary {
      background-color: var(--R600) !important;
      &:hover {
        background-color: var(--R500) !important;
      }
    }
  }
}

.selfTodo {
}

.finishPercentTip {
  display: flex;
  align-items: center;
  .divider {
    height: 12px;
    margin-right: 12px;
    width: 1px;
    background-color: var(--aBlack12);
  }
  .completeTip {
    color: var(--TextPrimary);
    font-size: 13px;
    line-height: 24px;
    margin-right: 4px;
  }
}
