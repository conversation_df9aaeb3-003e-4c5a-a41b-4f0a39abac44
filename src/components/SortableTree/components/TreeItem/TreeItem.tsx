import React, { forwardRef, HTMLAttributes } from 'react';
import classNames from 'classnames';

import styles from './TreeItem.less';
import { ImTriangleDownSolid1 } from '@babylon/popo-icons';
import { InsertPosition } from '@/utils/dndkitUtils';

export interface Props extends Omit<HTMLAttributes<HTMLLIElement>, 'id'> {
  childCount?: number;
  clone?: boolean;
  collapsed?: boolean;
  depth: number;
  disableInteraction?: boolean;
  disableSelection?: boolean;
  ghost?: boolean;
  handleProps?: any;
  indicator?: boolean;
  indentationWidth: number;
  value: string | numbers;
  onCollapse?(): void;
  onRemove?(): void;
  wrapperRef?(node: HTMLLIElement): void;
  disabled?: boolean;
  selectedId?: string | number;
  insertPosition?: InsertPosition;
  className?: string;
  isAbover?: boolean;
}

export const TreeItem = forwardRef<HTMLDivElement, Props>(
  (
    {
      childCount,
      clone,
      depth,
      disableSelection,
      disableInteraction,
      ghost,
      handleProps,
      indentationWidth,
      indicator,
      collapsed,
      onCollapse,
      onRemove,
      style,
      value,
      wrapperRef,
      itemRnder,
      data,
      handlerRender,
      draggable,
      collapsible,
      showCollapse,
      selectedId,
      insertPosition,
      className = '',
      isAbover,
      ...props
    },
    ref
  ) => {
    const lineDragProps = handlerRender ? {} : handleProps;

    return (
      <div
        className={classNames(
          styles.TreeItemWrapper,
          'sortable-tree-item',
          'flex-y-center',
          className,
          isAbover && styles.isAbover,
          clone && styles.clone,
          ghost && styles.ghost,
          indicator && styles.indicator,
          disableSelection && styles.disableSelection,
          disableInteraction && styles.disableInteraction,
          insertPosition && styles[insertPosition],
          {
            'sortable-tree-item__expanded': !collapsed,
            'sortable-tree-item__collapsed': collapsed,
            'sortable-tree-item__selected': selectedId === data.id,
            'sortable-tree-item__disabled': props.disabled,
            'sortable-tree-item__above': isAbover,
            [styles.disabled]: props.disabled,
          }
        )}
        style={style}
        ref={wrapperRef}
        {...lineDragProps}
        {...props}
        onClick={onCollapse}
      >
        {draggable && handlerRender && <div {...handleProps}>{handlerRender(data)}</div>}
        <div
          ref={ref}
          className={classNames('flex-y-center', 'sortable-tree-item__wrapper', styles.Wrapper, {
            [styles.Wrapper__drag]: draggable && !!handlerRender,
          })}
          style={
            {
              '--spacing': `${indentationWidth * depth}px`,
            } as React.CSSProperties
          }
        >
          <div className={`${styles.TreeItem} tree__item`}>
            {showCollapse && (
              <div
                className={classNames(styles.Collapse, collapsed && styles.collapsed, {
                  [styles.collapsible]: collapsible,
                })}
              >
                <ImTriangleDownSolid1 className={`${styles.collapse__icon} fs-16`} />
              </div>
            )}
            {itemRnder ? (
              itemRnder(data)
            ) : (
              <span className={`${styles.Text} ellipsis`}>{value}</span>
            )}
            {/* {!clone && onRemove && <Remove onClick={onRemove} />} */}
            {clone && childCount && childCount > 1 ? (
              <span className={styles.Count}>{childCount}</span>
            ) : null}
          </div>
        </div>
      </div>
    );
  }
);
