.wrapper {
  line-height: 0;
  img {
    width: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 500;
    line-height: 22.4px;
    text-align: left;
    color: var(--Brand600);
  }

  .des {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    color: var(--TextPrimary);
    margin: 6px 0 8px 0;
    word-break: break-word;
  }
  .bold {
    font-weight: bold;
  }
  .img {
    border-radius: 6px;
    border: 1px solid var(--aBlack4);
  }
  .btnWrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
    :global {
      .rock-btn-primary {
        background-color: var(--Brand500);
        color: var(--absWhite);
      }
    }
  }
}

.btn {
  border-color: var(--Brand500);
}
