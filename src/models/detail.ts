import { createModel } from '@rematch/core';

import { apiProjectFieldListPost, apiTaskCheckRolePost, apiTaskGetGet } from '@/api';
import { getBatchPermission } from '@/api-common';
import { DetailTodoInfo, TaskInfo, TaskPermission, TodoDetailStatus } from '@/types';
import { CustomField, FieldSchema } from '@/types/custom-field';
import {
  PermissionObjectType,
  TaskDetailPermissions,
  TaskPermissionEnum,
  validatesPermission,
} from '@/utils/permission';

import type { RootModel } from '.';
import { addItemActive, handleSeverData, removeItemActive, tfProject } from './utils';
import { AddMenuId, TaskTableRowTypeAdd } from '@/utils/const';

type TaskDetailState = {
  taskInfo: DetailTodoInfo;
  taskId?: number; //当前详情的待办id
  visibleDetail: boolean;
  /**
   * 当前项目的权限, 快捷视图目前也当项目处理
   */
  permissions: { name: TaskPermissionEnum; value: boolean }[];
  customFields: CustomField[];
  subTasks?: TaskInfo[];
  detailStatus?: TodoDetailStatus;
};

const initData: TaskDetailState = {
  taskInfo: {},
  taskId: undefined,
  visibleDetail: false,
  permissions: [],
  customFields: [],
  subTasks: [],
  detailStatus: undefined,
};

export const detail = createModel<RootModel>()({
  state: initData,
  reducers: {
    initData(state) {
      return {
        ...initData,
      };
    },
    setData(state, payload: Partial<TaskDetailState>) {
      return { ...state, ...payload };
    },
    setTaskInfo(state, payload: DetailTodoInfo) {
      return { ...state, taskInfo: { ...state.taskInfo, ...payload } };
    },
    updateDetail(state, payload: DetailTodoInfo) {
      return {
        ...state,
        taskInfo: {
          ...state.taskInfo,
          ...payload,
        },
      };
    },
    settaskId(state, payload: number | undefined) {
      return {
        ...state,
        taskId: payload,
      };
    },

    setVisibleDetail(state, payload: boolean) {
      return {
        ...state,
        visibleDetail: payload,
      };
    },
    clearMultMsg(state) {
      const { taskInfo } = state;
      const _taskInfo = {
        ...taskInfo,
        multMsgTitle: undefined,
        multMsgContent: undefined,
        multMsgDigest: undefined,
      };
      return {
        ...state,
        taskInfo: _taskInfo,
      };
    },
  },
  effects: (dispatch) => ({
    async getDetailInfo({ taskId }, state) {
      return Promise.all([
        apiTaskGetGet({ taskId: String(taskId) }, { errorSilent: true }),
        apiTaskCheckRolePost({
          taskIds: [taskId],
          roles: TaskDetailPermissions,
        }),
      ])
        .then(([detailRes, permissionsRes]) => {
          const { userInfo } = state.user;
          const _res = handleSeverData(detailRes, userInfo!) as DetailTodoInfo;
          if (_res.project?.projectId) {
            detailRes.project = tfProject(_res.project);
          } else {
            _res.project = {};
          }
          _res.permissions = [];
          if (permissionsRes[0].roles) {
            _res.permissions = permissionsRes[0].roles as {
              name: TaskPermissionEnum;
              value: boolean;
            }[];
            dispatch.permissions.updatePermissionsMap({ permission: permissionsRes[0] });
          }
          _res.subtask = _res.subtask?.map((item) => {
            return handleSeverData(item, userInfo!);
          });

          if (_res.subtask?.length) {
            dispatch.permissions.setPermissionsMap({
              list: _res.subtask,
              extraPermissionList: ['CAN_DELETE'],
            });
          }

          if (`${state.detail.taskId}` === `${taskId}`) {
            dispatch.detail.setData({
              subTasks: _res.subtask,
              taskInfo: _res,
              detailStatus: TodoDetailStatus.normal,
            });
          }
          return _res;
        })
        .catch((e) => {
          // 拉取详情出错，可能是因为没权限: 关闭详情，刷新列表
          dispatch.detail.closeDetail({});
          dispatch.viewSetting.refreshDataByDataChange({
            refreshList: true,
            refreshCount: false,
          });
          return e;
        });
    },
    /**
     * 获取任务详情接口
     * @param options
     * @param options.taskId 任务id, 如果传入null, 则使用当前的taskId
     * @param options.onlyDetail 是否只获取详情,不获取评论和日志
     * @param state
     * @returns
     */
    async getTodoDetail(
      options: number | null | { taskId: number | string | null; onlyDetail: boolean },
      state
    ) {
      let taskId: number;
      if (typeof options === 'object') {
        taskId = Number(options?.taskId || state.detail.taskId);
      } else {
        taskId = Number(options);
      }
      if (state.detail.visibleDetail || location.href.includes('pc/detail')) {
        return new Promise((resolve, reject) => {
          dispatch.detail
            .getDetailInfo({ taskId })
            .then((res) => {
              dispatch.detail.settaskId(taskId);

              dispatch.detail.setData({
                subTasks: res.subtask,
                taskInfo: res,
                detailStatus: TodoDetailStatus.normal,
              });

              if (typeof options !== 'object' || options?.onlyDetail !== true) {
                //更新日志
                dispatch.record.getRecordList({ page: 1 });
                //设置评论未读的最新状态
                dispatch.record.setUnreadComment(res.unreadComment!);
              }

              resolve(res);
            })
            .catch((res) => {
              dispatch.detail.setVisibleDetail(false);
              dispatch.detail.settaskId(undefined);
              const { status } = res?.response?.data || {};
              let statusAlias;
              // 待办不存在
              if (status === 110000) {
                statusAlias = TodoDetailStatus.notExist;
              } else if ([110200, 100403].includes(status)) {
                //无权限
                statusAlias = TodoDetailStatus.noPermission;
              } else {
                statusAlias = TodoDetailStatus.networkErr;
              }
              dispatch.detail.setData({ detailStatus: statusAlias });
            });
        });
      }
      //刷新权限
      // return Promise.all([
      //   apiTaskGetGet({ taskId: String(taskId) }, { errorSilent: true }),
      //   apiTaskCheckRolePost({
      //     taskIds: [taskId],
      //     roles: TaskDetailPermissions,
      //   }),
      // ]).then(([detailRes, permissionsRes]) => {
      //   const { userInfo } = state.user;
      //   const _res = handleSeverData(detailRes, userInfo!) as DetailTodoInfo;
      //   if (_res.project?.projectId) {
      //     detailRes.project = tfProject(_res.project);
      //   } else {
      //     _res.project = {};
      //   }
      //   _res.permissions = [];
      //   if (permissionsRes[0].roles) {
      //     _res.permissions = permissionsRes[0].roles as {
      //       name: TaskPermissionEnum;
      //       value: boolean;
      //     }[];
      //   }
      //   _res.subtask = _res.subtask?.map((item) => handleSeverData(item, userInfo!));
      //   if (
      //     (state.detail.visibleDetail || location.href.includes('pc/detail')) &&
      //     (!state.detail.taskId || `${state.detail.taskId}` === `${taskId}`)
      //   ) {
      //     dispatch.detail.settaskId(taskId);

      //     dispatch.detail.setData({
      //       subTasks: _res.subtask,
      //       taskInfo: _res,
      //       detailStatus: TodoDetailStatus.normal,
      //     });
      //   }
      //   if (typeof options !== 'object' || options?.onlyDetail !== true) {
      //     //更新日志
      //     dispatch.record.getRecordList({ page: 1 });
      //     //设置评论未读的最新状态
      //     dispatch.record.setUnreadComment(_res.unreadComment!);
      //   }
      //   return _res;
      // });
    },
    /**
     * 不更新日志
     * @param taskId
     * @param state
     * @returns
     */
    // async updateTodoBasicDetail(taskId: number, state) {
    //   return apiTaskGetGet({ taskId: String(taskId) }, { errorSilent: true }).then((res) => {
    //     const { userInfo } = state.user;
    //     const _res = handleSeverData(res, userInfo!) as DetailTodoInfo;
    //     dispatch.detail.setTaskInfo(_res);
    //     return _res;
    //   });
    // },
    /**
     * 打开详情的抽屉
     * @param taskId
     */
    async openDetail(taskId: number) {
      //判断当前用户是否有权限打开详情
      //非任务参与人，无权限查看
      const perms = [
        {
          objectIds: [String(taskId)],
          objectType: PermissionObjectType.TASK,
          operateType: TaskPermissionEnum.CAN_VIEW,
        },
      ];
      const permissionobj = await getBatchPermission<TaskPermission>(perms);
      const permissions = permissionobj[taskId].permissions;
      const [CAN_VIEW] = validatesPermission({
        permissions: permissions,
        key: [TaskPermissionEnum.CAN_VIEW],
      }) as boolean[];
      if (CAN_VIEW) {
        // dispatch.detail.settaskId(taskId);
        dispatch.detail.setVisibleDetail(true);
        await dispatch.detail.getTodoDetail(taskId);

        removeItemActive();
        addItemActive(taskId);
      }
      return Promise.resolve();
    },
    /**
     * 关闭详情的抽屉
     */
    closeDetail(opt, state) {
      dispatch.record.initData();
      dispatch.detail.initData();
      dispatch.detail.setVisibleDetail(false);
      dispatch.detail.settaskId(undefined);
      removeItemActive();
    },
    async getProjectFieldList(projectId) {
      apiProjectFieldListPost({
        projectId,
      }).then((res) => {
        let fields = (res.fields || []).map((item) => {
          let id = item.fieldId!;
          let shcema = JSON.parse(item.fieldSchema!) as FieldSchema;
          return {
            ...shcema,
            fieldId: id,
            fieldVersion: item.fieldVersion,
          };
        });
        dispatch.detail.setData({
          customFields: fields,
        });
      });
    },
    getVisibleDetail(opt, state) {
      return state.detail.visibleDetail;
    },
    insertSubTasks(opt, state) {
      const subTasks = state.detail.subTasks || [];
      if (typeof opt.taskId === 'number') {
        const index = subTasks.findIndex((item) => `${item.taskId}`.includes(TaskTableRowTypeAdd));
        if (index > -1) {
          subTasks[index] = opt;
        } else {
          subTasks.push(opt);
        }
      } else {
        subTasks.push(opt as TaskInfo);
      }
      dispatch.detail.setData({
        subTasks: [...subTasks],
      });
    },
    /**
     * 替换乐观更新的数据为接口数据
     * @param opt
     * @param state
     */
    replaceOptimisticTask(opt, state) {
      dispatch.detail.replaceTask({ targetId: AddMenuId, ...opt });
    },
    /**
     * 替换任务信息
     * @param opt
     * @param opt.taskInfo 任务信息
     * @param opt.targetId 替换目标id
     * @param state
     */
    replaceTask(opt, state) {
      const { taskInfo: subtaskInfo, updateType } = opt;
      const subTasks = state.detail.subTasks || [];
      const repalceTargetId = subtaskInfo.taskId;
      const index = subTasks?.findIndex((item) => item.taskId === repalceTargetId);
      if (index > -1) {
        subTasks[index] = subtaskInfo;
        if (updateType !== 'local') {
          dispatch.detail
            .getTodoDetail({ taskId: state.detail.taskId!, onlyDetail: true })
            .then((res) => {
              dispatch.task.updateTaskInfo({ taskInfo: res });
            });
        }

        dispatch.detail.setData({
          subTasks: [...subTasks],
        });
      }
    },

    updateSubtasks(opt, state) {
      const { subTasks } = opt;
      const taskInfo = state.detail.taskInfo;
      taskInfo.subtask = subTasks;
      dispatch.detail.setData({
        subTasks,
      });

      dispatch.task.updateTaskInfo({ taskInfo: taskInfo });
    },
  }),
});
