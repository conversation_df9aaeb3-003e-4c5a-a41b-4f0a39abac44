import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { ReactNode, useEffect, useMemo, useState } from 'react';

import { Dropdown, Placeholder, RenderTime } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { isControlled } from '@/utils';
import { FORMAT_DD, FORMAT_DD_mm, PPTimeFormat } from '@/utils/const';

import s from './index.less';
import Overlay from './overlay';

export type Props = {
  className?: string;
  value?: Dayjs;
  timeFormat?: PPTimeFormat;
  onChange?: (time: Dayjs) => void;
  placeholder?: ReactNode;
  hasArrow?: boolean;
  renderLabel?: (time: Dayjs, rrule?: string) => ReactNode;
  children?: ReactNode;
  labelClassName?: string;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
};

export enum QuickType {
  deadline,
  remind,
}

export default function CustomFieldTime(props: Props) {
  const {
    value: propsValue,
    onChange,
    placeholder,
    hasArrow,
    timeFormat,
    children,
    className,
    labelClassName,
    disabled,
    visible: propVisible,
    onVisible,
  } = props;
  const [value, setValue] = useState<Dayjs>();
  const [visible, setVisible] = useState<boolean>();

  const memoVisible = useMemo(() => {
    return isControlled(propVisible) ? propVisible : visible;
  }, [propVisible, visible]);

  const changeVisible = (v: boolean) => {
    if (isControlled(propVisible)) {
      onVisible?.(v);
    } else {
      setVisible(v);
    }
  };

  useEffect(() => {
    setValue(propsValue);
  }, [propsValue]);

  const showTimeLabel = () => {
    if (propsValue && timeFormat) {
      let FORMAT = FORMAT_DD;
      if (timeFormat === PPTimeFormat.olayDay) {
        FORMAT = FORMAT_DD;
      }
      if (timeFormat === PPTimeFormat.dateAndTime) {
        FORMAT = FORMAT_DD_mm;
      }
      return (
        <RenderTime
          className={classNames(s.renderTime, labelClassName)}
          time={propsValue}
          format={FORMAT}
          showLabelRRuleIcon={false}
          onRemove={() => {
            changeVisible?.(false);
          }}
          shortcut={false}
        ></RenderTime>
      );
    }
    return <Placeholder text={placeholder}></Placeholder>;
  };
  return (
    <Dropdown
      className={classNames(s.timePickerDropdown, className)}
      title={
        <DropdownIcon showArrow={memoVisible} hasArrow={hasArrow}>
          {children ? children : showTimeLabel()}
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      defaultOpen={false}
      open={memoVisible}
      onOpenChange={(open) => {
        changeVisible?.(open);
        if (value && !dayjs(propsValue).isSame(value)) {
          onChange?.(value);
        }
      }}
      disabled={disabled}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlayClassName={classNames(s.picker)}
      overlay={
        <Overlay
          timeFormat={timeFormat}
          value={value}
          onChange={(v) => {
            setValue(v);
          }}
        ></Overlay>
      }
    />
  );
}
