import classNames from 'classnames';
import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PropsWithChildren, useContext, useMemo } from 'react';

import { TableContext } from '../../context';
import s from './index.less';
export type Props = {
  className?: string;
  onRowClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  onRowMouseDown?: MouseEventHandler<HTMLDivElement>;
};

const checkWidth = (item: string | number) => {
  return item;
};

const TableRow: React.FC<PropsWithChildren<Props>> = React.forwardRef((props, refs) => {
  const { children, className, onRowClick, onRowMouseDown } = props;
  const { columnsWidths } = useContext(TableContext);
  const gridTemplateColumns = useMemo(() => {
    const arr = [...columnsWidths];
    arr.map((item) => checkWidth(item));
    return arr.join(' ');
  }, [columnsWidths]);
  return (
    <div
      onClick={(e) => {
        onRowClick?.(e);
      }}
      onMouseDown={onRowMouseDown}
      className={s.row}
    >
      <div
        ref={refs}
        style={{ gridTemplateColumns: gridTemplateColumns }}
        className={classNames('todo-tr', className)}
      >
        {children}
      </div>
    </div>
  );
});

export default TableRow;
