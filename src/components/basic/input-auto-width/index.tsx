import './index.less';

import classNames from 'classnames';
import debounce from 'lodash/debounce';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { InputProps, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

interface PageProps extends Omit<InputProps, 'onChange'> {
  placeholder?: string;
  className?: string;
  maxLength?: number;
  debounceChange?: (value: string) => void;
  debounceTime?: number;
  border?: boolean;
  round?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  autoFocus?: boolean;
}

const InputAutoWidth = React.forwardRef<
  {
    focus: () => void;
  },
  PageProps
>((props, refs) => {
  const {
    className,
    maxLength = 200,
    debounceChange,
    onChange: propOnChange,
    placeholder = I18N.auto.pleaseEnter,
    debounceTime = 200,
    value: propValue = '',
    autoFocus,
    onFocus,
    onBlur,
    disabled,
  } = props;

  const isCompositionRef = useRef<boolean>(false);
  const [value, setValue] = useState<string>(propValue || '');
  const [active, setActive] = useState<boolean>(false);
  const ref = useRef<any>();
  const isCallBackRef = useRef(debounceChange);
  isCallBackRef.current = debounceChange;
  const debounceHandler = useMemo(() => {
    return debounce((value: string) => {
      isCallBackRef.current?.(value);
    }, debounceTime);
  }, [debounceTime]);
  const onChange = (v: string) => {
    setValue(v);
    propOnChange?.(v);
  };
  const onSearch = (v: string) => {
    if (!isCompositionRef.current) {
      debounceHandler(v);
    }
  };
  useImperativeHandle(refs, () => ({
    focus: () => {
      ref.current?.focus?.();
    },
  }));
  useEffect(() => {
    setValue(propValue);
  }, [propValue]);
  return (
    <Tooltip title={value} onlyEllipsis>
      <div className={classNames('input-auto-width', className)}>
        <span className={'todo-input-origin'}>{value}</span>
        <input
          className={classNames('todo-input', {
            ['todo-input-active']: active,
            ['todo-input-disabled']: disabled,
          })}
          autoFocus={autoFocus}
          placeholder={placeholder}
          maxLength={maxLength}
          ref={ref}
          disabled={disabled}
          value={value}
          onChange={(e) => {
            if (!isCompositionRef.current) {
              onSearch(e.target.value);
            }
            onChange?.(e.target.value);
          }}
          onCompositionStart={() => {
            isCompositionRef.current = true;
          }}
          onCompositionEnd={(e) => {
            isCompositionRef.current = false;
            //@ts-ignore
            onSearch(e.target.value);
          }}
          onKeyDown={(e) => {
            e.stopPropagation();
          }}
          onBlur={(e) => {
            setActive(false);
            onBlur?.(e);
          }}
          onFocus={(e) => {
            setActive(true);
            onFocus?.(e);
          }}
        ></input>
      </div>
    </Tooltip>
  );
});

export default InputAutoWidth;
