import React, { useState } from 'react';

import { POPODatePicker } from '@/components/basic';
import { TimeValue } from '@/components/basic/popo-date-picker';
import { getTimePickerFormat2 } from '@/components/basic/popo-date-picker/utils';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  hasArrow?: boolean;
  value?: TimeValue;
  onChange: (v: TimeValue) => void;
  onOpen?: (v: boolean, itemName: string) => void;
};

const AddRow: React.FC<Props> = (props) => {
  const { hasArrow, value, onChange, onOpen } = props;

  const [visible, setVisible] = useState<boolean>(false);
  return (
    <POPODatePicker
      visible={visible}
      onVisible={(v) => {
        setVisible(v);
        onOpen?.(v, 'deadline');
      }}
      hasArrow={hasArrow}
      placeholder={I18N.auto.addTo}
      value={value}
      onChange={onChange}
      hasRRule={false}
      showLabelRRuleIcon
      renderLabel={(time) => {
        let hasHHmm = false;
        if (value?.timeFormat === PPTimeFormat.dateAndTime) {
          hasHHmm = true;
        }
        const str = getTimePickerFormat2({
          time: time,
          hasHHmm: hasHHmm,
        });
        return I18N.template(I18N.auto.end, {
          val1: str,
        });
      }}
    ></POPODatePicker>
  );
};

export default AddRow;
