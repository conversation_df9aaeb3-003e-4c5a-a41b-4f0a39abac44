import { Compiler } from 'webpack';

interface Option {
  versionId: number | string;
}

export default class VersionIdPlugin {
  opts: Option;
  constructor(opts: Option) {
    this.opts = opts;
  }
  apply(compiler: Compiler) {
    if (!this.opts.versionId) {
      console.error('versionId 不可为空');
      return;
    }
    const versionId = this.opts.versionId + '';
    compiler.hooks.emit.tap('VersionId', (compilation) => {
      compilation.assets['version.txt'] = {
        source: () => {
          return versionId + '';
        },
        size: () => {
          return Buffer.byteLength(versionId + '', 'utf8');
        },
      };
    });
  }
}
