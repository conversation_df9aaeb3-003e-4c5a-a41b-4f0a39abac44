import classNames from 'classnames';
import React, { CSSProperties, PropsWithChildren } from 'react';

export type Props = {
  className?: string;
  style?: CSSProperties;
};
const TableCell: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, className, style } = props;

  return (
    <div className={classNames('todo-td', className)} style={style}>
      {children}
    </div>
  );
};

export default TableCell;
