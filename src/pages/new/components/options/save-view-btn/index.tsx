import { Dropdown, Menu, Message, Modal } from '@/components/basic'
import { SaveAsView } from '@/components/basic-project/new-view/save-as-view'
import I18N from '@/utils/I18N'
import { SoftSave } from '@babylon/popo-icons'
import { ChevronDown } from '@bedrock/icons-react'
import React, { FC } from 'react'
import s from './index.less'
import { ViewTypeEnum } from '@/utils/permission'
import { useMemoizedFn } from 'ahooks'
import { useDispatch, useSelector } from 'react-redux'
import { Dispatch, RootState } from '@/models/store'
import { ViewTab } from '@/types'
import { useParams } from 'umi'
import { useGetShownFields } from '@/hooks'
import { planView2ScaleType } from '../../view/timeline/utils'
import { apiProjectResortTaskPost } from '@/api'
import { getIsInSession, isProjectId } from '@/models/utils'
import { OrderCustom } from '@/utils/const'


interface ISaveViewBtnProps {
  showSaveNewBtn: boolean
  isProjectTab: boolean
  canEditView?: boolean | boolean[]
  projectId?: number
  className?: string
}

const SaveViewBtn: FC<ISaveViewBtnProps> = ({
  showSaveNewBtn,
  isProjectTab,
  canEditView,
  projectId,
  className = '',
}) => {
  const { currentViewTab, viewTabList, navigatorId, projectInfo, projectTaskResortParamsList } = useSelector(
    (state: RootState) => ({
      currentViewTab: state.viewSetting.currentViewTab,
      viewTabList: state.viewSetting.viewTabList,
      navigatorId: state.viewSetting.navigatorId,
      projectInfo: state.project.projectInfo,
      projectTaskResortParamsList: state.viewSetting.currentViewTab.projectTaskResortParamsList,
    }),
  )

  const { conditions, querySort, queryGroupBy, planViewMode, queryGroupBys, parsedViewConfig } = currentViewTab
  const { cardConfig } = parsedViewConfig || {}
  const displayList = useGetShownFields()

  const dispatch = useDispatch<Dispatch>()
  const { id } = useParams()

  if (!projectId) {
    projectId = Number(id)
  }

  const handleSaveView = useMemoizedFn(() => {
    Modal.confirm({
      title: I18N.auto.saveAsDefault,
      content: I18N.auto.afterSavingItWillBeMade,
      okText: I18N.auto.cover,
      className: s.saveViewModal,
      mask: !getIsInSession(),
      onOk: () => {
        dispatch.viewSetting
          .updateSetting({})
          .then(() => {
            Message.success(I18N.auto.saveViewAs)
            // 更新viewTabList对应这个视图的值，让保存视图以及另存为新视图按钮消失
            // 保存视图的时候不需要获取viewList吧
            // dispatch.viewSetting.fetchViewList(projectId)
            dispatch.viewSetting.setData({
              currentViewTab: {
                ...currentViewTab,
                changeSearchParams: false,
                initialSearchParams: {
                  conditions,
                  querySort,
                  queryGroupBy,
                  displays: displayList,
                  planViewMode: planView2ScaleType(planViewMode),
                  cardConfig,
                  queryGroupBys,
                },
              },
            })
            // 如果是项目下任务的自定义排序，那么需要请求接口保存排序的顺序
            if (navigatorId && isProjectId(navigatorId) && (querySort as any)?.fieldName === OrderCustom) {
              if ((projectTaskResortParamsList?.length || 0) > 0) {
                apiProjectResortTaskPost({
                  projectId: ~~navigatorId,
                  ops: projectTaskResortParamsList,
                  viewId: currentViewTab.viewId,
                }).then(() => {
                  dispatch.viewSetting.setData({
                    currentViewTab: {
                      ...currentViewTab,
                      projectTaskResortParamsList: [],
                      hasViewChanged: false,
                      changeSearchParams: false,
                    },
                  })
                })
              }
            }
          })
          .catch(() => {
            Message.success(I18N.auto.saveViewLost)
          })
        const list = viewTabList.map(view => {
          if (view.viewId === currentViewTab.viewId) {
            return {
              ...currentViewTab,
              changeSearchParams: false,
            }
          }
          return view
        })
        dispatch.viewSetting.setViewTabList([...(list as ViewTab[])])
        //保存视图不需要刷新当前数据
        //dispatch.viewSetting.toggeleView(currentViewTab as ViewTab);
      },
      centered: true,
      width: 400,
    })
  })

  if (showSaveNewBtn && isProjectTab) {
    const getSaveAsView = ({ className = '' }) => (
      <SaveAsView>
        {/* <div className={s.overlay__content}>{I18N.auto.saveAsNewVision_2}</div> */}
        <div className={className}>{I18N.auto.saveAsNewVision_2}</div>
      </SaveAsView>
    )

    if (canEditView && currentViewTab.viewDistribution !== ViewTypeEnum.Personal) {
      return (
        <div className={s.saveBtnWrapper}>
          <div className={`${s.save__btn} flex-y-center`}>
            <div onClick={handleSaveView} className={`${s['save__btn--left']} flex-y-center`}>
              <SoftSave className="fs-16" />
              {I18N.auto.saveView}
            </div>
            <Dropdown
              align={{
                offset: [-2, 4],
              }}
              placement="bottomRight"
              trigger="hover"
              overlay={
                // <div
                //   className={s.overlay__wrapper}
                //   onClick={() => {
                //     console.log('dropdownRef.current', dropdownRef.current);
                //     dropdownRef.current.onBlur();
                //   }}
                // >
                //   {getSaveAsView({ className: s.overlay__content })}
                // </div>
                <SaveAsView>
                  <Menu className={s['save__view--menu']}>
                    <Menu.Item key={1}>{I18N.auto.saveAsNewVision_2}</Menu.Item>
                  </Menu>
                </SaveAsView>
              }
            >
              <div className={`${s['save__btn--right']} flex-y-center`}>
                <ChevronDown className="rock-dropdown-down-arrow-icon" />
              </div>
            </Dropdown>
          </div>
        </div>
      )
    }
    return (
      <div className={`${s.saveBtnWrapper} ${className}`}>
        <div className={`${s.save__btn} flex-y-center`}>
          <div style={{ paddingRight: '0.06rem' }} className={`${s['save__btn--left']} flex-y-center`}>
            <SoftSave className="fs-16" />
            {getSaveAsView({})}
          </div>
        </div>
      </div>
    )
  }
  return null
}

export default SaveViewBtn
