import { useMemo } from 'react';

import { RenderTime } from '@/components/basic';
import { FORMAT_DD, FORMAT_DD_mm, PPTimeFormat } from '@/utils/const';

const CardRenderTime: React.FC<any> = (props) => {
  const { timeFormat, value } = props;
  const FORMAT = useMemo(() => {
    let FORMAT = FORMAT_DD;
    if (timeFormat === PPTimeFormat.olayDay) {
      FORMAT = FORMAT_DD;
    }
    if (timeFormat === PPTimeFormat.dateAndTime) {
      FORMAT = FORMAT_DD_mm;
    }
    return FORMAT;
  }, [timeFormat]);
  return (
    <RenderTime
      time={value}
      format={FORMAT}
      showLabelRRuleIcon={false}
      shortcut={false}
    ></RenderTime>
  );
};

export default CardRenderTime;
