import classNames from 'classnames';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import { apiTodoUpdatePriorityPost } from '@/api';
import { IconBtn, Itembg, LevelSelect } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  value: number;
  onChange?: (level: number) => void;
  disabled?: boolean;
  taskId: number;
};

const LevelItem: React.FC<Props> = (props) => {
  const { taskId, value, disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { currentViewTab } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const dispatch = useDispatch<Dispatch>();
  const change = (v: number) => {
    const copyValue = value;
    if (v === copyValue) {
      return;
    }
    apiTodoUpdatePriorityPost({
      taskId: taskId,
      priority: v,
    })
      .finally(() => {
        // 重新请求详情数据
        dispatch.viewSetting.updateItemByDetail({ taskId });
      })
      .catch((error) => {
        console.log(error);
      });
  };
  return (
    <Itembg
      showbg={value > 0}
      className={classNames(s.levelWrapper, {
        [s.levelNoneWrapper]: typeof value === 'undefined',
        [s.levelDisableWrapper]: disabled,
      })}
    >
      <LevelSelect
        value={value}
        placeholder={disabled ? I18N.auto.nothing : I18N.auto.addTo}
        onChange={(v) => {
          change(v!);
        }}
        visible={visible}
        onVisible={setVisible}
        disabled={disabled}
      ></LevelSelect>
      {value > 0 && !disabled ? (
        <IconBtn
          iconClassName={s.clear}
          fontSize={16}
          title={I18N.auto.delete}
          iconName="icon-sys_close"
          onClick={() => {
            change(0);
          }}
        ></IconBtn>
      ) : null}
    </Itembg>
  );
};

export default LevelItem;
