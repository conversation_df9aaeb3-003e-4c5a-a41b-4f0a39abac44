import { ChevronLeft, Close } from '@bedrock/icons-react';
import s from './index.less';
import PeoplePicker from '@/components/basic-project/people-search';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@bedrock/components';
import { FC, PropsWithChildren, useContext, useEffect, useMemo, useState } from 'react';
import { MembersContext } from './context';
import { AvatarPeople, Icon, IconBtn, Message } from '@/components/basic';
import I18N from '@/utils/I18N';
import useProjectShare from '@/components/basic-project/more-option/project-share/overlay/use-share-project';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, RootState } from '@/models/store';
import { ProjectPermissionEnum, RoleList, validatesPermission } from '@/utils/permission';
import { SwitchPermission } from '@/components/basic-project';
import { useMemoizedFn } from 'ahooks';
import { Member } from '@/types';
import {
  apiProjectAddMemberPost,
  apiProjectModifyMemberPost,
  apiProjectRemoveMemberPost,
} from '@/api';
import { EnumRole, TaskNavigatorType } from '@/utils/const';
import { ImTriangleLineLeft1, OperateCloseS, OperateLink } from '@babylon/popo-icons';
import { PeopleSearchContext } from '@/components/basic-project/people-search/context';

interface IMembersOverlayProps {
  onClose: () => void;
  canShare?: boolean;
  onPeoplePickerOpenChange?: (open: boolean) => void;
}
const MembersOverlay: FC<IMembersOverlayProps> = ({
  onClose,
  canShare,
  onPeoplePickerOpenChange,
}) => {
  const [step, setStep] = useState(0);
  const [selectedMembers, setSelectedMembers] = useState<Member[]>([]);
  const { projectInfo } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo,
  }));
  const dispatch = useDispatch<Dispatch>();

  let content = null;

  const handleStepChange = (index: number) => {
    setStep(index);
  };

  const handleSelectMember = (member: Member[]) => {
    console.log('onSelect:', member);
    setSelectedMembers(member);
  };

  const handleUpdatePermissions = useMemoizedFn(() => {
    dispatch.viewSetting.updatePermissions({
      permissions: [
        ProjectPermissionEnum.CAN_ADD_MANAGER,
        ProjectPermissionEnum.CAN_ADD_EDITOR,
        ProjectPermissionEnum.CAN_ADD_VIEWER,
        ProjectPermissionEnum.CAN_REMOVE_MANAGER,
        ProjectPermissionEnum.CAN_REMOVE_EDITOR,
        ProjectPermissionEnum.CAN_REMOVE_VIEWER,
        ProjectPermissionEnum.CAN_EDIT_MEMBER,
        ProjectPermissionEnum.CAN_SHARE,
      ],
    });
  });

  useEffect(() => {
    if (projectInfo.projectId) {
      dispatch.project.getMemberList(String(projectInfo.projectId));
      handleUpdatePermissions();
    }
  }, [projectInfo.projectId]);

  if (step === 0) {
    content = <ExistMembersList />;
  }
  if (step === 1) {
    content = <InvitedMembers />;
  }
  return (
    <MembersContext.Provider
      value={{
        onClose,
        onStepChange: handleStepChange,
        onMembersChange: handleSelectMember,
        selectedMembers,
        currentStep: step,
        canShare,
        updatePermissions: handleUpdatePermissions,
        onPeoplePickerOpenChange,
      }}
    >
      {content}
    </MembersContext.Provider>
  );
};

interface IMembersOverlayContainerProps {
  title: string;
  showBack?: boolean;
  footer?: React.ReactNode;
}
const MembersOverlayContainer: FC<PropsWithChildren<IMembersOverlayContainerProps>> = ({
  title,
  showBack = false,
  children,
  footer,
}) => {
  const {
    onClose,
    onStepChange,
    onMembersChange,
    selectedMembers,
    currentStep,
    canShare,
    onPeoplePickerOpenChange,
  } = useContext(MembersContext);

  const { memberList, permissions } = useSelector((state: RootState) => ({
    memberList: state.project.memberList,
    permissions: state.viewSetting.permissions,
  }));

  const rolListMap = useMemo(() => {
    return RoleList.reduce((acc, cur) => {
      acc[cur.value] = cur.name;
      return acc;
    }, {} as any);
  }, []);

  const existMembersListMap = useMemo(() => {
    return memberList?.reduce((acc, cur) => {
      acc[cur.uid] = cur;
      return acc;
    }, {} as any);
  }, [memberList]);

  const handleMembersChange = (members: Member[]) => {
    let newMembers = members;

    console.log('members:', members);

    const newMembersMap = newMembers.reduce((acc, cur) => {
      acc[cur.uid] = cur;
      // if (existMembersListMap[cur.uid]) {
      //   return { [cur.uid]: { ...cur, ...existMembersListMap[cur.uid] } };
      // }
      return acc;
    }, {} as any);

    selectedMembers.forEach((item) => {
      if (newMembersMap[item.uid]) {
        delete newMembersMap[item.uid];
      }
    });

    memberList?.forEach((item) => {
      if (newMembersMap[item.uid]) {
        delete newMembersMap[item.uid];
      }
    });

    newMembers = Object.values(newMembersMap);
    if (currentStep === 0) {
      onStepChange(1);
    }
    onMembersChange([...selectedMembers, ...newMembers]);
  };

  const [CAN_EDIT_MEMBER] = validatesPermission({
    permissions: permissions,
    key: [ProjectPermissionEnum.CAN_EDIT_MEMBER],
  }) as boolean[];

  const handleBack = () => {
    onMembersChange([]);
    onStepChange(0);
  };

  const extraItemTag = (item: Member) => {
    const existMember = existMembersListMap[item.uid];
    if (existMember) {
      return {
        tagName: I18N.template(I18N.auto.haveBeenGranted, { val1: rolListMap[existMember.role] }),
        disabled: true,
      };
    }
  };

  return (
    <div className={s.members__overlay}>
      <div className={s.header}>
        <div className={`${s.title} flex-y-center`}>
          {showBack && <ImTriangleLineLeft1 onClick={handleBack} className="fs-20 mr-4" />}
          <div className={s.title__content}>{title}</div>
        </div>
        <Button size="small" type="text-subtle" icon={<Close />} onClick={onClose} />
      </div>
      <div className={s.content}>
        {CAN_EDIT_MEMBER ? (
          <PeopleSearchContext.Provider value={{ extraItemTag }}>
            <PeoplePicker
              value={[...(memberList || []), ...(selectedMembers || [])]}
              onChange={handleMembersChange}
              overlayClassName={s['people__picker--overlay']}
              onOpenChange={onPeoplePickerOpenChange}
            />
          </PeopleSearchContext.Provider>
        ) : (
          <Alert type="info" message={I18N.auto.viewersHasNoPermission} className={s.alert} />
        )}
        {children}
      </div>
      {canShare && footer && <div className={s.footer}>{footer}</div>}
    </div>
  );
};

/**
 * 已经选择的成员
 * @returns
 */
const ExistMembersList = () => {
  const { updatePermissions } = useContext(MembersContext);

  const { projectInfo, memberList, permissions, userInfo } = useSelector((state: RootState) => ({
    memberList: state.project.memberList,
    projectInfo: state.project.projectInfo,
    permissions: state.viewSetting.permissions,
    userInfo: state.user.userInfo,
  }));

  const { onShare, onCopy } = useProjectShare({ projectInfo });

  const dispatch = useDispatch<Dispatch>();

  const updateMembers = (member: Member, isDelete?: boolean) => {
    let members = memberList;
    if (isDelete) {
      members = memberList?.filter((item) => item.uid !== member.uid);
      dispatch.project.getProject({
        id: String(String(projectInfo?.projectId)),
        hideLoading: true,
      });
    } else {
      members = memberList?.map((item) => {
        if (item.uid === member.uid) {
          return member;
        }
        return item;
      });
    }

    dispatch.project.setData({
      memberList: members,
    });
    updatePermissions?.();
  };

  const update = useMemoizedFn((member: Member) => {
    apiProjectModifyMemberPost({
      projectId: projectInfo.projectId,
      members: [
        {
          memberId: member.uid,
          memberType: member.sessionType,
          role: member.role,
        },
      ],
    }).then(() => {
      updateMembers(member);
    });
  });

  const remove = useMemoizedFn((member: Member) => {
    apiProjectRemoveMemberPost({
      projectId: projectInfo.projectId,
      members: [
        {
          memberId: member.uid,
          memberType: member.sessionType,
          role: member.role,
        },
      ],
    }).then(() => {
      updateMembers(member, true);
    });
  });

  const onExit = useMemoizedFn(() => {
    dispatch.project.getPingProjectList();
    dispatch.viewSetting.openNavigator({
      navigatorId: TaskNavigatorType.assignToMe,
    });
  });

  const [CAN_ADD_MANAGER, CAN_EDIT_MEMBER] = validatesPermission({
    permissions: permissions,
    key: [ProjectPermissionEnum.CAN_ADD_MANAGER, ProjectPermissionEnum.CAN_EDIT_MEMBER],
  }) as boolean[];

  return (
    <MembersOverlayContainer
      title={I18N.auto.memberManagement}
      footer={
        <div className={s.members__footer}>
          <Button
            icon={<Icon fontSize={16} name="icon-details_nav_share" />}
            onClick={onShare}
            type="checked-neutral"
            size="medium"
            className={s.footer__btn}
          >
            {I18N.auto.shareToConversation}
          </Button>
          <Button
            size="medium"
            icon={<OperateLink className="fs-16" />}
            onClick={onCopy}
            type="checked-neutral"
            className={s.footer__btn}
          >
            {I18N.auto.copyLink}
          </Button>
        </div>
      }
    >
      <div className={s.member__list}>
        {memberList?.map((item) => {
          const isMe = userInfo?.uid === item.uid;

          return (
            <div style={{ minWidth: 0 }} className={`${s.member} flex-y-center`}>
              <div style={{ minWidth: 0 }} className={`${s.member__left} flex-y-center`}>
                <div className={s.member__avatar}>
                  <AvatarPeople className={s.avatar} avatarUrl={item?.avatarUrl} />
                </div>
                <div style={{ minWidth: 0 }} className="flex-y-center">
                  <span className={`${s.member__name} ellipsis`}>{item.name}</span>
                  {item.isCreator ? (
                    <div className={s.create__tag}>{I18N.auto.assignedBy}</div>
                  ) : null}
                </div>
              </div>
              <div className={s.member__right}>
                <SwitchPermission
                  value={item.role}
                  className={s.switch__permission}
                  onChange={(v) => {
                    update({
                      ...item,
                      role: v,
                    });
                  }}
                  showRemove={CAN_ADD_MANAGER && !isMe}
                  onRemove={() => {
                    remove(item);
                  }}
                  onExit={onExit}
                  showExit={isMe}
                  permissions={permissions}
                  // disabled={disabled}
                  isMe={isMe}
                  projectInfo={projectInfo}
                />
              </div>
            </div>
          );
        })}
      </div>
    </MembersOverlayContainer>
  );
};

/**
 * 邀请的成员
 * @returns
 */
const InvitedMembers = () => {
  const { projectInfo, memberList, permissions, userInfo } = useSelector((state: RootState) => ({
    memberList: state.project.memberList,
    projectInfo: state.project.projectInfo,
    permissions: state.viewSetting.permissions,
    userInfo: state.user.userInfo,
  }));
  const { selectedMembers, onMembersChange, onStepChange } = useContext(MembersContext);
  const dispatch = useDispatch<Dispatch>();

  const updateMember = (v: Member) => {
    const _members = selectedMembers?.map((item) => {
      if (item.uid === v.uid) {
        return v;
      }
      return item;
    });
    if (Array.isArray(_members)) {
      onMembersChange(_members);
    }
  };

  const onDelete = (uid: string) => {
    if (Array.isArray(selectedMembers)) {
      const newMembers = selectedMembers.filter((item) => item.uid !== uid);
      if (!newMembers.length) {
        handleCancel();
      } else {
        onMembersChange(newMembers);
      }
    }
  };

  const handleInvite = () => {
    const projectId = projectInfo?.projectId;

    apiProjectAddMemberPost({
      projectId,
      members: selectedMembers.map((item) => ({
        role: item.role,
        memberId: item.uid,
        memberType: item.sessionType,
      })),
    })
      .then(() => {
        // const _members = (memberList || []).concat(selectedMembers);
        // if (location.href.includes('/project')) {
        //   dispatch.project.getProject({
        //     id: String(projectId),
        //     hideLoading: true,
        //   });
        // }
        dispatch.project.getProject({
          id: String(projectId),
          hideLoading: true,
        });
        onMembersChange([]);
        dispatch.project.getMemberList(String(projectId));
        Message.success(I18N.auto.invitationSuccessful);
      })
      .finally(() => {
        onStepChange(0);
      });
  };

  const handleCancel = () => {
    onMembersChange([]);
    onStepChange(0);
  };

  return (
    <MembersOverlayContainer
      showBack={true}
      title={I18N.auto.inviteMembers}
      footer={
        <div
          className={`${s.members__footer} flex-y-center`}
          style={{ justifyContent: 'space-between' }}
        >
          <span className={s.footer__desc}></span>
          <div className={s.invite__actions}>
            <Button onClick={handleCancel} size="medium" type="checked-neutral">
              {I18N.auto.cancel}
            </Button>
            <Button
              className={s.primary__btn}
              disabled={!selectedMembers.length}
              size="medium"
              type="primary"
              onClick={handleInvite}
            >
              {I18N.auto.invitation}
            </Button>
          </div>
        </div>
      }
    >
      <div className={s.invited__list}>
        {!!selectedMembers.length && (
          <>
            <div className={s.desc}>{I18N.auto.theFollowingUsersWill}</div>
            <div className={s.list}>
              {selectedMembers?.map((item, index) => {
                return (
                  <div style={{ minWidth: 0 }} className={`${s.member} flex-y-center`}>
                    <div style={{ minWidth: 0 }} className={`${s.member__left} flex-y-center`}>
                      <div className={s.member__avatar}>
                        <AvatarPeople className={s.avatar} avatarUrl={item?.avatarUrl} />
                      </div>
                      <div style={{ minWidth: 0 }} className="flex-y-center">
                        <span className={`${s.member__name} ellipsis`}>{item?.name}</span>
                      </div>
                    </div>
                    <div className={`${s.member__right} flex-y-center`}>
                      <SwitchPermission
                        permissions={permissions}
                        value={item?.role}
                        onChange={(v) => {
                          updateMember({
                            ...item,
                            role: v,
                          });
                        }}
                        className={s.switch__permission}
                      />
                      {item.hiddenDel ? null : (
                        <IconBtn
                          className="ml-10 flex-no-shrink"
                          icon={<OperateCloseS className={s.close}></OperateCloseS>}
                          title={I18N.auto.remove}
                          onClick={() => {
                            onDelete(item.uid);
                          }}
                        />
                      )}
                    </div>
                    {/* <div className={s.item} key={index}>
                  <AvatarPeople className={s.avatar} avatarUrl={item.avatarUrl}></AvatarPeople>
                  <div className={s.name}>{item.name}</div>
                  <SwitchPermission
                    permissions={permissions}
                    value={item.role}
                    onChange={(v) => {
                      // updateMember({
                      //   ...item,
                      //   role: v,
                      // });
                    }}
                    className={s.permission}
                  ></SwitchPermission>
                  {
                    //@ts-ignore
                    item.hiddenDel ? null : (
                      <IconBtn
                        className="flex-no-shrink"
                        icon={<OperateCloseS className={s.close}></OperateCloseS>}
                        title={I18N.auto.remove}
                        onClick={() => {
                          // onDelete(item.uid);
                        }}
                      ></IconBtn>
                    )
                  }
                </div> */}
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </MembersOverlayContainer>
  );
};

export default MembersOverlay;
