import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { ReactNode, useMemo } from 'react';

import { Icon } from '@/components/basic';
import { FORMAT_DD_mm, getTimePickerFormat } from '@/components/basic/popo-date-picker/utils';
import { getComFormat } from '@/utils/date-format';

import s from './index.less';
export type Props = {
  className?: string;
  time?: number | Dayjs | Date;
  rrule?: string;
  showLabelRRuleIcon?: boolean;
  format?: string;
  onRemove?: () => void;
  renderLabel?: (dayjs: Dayjs) => ReactNode;
  shortcut?: boolean;
};
export default function RenderTime(props: Props) {
  const {
    className,
    time,
    rrule,
    showLabelRRuleIcon,
    format = FORMAT_DD_mm,
    renderLabel,
    shortcut = true,
  } = props;
  const showLabel = useMemo(() => {
    let hasHHmm = false;
    if (format === FORMAT_DD_mm) {
      hasHHmm = true;
    }
    if (time) {
      if (shortcut) {
        return getTimePickerFormat({
          time: dayjs(time),
          hasHHmm,
        });
      }
      return dayjs(time).format(
        getComFormat({
          diffYear: false,
          time: dayjs(time),
          hasHHmm,
        })
      );
    }
    return null;
  }, [time, format, shortcut]);
  return (
    <div className={classNames(s.labelTime, className, 'todo-base-label')}>
      <div className={`${s.labelTimeLeft} label__time--left`}>
        {renderLabel ? renderLabel(dayjs(time)) : showLabel}
        {rrule && showLabelRRuleIcon ? (
          <Icon className={s.icon} name="icon-pc_details_cycle"></Icon>
        ) : null}
      </div>
    </div>
  );
}
