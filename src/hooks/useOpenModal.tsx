import { Modal } from '@bedrock/components';
import { ModalFuncProps } from '@bedrock/components/lib/Modal';
import { useRef } from 'react';
interface IModalProps extends ModalFuncProps {}

export function useOpenModal() {
  const modalFuncsRef = useRef<{
    destroy: (...args: any[]) => void;
    update: (nextConfig: ModalFuncProps) => void;
  }>({});

  const openModal = ({
    content,
    onClose,
    onOk,
    params,
    ...rest
  }: IModalProps & { params: Record<string, any> }) => {
    console.log(params);

    modalFuncsRef.current = Modal.open({
      content,
      centered: true,
      onClose: (e) => {
        modalFuncsRef.current.destroy();
        onClose?.(e);
      },
      onOk: (e) => {
        modalFuncsRef.current.destroy();
        onOk?.(e);
      },
      ...rest,
    });
    return modalFuncsRef.current;
  };

  return { openModal };
}
