body {
  .rock-tooltip {
    max-width: 300px; //基础组件内不要写important, 如需覆盖去具体业务代码修改
    .rock-tooltip-inner {
      background-color: var(--admintoastBg);
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 12px;

      .rock-tooltip-title {
        max-height: 100px;
        overflow-y: auto;
        &::-webkit-scrollbar-thumb {
          background: rgba(153, 153, 153, 0.4);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(153, 153, 153, 0.6);
        }

        // 对于Firefox
        scrollbar-width: thin;
        scrollbar-color: rgba(153, 153, 153, 0.4) transparent;
      }
    }
    .rock-tooltip-title {
      line-height: 20px;
    }
    .rock-tooltip-arrow:before {
      background-color: var(--admintoastBg);
    }
  }
}
