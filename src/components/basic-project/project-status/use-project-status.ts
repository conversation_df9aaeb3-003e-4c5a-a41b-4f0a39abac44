import { useMemoizedFn } from 'ahooks';
import dayjs, { Dayjs } from 'dayjs';

import Modal from '@/components/basic/modal';
import { EnumProjectStatus } from '@/utils/const';
import { getComFormat } from '@/utils/date-format';
import I18N from '@/utils/I18N';

import { projectStatusList } from './project-status-tag/utils';

export type ProjectRecord = {
  preState: EnumProjectStatus;
  state: EnumProjectStatus;
  userName: string;
  updateTime?: number;
};

interface Props {
  onChange?: (k: EnumProjectStatus) => void;
}

export function getStatusChangeName(state?: EnumProjectStatus, preState?: EnumProjectStatus) {
  if (!preState && !state) {
    return '';
  }
  const currentName = projectStatusList.find((item) => item.key === state)?.name;
  if (state && !preState) {
    return I18N.template(I18N.auto.newProjectStatus, { val1: currentName });
  }
  const preName = projectStatusList.find((item) => item.key === preState)?.name;
  return I18N.template(I18N.auto.updatedTo, { val1: preName, val2: currentName });
}

export function getStatusTimeStr(updateTime?: number | Dayjs) {
  if (!updateTime) {
    return '';
  }
  return dayjs(updateTime).format(
    getComFormat({
      diffYear: true,
      time: dayjs(updateTime),
    })
  );
}

const useProjectStatus = (props: Props) => {
  const { onChange } = props;
  const changeProjectStatus = useMemoizedFn((v: EnumProjectStatus) => {
    const statusInfo = projectStatusList.find((item) => item.key === v) || {
      name: '',
      key: '',
      className: '',
      icon: '',
    };
    Modal.confirm(
      {
        centered: true,
        width: 400,
        title: I18N.template(I18N.auto.confirmTheChangeItem, { val1: statusInfo.name }),
        content: I18N.auto.afterTheChangeThereWillBe,
        cancelText: I18N.auto.cancel,
        okText: I18N.auto.determine,
        onOk: () => {
          onChange?.(v);
        },
        zIndex: 1100,
      },
      'info'
    );
  });

  return {
    changeProjectStatus,
  };
};

export default useProjectStatus;
