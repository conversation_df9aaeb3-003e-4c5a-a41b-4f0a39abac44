import classNames from 'classnames';
import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { IconBtn, RenderLevel, RenderPeoples } from '@/components/basic';
import { Level } from '@/components/basic/level-picker/utils';
import { Dispatch, RootState } from '@/models/store';
import { TaskGroupItem, UserInfo } from '@/types';
import { FieldTypeEnum } from '@/types/custom-field';
import { EnumKanbanAddSource, TaskNavigatorType, ViewAddSource } from '@/utils/const';
import { EnumGroupBy } from '@/utils/fields';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';

export interface ContainerProps {
  name: string;
}

export interface CardProps extends ContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  horizontal?: boolean;
  hover?: boolean;
  handleProps?: React.HTMLAttributes<any>;
  scrollable?: boolean;
  shadow?: boolean;
  onClick?: () => void;
  onRemove?: () => void;
  id: string;
}

const LaneContainer = React.forwardRef<any, CardProps>((props, ref) => {
  const { children, style, id } = props;
  const dispatch = useDispatch<Dispatch>();
  const [showBg, setShowBg] = useState(false);
  const { groupList, permissions, customFields } = useSelector((state: RootState) => ({
    groupList: state.kanban.groupList,
    permissions: state.viewSetting.permissions,
    customFields: state.viewSetting.customFields,
  }));
  const item = useMemo<TaskGroupItem>(() => {
    return groupList.find((item) => item.id === id) || ({} as TaskGroupItem);
  }, [groupList, id]);

  const onAdd = (id: string, defaultParams: Record<string, any>) => {
    // 标注新建来源
    ViewAddSource.kanban = EnumKanbanAddSource.laneTopCreate;
    dispatch.kanban.tableAddTaskItem({
      groupId: id,
      isHeader: true,
      defaultParams: defaultParams || {},
    });
  };
  const showAddBtn = useMemo(() => {
    return validatesPermission({
      permissions: permissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    });
  }, [permissions]);
  const memoHead = useMemo(() => {
    if (item.customFieldId) {
      const field = customFields.find((v) => v.fieldId === item.customFieldId);
      if (field?.type === FieldTypeEnum.user) {
        const customFieldUsers = item.customFieldUsers;
        return customFieldUsers?.length ? (
          <RenderPeoples
            showFinishedIcon
            list={customFieldUsers || []}
            count={customFieldUsers.length}
            maxShowCount={5}
            avatarClassName="mr-4"
          ></RenderPeoples>
        ) : (
          I18N.auto.nothing
        );
      }
      if (field?.type === FieldTypeEnum.option) {
        return item.title;
      }

      return item.title;
    } else {
      if (item.groupBy === EnumGroupBy.ASSIGNEE) {
        return item.assignees?.length ? (
          <RenderPeoples
            showFinishedIcon
            list={item.assignees || []}
            count={item.assigneeCount || item.assignees.length}
            maxShowCount={5}
            avatarClassName="mr-4"
          ></RenderPeoples>
        ) : (
          I18N.auto.nothing
        );
      }
      if (item.groupBy === EnumGroupBy.PRIORITY) {
        return item.priority === Level.default ? (
          I18N.auto.nothing
        ) : (
          <RenderLevel value={item.priority}></RenderLevel>
        );
      }
      return item.title;
    }
  }, [item]);
  return (
    <div ref={ref} className={classNames(s.laneContainer, { [s.showBg]: showBg })} style={style}>
      <div
        className={s.head}
        onMouseEnter={() => {
          setShowBg(true);
        }}
        onMouseLeave={() => setShowBg(false)}
      >
        <div className={s.left}>
          <div className={s.title}>{memoHead}</div>
          <div className={s.count}>{item.count}</div>
        </div>
        {showAddBtn ? (
          <IconBtn
            onClick={(e) => {
              e.stopPropagation();
              onAdd?.(id, item.defaultParams);
            }}
            title={I18N.auto.newTask}
            className={s.addIcon}
            iconName="icon-sys_add"
          ></IconBtn>
        ) : null}
      </div>
      <div className={s.body}>{children}</div>
    </div>
  );
});

export default LaneContainer;
