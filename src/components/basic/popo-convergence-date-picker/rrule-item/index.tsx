import classNames from 'classnames';
import React from 'react';

import { IconBtn, Itembg, RrulePicker, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import SelectItemContainer from '../select-item-container';
import s from './index.less';

export type Props = {
  className?: string;
  value?: string;
  suffixIcon?: React.ReactNode;
  showSource?: string;
  onChange?: (v: string) => void;
  disabled?: boolean;
  loopInducedDisabled?: boolean;
  time?: number;
};

const RruleItem: React.FC<Props> = (props) => {
  const { value, onChange, disabled, time, loopInducedDisabled, showSource, suffixIcon } = props;
  return (
    <Tooltip
      title={loopInducedDisabled ? I18N.auto.theNextOneHasBeenGenerated : ''}
      placement="topLeft"
    >
      <SelectItemContainer icon={suffixIcon} title={I18N.auto.repeat}>
        <Itembg disabled={disabled || loopInducedDisabled}>
          <RrulePicker
            time={time}
            value={value || 0}
            placeholder={I18N.auto.nothing}
            onChange={onChange}
            disabled={disabled || loopInducedDisabled}
            renderLabel={(label) => {
              return <div className={s.rrulelabel}>{label}</div>;
            }}
            className={showSource === 'detail' ? s.detailRuleItemPicker : ''}
          />
        </Itembg>
      </SelectItemContainer>
    </Tooltip>
  );
};

export default RruleItem;
