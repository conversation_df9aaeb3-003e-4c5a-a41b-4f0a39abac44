import { defineConfig } from 'umi';
import ScoutWebpackPlugin from '@scout/webpack-plugin/dist';
import versionIdPlugin from './build/versionIdPlugin';
import dayjs from 'dayjs';
//@ts-ignore
import px2rem from 'postcss-plugin-px2rem';
import { dist } from './publish/const';

let versionId = Date.now();

export default defineConfig({
  esbuildMinifyIIFE: true,
  hash: true,
  mfsu: false,
  outputPath: `./${dist}`,
  alias: { quill: '@popo/quill' },
  routes: [
    {
      path: '/',
      redirect: '/new',
    },
    {
      name: '任务',
      path: '/list',
      component: './new',
    },
    {
      name: '会话中项目任务列表',
      path: '/session/project/:id',
      component: './session/project',
    },
    {
      name: '会话中任务列表',
      // 请求方式 /session/tasks?sessionId=xxx&sessionType=dd
      path: '/session/tasks',
      component: './session/tasks',
    },
    {
      name: '任务',
      path: '/new',
      component: './new',
      routes: [
        {
          name: '任务',
          path: '/new/task',
          component: './new/task',
        },
        {
          name: '项目',
          path: '/new/project/:id',
          component: './new/project',
          routes: [
            {
              name: '项目-任务',
              path: '/new/project/:id/tasks',
              component: './new/project/tasks',
            },
            {
              name: '项目成员',
              path: '/new/project/:id/member',
              component: './new/project/members',
            },
            // {
            //   name: '订阅通知',
            //   path: '/new/project/:id/notification',
            //   component: './new/project/notification',
            // },
          ],
        },
        {
          name: '全部项目',
          path: '/new/project-list',
          component: './new/project-list',
        },
        {
          name: '全部项目',
          path: '/new/project-group/:id',
          component: './new/ProjectGroupDetail',
        },
        {
          name: '新增项目',
          path: '/new/project-add',
          component: './new/project-add',
        },
      ],
    },
    {
      // 所有/pc/路径下都是im内嵌界面,不要在配置不相关的路由
      name: 'pc任务',
      path: '/pc',
      component: './pc',
    },
    {
      name: '任务详情',
      path: '/pc/detail/:taskId',
      component: './pc/detail',
    },
    {
      name: '任务',
      path: '/pc/list/',
      component: './pc/list',
    },
  ],
  npmClient: 'pnpm',
  chainWebpack(config, { webpack }) {
    config.plugin('versionId').use(versionIdPlugin, [{ versionId }]);
    config.when(process.env.UMI_ENV === 'production', (config) => {
      config.plugin('scout').use(ScoutWebpackPlugin, [
        {
          appId: '33f71a1c',
          appKey: '7930ec70f0134e03a3fba9718b9a089f',
          exclude: [/bedrock/i],
          publicPath: '/',
        },
      ]);
    });
  },
  extraPostCSSPlugins: [
    px2rem({
      rootValue: 100,
      unitPrecision: 5,
      propWhiteList: [],
      exclude: false,
      ignoreIdentifier: false,
      replace: true,
      mediaQuery: false,
      minPixelValue: 2,
    }),
  ],
  plugins: [require.resolve('@bedrock/async-script-plugin/umi'), '@popo/fontsize2rem'],
  asyncScriptConfig: {
    extraScripts: [
      {
        content:
          'window.ScoutContext = window.loadScript({ src: "https://unpkg.oa.netease.com/@scout/browser/dist/scout.iife.min.js" })',
      },
    ],
    minify: false,
  },
  popoFontSize2Rem: {
    rootValue: 100,
  },
  define: {
    'process.env.UMI_ENV': process.env.UMI_ENV,
    'process.env.POPO_VERSION': versionId,
  },
  metas: [
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0',
    },
  ],
});
