import classNames from 'classnames';
import React, { useMemo } from 'react';

import { Tooltip } from '@/components/basic';
import TodoStatus from '@/components/basic-task/todo-status';
import { TaskInfo } from '@/types';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

export type UnplannedItemProps = {
  taskId: number;
  title: string;
  className?: string;
  data: TaskInfo;
};
const UnplannedItem: React.FC<UnplannedItemProps> = (props) => {
  const { taskId, title, className, data } = props;

  const permissions = useGetPermissions({ taskId });

  const disabledComplete = useMemo(() => {
    const [CAN_COMPLETE] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_COMPLETE],
    }) as boolean[];
    return !CAN_COMPLETE;
  }, [permissions]);
  return (
    <div className={classNames(s.item, className)} taskId={taskId}>
      <Tooltip title={I18N.auto.clickToCompleteTheTask}>
        {!!data?.taskId && (
          <TodoStatus
            className={classNames(s.status)}
            disabled={!data.editable && disabledComplete}
            taskInfo={data}
          ></TodoStatus>
        )}
      </Tooltip>
      <div className={s.todoStr}>{title}</div>
    </div>
  );
};

export default UnplannedItem;
