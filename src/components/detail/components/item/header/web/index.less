.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  height: 56px;
  padding: 14px 24px;
  border-bottom: 1px solid var(--aBlack6);
  .icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    line-height: 24px;
    // margin-left: 10px;
    text-align: center;
    border-radius: 4px;
    size: 24px;
    color: var(--IconPrimary);
    cursor: pointer;
    &:hover {
      background-color: var(--aBlack6);
    }
  }
}
.headLeft {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.deleteTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--TextPrimary);
}
.confirm {
  :global {
    .rock-btn-primary {
      background-color: var(--R600) !important;
      &:hover {
        background-color: var(--R500) !important;
      }
    }
  }
}
