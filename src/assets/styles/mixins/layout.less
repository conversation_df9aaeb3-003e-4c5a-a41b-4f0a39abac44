.absolute-center (@width: auto, @height: auto) {
  position: absolute;
  width: @width;
  height: @height;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.absolute (@top: auto, @right: auto, @bottom: auto, @left: auto) {
  position: absolute;
  top: @top;
  right: @right;
  bottom: @bottom;
  left: @left;
}

.cover () {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
