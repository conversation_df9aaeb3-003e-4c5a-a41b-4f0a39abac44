import { PropsWithChildren } from 'react';
import { Dropdown, Menu } from '../basic';
import I18N from '@/utils/I18N';
import s from './index.less';
import { SelectParam } from '@bedrock/components/lib/Menu/Menu';
import { Data16Pojectline, OperateCollapse2 } from '@babylon/popo-icons';

interface ICreateProjectDropdownProps {
  options?: {
    onCreateProject?: () => void;
    onCreateGroup?: () => void;
  };
}

const CreateProjectDropdown: React.FC<PropsWithChildren<ICreateProjectDropdownProps>> = ({
  options,
  children,
}) => {
  const handleClick = (param: SelectParam) => {
    const { key } = param;
    options?.[key as keyof typeof options]?.();
  };

  return (
    <Dropdown
      title={children}
      arrow={false}
      className={s.dropdown}
      overlay={
        <Menu className={s.menu} onClick={handleClick}>
          <Menu.Item
            icon={<Data16Pojectline className={`${s.icon} fs-16`} />}
            key="onCreateProject"
          >
            {I18N.auto.newProject}
          </Menu.Item>
          <Menu.Item icon={<OperateCollapse2 className={`${s.icon} fs-16`} />} key="onCreateGroup">
            {I18N.auto.newProjectGroup}
          </Menu.Item>
        </Menu>
      }
    />
  );
};

export default CreateProjectDropdown;
