import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode } from 'react';

import { Dropdown, Icon, RenderLevel } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';

import s from './index.less';
import { ILevelOption, Level, LevelSelectOptions } from './utils';

export type Props = {
  value?: number;
  onChange?: (v?: number) => void;
  className?: string;
  // showLabelDeleteIcon?: boolean;
  placeholder?: ReactNode;
  hasArrow?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  disabled?: boolean;
  getPopupContainer?: () => HTMLElement;
};

const LevelSelect: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    value,
    onChange,
    className,
    placeholder,
    hasArrow,
    visible = false,
    onVisible,
    disabled,
    getPopupContainer,
  } = props;
  const change = (item: ILevelOption) => {
    onChange?.(item.value);
    onVisible?.(false);
  };
  // const onRemove = () => {
  //   onChange?.(Level.default);
  //   onVisible?.(false);
  // };
  return (
    <Dropdown
      className={classNames(s.levelSelectDropdown, className)}
      title={
        <DropdownIcon showArrow={visible} hasArrow={hasArrow}>
          <RenderLevel value={value} placeholder={placeholder}></RenderLevel>
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      disabled={disabled}
      defaultOpen={visible}
      open={visible}
      onOpenChange={(open) => {
        onVisible?.(open);
      }}
      //@ts-ignore
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <div className={classNames('com-dropdown-select', s.panel)}>
          <div className={s.level}>
            {LevelSelectOptions.filter((item) => item.showSelect).map((item, index) => {
              return (
                <div
                  key={index}
                  className={classNames(s.levelItem, item.className, {
                    [s.active]: item.value === value,
                  })}
                  onClick={() => {
                    change(item);
                  }}
                >
                  <span>
                    <Icon
                      className={classNames(s.icon, 'com-level-icon')}
                      name={item.IconName}
                    ></Icon>
                    <span className="com-level-label">{item.name}</span>
                  </span>
                  {item.value === value && <Icon className={s.checkIcon} name="icon-sys_check" />}
                </div>
              );
            })}
          </div>
        </div>
      }
    />
  );
};

export default LevelSelect;
