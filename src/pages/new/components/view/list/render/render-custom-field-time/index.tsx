import dayjs, { Dayjs } from 'dayjs';
import React, { useMemo, useRef, useState } from 'react';

import { CustomFieldTime } from '@/components/basic-project';
import { TodoInfo } from '@/types';
import { CustomField } from '@/types/custom-field';
import { PPTimeFormat, TaskTableRowType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  value?: number;
  taskInfo: TodoInfo;
  filedInfo: CustomField;
  onChange?: (time?: number) => void;
  disabled?: boolean;
}

const RenderCreateTime: React.FC<Props> = (props) => {
  const { taskInfo, filedInfo, onChange, value, disabled } = props;
  const { _rowType: rowType } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const ref = useRef(false);
  const changeVisible = (v: boolean) => {
    setVisible(v);
  };
  const change = (v: Dayjs) => {
    onChange?.(dayjs(v).valueOf());
  };

  return (
    <AddRowBox
      iconName="icon-Soft_qk_td"
      tipTitle={value ? '' : I18N.template(I18N.auto.addTo_2, { val1: filedInfo.name })}
      focused={visible}
      disabled={disabled}
      taskId={taskInfo.taskId}
      hasDelete={!!value}
      onClick={(e) => {
        if (ref.current) {
          ref.current = false;
          return;
        }
        if (!visible) {
          changeVisible(!visible);
        }
      }}
      onClear={() => {
        onChange?.(undefined);
      }}
    >
      <div className={s.createTimeBox}>
        <CustomFieldTime
          visible={visible}
          onVisible={(v) => {
            ref.current = true;
            changeVisible(v);
          }}
          onChange={change}
          value={value}
          timeFormat={filedInfo.format as PPTimeFormat}
          // disabled={disabled}
        ></CustomFieldTime>
      </div>
    </AddRowBox>
  );
};

export default RenderCreateTime;
