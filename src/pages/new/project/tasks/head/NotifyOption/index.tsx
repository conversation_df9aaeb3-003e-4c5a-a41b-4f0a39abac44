import {
  apiProjectCheckRolePost,
  apiProjectCreateTeamPost,
  apiProjectNotifySettingListGet,
  apiProjectNotifySettingPut,
  apiProjectRelateTeamPost,
  apiProjectUnrelateTeamPost,
} from '@/api'
import { Button, Dropdown, Icon, Menu, Message, Modal, Switch } from '@/components/basic'
import { useOutsideClick } from '@/hooks'
import { Dispatch, RootState } from '@/models/store'
import Const, { GuideType, NOTIFICATION_TYPE } from '@/utils/const'
import I18N from '@/utils/I18N'
import {
  ImTime1,
  ImTodo1,
  OperateAdd,
  OperateAddcart,
  OperateNoti,
  OperateNewgroup,
  OperateAssociated,
  OperateUngroup,
} from '@babylon/popo-icons'
import { ClickParam } from '@bedrock/components/lib/Menu/Menu'
import { pp, removeStorage } from '@popo-bridge/web'
import { useRequest } from 'ahooks'
import { forwardRef, ReactNode, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import s from './index.less'
import { ConfigProvider } from '@bedrock/components'
import { TaskGuide } from '@/components/guide/useGuide'
import { validatesPermission, ProjectPermissionEnum } from '@/utils/permission'
import GuideCreateTeam from '../more-option/guide-create-team'
import GuideCreateTeamSuccess from '../more-option/guide-create-team-success'
import { Permission } from '@/types'
import { getStorage, StorageType } from '@/utils'

enum ChildrenType {
  help,
  errorTooltip,
  normal,
}

const NotifyOption = () => {
  const { projectInfo, permissions } = useSelector((state: RootState) => ({
    language: state.user.language,
    projectInfo: state.project.projectInfo,
    permissions: state.viewSetting.permissions,
  }))
  const childrenTyperef = useRef<ChildrenType>()

  const [permissionList, setPermissionList] = useState<Permission[]>(permissions)

  const btnRef = useRef<HTMLButtonElement>(null)
  const [open, setOpen] = useState(false)

  const onOk = () => {
    childrenTyperef.current = ChildrenType.normal
  }

  const handleToggleOpen = async () => {
    const nextOpen = !open
    if (nextOpen) {
      await getPermissionList()
    }
    setOpen(!open)
  }

  const outsideRef = useOutsideClick<HTMLDivElement>(e => {
    if (e?.target === btnRef.current || btnRef.current?.contains(e?.target)) return
    setOpen(false)
  })

  useEffect(() => {
    //初始化执行一次
    if (projectInfo?.projectId) {
      //Const.ProjectNewAdd 判断进入此界面是不是从创建页面而来
      const isAdd = !!getStorage(Const.ProjectNewAdd, StorageType.session)
      if (isAdd) {
        removeStorage(Const.ProjectNewAdd)
      }
      if (!childrenTyperef.current) {
        if (projectInfo.createTeamSuccess === false && isAdd) {
          childrenTyperef.current = ChildrenType.normal
        } else if (isAdd) {
          childrenTyperef.current = ChildrenType.help
        } else {
          childrenTyperef.current = ChildrenType.normal
        }
        setTimeout(() => {
          TaskGuide.start(GuideType.project)
        }, 10)
      }
      getPermissionList()
    }
  }, [projectInfo?.projectId])

  const getPermissionList = async () => {
    //参数 约定的操作类型 数组 如
    const keys = [
      ProjectPermissionEnum.CAN_CREATE_IM_TEAM,
      ProjectPermissionEnum.CAN_ENTER_IM_TEAM,
      ProjectPermissionEnum.CAN_CREATE_SCHEDULE,
      ProjectPermissionEnum.CAN_ADD_MANAGER,
      ProjectPermissionEnum.CAN_ADD_EDITOR,
      ProjectPermissionEnum.CAN_ADD_VIEWER,
      ProjectPermissionEnum.CAN_EXIT,
      ProjectPermissionEnum.CAN_DELETE,
    ]

    const list = (await apiProjectCheckRolePost({
      roles: keys,
      projectId: projectInfo?.projectId,
    })) as Permission[]
    setPermissionList(list)
  }

  const genTitle = useCallback(
    children => {
      let _title: React.ReactNode = null
      //项目引导 分2步 群引导和 项目引导属于不同的引导
      if (permissionList.length) {
        if (childrenTyperef.current === ChildrenType.help) {
          const [CAN_CREATE_IM_TEAM, CAN_ENTER_IM_TEAM] = validatesPermission({
            permissions: permissionList,
            key: [ProjectPermissionEnum.CAN_CREATE_IM_TEAM, ProjectPermissionEnum.CAN_ENTER_IM_TEAM],
          }) as boolean[]
          if (!!projectInfo?.tid && CAN_ENTER_IM_TEAM) {
            //设置引导数量
            TaskGuide.setList([GuideType.createTeamSuccess, GuideType.project])
            _title = <GuideCreateTeamSuccess onOk={onOk}>{children}</GuideCreateTeamSuccess>
          } else if (!projectInfo?.tid && CAN_CREATE_IM_TEAM) {
            //设置引导数量
            TaskGuide.setList([GuideType.canCreateTeam, GuideType.project])
            _title = <GuideCreateTeam onOk={onOk}>{children}</GuideCreateTeam>
          } else {
            TaskGuide.setList([GuideType.project])
            //只有项目引导
            _title = children
          }
        } else if (childrenTyperef.current === ChildrenType.normal) {
          if (projectInfo?.tid) {
            TaskGuide.setList([GuideType.createTeamSuccess, GuideType.project])
            _title = <GuideCreateTeamSuccess onOk={onOk}>{children}</GuideCreateTeamSuccess>
          } else {
            //只有项目引导
            TaskGuide.setList([GuideType.project])
            _title = <div>{children}</div>
          }
        } else {
          _title = <div>{children}</div>
        }
        return _title
      }
    },
    [permissionList, projectInfo?.tid],
  )

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext)
  const zIndex = useMemo(() => getGlobalZIndex(), [open])

  return (
    <Dropdown
      trigger="click"
      open={open}
      placement="bottomRight"
      overlay={<NotifyOverlay onToggleOpen={handleToggleOpen} ref={outsideRef} />}
      zIndex={zIndex}
    >
      {genTitle(
        <Button
          ref={node => (btnRef.current = node?.el)}
          onClick={handleToggleOpen}
          icon={<OperateNoti className="fs-16" />}
          type="checked-neutral"
        >
          {I18N.auto.nitifyAlert}
        </Button>,
      )}
    </Dropdown>
  )
}

const nitificationTypeMap = {
  [NOTIFICATION_TYPE.TASK_ASSIGN]: {
    name: I18N.auto.assignTasks,
    desc: I18N.auto.triggerNotificationAfterFilling,
    key: 'notify-add',
  },
  [NOTIFICATION_TYPE.TASK_COMPLETED]: {
    name: I18N.auto.taskCompleted,
    desc: I18N.auto.triggerNotificationAfterExecuted,
    key: 'notify-complete',
  },
  [NOTIFICATION_TYPE.TASK_EXPIRED]: {
    name: I18N.auto.taskOverdue,
    desc: I18N.auto.triggerNotificationOverdue,
    key: 'notify-overdue',
  },
}

const notiIconsMap: Record<string, ReactNode> = {
  [NOTIFICATION_TYPE.TASK_ASSIGN]: <OperateAdd className="fs-16"></OperateAdd>,
  [NOTIFICATION_TYPE.TASK_COMPLETED]: <ImTodo1 className="fs-16"></ImTodo1>,
  [NOTIFICATION_TYPE.TASK_EXPIRED]: <ImTime1 className="fs-16"></ImTime1>,
}

interface INotifyOverlayProps {
  onToggleOpen: () => void
}
/**
 * 关联、创建、解绑群
 * 群消息通知设置
 */
const NotifyOverlay = forwardRef(({ onToggleOpen }: INotifyOverlayProps, ref) => {
  const { projectInfo } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo,
  }))

  const dispatch = useDispatch<Dispatch>()

  const { data = {}, run: getNotifyList } = useRequest(() =>
    apiProjectNotifySettingListGet({ projectId: projectInfo.projectId! + '' }),
  )

  const { run: updateNotify } = useRequest(
    params => apiProjectNotifySettingPut({ projectId: projectInfo.projectId, ...params }),
    {
      manual: true,
      onSuccess: () => {
        Message.success(I18N.auto.savedSuccessfully)
        getNotifyList()
      },
      onError: () => {
        Message.error(I18N.auto.operationFailedPlease_3)
      },
    },
  )

  const { run: unbindTeam } = useRequest(() => apiProjectUnrelateTeamPost({ projectId: projectInfo.projectId }), {
    manual: true,
    onSuccess: () => {
      dispatch.project.getProject({
        id: String(projectInfo.projectId),
        hideLoading: true,
      })
      getNotifyList()
      Message.success(I18N.auto.operateSuccess)
    },
    onError: () => {
      Message.error(I18N.auto.operationFailedPlease_3)
    },
  })

  const { settings = [] } = data

  const handleMenuClick = (param: ClickParam) => {
    const { key, domEvent } = param
    domEvent?.stopPropagation()
    if (key === 'creaet-group') {
      onToggleOpen?.()
      createTeamByProject({ projectId: projectInfo?.projectId!, dispatch }).then(getNotifyList)
    }
    if (key === 'bind-group') {
      onToggleOpen?.()
      linkTeam({ projectId: projectInfo?.projectId!, dispatch }).then(getNotifyList)
    }
    if (key === 'open-group') {
      onToggleOpen?.()
      openSession({ teamId: projectInfo.tid })
    }
    if (key === 'unbind-group') {
      onToggleOpen?.()
      Modal.warning({
        title: I18N.auto.confirmToUnbind,
        content: I18N.auto.afterUnbind,
        centered: true,
        onOk: unbindTeam,
      })
    }
  }

  // @ts-ignore
  const handleInternalRender = (originNode, props) => {
    if (props.eventKey === 'notify-cat') {
      return <div className={s.notify__cat}>{I18N.auto.groupNotification}</div>
    }
    return originNode
  }

  const handleNotifyChange = async (checked: boolean, itemData: any) => {
    try {
      if (!projectInfo.tid) {
        Message.error(I18N.auto.associateGroupFirst)
        return
      }
      updateNotify({
        ...itemData,
        status: checked ? 1 : 0,
      })
    } catch (error) {
      console.log('error', error)
    }
  }

  return (
    <div ref={ref}>
      <Menu
        className={s.notify__menu}
        onClick={handleMenuClick}
        getPopupContainer={() => ref?.current || document.querySelector(`.${s.notify__menu}`)}
        // @ts-ignore
        _internalRenderMenuItem={handleInternalRender}
      >
        {!projectInfo?.tid ? (
          <Menu.SubMenu
            icon={<OperateAddcart className="fs-16" />}
            popupClassName={s.submenu__popover}
            title={I18N.auto.bindTeam}
          >
            <Menu.Item icon={<OperateNewgroup className="fs-16" />} key="creaet-group">
              <span>{I18N.auto.createANewGroupChat}</span>
            </Menu.Item>
            <Menu.Item icon={<OperateAssociated className="fs-16" />} key="bind-group">
              <span>{I18N.auto.relatedGroupChat}</span>
            </Menu.Item>
          </Menu.SubMenu>
        ) : (
          <>
            <Menu.Item icon={<Icon name="icon-details_nav_incart" />} key="open-group">
              <span>{I18N.auto.enterGroup}</span>
            </Menu.Item>
            <Menu.Item icon={<OperateUngroup className="fs-16" />} key="unbind-group">
              <span>{I18N.auto.unbindTeam}</span>
            </Menu.Item>
          </>
        )}

        <Menu.Item icon={<Icon name="icon-details_nav_incart" />} key="notify-cat">
          <span>{I18N.auto.groupNotification}</span>
        </Menu.Item>
        {settings.map(item => {
          const icon = notiIconsMap[item.type!]
          const { name, key } = nitificationTypeMap[item.type! as keyof typeof nitificationTypeMap]
          return (
            <Menu.Item key={key} icon={icon}>
              <span className="flex-1">{name}</span>
              <Switch
                className="ml-10"
                checked={!!item.status && !!projectInfo?.tid}
                onChange={checked => handleNotifyChange(checked, item)}
              />
            </Menu.Item>
          )
        })}
      </Menu>
    </div>
  )
})

interface ITeamProps {
  projectId: number
  dispatch: Dispatch
}

/**
 * 创建群聊
 * @param param0
 * @returns
 */
export const createTeamByProject = ({ projectId, dispatch }: ITeamProps) => {
  return new Promise(resolve => {
    Modal.confirm({
      width: 400,
      title: I18N.auto.createAGroupChat,
      content: I18N.auto.allWithinTheProject,
      centered: true,
      cancelText: I18N.auto.cancel,
      okText: I18N.auto.establish,
      zIndex: 1100,
      // className: s.confirm,
      onOk: () => {
        apiProjectCreateTeamPost({
          projectId: projectId,
        })
          .then(res => {
            if (res.isSuccess) {
              dispatch.project.getProject({
                id: String(String(projectId)),
                hideLoading: true,
              })
              if (res.tid) {
                resolve(res.tid)
                pp.openMessageSession({
                  id: res.tid,
                  type: 2, //此桥 群的type为2
                })
              }
            } else {
              if (res.errorMsg) {
                Message.error(res.errorMsg)
              }
            }
          })
          .catch(error => {
            console.log(error)
          })
      },
    })
  })
}

/**
 * 关联群
 * @param param0
 * @returns
 */
export const linkTeam = ({ projectId, dispatch }: ITeamProps) => {
  return new Promise(resolve => {
    pp.chooseIMContacts({
      groupCollapse: false,
      uncheckableDefaultItems: [],
      organizationSupportSelected: true,
      title: I18N.auto.relatedGroupChat,
      //maxCount: 1,
      multiSelectStatus: 1,
    })
      .then(res => {
        if (Array.isArray(res.data) && res.data.length === 1) {
          const [team] = res.data
          if (team.type === 3) {
            apiProjectRelateTeamPost(
              {
                projectId: projectId,
                teamId: team.id,
              },
              { errorSilent: true },
            )
              .then(() => {
                Message.text(I18N.auto.successfullyAssociated)
                //更新项目详情
                dispatch.project.getProject({
                  id: String(projectId),
                  hideLoading: true,
                })
                resolve(team.id)
              })
              .catch(error => {
                console.log('apiProjectRelateTeamPost error', error)
              })
          } else {
            Message.text(I18N.auto.onlySupportsAssociation)
          }
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        })
      })
  })
}

/**
 * 打开群聊
 * @param teamId 群id
 * @returns
 */
export const openSession = ({ teamId }: { teamId?: string }) => {
  console.log('进入群聊', teamId)
  if (teamId) {
    pp.openMessageSession({
      id: teamId,
      type: 2, //此桥 群的type为2
    })
  }
}

export default NotifyOption
