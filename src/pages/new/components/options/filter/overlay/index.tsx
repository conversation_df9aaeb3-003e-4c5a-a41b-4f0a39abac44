import classNames from 'classnames';
import _ from 'lodash';
import { PropsWithChildren, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, Icon, IconBtn, Scrollbar } from '@/components/basic';
import { CustomFieldSelect } from '@/components/basic-project';
import { Dispatch, RootState } from '@/models/store';
import { isProjectId } from '@/models/utils';
import { FieldTypeEnum } from '@/types/custom-field';
import { Finished } from '@/utils/const';
import {
  attrProject,
  CustomFieldName,
  DeadlineTimeSelectOptions,
  EnumField,
  FilterTime,
  FinishedStatus,
  IField,
  IQuickFilter,
  Priority,
  QuickFilter,
  QuickFilterList,
  SelectType,
  StatusSelectOptions,
  taskSelectFields,
  TimeSelectOptions,
} from '@/utils/fields';
import I18N from '@/utils/I18N';
import { EnumTrack<PERSON><PERSON>ey } from '@/utils/skyline';

import Container from '../../overlay-container';
import Add from '../add';
import { ComFieldSelect, PeopleSelect, PrioritySelect, ProjectSelect } from '../field';
import FieldSelect from '../field-select';
import s from './index.less';

interface Props {}

const Overlay: React.FC<PropsWithChildren<Props>> = (props) => {
  const scrollRef = useRef<any>(null);
  const { conditions, userInfo, customFields, navigatorId } = useSelector((state: RootState) => ({
    conditions: state.viewSetting.currentViewTab?.conditions || [],
    userInfo: state.user.userInfo,
    customFields: state.viewSetting.customFields,
    navigatorId: state.viewSetting.navigatorId,
  }));
  const dispatch = useDispatch<Dispatch>();
  const memoQuickList = useMemo(() => {
    const _QuickFilterList = _.cloneDeep(QuickFilterList);
    conditions.forEach((item) => {
      if (item.fieldName === EnumField.finished) {
        if (item.values?.includes(`${Finished.unFinished}`)) {
          _QuickFilterList[0].checked = true;
        }
        if (item.values?.includes(`${Finished.finished}`)) {
          _QuickFilterList[1].checked = true;
        }
      }
      if (item.fieldName === EnumField.assignee) {
        if (item.values?.includes(userInfo?.uid)) {
          _QuickFilterList[2].checked = true;
        }
      }
      if (item.fieldName === EnumField.deadline) {
        if (item.values?.includes(FilterTime.today)) {
          _QuickFilterList[3].checked = true;
        }
        if (item.values?.includes(FilterTime.thisWeek)) {
          _QuickFilterList[4].checked = true;
        }
      }
    });
    return _QuickFilterList;
  }, [conditions, userInfo]);

  //所有字段
  const allFieldList = useMemo(() => {
    let customFieldRenderType: Record<string, SelectType> = {
      [FieldTypeEnum.option]: SelectType.customOptions,
      [FieldTypeEnum.multiOption]: SelectType.customOptions,
      [FieldTypeEnum.user]: SelectType.people,
      [FieldTypeEnum.datetime]: SelectType.time,
    };
    let customs = customFields
      .filter((f) => f.type in customFieldRenderType)
      .map((f) => {
        return {
          name: f.name,
          value: f.fieldId,
          customField: f,
          renderType: customFieldRenderType[f.type],
        };
      });
    let _taskSelectFields = taskSelectFields;
    _taskSelectFields = _taskSelectFields.filter(
      (item) => !(isProjectId(navigatorId) && item.value === attrProject.value)
    );
    if (customs.length) {
      return _taskSelectFields.concat(customs);
    } else {
      return _taskSelectFields;
    }
  }, [customFields, navigatorId]);

  const memoExisting = useMemo(() => {
    return conditions
      .filter((v): v is NonNullable<typeof v> => !!v)
      .reduce((pre, cur) => {
        pre[cur.customFieldId || cur.fieldName!] = cur.values || [];
        return pre;
      }, {} as Record<string, string[] | FilterTime[] | Priority[] | undefined>);
  }, [conditions]);

  const memoOtherFields = useMemo(() => {
    return allFieldList.filter((item) => !memoExisting[item.value]);
  }, [memoExisting, allFieldList]);

  const onQuickChange = (item: IQuickFilter) => {
    let _conditions = _.cloneDeep(conditions);
    // 未完成
    if (item.fieldName === QuickFilter.unfinished) {
      let conditionItem = _conditions.find((item) => {
        return item.fieldName === EnumField.finished;
      });
      if (conditionItem) {
        if (conditionItem.values?.includes(`${FinishedStatus.UnFinished}`)) {
          conditionItem.values = conditionItem.values.filter(
            (v) => v !== `${FinishedStatus.UnFinished}`
          );
        } else {
          conditionItem.values?.push(`${FinishedStatus.UnFinished}`);
        }
      } else {
        _conditions.push({
          fieldName: EnumField.finished,
          values: [`${FinishedStatus.UnFinished}`],
        });
      }
    }
    // 已完成
    if (item.fieldName === QuickFilter.finished) {
      let conditionItem = _conditions.find((item) => {
        console.log(EnumField.finished === item.fieldName);
        return item.fieldName === EnumField.finished;
      });
      if (conditionItem) {
        if (conditionItem.values?.includes(`${FinishedStatus.Finished}`)) {
          conditionItem.values = conditionItem.values.filter(
            (v) => v !== `${FinishedStatus.Finished}`
          );
        } else {
          conditionItem.values?.push(`${FinishedStatus.Finished}`);
        }
      } else {
        _conditions.push({
          fieldName: EnumField.finished,
          values: [`${FinishedStatus.Finished}`],
        });
      }
    }

    if (item.fieldName === QuickFilter.assignToMe) {
      let conditionItem = _conditions.find((item) => item.fieldName === EnumField.assignee);
      if (conditionItem) {
        if (conditionItem.values?.includes(userInfo?.uid)) {
          conditionItem.values = conditionItem.values.filter((v) => v !== userInfo?.uid);
        } else {
          conditionItem.values?.push(userInfo?.uid);
        }
      } else {
        _conditions.push({
          fieldName: EnumField.assignee,
          values: [userInfo?.uid],
        });
      }
    }
    if (item.fieldName === QuickFilter.deadlineToday) {
      let conditionItem = _conditions.find((item) => item.fieldName === EnumField.deadline);
      if (conditionItem) {
        if (conditionItem.values?.includes(FilterTime.today)) {
          conditionItem.values = conditionItem.values.filter((v) => v !== FilterTime.today);
        } else {
          conditionItem.values?.push(FilterTime.today);
        }
      } else {
        _conditions.push({
          fieldName: EnumField.deadline,
          values: [FilterTime.today],
        });
      }
    }
    if (item.fieldName === QuickFilter.this_week_deadline) {
      let conditionItem = _conditions.find((item) => item.fieldName === EnumField.deadline);
      if (conditionItem) {
        if (conditionItem.values?.includes(FilterTime.thisWeek)) {
          conditionItem.values = conditionItem.values.filter((v) => v !== FilterTime.thisWeek);
        } else {
          conditionItem.values?.push(FilterTime.thisWeek);
        }
      } else {
        _conditions.push({
          fieldName: EnumField.deadline,
          values: [FilterTime.thisWeek],
        });
      }
    }
    dispatch.viewSetting.changeCondition({
      conditions: _.cloneDeep(_conditions),
    });
  };
  const changeSlectFiled = (preField: string, nextField: IField) => {
    let index = conditions.findIndex(
      (item) => item.customFieldId == preField || item.fieldName == preField
    );
    if (index > -1) {
      let list = [...conditions];
      let customField = nextField.customField;
      list.splice(
        index,
        1,
        customField
          ? {
              fieldName: CustomFieldName,
              customFieldId: customField.fieldId,
              customFieldVersion: customField.fieldVersion,
              values: [],
            }
          : {
              fieldName: nextField.value,
              values: [],
            }
      );
      dispatch.viewSetting.changeCondition({
        conditions: _.cloneDeep(list),
      });
    }
  };
  const onAddField = (v: IField) => {
    let customField = v.customField;
    const _conditions = _.cloneDeep(conditions);
    _conditions.push(
      customField
        ? {
            fieldName: CustomFieldName,
            customFieldId: customField.fieldId,
            customFieldVersion: customField.fieldVersion,
            values: [],
          }
        : {
            fieldName: v.value,
            values: [],
          }
    );
    dispatch.viewSetting.changeCondition({
      conditions: _conditions,
    });
    // 新增筛选条件 滚动条置底
    setTimeout(() => {
      scrollRef.current.scrollTop(500);
    }, 0);
  };
  const onDeleteField = (v: IField) => {
    const _conditions = conditions.filter(
      (item) => item.customFieldId !== v.value && item.fieldName !== v.value
    );
    dispatch.viewSetting.changeCondition({
      conditions: _.cloneDeep(_conditions),
    });
  };
  const onClear = () => {
    dispatch.viewSetting.changeCondition({ conditions: [] });
  };
  const onChange = (key: string, v: string[]) => {
    // 埋点 自定义搜索条件 触发值改变时
    dispatch.user.trackingByView({
      key: EnumTrackeKey.TaskCustomFilter,
      fieldName: key,
      value: v,
    });
    //服务端要求全部是字符串
    v = v.map((item) => `${item}`);
    const _conditions = conditions.map((item) => {
      if (item.fieldName === key || item.customFieldId == key) {
        return {
          ...item,
          values: v,
        };
      } else {
        return item;
      }
    });
    dispatch.viewSetting.changeCondition({
      conditions: _.cloneDeep(_conditions),
    });
  };
  return (
    <Container
      title={I18N.auto.screen}
      className={s.overlay}
      headClassName={s.filterHead}
      showClear={true}
      onClear={onClear}
    >
      <div>
        {/* <FieldTextInput></FieldTextInput> */}
        <Scrollbar autoHide autoHeight autoHeightMax={308} ref={scrollRef}>
          <div className={s.box}>
            <div className={s.boxTitle}>{I18N.auto.quickFilterBar}</div>
            <div className={s.quicker}>
              {memoQuickList.map((item, index) => {
                const isLast = index === memoQuickList.length - 1;
                return (
                  <Button
                    key={index}
                    className={classNames(isLast ? '' : 'mr-10', {
                      ['rock-btn-checked']: item.checked,
                    })}
                    icon={<Icon name={item.iconName} fontSize={16}></Icon>}
                    type="checked-neutral"
                    onClick={() => {
                      onQuickChange(item);
                      // 埋点 快捷筛选
                      dispatch.user.trackingByView({
                        key: EnumTrackeKey.TaskQuickFilter,
                        fieldName: item.fieldName,
                      });
                    }}
                  >
                    {item.name}
                  </Button>
                );
              })}
            </div>
          </div>
          <div className={classNames(s.box, s.filterBox)}>
            <div className={s.boxTitle}>{I18N.auto.allFilteringItems}</div>
            <div className={s.filterList}>
              {conditions.map((item) => {
                const self = allFieldList.find(
                  (v) => v.value === item.customFieldId || v.value == item.fieldName
                )!;
                const fieldSelectOptions = allFieldList.filter((item) => {
                  return !(item.value in memoExisting) || item.value === self.value;
                });
                const fieldNames = memoOtherFields.map((v) => v.value);
                fieldNames.push(item.fieldName!);
                const selectData = [...(memoExisting[self?.value] || [])];
                return (
                  <div className={s.filteritem} key={item.fieldName}>
                    <div className={s.attr}>
                      <FieldSelect
                        data={fieldSelectOptions}
                        value={item.customFieldId || item.fieldName!}
                        onChange={(v, field) => {
                          changeSlectFiled(item.customFieldId || item.fieldName!, field);
                        }}
                      ></FieldSelect>
                    </div>
                    <div className={s.value}>
                      {self?.renderType === SelectType.people ? (
                        <PeopleSelect
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.people}
                        ></PeopleSelect>
                      ) : null}
                      {self.renderType === SelectType.time ? (
                        <ComFieldSelect
                          options={TimeSelectOptions}
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.time}
                        ></ComFieldSelect>
                      ) : null}
                      {self?.renderType === SelectType.deadline ? (
                        <ComFieldSelect
                          options={DeadlineTimeSelectOptions}
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.time}
                        ></ComFieldSelect>
                      ) : null}
                      {self?.renderType === SelectType.status ? (
                        <ComFieldSelect
                          options={StatusSelectOptions}
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.status}
                        ></ComFieldSelect>
                      ) : null}
                      {self?.renderType === SelectType.priority ? (
                        <PrioritySelect
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.priority}
                        ></PrioritySelect>
                      ) : null}
                      {self?.renderType === SelectType.project ? (
                        <ProjectSelect
                          value={selectData}
                          onChange={(v: string[]) => {
                            onChange(self.value, v);
                          }}
                          type={SelectType.project}
                        ></ProjectSelect>
                      ) : null}
                      {self?.renderType === SelectType.customOptions ? (
                        <CustomFieldSelect
                          value={selectData as string[]}
                          options={self.customField!.options || []}
                          multiple
                          border
                          size="medium"
                          onChange={(v) => {
                            onChange(self.value, v as string[]);
                          }}
                        ></CustomFieldSelect>
                      ) : null}
                    </div>
                    <IconBtn
                      iconName="icon-close"
                      className={s.delete}
                      onClick={() => {
                        onDeleteField(self);
                      }}
                    ></IconBtn>
                  </div>
                );
              })}
            </div>
          </div>
          <div>
            {memoOtherFields.length ? (
              <div className={s.footer}>
                <Add onChange={onAddField} data={memoOtherFields}></Add>
              </div>
            ) : null}
          </div>
        </Scrollbar>
      </div>
    </Container>
  );
};

export default Overlay;
