import { RenderSelectedItem } from '@bedrock/components/es/Select';
import { ChangeParams } from '@bedrock/components/lib/Select';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo } from 'react';

import { Icon, IconBtn, Select, SelectProps, Tooltip } from '@/components/basic';
import SelectedItem from '@/components/basic/select/selected-item';
import { FieldOptionColor, SelectFieldOption } from '@/types/custom-field';
import { getCustomColor } from '@/utils/customfields';
import I18N from '@/utils/I18N';

import s from './index.less';

export interface IPriorityField {
  name: string;
  value: string;
  className: string;
}

export interface Props extends SelectProps {
  className?: string;
  options: SelectFieldOption[];
  size?: 'large' | 'medium';
  border?: boolean;
  placeholder?: string;
  /**
   * @description 当设置 border:false时，active状态下，是否展示蓝色边框，默认false，即背景色加深
   */
  showBorderWhenActive?: boolean;
  noRadius?: boolean;
  showOptionClose?: boolean;
}

const CustomFieldSelect: React.FC<Props> = ({
  // taskId,
  options,
  size = 'medium',
  border = false,
  showBorderWhenActive = false,
  placeholder = '',
  noRadius = false,
  className = '',
  multipleCheckedType = 'check-icon',
  value,
  ...restProps
}) => {
  const renderLabel = useCallback(
    (option: any) => (
      <Tooltip title={option.name} onlyEllipsis>
        <div
          className={classNames(option.className, 'custom-select-label', s.label)}
          style={{ backgroundColor: getCustomColor(FieldOptionColor[option.color]) }}
        >
          {option.name}
        </div>
      </Tooltip>
    ),
    []
  );

  const renderSelectedItem = useMemoizedFn<RenderSelectedItem>(
    (option: any, params): React.ReactNode => {
      let showClose = 'tagCloseable' in restProps ? !!restProps.tagCloseable : !!restProps.multiple;
      return (
        <>
          <div
            className={classNames(s.label, 'custom-select-label', { [s.withClose]: showClose })}
            style={{ backgroundColor: getCustomColor(FieldOptionColor[option.color]) }}
          >
            <SelectedItem
              showClose={showClose && !restProps.disabled}
              onClose={(e) => {
                params?.onClose?.(e, option);
              }}
              closeClass={s.optionClose}
            >
              {option.name}
            </SelectedItem>
          </div>
        </>
      );
    }
  );

  const renderRest = useCallback(
    (options: any[]) => (
      <Tooltip title={options.map((opt) => opt.name).join(',')}>
        <span className={`${s.label} ${s.rest} custom-select-label`}>+{options.length}</span>
      </Tooltip>
    ),

    []
  );

  return (
    <Select
      multipleCheckedType={multipleCheckedType}
      notFoundContent={<div className={s.notFoundContent}>{I18N.auto.noOptionsAvailable}</div>}
      size={size}
      border={border}
      placeholder={placeholder}
      options={options}
      {...restProps}
      value={value}
      className={`${s.select} ${restProps.disabled ? s.disbaled : ''} ${className} ${
        !showBorderWhenActive ? s.noActiveBorder : ''
      } ${noRadius ? s.noRadius : ''}`}
      dropdownClassName={`${s.dropdownSelect} ${restProps.dropdownClassName || ''}`}
      renderLabel={renderLabel}
      autoHideTitle
      //@ts-ignore
      renderSelectedItem={renderSelectedItem}
      renderRest={renderRest}
    />
  );
};

export default CustomFieldSelect;
