import './index.less';

import Quill from 'quill';

import { POPODocProps, POPOEmbedClass } from '@/components/basic-task/comment/editor/types';
interface PopoDocBlotProps extends POPODocProps {}

class PopoDocBlot extends Quill.import('blots/embed') {
  static create(value: PopoDocBlotProps) {
    let node = super.create();
    if (value.docIcon) {
      let text = value.text;
      if (value.text.length > 50) {
        text = value.text.substring(0, 50) + '...';
      }
      node.innerHTML = `<span class="ql-popo-doc-span"><img src="${value.docIcon}" class="ql-popo-doc-img"></img>${text}</span>`;
    } else {
      node.innerHTML = `${value.text}`;
    }
    node.setAttribute('data-docUrl', value.docUrl);
    node.setAttribute('data-text', value.text);
    // node.contentEditable = false;
    return node;
  }

  static value(node: any) {
    return {
      docUrl: node.getAttribute('data-docUrl'),
      text: node.getAttribute('data-text'),
      docIcon: node.querySelector('img').getAttribute('src'),
    };
  }
}
PopoDocBlot.blotName = 'popoDoc';
PopoDocBlot.tagName = 'span';
PopoDocBlot.className = POPOEmbedClass.doc; //

Quill.register(PopoDocBlot);
