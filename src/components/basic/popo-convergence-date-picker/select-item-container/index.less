.item__container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  margin-left: -4px;
  margin-right: -4px;
  padding: 0 4px;

  .left {
    column-gap: 8px;
    color: var(--TextPrimary);
    margin-right: 8px;
    font-size: 13px;
    white-space: nowrap;
  }

  &:hover {
    background-color: var(--aBlack6);
  }

  :global {
    .todo-item-bg {
      justify-content: flex-end;
      .rock-dropdown-trigger-default {
        // width: auto;
        justify-content: flex-end !important;
        .com-placeholder {
          width: auto;
        }

        & > span:not([class]) {
          max-width: calc(100% - 20px);
          width: auto !important;
        }
      }
    }

    .label__time--left {
      display: block !important;
      width: 100%;
      text-overflow: ellipsis;
      min-width: 0;
    }

    // 修改行内标签容器样式
    .remind__label {
      display: inline-block;
      max-width: 100%;
      vertical-align: middle;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
