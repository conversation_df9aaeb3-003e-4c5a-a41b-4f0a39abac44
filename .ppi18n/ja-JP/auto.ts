export default {
  associateProjectLimit: '关联项目超过上限',
  selectProjectGroup: 'フォルダーを選択',
  addProjectGroup: 'フォルダーに追加',
  ungroupedProject: 'フォルダーに追加されないプロジェクト',
  addProject: 'プロジェクトを追加',
  projectGroupGuideDesc: 'プロジェクトを簡単に分類・管理します',
  moveToGroup: 'フォルダーに追加',
  canDragToGaoup: 'プロジェクトをこちらにドラッグして追加可能',
  afterProjectDelGroup: 'フォルダーを削除しても、中のプロジェクトは保留されます。',
  confirmDelProjectGroup: 'フォルダー {val1} を削除しますか？',
  delGroup: '削除',
  newGroup: '新規フォルダー',
  newProjectGroup: '新規フォルダー',
  new: '新規作成',
  members: 'メンバー{val1}',
  setTaskNotify: 'タスクの通知を設定し、いつでも進捗状況を追跡できます。',
  clickJumpGTeam:
    'こちらからプロジェクト専用グループにアクセスし、コミュニケーションを開始しよう！',
  afterUnbind: '続行すると、タスク関連の通知はグループ会話に送信されなくなります。',
  operateSuccess: '正常に操作しました',
  confirmToUnbind: 'グループ会話との関連付けを解除しますか？',
  unbindTeam: 'グループの関連付けを解除',
  bindTeam: '関連グループ',
  nitifyAlert: '通知',
  membersAuth: 'メンバーと権限',
  haveBeenGranted: '{val1}権限あり',
  viewersHasNoPermission:
    '共同編集者を招待する権限がありません。他のユーザーを招待する必要がある場合は、管理者に連絡してください。',
  enterGroup: '関連グループに参加',
  past: '延滞中',
  viewSubTask: 'サブタスクを表示',
  subTask: 'サブタスク',
  subTaskPlaceholder_2: 'サブタスクのタイトルを入力',
  subTaskPlaceholder: 'サブタスクのタイトルを入力、Enterで確認',
  delSubTask: 'サブタスクを削除',
  transferIndependent: '独立タスクに格上げ',
  transferIndependentSuccess: '独立したタスクに格上げました',
  AISplitSubtask: '自動分割',
  deleteSubtasksConcurrent: 'すべてのサブタスクを削',
  keepSubtasks: 'サブタスクを削除しない',
  newSubtask: '新規サブタスク',
  smartSubtaskDesc:
    'タスク内にサブタスクを作成し、AI を活用してタスクを分割することで、タスクをよりシンプルにし、作業効率を向上させることができます。',
  smartSubtask: '自動サブタスク管理',
  subtaskLimit: '同一タスクに最大で{val1}のサブタスクを追加可能です',
  regenerate: '再生成',
  splitSubtaskFail: 'サブタスクの生成に失敗しました。再試行してください',
  editingNotAllowed: '編集できません',
  backToParentTask: '親タスクへ',
  addTitleEnter: 'サブタスクのタイトルを入力、Enterで確認',
  unnamedProject: '名称未設定',
  project: 'プロジェクト',
  terminateTheTaskAnd: 'プロジェクト [{val1}] との関連付けを解除します',
  associateTasks: 'タスクをプロジェクト [{val1}] に関連付けます',
  removePermissions: '権限を削除',
  exitTheProject: 'プロジェクトを退出',
  onlyViewingIsNotAllowed: 'タスクを表示できますが、タスクやプロジェクトを編集することができません',
  viewOnly: '閲覧者',
  modifyExceptProject: 'タスクを編集したり、完了にしたりすることができます',
  projectMembers: 'メンバー',
  canModifyAll:
    'プロジェクト名、プロジェクトステータス、管理タスク、パブリックビュー、カスタムフィールドを編集',
  administrators: '管理者',
  file: 'アーカイブ済み',
  thereIsARiskInvolved: 'リスクあり',
  suspend: '一時停止',
  haveInHand: '進行中',
  afterTheChangeThereWillBe:
    '続行すると、関連する編集履歴が生成され、プロジェクトメンバーに通知が送信されます。',
  confirmTheChangeItem: 'ステータスを{val1}に変更しますか？',
  updatedTo: '{val1} から {val2} に更新されました',
  newProjectStatus: '新規プロジェクトのステータス：{val1}',
  searchForProjects: 'プロジェクトを検索',
  thereIsCurrentlyNoBelongingItem: '関連プロジェクトなし',
  searchGroupChatOr: 'グループ会話またはユーザーを検索',
  chooseGroupChatOr: 'グループ会話またはユーザーに共有',
  copySuccessful: 'コピーしました',
  sharingFailed: '共有に失敗しました',
  sharingSuccess: '共有しました',
  shareTo: '会話まで共有',
  copyLink: 'コピー',
  shareToConversation: '会話まで共有',
  invitation: '招待',
  inviteTheProjectToBecome: 'プロジェクトメンバーを招待',
  awardedForQueryOnly: '閲覧権限あり',
  projectAwarded: 'メンバー権限あり',
  managementHasBeenGranted: '管理者権限あり',
  afterExitingYouWill:
    '退出後にはプロジェクト内のタスクを表示できなくなりますが、割り当てられたタスクは保持されます。',
  signOut: '退出',
  confirmDisbandItem: 'プロジェクト「{val1}」を解散しますか？',
  confirmExitItem: 'プロジェクト「{val1}」から退出しますか？',
  preservation: '保存',
  editProject: 'プロジェクトを編集',
  pleaseEnterTheProject: 'プロジェクト名を入力してください',
  projectIconAnd: 'プロジェクトアイコンと名称',
  theFollowingUsersWill: '以下のユーザーに招待状を送信',
  inviteMembersToAdd: 'メンバーの追加',
  taskAssistant: 'タスクヘルパー',
  atProjectInitiation:
    'プロジェクトを開始するときは、チームメンバー間の効率的情報共有や意思疎通を確保するために、プロジェクト専用グループを作成することをお勧めします。',
  createANewProjectGroup: 'プロジェクト専用グループの新規作成',
  clickHereToStand:
    'こちらからプロジェクト専用グループにアクセスし、コミュニケーションを開始しよう！',
  theProjectGroupHasBeenCreated: 'プロジェクト専用グループ作成完了',
  pleaseEnter: '入力してください',
  chooseAColor: 'カラーの選択',
  selectAvatar: 'アイコンの選択',
  changeIcon: 'アイコンを変更',
  selectIcon: 'アイコンの選択',
  easyToLearnItems: '簡単プロジェクト',
  invitationSuccessful: '招待成功',
  userFeedback: 'フィードバック',
  userManual: 'ご利用ガイド',
  member: 'メンバー',
  operation: '操作',
  preview: 'プレビュー',
  notificationScenario: '通知シナリオ',
  event: 'イベント',
  operationFailedPlease_3: '操作に失敗しました。再試行してください',
  savedSuccessfully: '保存しました',
  subscriptionNotification: '登録しました',
  planSmartWith:
    'タイムラインビューを使用するには、タスクの開始・終了時間を設定する必要があります。',
  setTaskTimeframes: '時間範囲を設定\n',
  trackProgress:
    'タスクの進捗情報、重要ノードを視覚化するのに役立つタイムラインビューが登場しました',
  newTimelineView: 'タイムラインビューが登場',
  relatedGroupChat: 'グループと関連付け',
  onlyAssociatedWithYou: '自分が所有者/管理者を担当者しているグループのみと関連付け可能',
  successfullyAssociated: 'グループと関連付けました',
  onlySupportsAssociation: '既存のグループのみと関連付け可能',
  view: '表示',
  setSpecificTime: '時間を追加',
  dueTomorrow: '締切：明日',
  dueToday: '締切：今日',
  setStartDueDate: '開始・締切時間設定',
  viewPreview: 'プレビュー',
  groupNotification: 'グループ会話内通知',
  associateGroupFirst: 'グループ会話の関連付けを完了してください',
  triggerNotificationOverdue:
    '締切時間までに未完了のタスクがある場合、プロジェクトグループに通知を送信',
  taskOverdue: 'タスク延滞通知',
  triggerNotificationAfterExecuted: 'タスク完了時にプロジェクトグループに通知を送信',
  triggerNotificationAfterFilling:
    '新しいタスクを作成し、担当者を追加し、プロジェクトグループに通知を送信',
  assignTasks: 'タスク作成通知',
  expandTasksOneClick: '展開する',
  collapseTasksOneClick: '折りたたみ',
  nextScreen: '次へ',
  previousScreen: '前へ',
  addGroup: 'グループを追加',
  completionIcon: '完了マーク',
  cardSideColor: '左枠線の色',
  cardConfig: 'カード設定',
  expandTaskList: 'タスクリストを展開',
  collapseTaskList: '折りたたみ',
  timeline: 'タイムライン',
  associatedGroup: '関連プロジェクト:',
  assignmentCompleted: '私が担当するタスクを完了しました',
  completedByEveryone: '全員がタスクを完了しました',
  toDoCompleted_2: 'タスクをアーカイブしました',
  newNews: '新着',
  assigneeCancellation: '{val1}はタスクのアーカイブを解除しました',
  appointedPersonForAcceptance: 'アーカイブ済み',
  selectItem: '{val1}件選択済み',
  everyDay_2: '3日ごと',
  nextWeek: '来週',
  laterToday: '後で',
  whenDeletingReminders: 'リマインダーを削除',
  incompleteParticipants: '未完了 計{val1}人',
  participantCompleted: '完了済み 計{val1}人',
  allParticipants: '担当者 （全員完了 計{val1}人）',
  assignedBy: '所有者',
  addAComment: '@で他人をメンション、Enterでコメントを送信',
  valPersonNotYet: '未完了：{val1}人',
  valPersonFinished: '完了済み：{val1}人、',
  reminderTime: 'リマインダー',
  addParticipants: '担当者を追加',
  start: '{val1} から',
  openToDo: '詳細情報を見る',
  unsort: 'デフォルト',
  sort: '並べ替え',
  return: '戻る',
  toDoFor_2: '{val1} とのタスク',
  toDoFor: '{val1} のタスク',
  successfulTransferRefersTo_2: '所有者を変更しました',
  successfulTransferRefersTo: '所有者を変更し、このタスクから退出しました',
  currentlyNotSupportedForAdding: '所有者を担当者に設定することができません',
  theCurrentVersionDoesNot:
    'この機能を利用できません。POPOを最新バージョンにアップデートしてください',
  attachment: '添付ファイル',
  addAttachments: '添付ファイルを追加',
  deadlineSet:
    'ここには締め切りのあるタスクが表示されます。現在、スケジュールされたタスクはありません。',
  startTimeNotAvailable: '締め切りより後の日付を開始日に設定することはできません',
  deadlineNotAvailable: '開始日より前の日付を締め切りに設定することはできません',
  batchAdd: '一括追加',
  ifAddingStarts: '開始日を設定するには、締め切りを設定してください',
  currentListDefault_2: 'すべてのタスクの担当者は私です',
  currentListDefault: 'すべてのタスクの所有者は私です',
  inASeparateWindow: '新しいウィンドウで開く',
  closeIndependentWindows: 'ウインドウを閉じる',
  nextFriday: '来週の金曜日',
  nextMonday: '来週の月曜日',
  thisFriday: '今週の金曜日',
  remove: '外す',
  thisIsAlreadyAToDoList: 'このユーザーはタスクの所有者であるため、担当者には設定できません',
  escCancel: 'Escでキャンセル',
  enter: 'Enter︎で選択',
  switch: '↑↓で切り替え',
  recentContacts: '最近使用した連絡先',
  send: '送信',
  mention: 'メンション',
  expression: 'スタンプ',
  nonToDoParticipation: 'メンションされたユーザーに招待状を送信',
  cannotSendEmpty: '空白送信はできません',
  everyone: '全員',
  mentionAllAchievements: '全員をメンション',
  me: '@私',
  okToDeleteThis: 'このコメントを削除しますか？',
  deleteComment: '削除',
  completionTime: '完了日時',
  characterCountExceeds: '文字数制限を超えています',
  thisTaskIsCurrentlyUnavailable: 'まだ担当者がいません',
  sortBy: '{val1}の順に並べ替え',
  unfinishedToDoList: 'タスク未完了の担当者',
  contacts: '連絡担当者',
  participants: '担当者',
  notJoined: '未参加者',
  thisWeek: '今週',
  theNextOneHasBeenGenerated: '次の繰り返しタスクが既に生成されたため、編集できません。',
  confirmToCancelTheClosure_2: 'フォローを解除しますか？',
  confirmToRemoveFrom: '自分をタスクから外しますか？',
  afterTheTransferYouWill: '続行すると、タスクの所有者ではなくなります。',
  atMostOnce: '一度に最大で5つのファイルをアップロードできます',
  thereAreMoreThanM: '選択中のファイルには200MBを超えたものがあります',
  thereAreCurrentlyNoProjectsAvailable: 'プロジェクトなし',
  state: 'ステータス',
  entryName: 'プロジェクト名',
  allProjects: 'すべてのプロジェクト',
  membersCanCreate: '同時プロジェクト専用グループを作成',
  newSchedule: '予定の作成',
  enterGroupChat: '会話を開く',
  createANewGroupChat: '新規グループ',
  createANewProjectGroup_2:
    'プロジェクト専用グループの作成に失敗しました。「他の設定」から再試行してください。',
  reasonMustBeFilledIn: '招待理由を入力してください（必須）',
  addTheFollowingMembers:
    '以下のユーザーを招待するには、それぞれの直属の上司に許可を求める必要があります。招待理由を入力してください。',
  inviteToJoinTheGroupApplication: 'メンバー招待申請',
  waitingForPeopleToJoinTheGroup:
    '追加できるメンバー数の上限に達したため、{val1}など計{val2}人の追加に失敗しました。',
  role: 'ロール',
  joinTime: '参加日時',
  fullName: '氏名',
  searchForExistingItems: '既存のメンバーを検索',
  memberManagement: 'メンバー管理',
  inviteMembers: 'メンバーを招待',
  changeProjectStatus: 'ステータスを変更',
  more: 'もっと見る',
  viewAllItems: 'すべてのプロジェクト',
  inviteTheTeamToBecome:
    'メンバーを招待して、作成したタスクを割り当てたり、フォローアップしたりします。',
  newProject: '新規プロジェクト',
  cancel: 'キャンセル',
  addComments: '備考を追加',
  priority: '優先度',
  deadline: '締め切り',
  reply: '返信',
  transferAssignor_2: '{val1} をタスクの所有者に設定しました',
  unassignPending: '{val1} を担当者から外しました',
  assignToDoTo: '{val1} をタスクの担当者に設定しました',
  changeTheTitleTo: 'タイトルを{val1}に変更しました',
  rebuildTheToDoList: 'タスクを再開しました',
  toDoCompleted: 'タスクを完了しました',
  createdToDo: 'タスクを作成しました',
  editRecord: '編集履歴',
  whole: 'すべて',
  comment: 'コメント',
  source_1: 'From',
  source: 'From：',
  annually: '毎年',
  monthly: '毎月',
  biweekly: '隔週',
  weekly: '毎週',
  everyDay: '毎日',
  everyWorkingDay: 'すべての出勤日（月～金）',
  determine: '完了',
  repeat: '繰り返し',
  time: '時刻',
  hangInTheAir: '未完了',
  completed: '完了済み',
  noSearchResults: '一致する結果がありません',
  delete: '削除',
  addTo: '追加',
  low: '低',
  in: '中',
  high: '高',
  urgent: '緊急',
  search: '検索',
  confirmToDeletePending: 'このタスクを削除しますか：',
  dateAndTime: 'カスタム',
  expiredPending: '延滞中のタスクがここに表示されます。延滞中のタスクがありません。',
  noPointsForTheTimeBeing: 'あなたに割り当てられているタスクがありません。',
  notYet: '完了済みタスクがありません。「すべて」タブからタスクをチェックできます。',
  markAsUrgent: '優先度が「緊急」・「高」であるタスクがここに表示されます。',
  viewHere: 'こちらですべてのタスクをチェックできます。処理待ちタスクがありません。良い1日を',
  noDataAvailable: 'データなし',
  assignToMe: '割り当てられたタスク',
  distribution: '割り当て',
  viewDetails: '詳細',
  replyTo: '{val1} に返信しました',
  deletedToDoList: 'タスクを削除しました',
  addedAComment: 'コメントしました',
  deletedComment: 'コメントを削除しました',
  changeStartTime: '開始日時を {val1} に変更しました',
  changeDeadline: '締め切りを{val1} に変更しました',
  deletedStartTime: '開始日時 {val1} を削除しました',
  deletedDueDate: '締め切り {val1} を削除しました',
  addedStartTime: '開始日時を {val1} に設定しました',
  addedDeadline: '締め切りを {val1} に設定しました',
  year: '年',
  youCurrentlyDoNotHave: '処理待ちタスクがありません。良い1日を',
  createAnd: '今日作成されたタスクと、今日が締め切りのタスクがここに表示されます。',
  focusOnToday: '仕事を整えよう',
  remind: '通知',
  hasBeenSent: '送信完了',
  sendOrNot: '通知を送信しますか？',
  searching: '検索中…',
  unableToMarkAll: 'このタスクを完了にすることができません',
  exitEsc: '終了（Esc）',
  commentFailedPlease: 'コメントに失敗しました。しばらくしてから再試行してください',
  deleteDeadline: '締め切りを削除',
  deletePriority: '優先度を削除',
  addUpToPeople: '最大{val1}人まで追加可能',
  operationFailedPlease: '操作に失敗しました。しばらくしてから再試行してください',
  afterDeletingOneself: '自分をこのタスクから解除すると、このタスクを閲覧・編集できなくなります',
  deleteToDoItems: 'タスクを{val1}件削除します',
  canOnlyDeleteMe_2: '自分が作成したタスクのみを削除できます。タスクを {val1} 件削除します',
  canOnlyDeleteMe: '自分が作成したタスクのみを削除できます',
  allocationFrom: '所有者',
  close: '閉じる',
  selectedWaitingItems: 'タスクを {val1} 件選択しました。一度に最大 {val2} 件まで処理できます',
  toDoCannotBeDone: '{val1}件のタスクを完了にすることに失敗しました',
  repeat_2: '繰り返し {val1}',
  end: '締め切り {val1}',
  personCompleted: '{val1}人が既にタスクを完了しました',
  addDuplicates: '繰り返し',
  addPriority: '優先度',
  addDeadline: '締め切り',
  noPermissions: '権限なし',
  toDoHasBeenDeleted: 'このタスクは既に削除されました',
  networkRequestLoss: 'ネットワークへの接続に失敗しました',
  theresNoMeAtTheMoment:
    '他人に割り当てられたタスクがここに表示されます。現在、割り当てられたタスクはありません。',
  toDoList: 'タスク',
  personCompleted_2: '完了済み：{val1}人',
  allCompleted: '全員完了 計{val1}人',
  iHavePublishedAReview: '{val1}...など{val2}人がコメントしました',
  commented: '{val1}がコメントしました',
  open: '詳細',
  assignee: '担当者',
  valReminder_2: '{val1} から、 {val2} 通知する',
  valReminder: '{val1} に通知',
  whenSettingReminders: 'リマインダー',
  changeToResponsible: 'フォロワーを担当者に変更すると、フォロワー状態が解除されます',
  createdInAConversation: 'From：',
  otherTimes: 'カスタマ',
  startDateSelection: '開始日（オプション）',
  specificTime: '開始時点',
  startDate: '開始日',
  theTaskHasBeenRestarted: 'タスクが再開しました',
  confirmRestart: '続行する',
  afterRestartingTheTask: 'タスクを再開すると、全員のタスクが未完了状態になります。続行しますか？',
  restartTask: 'タスクを再開',
  confirmCompletion: '完了にする',
  andThePersonInCharge: 'まだタスクを完了していない担当者がいます。全員のタスクを完了にますか？',
  restartEverything: '全員のタスクを再開する',
  restartMyTask: '私のタスクを再開する',
  allCompleted_2: '全員のタスクを完了にする',
  onlyIHaveCompletedIt: '私のタスクを完了にする',
  completeTheTask: '完了にする',
  taskCompleted: 'タスク完了通知',
  focusOnTasks: 'フォロー',
  followedBy: 'フォロー中',
  dontPayAttentionAnymore: '続行する',
  confirmToCancelTheClosure:
    'このタスクのフォローを解除しますか？続行すると、このタスクを閲覧できなくなります',
  peopleFollow: '人のフォロワー',
  nonTaskResponsible: 'メンションされたユーザーは担当者でない場合、ユーザーに招待状を送信',
  cancelSelection: '選択を取り消す',
  markingCompleted: '完了にする',
  markingIncomplete: '未完了にする',
  end_2: '締め切り {val1}',
  notCurrentlySupported: '所有者/担当者をフォロワーに設定することができません',
  addFollowers: 'フォロワーを追加',
  anyoneCanFinishIt: '一人が完了した時点でタスクが完了',
  everyoneNeedsTo: '全員が完了した時点でタスクが完了',
  notSet: '未設定',
  other: 'その他 {val1} 件',
  executor: '担当者 {val1} 人',
  attachments: '添付ファイル {val1} 個',
  theCurrentViewDoesNot: 'カレンダービューでは並べ替え機能を \r\n使用できません',
  plan: 'カレンダービュー',
  list: 'リストビュー',
  unplannedAppointment: '無期限タスク',
  collapseUnplanned: '閉じる',
  unplannedDeployment: '無期限タスクを表示',
  fieldConfiguration: 'フィールド設定',
  showWeekends: '土日を表示',
  allocation: '表示内容',
  viewLayoutWord: '表示内容設定',
  month: '月',
  week: '週',
  addTitle: 'タイトル',
  clickToCompleteTheTask: '完了にする',
  clickToRebuildTheTask: 'タスクを再開',
  atTheBeginningOfTheSetting: '開始日時',
  setDuplicateRules: '繰り返し',
  startTime: '開始日時',
  cancelAddingTo: 'カレンダーから外す',
  addToCalendar_2: 'カレンダーに追加',
  cancelledAdding: 'カレンダーから外しました',
  successfullyAdded: '追加しました',
  addToCalendar: 'カレンダーに追加',
  clickToSynchronize: 'クリックして追加',
  addedToDay: 'カレンダーに追加済み',
  clickToCancelTheSame: 'クリックしてカレンダーから外す',
  putAwayTheDayAndWait: '折り畳む',
  expandTheDayOfWaiting: '展開する',
  assignedBy_2: '(所有者)',
  viewPersonalInformation: 'プロフィール',
  enterTitleBack: 'タイトルを入力して、Enterキーを押すとタスクが作成されます',
  viewNotes: '備考を見る',
  viewAttachments: '添付ファイルを見る',
  share: '共有',
  forwardTo: '共有先',
  transferAssignor: '所有者の変更',
  networkException: 'ネットワークエラー',
  noRemarks: '備考なし',
  withRemarks: '備考あり',
  remarks: '備考',
  noPriority: '優先度なし',
  withPriority: '優先度あり',
  noDeadline: '期限なし',
  thereIsADeadline: '期限あり',
  inTheFutureAndToday: '未来（明日以降）',
  tomorrow: '明日',
  deferred: '延滞中',
  createdToday: '今日',
  creationTime: '作成日時',
  showCompleted: '完了済みタスクを表示',
  minute: '分',
  hour: '時',
  day: '日',
  today: '今日',
  checkInTheCalendar: 'カレンダーで表示',
  needToBeDealtWith: 'タスク',
  notLoggedIn: '未ログイン',
  assignedTo: '担当者',
  title: 'タイトル',
  cancelToDo: '削除',
  newSuccessfully: '作成完了',
  throughKeywords: 'キーワードで検索',
  empty: 'クリア',
  screen: 'フィルター',
  thereIsCurrentlyNoScreeningThatMeetsTheCriteria:
    '一致する結果がありません。抽出条件を変更して、再試行してください。',
  noTasksAvailableAtTheMoment: 'タスクがありません',
  deleteTask: '削除',
  completedTasks: '完了したタスク',
  unfinishedTasks: '未完了のタスク',
  noDeadlineSet: '未設定',
  deadlineNextMonth: '来月',
  deadlineNextWeek_2: '来週',
  thisWeeksDeadline_2: '今週',
  deadlineTomorrow: '明日',
  deadlineToday_2: '今日',
  cutOffBeforeToday: '昨日以前',
  addAsFollow: 'フォロワーに設定',
  humanCompletion: '{val1}人完了済み',
  viewComments: 'コメントを表示',
  atTheBeginningOfAdding: '開始日時',
  addPriority_2: '優先度',
  helpCenter: 'ヘルプセンター',
  welcomeToParticipateInTheProduction: 'ユーザーの声募集中',
  iGotIt: 'わかった',
  taskManagementAgain: 'タスク昨日がアップグレードされました。「ボードビュー」登場！',
  kanbanView: 'ボードビュー',
  immediateMedicalExamination: '試す',
  afterFollowingTheTask: 'タスクをフォローすると、チャットボットから進捗通知が送信されます。',
  realTimeMonitoringOfAnyTask: 'タスクの進捗状況をリアルタイムで把握',
  iHaveCompletedIt: '完了済み',
  enterComment: 'コメントする',
  confirmTheTransferOfResponsibilities: '所有者を変更しますか？',
  transferAndExit: '所有者を変更して、このタスクから退出します',
  transferAndTransferFrom: '所有者を変更して、自分を担当者に設定します',
  humanExecution: '{val1}人の担当者',
  confirmDeletion: '削除しますか？',
  closingDate: '締め切り',
  allExecutors: '担当者全員がタスクを完了しました',
  noExecutorYet: 'タスクを完了した担当者はいません',
  people: '人',
  itIsAlreadyATaskExecution: 'タスクの担当者をフォロワーに設定することができません',
  itIsAlreadyATaskCreation: 'タスクの所有者をフォロワーに設定することができません',
  noEditingPermission: '編集権限がありません',
  noGrouping: 'なし',
  thisWeeksDeadline: '今週締め切り',
  addDeadline_2: '締め切りを設定',
  clickToFollowRen: 'クリックしてタスクをフォロー',
  cancelFollow: 'フォローを解除',
  peopleFollow_2: '0人のフォロワー',
  updatedCompleted_3: '処理完了の判断条件を「{val1}」に変更しました',
  updatedCompleted_2: '処理完了の判断条件を「全員が完了した時点でタスクが完了」に変更しました',
  updatedCompleted: '処理完了の判断条件を「一人が完了した時点でタスクが完了」に変更しました',
  restartedTheEntireSystem: 'タスク全体を再開しました',
  iRestartedMyself: '自分のタスクを再開しました。タスク全体は再開されました',
  willValBe: '{val1}をフォロワーから担当者に変更しました',
  cancelVal: '{val1}がフォローを解除しました',
  valFollow: '{val1}がタスクをフォロワーになりました',
  removedTask: 'タスクのフォロワー{val1}をタスクから外しました',
  addVal: '{val1}をタスクのフォロワーとして設定しました',
  completedTheTask_2: 'タスクを完了しました。タスク全体が完了されました',
  completedTheTask: 'タスクを完了しました',
  completedTheEntireTask: 'タスク全体を完了にしました',
  notArranged: '期限なし',
  inTheFuture: '７日以後',
  futureDays: '未来の７日間',
  customSorting: 'カスタム',
  nextMonth: '翌月',
  beforeToday: '遅延中',
  deadlineNextWeek: '来週締め切り',
  deadlineToday: '今日締め切り',
  completionStatus: '進捗状況',
  iCreatedIt: '自分が作成',
  whatIAmConcernedAbout: 'フォロー中',
  allTasks: 'すべてのタスク',
  belongingProject: '関連プロジェクト',
  personLiable: '担当者',
  assignPerson: '所有者',
  clickOnAscendingOrder: '昇順に並べ替え',
  clickOnDescendingOrder: '降順に並べ替え',
  deadline_2: '締め切り',
  followPeople: 'フォロワー',
  taskName: 'タイトル',
  nothing: 'なし',
  allocatedTo: '割り当て先',
  tabOperation: 'タブ・ページ操作',
  rename: '名称の変更',
  addNewTab: '新規タブ',
  bulletinBoard: 'ボードビュー',
  myTask: '私のタスク',
  shortcuts: 'ショートカット',
  task: 'タスク',
  sort_2: '並べ替え：',
  positiveSequence: '正順',
  reverseOrder: '逆順',
  hide: '非表示フィールド',
  newTask: '新規タスク',
  grouping_2: 'グループ分け条件',
  grouping: 'グループ分け条件：{val1}',
  allFilteringItems: 'すべての抽出条件',
  quickFilterBar: 'よく使う抽出条件',
  pleaseSearchFirstAndThen: 'キーワードを入力して検索してください',
  addConditions: '追加',
  fieldHiding: '非表示フィールド設定',
  time_2: '日時',
  thePrevious: '前へ',
  projectManager_2:
    'プロジェクト管理者は、プロジェクトのアイコン、名前、ステータス、メンバーを編集したり、プロジェクトを共有したりできます。',
  quickProjectDesign: 'クイック設定',
  screenBasedOnDemands:
    '必要に応じてタスクを並べ替えられます。プロジェクトの管理者は編集されたビューをパブリックビューとして保存でき、プロジェクトメンバーはそれを個人ビューとして保存できます。',
  personalizedTasks: 'カスタムタスクビュー',
  projectManager:
    'プロジェクト管理者はタスクのためにテキスト、複数選択、単一選択、ユーザー、日付、数字などのフィールドをカスタムできます。',
  addCustom: 'カスタムフィールドを追加',
  next: '次へ',
  notFoundIncludes:
    '「{val1}」を含む検索結果がありません。キーワードを変更して、再試行してください。',
  startImmediately: '最初のプロジェクトを始めましょう',
  dissolveTheProject: 'プロジェクトを解散',
  notSupportedForModification: 'フィールド種類を変更できません',
  noSearchResultsFound: '一致する結果がありません',
  afterDisbandingTheProject:
    'プロジェクトが解散されると、関連タスクは保持されますが、タスク内のカスタムフィールドの内容は削除されます。解散後のプロジェクトは復元できないため、ご注意ください。',
  dissolution: '解散',
  atLeastInTheProject: '少なくとも1人のパブリックビューが必要です',
  operationFailedPlease_2: '操作に失敗しました。少なくとも1人の管理者が必要です',
  projectMembersHave: 'メンバー数上限に達しました',
  addToNavigation: 'ナビゲーションバーに追加',
  removeFromNavigation: '外す',
  newlyBuiltProject: '[プロジェクト]{val1}を作成しました',
  establish: '作成',
  allWithinTheProject:
    'デフォルトでは、すべてのプロジェクトメンバーをグループに招待します。同名のタスク専用グループ会話を作成しますか？',
  createAGroupChat: 'グループ会話を作成',
  addTo_2: '{val1}を追加',
  more_2: 'その他（{val1}）',
  managementView_2: 'ビュー管理',
  newView: '新規ビュー',
  saveView: '保存',
  saveViewLost: '保存に失敗しました。再試行してください',
  cover: '上書き',
  afterSavingItWillBeMade:
    '保存後、現在のビューはデフォルトで表示されるビューとして使用されます。この変更はすべてのプロジェクトメンバーに同期されます。',
  saveAsDefault: 'デフォルトビューとして保存',
  notFilledIn: '未入力',
  thisItemCannotBe: '必須項目です',
  option_4: 'オプション{val1}',
  option_3: 'オプション2',
  option_2: 'オプション１',
  format: '形式',
  includingTimeDivision: '時刻を含む',
  option: 'オプション',
  fieldType: 'フィールド種類',
  enterFieldLabels: '見出しを入力',
  newField: '新規フィールド',
  modifiedSuccessfully: '変更しました',
  percentageIsSmall: 'パーセンテージ（小数第2位まで）',
  percentage: 'パーセンテージ',
  retainDecimalPlaces_2: '小数第2位まで表示',
  retainDecimalPlaces: '小数第１位まで表示',
  integer: '整数',
  number: '数字',
  date: '日付',
  personnel: 'ユーザー',
  multipleChoice: '複数選択',
  singleChoice: '単一選択',
  text: 'テキスト',
  pleaseChoose: '選択してください',
  moveFromProject: 'プロジェクトから外す',
  taskAfterDeletion: '続行すると、関連する入力内容が削除されます',
  deleteField: 'フィールド「{val1}」を削除します',
  editFields: 'フィールド編集',
  updateStatus: 'ステータス編集',
  projectManagementOnly: 'プロジェクト管理者のみ編集可能',
  addToProject:
    'プロジェクトを追加すると、デフォルトのタスク担当者が自動的にプロジェクトに招待されます。',
  calendar: 'カレンダー',
  saveAsNewVision_2: '新規ビューとして保存',
  saveAsNewVision: '新規ビューとして保存するに失敗しました',
  publicView_3:
    '追加できるパブリックビューと個人ビュー数の上限に達しました。追加するには、既存のビューを削除してください。',
  thisViewIsFor: '個人ビューです。自分のみが表示できます',
  thisTypeHasReached:
    '追加できるビューの上限に達しました。追加するには、既存のビューを削除してください。',
  viewRange: '表示範囲',
  viewMode: 'ビューのモデル',
  enterViewName: '名称を入力してください',
  viewName: 'ビューの名称',
  saveViewAs: '保存しました',
  allMembersAre: '全員が表示可能',
  publicView_2: 'パブリックビュー',
  onlyYouCanDoItYourself: '自分のみ表示可能',
  personalView: '個人ビュー',
  deleteFailed: '削除に失敗しました',
  deleted: '削除しました',
  thisViewIsForPublic: 'パブリックビューです。管理者のみが編集できます。',
  thisViewIsForTheSystem: 'システムビューです',
  theTaskWillNotBeAffected: 'このビューのみが削除されます。タスクには影響しません',
  deleteView: 'ビューを削除',
  confirmToDeleteThis: 'このビューを削除しますか？',
  addView: '新規ビュー',
  managementView: 'ビューの管理（{val1}）',
  noPermissionToAdd: '追加権限がありません',
  numberOfPersonalViews:
    '追加できる個人ビュー数の上限に達しました。追加するには、既存のビューを削除してください。',
  publicView:
    '追加できるパブリックビューと個人ビュー数の上限に達しました。追加するには、既存のビューを削除してください。',
  selectDate: '日付を選択',
  noOptionsAvailable: 'オプションなし',
  addPersonnel: 'ユーザーを追加',
  itCanAlsoBeBasedOn: '{val1}などの複数の抽出条件を同時に設定できます。',
  useCustom: 'カスタムフィルターを活用し、ワンクリックで重要なデータを抽出',
  taskStatusCutoff: 'タスクステータス、締め切り、担当者',
  reloadingInProgress: '再読み込み中',
  chooseToCompleteOr: 'タスクを完了または再開する方法を選択してください',
  simultaneouslyCreateItems: '同時プロジェクト専用グループを作成',
  transformToSubtask: 'サブタスクに設定',
  searchTask: 'タスクを検索',
  searchNoResult: '一致する結果なし',
  recent: '最近',
  setParentTask: '所属ンタス設定',
};
