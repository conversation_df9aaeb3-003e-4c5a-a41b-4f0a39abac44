import React from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { apiTaskFollowerAddSelfPost, apiTaskFollowerDeleteSelfPost } from '@/api';
import { Message } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import Client from './client';
import Web, { Props } from './web';
import useDelTaskConfirm from '@/hooks/useDelTaskConfirm';
import { getIsInSession } from '@/models/utils';

const Container: React.FC<Props> = (props) => {
  const { taskInfo, permissions } = props;
  const { taskId } = taskInfo || {};
  const { currentViewTab } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const dispatch = useDispatch<Dispatch>();

  const { open } = useDelTaskConfirm();

  const onRemove = () => {
    open({
      taskId: String(taskId),
      taskInfo,
    });

    // Modal.confirm(
    //   {
    //     rootClassName: s.confirm,
    //     width: 420,
    //     title: I18N.auto.deleteTask,
    //     content: (
    //       <div className={classNames('line-ellipsis-3')}>
    //         {I18N.auto.confirmToDeletePending} {title}
    //       </div>
    //     ),
    //     // centered: true,
    //     cancelText: I18N.auto.cancel,
    //     okText: I18N.auto.delete,
    //     onOk: () => {
    //       apiTodoCancelGet({ taskId: String(taskId) })
    //         .then(() => {})
    //         .finally(() => {
    //           dispatch.viewSetting.refreshDataByDataChange({
    //             refreshList: true,
    //             refreshCount: true,
    //           });
    //           onClose?.();
    //         });
    //     },
    //     zIndex: 1100,
    //     getContainer: () => (containerElement ? containerElement : document.body),
    //   },
    //   'warning'
    // );
  };

  const onFollowChange = (follow: boolean) => {
    const api = follow ? apiTaskFollowerAddSelfPost : apiTaskFollowerDeleteSelfPost;
    api({ taskId: taskId }).then(() => {
      if (follow) {
        Message.success(I18N.auto.followedBy);
      }
      //dispatch.detail.getTodoDetail(taskId!);

      if (follow) {
        dispatch.detail.getTodoDetail(taskId!);
      } else {
        const newPermissions = permissions?.map((item) => {
          return {
            ...item,
            value: false,
          };
        });
        dispatch.detail.setTaskInfo({
          ...taskInfo,
          isFollower: follow,
          permissions: newPermissions,
        });
      }
      dispatch.viewSetting.refreshDataByDataChange({
        refreshList: true,
        refreshCount: true,
      });
    });
  };
  if (currentViewTab?.type || getIsInSession()) {
    return <Web {...props} onRemove={onRemove} onFollowChange={onFollowChange}></Web>;
  } else {
    return <Client {...props} onFollowChange={onFollowChange}></Client>;
  }
};

export default Container;
