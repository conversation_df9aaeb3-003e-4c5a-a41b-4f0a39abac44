// 基础变量定义
@radius: 8px;
@border-color: var(--aBlack6);
@header-bg: var(--bgBottom);
@row-hover-bg: var(--N60);
@add-row-bg: var(--aBrand6);

.tanstack-table__wrapper {
  position: relative;
  width: 100%;
  height: calc(100vh - 1.38rem);
  padding-right: 20px;
  overflow: auto;

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background-color: transparent;

    &-thumb {
      border: 4px solid transparent !important;
      background-clip: content-box;
    }
  }

  .tanstack-table {
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;

    // 表头样式
    .tanstack-table__thead {
      position: sticky;
      top: 0;
      z-index: 2;

      .tanstack-table__tr {
        background-color: @header-bg;
        // 边框线
        &::before,
        &::after {
          content: '';
          position: absolute;
          z-index: 4;
          left: 0;
          width: 100%;
          height: 1px;
          background-color: @border-color;
        }

        &::before {
          top: 0;
        }
        &::after {
          bottom: -1px;
        }

        // hover 效果
        &:hover {
          background-color: @row-hover-bg;

          &::after,
          &::before {
            background-color: transparent;
          }
        }
      }

      // 当第一行 hover 时隐藏底部边框
      &:has(+ .tanstack-table__tbody .tanstack-table__tr:first-child:hover),
      &:has(+ .tanstack-table__tbody .tanstack-table__tr:first-child.active) {
        .tanstack-table__tr::after {
          background-color: transparent;
        }
      }
    }

    // 表体样式
    .tanstack-table__tbody {
      position: relative;
      z-index: 1;

      .tanstack-table__tr {
        border-top: 1px solid @border-color;
        border-radius: var(--radius);

        // hover 和 active 状态
        &:hover,
        &.active {
          & + .tanstack-table__tr {
            border-color: transparent;
          }

          .drag__handler {
            opacity: 1;
          }
        }

        &:first-child {
          border-top-color: transparent;
        }

        &:has(.task-tr-add) {
          background-color: @add-row-bg !important;
        }
      }

      // 添加行按钮样式
      .task-tr-add-btn,
      .task-tr-add {
        > td {
          &::before {
            // border-top-right-radius: 0 !important;
            // border-bottom-right-radius: 0 !important;
            border-right: unset !important;
            width: calc(100% + 1px);
          }

          // &::after {
          //   right: 5px;
          // }
        }
      }

      // 第一列背景色
      .tanstack-table__tr:not(.task-tr-add-btn):not(.task-tr-add) > td:first-child {
        background-color: @header-bg;
      }

      // 添加行特殊样式
      .task-tr-add {
        > td::before {
          background-color: @add-row-bg;
        }

        .custom-input {
          min-width: 280px;
          max-width: 620px;
          will-change: width;
        }

        // peoples 容器样式
        .peoples__container {
          display: flex;
          justify-content: center;
          background: var(--bgTop);
          border: 1px solid var(--aBlack12);
          padding: 2px;

          &:hover {
            background-color: var(--aBlack4);
          }

          .avatarBox {
            height: auto;
            width: auto;
          }

          .todo-rendedr-peoples-avatar {
            width: 18px !important;
            height: 18px !important;
          }

          .todo-rendedr-peoples-count {
            width: 18px;
            height: 18px;
            font-size: 8px;
          }
        }

        // 输入框样式
        .add-row-focused {
          border: none !important;
          box-shadow: none !important;
          background-color: transparent !important;
        }

        // 时间标签样式
        .add-row-time-label {
          height: 24px;
          padding: 0 8px;
          color: var(--text-color-2);
          background-color: var(--bgTop);
          border: 1px solid var(--aBlack12) !important;

          &:hover {
            background-color: var(--aBlack4);
          }

          .add-row-clear-icon {
            background-color: transparent;
            height: 26px;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
            justify-content: center;
            right: 0;
            width: 20px;

            &:hover {
              background-color: var(--aBlack4);
              display: flex;
            }

            .babylon-popo-icon {
              font-size: 16px;
            }
          }
        }
      }

      // 详情抽屉打开状态
      &.detail-drawer-open .tanstack-table__tr {
        td:first-child {
          .taskname__operatoin {
            display: none;
          }

          .title__wrap {
            pointer-events: none;
          }
        }

        td:not(:first-child)::before {
          content: '';
          position: absolute;
          z-index: 4;
          inset: 0;
        }
      }
    }

    // 水平滚动状态
    &.horizon-scrolled {
      // .tanstack-table__thead .tanstack-table__tr {
      //   // &:hover {
      //   & > th:first-child {
      //     border-right: 1px solid @border-color;
      //   }
      //   // }
      // }

      .tanstack-table__tr {
        td,
        th {
          &:first-child:not(.single__cell)::before {
            border-right: 1px solid @border-color;
          }

          &:last-child {
            border-right: none;
          }
        }
      }
    }
  }
}

// 行和单元格基础样式
.tanstack-table__tr {
  position: relative;
  display: flex;
  will-change: transform;
  --radius: @radius;

  td,
  th {
    height: 35px;
    flex-shrink: 0;
    padding: 0 1px;
    width: 100%;
    height: 100%;
    user-select: none;
    -webkit-user-select: none;

    * {
      user-select: none;
      -webkit-user-select: none;
    }

    &:first-child {
      background-color: @header-bg;
      &::before {
        box-sizing: border-box;
        content: '';
        position: absolute;
        z-index: -1;
        top: -1px;
        right: 0;
        bottom: 0;
        left: -0;
        width: 100%;
        border-right: 1px solid transparent;
        border-top-left-radius: var(--radius);
        border-bottom-left-radius: var(--radius);
      }
    }
  }
  &:hover,
  &.active {
    background-color: @row-hover-bg;
    border-color: transparent !important;
    border-radius: var(--radius);

    td,
    th {
      &:first-child {
        &::before {
          border-top-left-radius: var(--radius);
          border-bottom-left-radius: var(--radius);
          background-color: @row-hover-bg;
        }
        &::after {
          box-sizing: border-box;
          content: '';
          position: absolute;
          z-index: -2;
          inset: -1px;
          right: 0;
          background-color: @header-bg;
        }
      }

      &:last-child {
        border-top-right-radius: var(--radius);
        border-bottom-right-radius: var(--radius);
        overflow: hidden;

        &::before {
          border-top-right-radius: var(--radius);
          border-bottom-right-radius: var(--radius);
        }
      }
    }

    .resizer {
      opacity: 1;
    }
  }

  // 表头特殊样式
  th {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 8px 0 7px;

    // 调整器样式
    .resizer {
      cursor: col-resize;
      position: absolute;
      z-index: 2;
      right: 0;
      top: 50%;
      width: 6px;
      height: 22px;
      transform: translateY(-50%);
      touch-action: none;
      user-select: none;
      opacity: 0;

      &::after {
        content: '';
        position: absolute;
        z-index: 10;
        right: 0;
        height: 100%;
        width: 2px;
        border-radius: 1px;
        background-color: @border-color;
        transition: background-color, width 0.2s ease;
      }

      &:hover::after {
        width: 3px;
        background-color: var(--Brand600);
      }

      &.isResizing::after {
        background-color: var(--Brand600);
        height: calc(100vh - 1.36rem);
        top: -10px;
      }
    }

    &:hover .todo-sort {
      display: flex;
    }

    &:last-child .resizer {
      display: none;
    }
  }

  // 内容区域
  .th__content,
  .todo-th {
    width: 100%;
    height: 100%;
  }

  // 拖拽处理器
  .drag__handler {
    opacity: 0;
    width: 24px;
    height: 24px;
    justify-content: center;
    cursor: pointer;

    .babylon-popo-icon {
      color: var(--aBlack16);
      border-radius: 2px;

      &:hover {
        background-color: var(--aBlack6);
      }
    }

    &.only_subtasks {
      position: absolute;
      z-index: 1;
    }
  }

  // 展开状态
  &--expanded .expanded__icon {
    transform: rotate(90deg);
  }

  // 拖拽指示器
  &.before::before,
  &.after::after {
    content: '';
    position: absolute;
    z-index: 10;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--Brand500);
  }

  &.before::before {
    top: 0;
  }
  &.after::after {
    bottom: 0;
  }
}
