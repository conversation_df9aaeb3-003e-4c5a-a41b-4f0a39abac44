.projectPicker {
  width: 100%;
  height: 100%;
}

.project {
  width: 100%;
  position: relative;

  &.showArrow {
    padding-right: 12px;
  }
  .arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 8px;
    font-size: 16px;
    color: var(--IconTertiary);
  }
}

.projectSelectDropdown {
  //cursor: auto !important;
  &:global(.rock-dropdown-trigger-default) {
    & > span {
      width: 100%;
    }
    width: 100%;
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    background-color: transparent !important;
  }
}
