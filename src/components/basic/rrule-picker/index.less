.rrulePickerDropdown {
  // .clear__icon {
  //   &:hover {
  //     background-color: var(--aBlack6);
  //   }
  // }

  & > span {
    width: auto !important;
  }
  &:global(.rock-dropdown-trigger-default) {
    width: 100%;
    min-width: unset !important;
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    background-color: transparent !important;
  }
  :global {
    .rock-dropdown-down-arrow-icon {
      width: auto !important;
      color: var(--TextTertiary);
      margin-left: 4px;
      font-size: 16px;
    }

    .com-placeholder {
      min-width: unset;
    }
  }
}

.rrulePicker {
  min-width: 200px !important;
}
.label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  color: var(--TextPrimary);
  font-size: 13px;
}

.value {
  white-space: nowrap;
  color: var(--TextTertiary);
  font-size: 13px;
  line-height: 18px;
}

.panel {
  //width: 208px;
  padding: 4px;
  .item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 0 36px 0 12px;
    border-radius: 4px;
    color: var(--TextPrimary);
    font-size: 13px;
    cursor: pointer;
    &:hover,
    .active {
      background-color: var(--aBlack4);
    }
    .activeIcon {
      position: absolute;
      right: 8px;
      color: var(--Brand500);
    }
  }
}

.placeholder {
  font-size: 13px;
  color: var(--TextTertiary);
}
