import { useMemo } from 'react';
import { Divider, IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';
import classNames from 'classnames';
import { RockEditorPlugin, PluginKey, convertToHTML } from '@bedrock/editor';
import s from './index.less';
import { useMemoizedFn } from 'ahooks';
import { cleanTagP } from '../../utils';

interface ISubmitProps {
  type: 'remark' | 'comment';
  imageUpload?: any;
  onSubmit: () => void;
}

const Submit: React.FC<ISubmitProps> = (props) => {
  const { type, imageUpload, onSubmit } = props;

  const isEmptyEs = useMemoizedFn(() => {
    const newEs = props?.editorState;
    const htmlValue = convertToHTML(newEs) || '';
    return cleanTagP(htmlValue).length === 0;
  });

  return (
    type === 'comment' && (
      <div className={s.submitWrapper}>
        <Divider type="vertical" />
        <IconBtn
          iconName="icon-input_solid"
          title={I18N.auto.send}
          iconId="comment-submit-btn"
          iconClassName={classNames(s.commentSubmit, {
            [s.active]: !isEmptyEs() && !imageUpload?.current,
          })}
          onClick={onSubmit}
        />
      </div>
    )
  );
};

interface PluginOptions {
  type: 'remark' | 'comment';
  imageUpload?: any;
  onSubmit?: () => void;
}

export default function createSubmitPlugin(options: PluginOptions = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('submitPlugin'),
    getToolbarConfig: (plugins) => {
      return [...plugins, [(p) => Submit({ ...p, ...options })]];
    },
  });
}
