# 分组任务编辑行重复问题修复

## 问题描述
在进行用户分组时，同一条任务会出现在不同的人员分组下，对这些重复任务中某一条进行添加子任务操作时，其他任务也会出现编辑行。

## 问题原因
1. 分组时，同一个任务会出现在多个分组中，它们共享相同的 `taskId`
2. 在表格中，这些任务有不同的 `tableRowData.id`（表格行ID）
3. 当添加子任务时，`addSubLine` 方法只基于 `targetId`（即 `tableRowData.parentId`）来定位目标任务
4. 没有考虑到分组的唯一性，导致所有相同 `taskId` 的任务都会添加编辑行

## 解决方案
修改 `src/models/task.ts` 中的 `addSubLine` 方法：

### 主要修改点：
1. **识别分组任务**：通过 `addTarget?.groupedIds?.length > 0` 判断是否为分组任务
2. **生成唯一ID**：为分组任务的编辑行生成包含当前行ID的唯一标识符
3. **保留分组信息**：确保编辑行继承正确的分组信息（`groupId` 和 `groupedIds`）

### 修改内容：
```typescript
// 对于分组任务，确保只更新被点击的特定分组中的任务
// 通过 tableRowData.id 来精确定位，避免影响其他分组中的相同任务
const isGroupedTask = addTarget?.groupedIds?.length > 0
const currentRowId = tableRowData.id

// 为分组任务生成唯一的编辑行ID，包含当前行的ID信息
// 这样可以确保不同分组中的相同任务有不同的编辑行
const uniqueId = isGroupedTask 
  ? `${TaskTableRowTypeAdd}-${currentRowId}-${TaskTableRowType.add}-${now}`
  : `${TaskTableRowTypeAdd}-${id}-${TaskTableRowType.add}-${now}`

const addItem = getAddItem({
  // ... 其他属性
  id: uniqueId,
  taskId: uniqueId,
  // 保留分组信息，确保编辑行属于正确的分组
  groupId: addTarget.groupId,
  groupedIds: addTarget.groupedIds,
  // ... 其他属性
})
```

## 技术细节
1. **唯一性保证**：通过在编辑行ID中包含 `currentRowId`，确保不同分组中的相同任务有不同的编辑行ID
2. **分组信息继承**：编辑行继承父任务的 `groupId` 和 `groupedIds`，确保它属于正确的分组
3. **向下兼容**：对于非分组任务，保持原有的ID生成逻辑不变

## 预期效果
修复后，当用户在分组视图中对某个任务添加子任务时：
1. 只有被点击的那个任务实例会出现编辑行
2. 其他分组中的相同任务不会受到影响
3. 编辑行会正确地属于被点击任务所在的分组

## 相关文件
- `src/models/task.ts` - 主要修改文件
- `src/hooks/useGetGroupedList/groupedList.ts` - 分组逻辑
- `src/models/task.ts` 中的 `tableRemoveTaskItem` - 删除逻辑（已经正确处理分组）
