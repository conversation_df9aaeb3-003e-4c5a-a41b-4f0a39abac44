body {
  .rock-guide-popover-arrow {
    background-color: var(--TaskGuideBg);
  }
  .rock-guide-popover-inner {
    z-index: 10000;
    width: 280px;
    background: linear-gradient(180deg, var(--TaskGuideBg) 0%, var(--bgTop) 100%);
    border: 1px solid var(--aBlack8);
    border-radius: 8px;
    .rock-guide-popover-title {
      color: var(--Brand600);
      font-size: 16px;
    }
    .rock-guide-popover-content {
      color: var(--TextPrimary);
    }
    .rock-guide-popover-footer {
      margin-top: 14px;
      color: var(--TextTertiary);
      .rock-guide-popover-btn-before {
        color: var(--TextSecondary);
        border: 1px solid var(--aBlack12);
        border-radius: 6px;
      }

      .rock-guide-popover-btn-next {
        background-color: var(--Brand500);
        color: var(--absWhite);
        border-radius: 6px;
      }
      .rock-btn {
        height: 26px;
        font-size: 12px;
      }
    }
    .rock-guide-popover-title + .rock-guide-popover-content {
      margin-top: 4px;
    }
  }
  .rock-guide-mask {
    border-color: rgba(0, 0, 0, 0.4);
    &::after {
      box-shadow: none;
      border-color: var(--bgTop) !important;
      border-width: 1px;
    }
  }
}
