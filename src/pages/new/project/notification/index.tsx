import { OperateEdit, OperatePreview } from '@babylon/popo-icons';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiProjectNotifyGetGet, apiProjectNotifyOpenPost } from '@/api';
import { Message, Popover, Switch } from '@/components/basic';
import PageErrorBoundary from '@/components/page-error-boundary';
import TodoTable from '@/components/table';
import { Dispatch, RootState } from '@/models/store';
import { NotificationEventItem, ProjectInfo } from '@/types';
import { CNDPath, EnumNotificationEvent, EnumRole, NotificationEventMap } from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectDetailPermissions, RoleMemberMap } from '@/utils/permission';
import { CorlorMode } from '@/utils/platform';

import Head from './head';
import s from './index.less';

const Notification: React.FC = () => {
  const { corlorMode, language, projectInfo } = useSelector((state: RootState) => ({
    corlorMode: state.user.corlorMode,
    language: state.user.language,
    projectInfo: state.project.projectInfo,
  }));
  const [dataList, setDataList] = useState<NotificationEventItem[]>([]);

  const dispatch = useDispatch<Dispatch>();
  const getData = () => {
    apiProjectNotifyGetGet({
      projectId: projectInfo?.projectId,
    }).then((res) => {
      setDataList(res.notifyVos || []);
    });
  };
  const change = useMemoizedFn((v: NotificationEventItem) => {
    const _dataList = dataList.map((item) => {
      if (item.eventId === v.eventId) {
        return {
          ...v,
        };
      }
      return item;
    });
    setDataList(_dataList);
    //API 调用接口更新操作
  });
  const changeStatus = useMemoizedFn((v: NotificationEventItem) => {
    change(v);
    //API 调用接口更新操作
    apiProjectNotifyOpenPost({
      projectId: projectInfo.projectId,
      isOpen: v.isOpen,
      eventId: v.eventId,
    })
      .then(() => {
        Message.text(I18N.auto.savedSuccessfully);
      })
      .catch((error) => {
        console.log(error);
        Message.text(I18N.auto.operationFailedPlease_3);
      });
  });
  useEffect(() => {
    if (projectInfo.projectId) {
      getData();
      dispatch.viewSetting.updatePermissions({
        permissions: ProjectDetailPermissions,
      });
    }
  }, [projectInfo.projectId]);
  const memoColums = useMemo(() => {
    return [
      {
        title: <div className={s.headTaskName}>{I18N.auto.event}</div>,
        dataIndex: 'name',
        align: 'left',
        width: 'minmax(3.2rem, 1fr)',
        render: (text: EnumNotificationEvent, item: NotificationEventItem) => {
          return <div className={classNames(s.colum)}>{text}</div>;
        },
      },
      {
        title: <div className={s.headTaskName}>{I18N.auto.notificationScenario}</div>,
        dataIndex: 'roles',
        align: 'left',
        width: '1.8rem',
        render: (v: EnumRole[] = [], item: NotificationEventItem) => {
          const url = `${CNDPath}/notification/${NotificationEventMap[item.eventId].bgName}-${
            corlorMode === CorlorMode.dark ? 'dark' : 'light'
          }-${language}.png`;
          return (
            <div className={classNames(s.colum, 'pl-8')}>
              <Popover
                overlayClassName={s.popoverBox}
                showArrow={false}
                content={
                  <div className={s.previewPopover}>
                    <img src={url} className={s.img}></img>
                  </div>
                }
              >
                <div className={s.preview}>
                  <OperatePreview className="fs-16 mr-3"></OperatePreview> {I18N.auto.preview}
                </div>
              </Popover>
            </div>
          );
        },
      },
      {
        title: <div className={s.headTaskName}>{I18N.auto.operation}</div>,
        dataIndex: 'eventId',
        align: 'left',
        width: '1.2rem',
        render: (v: string, item: NotificationEventItem) => {
          return (
            <div className={classNames(s.colum, 'pl-8')}>
              <Switch
                value={!!item.isOpen}
                className={s.switch}
                onChange={(v) => {
                  changeStatus({
                    ...item,
                    isOpen: v,
                  });
                }}
              ></Switch>
              {/* <div className="ml-12">
               <Edit
                 projectId={projectInfo.projectId}
                 notificationEvent={item}
                 onChange={(v) => {
                   change({
                     ...item,
                     roles: v,
                   });
                 }}
               >
                 <IconBtn title="编辑" icon={<OperateEdit></OperateEdit>}></IconBtn>
               </Edit>
              </div> */}
            </div>
          );
        },
      },
    ];
  }, [changeStatus, language, corlorMode]);
  return (
    <PageErrorBoundary
      errorId="Page-Project-Members-List-Render-Error"
      //@i18n-ignore
      errorMsg="列表渲染异常"
    >
      <div className={s.projectList}>
        <Head className={s.head}></Head>
        <div className={s.list}>
          <TodoTable<ProjectInfo>
            columns={memoColums}
            data={dataList}
            drag={false}
            dark={corlorMode === CorlorMode.dark}
          ></TodoTable>
        </div>
      </div>
    </PageErrorBoundary>
  );
};
export default Notification;
