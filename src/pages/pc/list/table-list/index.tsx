import classNames from 'classnames';
import React, { ReactNode, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import TodoTable from '@/components/table';
import { Dispatch, RootState, store } from '@/models/store';
import { TableNoDataProp, TaskInfo, TodoInfo } from '@/types';
import { TaskNoDataDark, TaskNoDataLight } from '@/utils/const';
import { attrTaskName } from '@/utils/fields';
import I18N from '@/utils/I18N';

import s from './index.less';
import { RenderTaskName } from './render';
import { ConfigProvider } from '@bedrock/components';
import TanstackTable from '@/components/TanstackTable';
import { apiTaskResortPost } from '@/api';
import { EnumTrackeKey } from '@/utils/skyline';
import { ColumnDef } from '@tanstack/react-table';
import { useCurrentModel } from '@/hooks';
import { setExpandedIndexMap } from '@/models/utils';
import { SESSION_TASKS_VIEW_ID } from '@/models/im-todo-list';

interface Props {
  className?: string;
  columns?: ColumnDef<TodoInfo, any>[];
  extra?: ReactNode;
}

const TableList: React.FC<Props> = (props) => {
  const { columns, extra } = props;

  const { dataList, loading, expandedKeysMap } = useSelector((state: RootState) => ({
    dataList: state.imTodoList.dataList || [],
    loading: state.imTodoList.loading,
    expandedKeysMap: state.imTodoList.expandedKeysMap,
  }));

  const memoColumns = useMemo(() => {
    if (columns?.length) {
      return columns;
    }

    const taskNameColumn = {
      header: () => <div className={s.headTaskName}>{I18N.auto.taskName}</div>,
      accessorKey: attrTaskName.value,
      align: 'left',
      //长度设置限制 不然在p2p和群的列表上有异常
      // width: 'calc(100vw - 28px)',
      cell: ({ row, getValue }) => {
        const original = row.original;
        return <RenderTaskName taskInfo={original} tableRowData={row}></RenderTaskName>;
      },
      meta: {
        resizable: false,
        width: 'calc(100vw - 36px)',
      },
    };
    return [taskNameColumn];
  }, [columns]);

  const dispatch = useDispatch<Dispatch>();

  const { visibleDetail, taskId: detailTaskId } = store.getState().detail;

  const handleDragEnd = ({
    list,
    activeIndex,
    overIndex,
    preTaskId,
    tailTaskId,
    activeTaskId,
    activeParent,
  }) => {
    if (activeParent) {
      dispatch.imTodoList.updateTaskInfo({
        taskInfo: activeParent,
      });
      // if (visibleDetail && activeParent.taskId === detailTaskId) {
      //   dispatch.detail.setData({ taskInfo: activeParent });
      // }
    } else {
      dispatch.imTodoList.setData({
        dataList: list,
      });
    }

    apiTaskResortPost({
      currentTaskId: activeTaskId,
      preTaskId,
      tailTaskId,
    })
      .then(() => {
        // 如果拖拽的是子任务，更新父任务
        if (visibleDetail && activeParent?.taskId === detailTaskId) {
          dispatch.detail.getDetailInfo({ taskId: activeParent?.taskId });
        }
      })
      .catch(() => {
        dispatch.imTodoList.getTodoList({
          size: store.getState().imTodoList.totalTaskCount,
        });
      });
    // 埋点: 列表自定义排序  拖拽
    dispatch.user.tracking({ key: EnumTrackeKey.ListDrag });
  };

  useEffect(() => {
    dispatch.imTodoList.getTodoList({ page: 1 });
  }, []);

  const memoNoDataProps: TableNoDataProp = useMemo(() => {
    return {
      isNoData: !dataList.length && !loading,
      descriptive: I18N.auto.noTasksAvailableAtTheMoment,
      bgUrl: {
        dark: TaskNoDataDark,
        light: TaskNoDataLight,
      },
    };
  }, [dataList, loading]);

  return (
    <ConfigProvider
      getPopupContainer={(triggerNode) => {
        if (
          ['rock-select', 'rock-dropdown-trigger-default'].some((item) =>
            triggerNode?.classList.contains(item)
          )
        ) {
          return document.querySelector('#tanstack-table') as HTMLElement;
          // return document.querySelector('.tanstack-table__wrapper')?.parentNode as HTMLElement;
        }
        return document.body;
      }}
    >
      <TanstackTable
        onSortChange={(orderBy, sortOrder) => {
          dispatch.viewSetting.sort({
            order: sortOrder,
            orderBy: orderBy,
          });
        }}
        data={[...(dataList || [])]}
        columns={memoColumns}
        expandedKeysMap={expandedKeysMap}
        onExpandedChange={(expanded) => {
          setExpandedIndexMap(expanded as Record<string, boolean>, SESSION_TASKS_VIEW_ID);
        }}
        onDragEnd={handleDragEnd}
        rowsDraggable={false}
        emptyNodeProps={memoNoDataProps}
        extra={extra}
      />
    </ConfigProvider>
  );
};

export default TableList;
