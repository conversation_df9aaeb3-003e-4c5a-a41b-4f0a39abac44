import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { apiProjectCreatePost } from '@/api';
import { Button, Checkbox, Message, ToggleMenu } from '@/components/basic';
import { EditMember, EditProjectName } from '@/components/basic-project';
import { Dispatch, RootState } from '@/models/store';
import { CacheAddinfo, Permission } from '@/types';
import { getStorage, removeStorage, setStorage, StorageType } from '@/utils';
import Const, {
  AddMenuId,
  CNDPath,
  EnumRole,
  EnumSessionType,
  TaskNavigatorType,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum } from '@/utils/permission';
import { CorlorMode } from '@/utils/platform';

import s from './index.less';
import AddToGroup from '../components/AddToGroup';
import { getEmptyProject } from '@/models/project';

const ProjectAdd: React.FC = () => {
  let { language, corlorMode, userInfo, cacheAddinfo } = useSelector((state: RootState) => {
    return {
      language: state.user.language,
      corlorMode: state.user.corlorMode,
      userInfo: state.user.userInfo,
      cacheAddinfo: state.project.cacheAddinfo,
    };
  });

  const defaultCacheInfo = useRef<CacheAddinfo | undefined>();

  if (!cacheAddinfo?.icon) {
    if (!defaultCacheInfo.current) {
      defaultCacheInfo.current = getEmptyProject({});
    }
    cacheAddinfo = defaultCacheInfo.current;
  }

  //创建默认有全部权限
  const [permissions] = useState<Permission[]>([
    {
      name: ProjectPermissionEnum.CAN_ADD_MANAGER,
      value: true,
    },
    {
      name: ProjectPermissionEnum.CAN_ADD_EDITOR,
      value: true,
    },
    {
      name: ProjectPermissionEnum.CAN_ADD_VIEWER,
      value: true,
    },
    {
      name: ProjectPermissionEnum.CAN_REMOVE_MANAGER,
      value: true,
    },
    {
      name: ProjectPermissionEnum.CAN_REMOVE_EDITOR,
      value: true,
    },
    {
      name: ProjectPermissionEnum.CAN_REMOVE_VIEWER,
      value: true,
    },
  ]);
  const loadingRef = useRef(false);

  const dispatch = useDispatch<Dispatch>();
  const memoImg = useMemo(() => {
    if (corlorMode && language) {
      return `${CNDPath}/project/addpro-pic2-${
        corlorMode === CorlorMode.dark ? 'dark' : 'light'
      }-${language}.png`;
    }
    return '';
  }, [language, corlorMode]);

  const disabled = useMemo(() => {
    return !cacheAddinfo?.name?.trim();
  }, [cacheAddinfo?.name]);

  const onChangeValue = (_cacheAddinfo: CacheAddinfo) => {
    const data = {
      ...cacheAddinfo,
      ..._cacheAddinfo,
    };
    dispatch.project.setCacheAddinfo(data);
    setStorage(Const.ProjectAddItem, JSON.stringify(data), StorageType.local);
  };
  // 取消新建项目
  const cancel = () => {
    //如果当前没有项目 导航跳转到全部任务
    dispatch.project.setCacheAddinfo(undefined);
    removeStorage(Const.ProjectAddItem, StorageType.local);
    let isGroup = false;
    let id = getStorage(Const.PreNavigatorId, StorageType.session);
    if (`${id}`.includes('-group')) {
      id = Number(id.split('-')[0]);
      isGroup = true;
    }
    if (id && id != AddMenuId) {
      dispatch.viewSetting.openNavigator({
        navigatorId: id,
        isGroup,
      });
    } else {
      dispatch.viewSetting.openNavigator({
        navigatorId: TaskNavigatorType.assignToMe,
      });
    }

    // history.push('/new/task');
  };
  const createProject = () => {
    if (loadingRef.current === true) {
      return;
    }
    const params = {
      creator: userInfo?.uid,
      name: cacheAddinfo?.name?.trim(),
      icon: cacheAddinfo?.icon,
      iconColor: cacheAddinfo?.iconColor,
      createTeam: cacheAddinfo?.createTeam || false,
      groupId: Number(cacheAddinfo?.groupId),
      members: cacheAddinfo?.members?.map((item) => ({
        memberId: item.uid,
        memberType: item.sessionType,
        role: item.role,
      })),
    };
    loadingRef.current = true;
    return apiProjectCreatePost(params)
      .then((res) => {
        dispatch.project.setCacheAddinfo(undefined);
        removeStorage(Const.ProjectAddItem, StorageType.local);
        setStorage(Const.ProjectNewAdd, 1, StorageType.session);
        dispatch.project.getPingProjectList({});
        // pp.showToast({
        //   title: I18N.template(I18N.auto.newlyBuiltProject, { val1: params.name }),
        // });
        if (!res.teamVo?.isSuccess && res.teamVo?.errorMsg) {
          setTimeout(() => {
            Message.text({
              title: res.teamVo?.errorMsg,
              duration: 5,
            });
          }, 300);
        }
        // if (cacheAddinfo?.source) {
        //   // dispatch.project.setData({
        //   //   createTeamInfo: res.teamVo,
        //   // });
        //   dispatch.viewSetting.openNavigator({
        //     navigatorId: cacheAddinfo?.source,
        //     isGroup: !!cacheAddinfo?.groupId,
        //   });
        // } else {
        // }
        //清空项目详情
        dispatch.project.initData();
        //打开项目导航
        dispatch.viewSetting.openNavigator({
          navigatorId: res.projectId!,
        });
        if (res.teamVo?.isSuccess) {
          dispatch.project.setData({
            createTeamInfo: res.teamVo,
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        loadingRef.current = false;
      });
  };

  useEffect(() => {
    dispatch.detail.setVisibleDetail(false);
  }, []);

  return (
    <div className={s.addPage}>
      <div className={s.head}>
        <ToggleMenu></ToggleMenu>
      </div>
      <div className={s.container}>
        <div className={s.content}>
          <div className={s.left}>
            <div className={s.title}>{I18N.auto.newProject}</div>
            <div className={s.desc}>{I18N.auto.inviteTheTeamToBecome}</div>
            <EditProjectName
              className={s.item}
              autoFocus
              iconName={cacheAddinfo?.icon}
              iconBgColor={cacheAddinfo?.iconColor}
              onChangeIcon={(v) => {
                onChangeValue({ icon: v });
              }}
              onChangeColor={(v) => {
                onChangeValue({ iconColor: v });
              }}
              projectName={cacheAddinfo?.name}
              onChangeProjectName={(v) => {
                onChangeValue({ name: v });
              }}
            />
            {/* <AddToGroup
              onChange={(v) => {
                onChangeValue({ groupId: v });
              }}
              groupId={cacheAddinfo?.groupId}
            /> */}
            <EditMember
              className={s.item}
              value={cacheAddinfo?.members || []}
              onChange={(v) => {
                onChangeValue({ members: v });
              }}
              excludeUsers={[
                {
                  uid: userInfo?.uid || '',
                  name: userInfo?.name || '',
                  avatarUrl: userInfo?.avatarUrl || '',
                  sessionType: EnumSessionType.p2p,
                  role: EnumRole.admin,
                },
              ]}
              permissions={permissions}
            ></EditMember>
            <div className={classNames(s.other)}>
              <Checkbox
                checked={cacheAddinfo?.createTeam}
                onChange={(e) => {
                  onChangeValue({
                    createTeam: e.target.checked,
                  });
                }}
                className={s.checkbox}
              ></Checkbox>
              <div
                className={s.createGroup}
                onClick={() => {
                  onChangeValue({
                    createTeam: !cacheAddinfo?.createTeam,
                  });
                }}
              >
                <span className={s.tipDesc}>{I18N.auto.membersCanCreate}</span>
              </div>
            </div>
            <div className={s.btnBox}>
              {/* <Tooltip title="取消会清除新建缓存"> */}
              <Button
                type="checked-neutral"
                className={classNames(s.btn, s.cancel, 'mr-10')}
                onClick={cancel}
              >
                {I18N.auto.cancel}
              </Button>
              {/* </Tooltip> */}
              <Button
                disabled={disabled}
                type="primary"
                className={classNames(s.btn, s.ok)}
                onClick={createProject}
              >
                {I18N.auto.newProject}
              </Button>
            </div>
          </div>
          <div className={s.right}>
            <img className={s.img} src={memoImg}></img>
          </div>
        </div>
      </div>
      <div className={s.footer}></div>
    </div>
  );
};
export default ProjectAdd;
