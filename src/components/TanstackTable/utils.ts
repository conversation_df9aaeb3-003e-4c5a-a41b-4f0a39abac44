import { TodoInfo } from '@/types';
import { Header } from '@tanstack/react-table';

export const tableTaskNameWidth = 420;
type StorageInfo = {
  width: { [key: string]: number };
  type: string;
};

let colSizes: { [key: string]: number } = {};
let preNavigatorId: string | number = '';
interface IGetColumnsWidthParams {
  headers: Header<TodoInfo, unknown>[];
  containerEl: HTMLDivElement | null;
  columnSizingInfo?: any;
  actionType?: 'window_resize' | 'init';
  navigatorId?: string | number;
}
export const getColumnsWidth = ({
  headers,
  containerEl,
  columnSizingInfo,
  actionType,
  navigatorId = 'all',
}: IGetColumnsWidthParams) => {
  if (preNavigatorId !== navigatorId) {
    preNavigatorId = navigatorId;
    colSizes = {};
  }

  let storageWidth: { [key: string]: number } = {};
  let storageWidthType = 'auto'; // 自动 || 手动 : auto || manual
  let storageData: { [key: string]: StorageInfo } = {};
  try {
    const storageWidthInfoStr = localStorage.getItem('columns-width-info');
    storageData = storageWidthInfoStr ? JSON.parse(storageWidthInfoStr) : {};
    const storageInfo = storageData[`${navigatorId}`] || {};
    storageWidth = storageInfo.width || {};
    storageWidthType = storageInfo.type || 'auto';
  } catch (error) {
    console.error('获取本地存储失败', error);
    storageWidth = {};
    storageWidthType = 'auto';
  }

  const columnSizingKey = columnSizingInfo?.isResizingColumn;
  if (columnSizingKey) {
    storageWidthType = 'manual';
    localStorage.setItem(
      'columns-width-info',
      JSON.stringify({
        ...storageData,
        [navigatorId]: { type: storageWidthType, width: storageWidth },
      })
    );
  }

  // 计算非taskName列的总宽度
  let totalFixedWidth = 0;
  let taskNameHeader = null;

  for (let i = 0; i < headers.length; i++) {
    const header = headers[i]!;
    // 找到taskName列
    if (header.id === 'taskName') {
      taskNameHeader = header;
      continue;
    }
    const initWidth = storageWidth[header.id] || header.getSize();
    let size =
      colSizes[`--header-${header.id}-size`] || storageWidth[header.id] || header.getSize();
    // if (columnSizingInfo?.isResizingColumn === header.id) {
    //   size += columnSizingInfo.deltaOffset;
    // }

    if (columnSizingKey === header.id && storageWidthType === 'manual') {
      const columnDef = header.column.columnDef;

      const minSize = columnDef?.minSize || 428;
      size = initWidth + columnSizingInfo.deltaOffset;

      if (size < minSize) {
        size = minSize;
      }
    }

    // 累加其他列的宽度
    totalFixedWidth += size;
    colSizes[`--header-${header.id}-size`] = size;
    colSizes[`--col-${header.column.id}-size`] = size;

    if (!columnSizingKey) {
      storageWidth[header.id] = size;
    }
  }

  // 如果找到taskName列，非手动情况下，计算其自适应宽度
  if (taskNameHeader && (!columnSizingKey || columnSizingKey === taskNameHeader.id)) {
    const initTaskNameWidth = storageWidth[taskNameHeader.id] || taskNameHeader.getSize();
    let taskNameWidth: number;

    const columnDef = taskNameHeader.column.columnDef;

    if (storageWidthType === 'manual') {
      const minSize = columnDef?.minSize || 428;
      // 有columnSizingKey代表拖动中
      if (columnSizingKey) {
        taskNameWidth = Math.max(initTaskNameWidth + columnSizingInfo.deltaOffset, minSize);
      } else {
        const tableWidth = containerEl?.clientWidth || 1200;

        taskNameWidth = Math.min(
          tableWidth - 20,
          colSizes[`--header-${taskNameHeader.id}-size`] || storageWidth[taskNameHeader.id]
        );

        if (taskNameWidth < minSize) {
          taskNameWidth = minSize;
        }
      }
    } else {
      // 获取表格容器宽度，如果无法获取则使用一个默认值
      const tableWidth = containerEl?.clientWidth || 1200;

      taskNameWidth = taskNameHeader.getSize();
      if (taskNameWidth + totalFixedWidth < tableWidth) {
        taskNameWidth = Math.max(
          tableTaskNameWidth,
          tableWidth - totalFixedWidth,
          columnDef?.minSize || 428
        );
      } else {
        taskNameWidth = tableTaskNameWidth;
      }
    }

    colSizes[`--header-${taskNameHeader.id}-size`] = taskNameWidth;
    colSizes[`--col-${taskNameHeader.column.id}-size`] = taskNameWidth;
    if (!columnSizingKey) {
      storageWidth[taskNameHeader.id] = taskNameWidth;
    }
  }
  if (!columnSizingKey) {
    localStorage.setItem(
      'columns-width-info',
      JSON.stringify({
        ...storageData,
        [`${navigatorId}`]: { type: storageWidthType, width: storageWidth },
      })
    );
  }

  return colSizes;
};
