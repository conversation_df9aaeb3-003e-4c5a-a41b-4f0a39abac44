import s from './index.less';
import axios from 'axios';
import React, {
  useEffect,
  useMemo,
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import Editor, {
  RockEditorPlugin,
  PluginKey,
  createExternalPlugin,
  convertToHTML,
  EditorState,
  DOMSerializer,
  TextSelection,
  EditorConfig,
} from '@bedrock/editor';
import {
  createSubmitPlugin,
  createAtPlugin,
  createEmojiPlugin,
  createPopoDocRecognitonPlugin,
  createTextCountPlugin,
} from '../plugins';
import { apiTodoUpdateRemarkPost } from '@/api';
import { useDispatch } from 'react-redux';
import { insertTextAtCursor, toHttps } from '@/utils';
import AtPopover from '../plugins/at/at-popover';
import { useMemoizedFn } from 'ahooks';
import {
  getPopoAtText,
  getPopoDocText,
  getPopoEmojiText,
  MentionExtraData,
  processExternalLinks,
} from '@/components/basic-task/comment/editor/utils';
import { Checkbox, Message } from '@/components/basic';
import I18N from '@/utils/I18N';
import classNames from 'classnames';
import { startTransition } from 'react';
import { DetailTodoInfo, UserInfo } from '@/types';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';
import { getFpUploadUrl } from '@/api-common';
import createCancelAndConfirmPlugin from '../plugins/cancelAndConfirm';
import { cleanTagP } from '../utils';
import { pp } from '@popo-bridge/web';
import debounce from 'lodash/debounce';

interface IEditorContainerProps {
  taskId: number;
  type: 'comment' | 'remark';
  value?: string; // 传递给编辑器的值
  isEditing: boolean; // 控制编辑器的启用或禁用状态
  taskInfo?: DetailTodoInfo;
  reply?: React.ReactNode;
  disabled?: boolean;
  permissions?: { name: TaskPermissionEnum; value: boolean }[];
  onChange?: (newValue: string) => void; // 处理编辑器值的变化
  onSubmit?: (value: string, inviteJoinTaskWhenAt: boolean, atFollowers) => Promise<void>; // 处理编辑器提交的事件
  onIsEditingChange?: (isEditing: boolean) => void; // 处理编辑器失去焦点的事件
}

interface EditorRef {
  submit: () => string;
  clearContent: () => void;
}

const BedrockEditor = React.memo(
  forwardRef<EditorRef, IEditorContainerProps>((props, ref) => {
    const {
      taskId,
      value: v,
      type,
      isEditing,
      taskInfo,
      reply,
      permissions,
      disabled,
      onSubmit,
      onIsEditingChange,
    } = props;
    const [es, setEs] = useState<EditorState>(); // 初始值为空字符串，或者根据需要设置默认值
    const [inviteJoinTaskWhenAt, setInviteJoinTaskWhenAt] = useState<boolean>(false);
    const dispatch = useDispatch();
    const editorRef = useRef<any>(); // 创建一个ref来存储编辑器实例
    const mentionRef = useRef<any>();
    const [atPersonList, setAtPersonList] = useState<UserInfo[]>([]);
    const atPersonListRef = useRef<UserInfo[]>([]);
    const originalHtml = useRef(v);
    const insertImageRef = useRef<any>(false);
    const wrapperRef = useRef<any>(null);
    const [uploading, setUploading] = useState<boolean>(false);
    const handleAtClick = () => {
      insertTextAtCursor('@'); // 插入文本
    };

    useEffect(() => {
      if (editorRef.current) {
        // 如果编辑器实例存在，则调用setEditorState方法来更新编辑器的状态
        const editorState = editorRef.current?.convertFromHtml(v);
        setEs(editorState); // 更新编辑器的状态
      }
    }, [v]);

    // 主动聚焦
    useEffect(() => {
      if (isEditing && editorRef.current) {
        const view = editorRef.current?.getEditorView(); // 获取编辑器的view实例
        const { state } = view;
        const endSelection = TextSelection.atEnd(state.doc);
        // view.dispatch(state.tr.setSelection(endSelection).scrollIntoView());
        view.dispatch(state.tr.setSelection(endSelection));
        view.focus();
      }
    }, [isEditing]);

    // 这一步是为了给插入图片增加状态来避免因为点击插入图片失去焦点导致编辑器关闭
    useEffect(() => {
      const btnElement = document.getElementsByClassName('insert-image')[0];
      function imageBtnClick() {
        insertImageRef.current = true;
      }
      if (btnElement) {
        btnElement.addEventListener('click', imageBtnClick); // 监听按钮的点击事件
      }
      return () => {
        if (btnElement) {
          btnElement.removeEventListener('click', imageBtnClick); // 移除按钮的点击事件
        }
      };
    }, [editorRef.current]);

    const isEmptyEs = useMemoizedFn(() => {
      // 检查编辑器是否为空
      const newEs = editorRef.current?.getEditorState(); // 获取编辑器的状态
      const htmlValue = convertToHTML(newEs) || ''; // 将es数据转换成html
      return cleanTagP(htmlValue).length === 0; // 检查html是否为空
    });

    const handleCommentSubmit = (editorState) => {
      // 处理编辑器提交的事件
      let content = '';
      const serializer = DOMSerializer.fromSchema(editorState.schema);
      editorState?.doc.descendants((node, _pos) => {
        if (node.type.name === 'popoEmoji') {
          content += getPopoEmojiText(node.attrs);
        } else if (node.type.name === 'popoAt') {
          // 处理@功能
          content += getPopoAtText(node.attrs);
        } else if (node.type.name === 'popoDoc') {
          // 处理popo文档识别
          content += getPopoDocText(node.attrs);
        } else if (node.type.name === 'text') {
          content += node.text;
        } else {
          const dom = serializer.serializeNode(node);
          const tempDiv = document.createElement('div');
          tempDiv.appendChild(dom);
          content += tempDiv.innerHTML;
          return false;
        }
      });

      return content;
    };

    const handleSubmit = useMemoizedFn(() => {
      const newEs = editorRef.current?.getEditorState() as EditorState; // 获取编辑器的状态
      const htmlValue = convertToHTML(newEs) || ''; // 将es数据转换成html
      if (onSubmit) {
        const content = handleCommentSubmit(newEs); // 处理编辑器提交的事件
        if (content.length > 5000) {
          Message.text(I18N.auto.characterCountExceeds);
          return;
        }

        if (isEmptyEs()) {
          // 检查编辑器是否为空
          return;
        }

        if (uploading) {
          return;
        }

        onSubmit(content, !!inviteJoinTaskWhenAt, atFollowers);

        editorRef.current?.clearContent(); // 清空编辑器的内容
        onIsEditingChange?.(false); // 关闭编辑器
      } else {
        // 将es数据转换成html
        if (htmlValue.length > 5000) {
          Message.text(I18N.auto.characterCountExceeds);
          return;
        }
        if (htmlValue !== v) {
          const hrefTransformedHtml = processExternalLinks(htmlValue, (url) => {
            return `<a class='prosemirror-link' href="${url}" target="_blank" >${url}</a>`;
          });
          apiTodoUpdateRemarkPost({
            taskId,
            remark: isEmptyEs() ? '' : hrefTransformedHtml,
          })
            .then(() => {
              dispatch.detail.getTodoDetail(taskId);

              originalHtml.current = hrefTransformedHtml;
              // dispatch.viewSetting.refreshDataByDataChange({
              //   refreshList: true,
              //   refreshCount: false,
              // });
            })
            .catch((error) => {
              console.log(error);
            })
            .finally(() => {
              startTransition(() => {
                onIsEditingChange?.(false);
              });
              removeRemarkContentHeight();
            });
        } else {
          onIsEditingChange?.(false);
        }
        removeRemarkContentHeight();
      }
      mentionRef.current?.hide();
    });

    const submitBtnStyleChange = (type: 'add' | 'remove') => {
      const submitBtn = document.getElementById('comment-submit-btn'); // 获取提交按钮元素
      if (submitBtn) {
        if (type === 'add') {
          submitBtn.style.removeProperty('color');
        } else {
          submitBtn.style.color = 'var(--IconQuartus) !important';
        }
      }
    };

    const scrollIntoView = useMemoizedFn(
      debounce(() => {
        const editorWrapper = document.querySelector(
          '#detail_body .remark__content .editor__wrapper'
        ) as HTMLDivElement;
        if (editorWrapper) {
          const { top, bottom } = editorWrapper.getBoundingClientRect();
          if (bottom < 100 || top > window.innerHeight - 100) {
            editorWrapper.scrollIntoView({ block: 'nearest' });
          }
        }
      }, 320)
    );

    const removeRemarkContentHeight = debounce(() => {
      if (type === 'remark') {
        const remarkContentEl = document.querySelector(
          '#detail_body .remark__content'
        ) as HTMLDivElement;

        if (remarkContentEl) {
          remarkContentEl.style.height = 'unset';
        }
      }
    }, 32);

    const config: EditorConfig = useMemo(() => {
      return {
        features: {
          inlineCode: false,
          fontSize: false, // 禁用字体大小
          image: {
            // 单独的图片上传按钮
            singleButton: true,
            // 支持图片编辑(自定义宽高)
            allowEdit: true,
            hideDeleteButton: true,
            hideAlignButton: true,
            allowFloat: false,
            imageAutoResizeWhenUpload: true,
          },
          invertWhenPasteSimilarColor: {
            enable: true,
            threshold: 1,
          },
        },
        uploadImage: async function (file, { onProgress }) {
          setUploading(true);
          submitBtnStyleChange('remove');
          if (!file) return Promise.reject('no file');
          let progress = 0;
          const timer = setInterval(() => {
            progress += 1;
            // 更新进度条
            onProgress(progress);
            if (progress >= 90) {
              clearInterval(timer);
            }
          }, 100);
          try {
            const formData = new FormData();
            formData.append('fpfile', file);
            const uploadInfo = await getFpUploadUrl();
            formData.append('Authorization', uploadInfo?.token);
            const data = await axios.post(toHttps(uploadInfo?.uploadUrl), formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            });
            if (data?.status === 200) {
              setUploading(false);
              submitBtnStyleChange('add');
              return {
                ...data.data,
                src: data?.data?.url,
              };
            }
            setUploading(false);
            submitBtnStyleChange('add');
          } catch (error) {
            setUploading(false);
            submitBtnStyleChange('add');
            console.log(error);
          }
        },
        configProviderProps: {
          getPopupContainer: (trigger) => trigger?.parentElement || document.body,
        },
        onImagePreview: (src) => {
          pp.previewImage({ images: [src] }); // 调用pp.previewImage方法来预览图片
        },
        getPlugins: (ps) =>
          [
            ...ps,
            // 启用外链
            createExternalPlugin(),
            // 启用表情包
            // createEmojiPlugin(),
            // createEmojiPlugin({ covertNameToSrc, insertAfter: 'TextBgPicker' }),
            // 启用@功能插件
            // createAtPlugin({ mentionRef, type }),
            // createMentionPlugin(),
            // 启用发送插件
            createSubmitPlugin({
              type,
              onSubmit: handleSubmit, // 提交函数
            }),
            createCancelAndConfirmPlugin({
              type,
              onCancel: handleCancelClick,
              onConfirm: handleConfirmClick,
            }),
            //启动popo文档识别插件
            createPopoDocRecognitonPlugin(),
            // 启动字数限制插件
            createTextCountPlugin(),
            // createLinkRecognitionPlugin(),
            new RockEditorPlugin({
              key: new PluginKey('someUniqueName'), // 确保每个插件都有唯一的key
              getToolbarConfig: (plugins) => {
                // 通过splice删除二维数组中的按钮
                console.log('plugins: ', plugins);
                // 移除 `撤销`、`重做` 、`清除格式` 选项
                plugins[0].splice(0, 3);

                //
                plugins[3].splice(2, 3);

                plugins[2].splice(0, 1);
                //
                plugins[4].splice(0, 1);

                plugins[3].splice(2, 1);

                plugins[4].splice(2, 1);
                // 移除有序列表
                plugins[4].splice(0, 1);
                return plugins;
              },
            }),
          ].concat(
            type === 'comment' ? [createEmojiPlugin(), createAtPlugin({ mentionRef, type })] : []
          ),
      };
    }, [handleSubmit, isEmptyEs]);

    useImperativeHandle(ref, () => ({
      submit: () => {
        return convertToHTML(editorRef?.current?.getEditorState() as EditorState) || '';
      },
      clearContent: () => {
        editorRef.current?.clearContent();
      },
      onClick: handleAtClick,
    }));

    const hasNoParticipant = useMemo(() => {
      return atPersonList
        .filter((v) => !!v.uid && v.uid !== taskInfo?.assigner?.assignerUid)
        .some((item) => taskInfo?.participants?.findIndex((v) => v.uid === item.uid) === -1);
    }, [atPersonList, taskInfo?.participants, taskInfo?.assigner?.assignerUid]);

    const atFollowers = useMemo(() => {
      return atPersonList
        .filter((v) => !!v.uid && v.uid !== taskInfo?.assigner?.assignerUid)
        .filter((item) => taskInfo?.followers?.findIndex((v) => v.uid === item.uid) !== -1);
    }, [atPersonList, taskInfo?.followers, taskInfo?.assigner?.assignerUid]);

    const memoPermissions = useMemo(() => {
      const [CAN_EDIT] = validatesPermission({
        permissions: permissions,
        key: [TaskPermissionEnum.CAN_EDIT],
      }) as boolean[];
      return {
        CAN_EDIT,
      };
    }, [permissions]);

    const handleEsChange = (es: EditorState) => {
      setEs(es); // 更新编辑器的状态
      const atlist: UserInfo[] = [];
      es.doc.descendants((node) => {
        if (node.type.name === 'popoAt') {
          atlist.push({
            uid: node.attrs.uid,
            name: node.attrs.name,
          });
        }
      });
      if (atPersonListRef.current.sort().join() !== atlist.sort().join()) {
        setAtPersonList(atlist);
        atPersonListRef.current = atlist;
      }
      scrollIntoView();
    };

    // 点击了取消需要回退到原来的内容
    const handleCancelClick = () => {
      const editorState = editorRef.current?.convertFromHtml(originalHtml.current);
      setEs(editorState); // 更新编辑器的状态
      setTimeout(() => {
        onIsEditingChange?.(false);
      }, 0);
    };

    const handleConfirmClick = () => {
      handleSubmit();
    };

    const handleBlur = () => {
      setRemarkDefaultHeight(type);
      if (type === 'remark') {
        mentionRef.current?.hide();
        handleSubmit();
        onIsEditingChange?.(false);
      }

      if (type === 'comment') {
        if (isEmptyEs() && !reply) {
          onIsEditingChange?.(false);
        }
        mentionRef.current?.hide();
      }
    };

    // 在非编辑状态下点击对应文本后打开链接
    const handleEditorClick = (e) => {
      // 如果权限不够的话不进入编辑态
      if (disabled) return;
      if (type === 'remark' && !isEditing) {
        if (
          e.target.tagName === 'IMG' ||
          e.targe?.querySelector?.('img.rock-editor__image-view-body-img') ||
          e.target.querySelector?.('span.editor-popo-doc-span')
        ) {
          return;
        }
        setRemarkDefaultHeight(type);
        onIsEditingChange?.(true);
      } else if (type === 'remark' && isEditing) {
        if (e.target.closest('.prosemirror-popo-doc')) {
          //@ts-ignore
          const popoDocE = e.target.closest('.prosemirror-popo-doc');
          const url = popoDocE.getAttribute('data-src');
          pp.openSysBrowser({ url: url });
        }
      }
      // 当点击到输入区的时候关闭弹窗
      if (e.target.closest('.ProseMirror')) {
        mentionRef.current?.hide();
      }
    };

    useEffect(() => {
      function handleMouseDown(e) {
        const { target } = e;
        const parentNode = wrapperRef.current?.parentNode;
        if (parentNode) {
          // 排除点击回复按钮的时候触发
          if (!parentNode.contains(target) && isEditing && !e.target.closest('.replyBtn')) {
            handleBlur(e);
          }
        }
      }

      document.addEventListener('mousedown', handleMouseDown);
      return () => {
        document.removeEventListener('mousedown', handleMouseDown);
      };
    }, [wrapperRef, handleBlur]);

    const memoShow = useMemo(() => {
      return hasNoParticipant && memoPermissions.CAN_EDIT;
    }, [hasNoParticipant, memoPermissions.CAN_EDIT]);

    useEffect(() => {
      setTimeout(() => {
        removeRemarkContentHeight();
      }, 200);

      function stopPropagation(e) {
        e.stopPropagation();
      }

      const editorWrapper = document.querySelector(
        '#task-detail-wrapper .editor__wrapper .ProseMirror'
      );
      if (editorWrapper) {
        editorWrapper.addEventListener('scroll', stopPropagation);
      }

      return () => {
        if (editorWrapper) {
          editorWrapper.removeEventListener('scroll', stopPropagation);
        }
      };
    }, [isEditing]);

    useEffect(() => {
      const editorContainer = document.querySelector('#todo-remark-input-editor .ProseMirror');
      const handleLinkClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const linkElement = target.closest('.rock-editor__link');
        if (linkElement) {
          e.preventDefault();
          // 原逻辑：获取链接并打开
          const url = linkElement.getAttribute('href');
          pp.openSysBrowser({ url: url });
          e.stopPropagation();
        }
        const docElement = target.closest('.prosemirror-popo-doc');
        if (docElement) {
          e.preventDefault();
          const url = docElement.getAttribute('data-src');
          pp.openSysBrowser({ url: url });
          e.stopPropagation();
        }
      };

      if (editorContainer) {
        editorContainer.addEventListener('click', handleLinkClick, { capture: true });
      }

      return () => {
        if (editorContainer) {
          editorContainer.removeEventListener('click', handleLinkClick, { capture: true });
        }
      };
    }, [isEditing]);

    return (
      <>
        <div
          ref={wrapperRef}
          className={classNames({ [s.editorWrapper]: isEditing, editor__wrapper: isEditing })}
        >
          {type === 'comment' && reply}
          <AtPopover ref={mentionRef} participants={MentionExtraData.participants || []}>
            <Editor
              ref={editorRef}
              id="todo-remark-input-editor" // 确保每个编辑器都有唯一的id
              className={classNames(s.editor, {
                [s.noEditing]: !isEditing,
                [s.comment]: type === 'comment',
                [s.show]: type === 'comment' && memoShow,
              })}
              editorState={es}
              // initialHtml={initialHtml}
              placeholder={type === 'remark' ? I18N.auto.edit : I18N.auto.enterComment}
              onChange={handleEsChange}
              config={config}
              readOnly={!isEditing}
              onClick={handleEditorClick}
              isHideToolbar={!isEditing}
              autoFocus
              // onBlur={handleBlur}
              onFocusChange={(focused) => {
                if (focused) {
                  insertImageRef.current = false;
                }
              }}
            />
          </AtPopover>
        </div>

        {/* <AtPopover ref={mentionRef} /> */}
        {type === 'comment' && (
          <div
            className={classNames(s.otherCheckbox, {
              [s.show]: memoShow,
            })}
          >
            <Checkbox
              checked={inviteJoinTaskWhenAt}
              onChange={(e) => {
                setInviteJoinTaskWhenAt(e.target.checked);
              }}
            ></Checkbox>
            <div className={s.tip}>{I18N.auto.nonTaskResponsible}</div>
          </div>
        )}
      </>
    );
  })
);

export const setRemarkDefaultHeight = (type: string) => {
  if (type === 'remark') {
    const remarkContentEl = document.querySelector(
      '#detail_body .remark__content'
    ) as HTMLDivElement;
    if (remarkContentEl) {
      const editorWrapper = document.querySelector(
        '#detail_body .remark__content .editor__wrapper'
      ) as HTMLDivElement;
      const editorHeight =
        editorWrapper?.getBoundingClientRect().height ||
        remarkContentEl.getBoundingClientRect().height + 11;
      if (remarkContentEl) {
        remarkContentEl.style.height = `${editorHeight}px`;
      }
    }
  }
};

export default BedrockEditor;
