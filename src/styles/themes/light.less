body,
body[data-theme='light'] {
  --Brand50: #e2f0ff;
  --Brand100: #d1e6ff;
  --Brand200: #a8d0ff;
  --Brand300: #80bbff;
  --Brand400: #57a5ff;
  --Brand500: #2f8fff;
  --Brand600: #207ce5;
  --Brand700: #0c65cc;
  --Brand800: #0b58b2;
  --Brand900: #034899;

  --N0: #ffffff;
  --N50: #f8f8f8;
  --N60: #f5f5f5;
  --N100: #f1f1f1;
  --N200: #ebebeb;
  --N300: #dddddd;
  --N400: #bfbfbf;
  --N500: #999999;
  --N600: #666666;
  --N700: #444444;
  --N800: #313131;
  --N900: #111111;

  --aBlack: rgb(0, 0, 0);
  --aBlack90: rgba(0, 0, 0, 0.9);
  --aBlack80: rgba(0, 0, 0, 0.8);
  --aBlack60: rgba(0, 0, 0, 0.6);
  --aBlack50: rgba(0, 0, 0, 0.5);
  --aBlack40: rgba(0, 0, 0, 0.4);
  --aBlack24: rgba(0, 0, 0, 0.24);
  --aBlack20: rgba(0, 0, 0, 0.2);
  --aBlack16: rgba(0, 0, 0, 0.16);
  --aBlack12: rgba(0, 0, 0, 0.12);
  --aBlack10: rgba(0, 0, 0, 0.1);
  --aBlack8: rgba(0, 0, 0, 0.08);
  --aBlack6: rgba(0, 0, 0, 0.06);
  --aBlack4: rgba(0, 0, 0, 0.04);

  --aWhite90: rgba(255, 255, 255, 0.9);
  --aWhite80: rgba(255, 255, 255, 0.8);
  --aWhite60: rgba(255, 255, 255, 0.6);
  --aWhite50: rgba(255, 255, 255, 0.5);
  --aWhite40: rgba(255, 255, 255, 0.4);
  --aWhite24: rgba(255, 255, 255, 0.24);
  --aWhite20: rgba(255, 255, 255, 0.2);
  --aWhite16: rgba(255, 255, 255, 0.16);
  --aWhite12: rgba(255, 255, 255, 0.12);
  --aWhite10: rgba(255, 255, 255, 0.1);
  --aWhite8: rgba(255, 255, 255, 0.08);
  --aWhite6: rgba(255, 255, 255, 0.06);
  --aWhite4: rgba(255, 255, 255, 0.04);

  --aBrand90: rgba(47, 143, 255, 0.9);
  --aBrand80: rgba(47, 143, 255, 0.8);
  --aBrand60: rgba(47, 143, 255, 0.6);
  --aBrand50: rgba(47, 143, 255, 0.5);
  --aBrand40: rgba(47, 143, 255, 0.4);
  --aBrand24: rgba(47, 143, 255, 0.24);
  --aBrand20: rgba(47, 143, 255, 0.2);
  --aBrand16: rgba(47, 143, 255, 0.16);
  --aBrand12: rgba(47, 143, 255, 0.12);
  --aBrand10: rgba(47, 143, 255, 0.1);
  --aBrand8: rgba(47, 143, 255, 0.08);
  --aBrand6: rgba(47, 143, 255, 0.06);
  --aBrand4: rgba(47, 143, 255, 0.04);

  --R50: #fff0f0;
  --R100: #ffd6d6;
  --R200: #ffb9bb;
  --R300: #ff959b;
  --R400: #ff757c;
  --R500: #ff525d;
  --R600: #da273d;
  --R700: #b31732;
  --R800: #8b0b24;

  --G50: #eeffeb;
  --G100: #cbfcca;
  --G200: #a4f8a0;
  --G300: #7bf07e;
  --G400: #4de05b;
  --G500-t: 36, 196, 70;
  --G500: #24c446;
  --G600-t: 1, 158, 51;
  --G600: #019e33;
  --G700: #007821;
  --G800: #015215;

  --B50: #e6f8fe;
  --B100: #bee9fd;
  --B200: #87d5fd;
  --B300: #57bffc;
  --B400: #15a9fb;
  --B500: #018df5;
  --B600: #016ed5;
  --B700: #0151b0;
  --B800: #003b89;

  --O50: #fff3e5;
  --O100: #ffe1c7;
  --O200: #ffbe8f;
  --O300: #fdb47c;
  --O400: #ff9346;
  --O500: #ff842c;
  --O600: #ff6f08;
  --O700: #ba4e1f;
  --O800: #a13100;

  --M50: #ffebf3;
  --M100: #fec9e1;
  --M200: #ff9eca;
  --M300: #ff78b7;
  --M400: #fe50a1;
  --M500: #f5278a;
  --M600: #df1779;
  --M700: #b70b65;
  --M800: #900250;

  --Y50: #fffcdb;
  --Y100: #fff4b8;
  --Y200: #ffee8a;
  --Y300: #fee26c;
  --Y400: #ffd44c;
  --Y500: #ffc529;
  --Y600: #dea637;
  --Y700: #b88127;
  --Y800: #905e19;

  --L50: #f4ffdb;
  --L100: #e5ffad;
  --L200: #d5fc88;
  --L300: #c6f273;
  --L400: #abe548;
  --L500: #95d62b;
  --L600: #76b328;
  --L700: #568c1c;
  --L800: #3a6611;

  --C50: #e1fefb;
  --C100: #b0f5f5;
  --C200: #71ebeb;
  --C300: #2de0e0;
  --C400: #00d1d1;
  --C500: #00bdbd;
  --C600: #00a3a3;
  --C700: #00858a;
  --C800: #006970;

  --I50: #eeecfe;
  --I100: #ccceff;
  --I200: #9c9fff;
  --I300: #6a6af7;
  --I400: #473bf5;
  --I500: #2d1adb;
  --I600: #1e14ba;
  --I700: #060a96;
  --I800: #00017a;

  --P50: #f7ebff;
  --P100: #e9cbfe;
  --P200: #d5a1fc;
  --P300: #c888fc;
  --P400: #ad5cf0;
  --P500: #8e31e3;
  --P600: #6d22bf;
  --P700: #4f1399;
  --P800: #330a74;

  --V50: #ffebfe;
  --V100: #fecbfb;
  --V200: #fd96f8;
  --V300: #fc78f6;
  --V400: #fb50f3;
  --V500: #f022e5;
  --V600: #dc17ce;
  --V700: #b20ba9;
  --V800: #850485;

  --bgApplication: #f2f6f9;
  --bgApplicationHover: rgba(255, 255, 255, 0.7);
  --bgApplicationFocus: rgba(47, 143, 255, 0.12);
  --bgAlert: #f1f1f1;
  --bgBottom: #ffffff;
  --bgMiddle: #ffffff;
  --bgMiddleLight: #ffffff;
  --bgTop: #ffffff;
  --bgMenu: rgba(255, 255, 255, 0.7);
  --bgonBubble: #ffffff;
  --bgcard-onbubble: rgba(255, 255, 255, 0.88);

  --TextPrimary-strong: #0c0c0c;
  --TextPrimary: rgba(17, 17, 17, 0.88);
  --TextSecondary: rgba(17, 17, 17, 0.72);
  --TextSecondary-ongrey: rgba(17, 17, 17, 0.6);
  --TextTertiary: rgba(17, 17, 17, 0.5);
  --TextQuartus: rgba(17, 17, 17, 0.36);

  --IconBlack: rgba(17, 17, 17, 0.8);
  --IconPrimary: rgba(17, 17, 17, 0.62);
  --IconSecondary: rgba(17, 17, 17, 0.5);
  --IconTertiary: rgba(17, 17, 17, 0.4);
  --IconQuartus: rgba(17, 17, 17, 0.28);

  --LinkNormal: #207ce5;
  --LinkHover: #2f8fff;
  --LinkPressed: #0c65cc;

  --absWhite: #ffffff;
  --absaWhite90: rgba(255, 255, 255, 0.9);
  --absaWhite80: rgba(255, 255, 255, 0.8);
  --absaWhite60: rgba(255, 255, 255, 0.6);
  --absaWhite40: rgba(255, 255, 255, 0.4);
  --absaWhite24: rgba(255, 255, 255, 0.24);
  --absaWhite16: rgba(255, 255, 255, 0.16);
  --absaWhite12: rgba(255, 255, 255, 0.12);
  --absaWhite8: rgba(255, 255, 255, 0.08);
  --absaBlack: #000000;
  --absaBlack80: rgba(0, 0, 0, 0.8);
  --absaBlack60: rgba(0, 0, 0, 0.6);
  --absaBlack40: rgba(0, 0, 0, 0.4);
  --absaBlack24: rgba(0, 0, 0, 0.24);
  --absaBlack16: rgba(0, 0, 0, 0.16);
  --absaBlack12: rgba(0, 0, 0, 0.12);
  --absaBlack8: rgba(0, 0, 0, 0.08);
  --absaBlack6: rgba(0, 0, 0, 0.06);
  --absaBlack4: rgba(0, 0, 0, 0.04);

  --admintopBarBgLight: #e6eff4;
  --admintopBarBgStrong: #e6eff4;
  --admintopBarSearchBg: #ffffff;
  --admintopBarSearchBgHover: #f7f9fb;
  --admintopBarSearchIcon: rgba(0, 0, 0, 0.6);
  --admintopBarSearchIconHover: rgba(0, 0, 0, 0.8);
  --admintopBarSearchText: rgba(17, 17, 17, 0.6);
  --admintopBarSearchTextInput: rgba(17, 17, 17, 0.96);
  --admintopBarIcon: rgba(12, 12, 12, 0.62);
  --admintopBarIconBgHover: rgba(255, 255, 255, 0.5);
  --admintopBarIconBgPress: rgba(255, 255, 255, 0.6);
  --admintopLine: rgba(0, 0, 0, 0.24);
  --adminnavBg: #e2edf3;
  --adminnavIcon: rgba(12, 12, 12, 0.4);
  --adminnavIconBgHover: rgba(255, 255, 255, 0.6);
  --adminnavText: rgba(12, 12, 12, 0.7);
  --admintoastBg: #313131;
  --adminmask: rgba(0, 0, 0, 0.4);
  --adminqrCode: #000000;
  --admindisableMask: rgba(255, 255, 255, 0.8);

  --imgrayBubble: #eef0f1;
  --imbrandBubble: #dbecff;
  --imunread: rgba(1, 158, 51, 0.6);
  --imemojiReply: rgba(255, 255, 255, 0.4);
  --imAi: #6a6af7;
  --imAiBg: rgba(255, 255, 255, 0.5);

  --appLetscardFill: #f8f8f8;

  --cardB: #2f8fff;
  --cardBO16: rgba(47, 143, 255, 0.16);
  --cardGO16: rgba(36, 196, 70, 0.16);
  --cardOO16: rgba(255, 132, 44, 0.16);
  --cardOO8: rgba(255, 132, 44, 0.08);
  --cardRO16: rgba(255, 82, 93, 0.16);
  --cardNO16: rgba(153, 153, 153, 0.16);
  --cardbg: #ffffff;
  --cardborderMobile: rgba(0, 0, 0, 0.24);
  --cardborderDesktop: rgba(0, 0, 0, 0.12);
  --cardline: rgba(0, 0, 0, 0.12);
  --cardblue1: #2f8fff;
  --cardgreen1: #24c446;
  --cardorange1: #ff842c;
  --cardred1: #ff525d;
  --cardgray1: #999999;
  --cardblue2: #e2f0ff;
  --cardgreen2: rgba(36, 196, 70, 0.16);
  --cardorange2: rgba(255, 132, 44, 0.16);
  --cardred2: rgba(255, 82, 93, 0.16);
  --cardgray2: #dae0e5;
  --cardblue3: rgba(47, 143, 255, 0.08);
  --cardgreen3: rgba(36, 196, 70, 0.08);
  --cardorange3: rgba(255, 132, 44, 0.08);
  --cardred3: rgba(255, 82, 93, 0.08);
  --cardgray3: rgba(153, 153, 153, 0.08);

  --caltimeConflict: #ffb9bb;

  --TasksUrgentBg: #fdebec;
  --TasksHighBg: #faecdd;
  --TasksMediumBg: #e6deed;
  --TasksLowBg: #deecdc;
  --TasksUrgentText: #d44c47;
  --TasksHighText: #d9730d;
  --TasksMediumText: #9065b0;
  --TasksLowText: #448361;
  --TasksUrgentIcon: #d44c47;
  --TasksHighIcon: #da730e;
  --TasksMediumIcon: #8f66b0;
  --TasksLowIcon: #458262;
  --TasksDone: #019e33;
  --TasksTagbg: #f2f4f7;
  --TasksBGIV: rgba(255, 82, 93, 0.2);
  --TasksBGIII: rgba(255, 132, 44, 0.2);
  --TasksBGII: rgba(142, 49, 227, 0.2);
  --TasksBGI: rgba(36, 196, 70, 0.2);
  --TasksBG0: #e8ebed;
  --TasksBorderIV: rgba(255, 82, 93, 0.4);
  --TasksBorderIII: rgba(255, 132, 44, 0.4);
  --TasksBorderII: rgba(142, 49, 227, 0.4);
  --TasksBorderI: rgba(36, 196, 70, 0.4);
  --TasksTextIV: #d44c47;
  --TasksTextIII: #d9730d;
  --TasksTextII: #9065b0;
  --TasksTextI: #448361;
  --TasksText0: #3e4143;
  //私自加的 替换新版本时要确认有没有这个
  --TasksProjectStatusI: rgba(25, 114, 215, 0.12);
  --TasksProjectStatusII: rgba(255, 111, 8, 0.12);
  --TasksProjectStatusIII: rgba(218, 39, 61, 0.12);
  --TaskGuideBg: #eef6ff;
}
