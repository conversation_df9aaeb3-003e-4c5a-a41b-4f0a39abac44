import { ConfigProvider } from '@bedrock/components';
import { useMemoizedFn } from 'ahooks';
import { useContext, useEffect, useMemo } from 'react';

import s from './index.less';

//添加蒙层做屏蔽
export function usePopoverContainer({
  visible,
  onClick,
}: {
  visible: boolean;
  onClick: (e: MouseEvent) => any;
}) {
  let div = useMemo(() => {
    let el = document.createElement('div');
    el.classList.add(s.container);
    document.body.appendChild(el);
    return el;
  }, []);

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext);

  const clickHandle = useMemoizedFn(onClick);

  useEffect(() => {
    div.onmousedown = (e) => {
      if (e.target == div) {
        clickHandle(e);
      }
    };
    return () => {
      div.onmousedown = null;
      document.body.removeChild(div);
    };
  }, [div]);

  useEffect(() => {
    if (visible) {
      div.classList.add(s.mask);
      div.style.zIndex = getGlobalZIndex() + '';
    } else {
      div.classList.remove(s.mask);
      div.style.zIndex = 'auto';
    }
  }, [visible, div, getGlobalZIndex]);

  return div;
}
