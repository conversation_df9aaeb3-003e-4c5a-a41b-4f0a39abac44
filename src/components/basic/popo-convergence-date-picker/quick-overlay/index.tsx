import classNames from 'classnames';
import dayjs from 'dayjs';
import { useMemo } from 'react';

import { Divider } from '@/components/basic';
import { dddd, PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import { getDayEnd, getDayStart, TimeValue } from '../utils';
import s from './index.less';

export type Props = {
  className?: string;
  onChange?: (time: TimeValue) => void;
  onShowDateTimePanel?: () => void;
  showDelete?: boolean;
  isGetEnd?: boolean;
};
type Item = {
  title: string;
  showDesc: boolean;
  days: number;
};

export default function DatePickerOverlay(props: Props) {
  const { onChange, onShowDateTimePanel, showDelete, isGetEnd = true } = props;
  const onTime = (days: number) => {
    let time = getDayEnd(dayjs().add(days, 'day')).valueOf();
    if (!isGetEnd) {
      time = getDayStart(dayjs().add(days, 'day')).valueOf();
    }
    onChange?.({
      time: time,
      timeFormat: PPTimeFormat.olayDay,
      rrule: '',
    });
  };
  const onDeleteTime = () => {
    onChange?.({
      time: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
    });
  };
  const list = useMemo(() => {
    let items: Item[] = [
      {
        title: I18N.auto.today,
        showDesc: true,
        days: 0,
      },
      {
        title: I18N.auto.tomorrow,
        showDesc: true,
        days: 1,
      },
    ];
    return items;
  }, []);
  return (
    <div className={classNames('com-dropdown-select', s.panel)}>
      {list.map((item, index) => (
        <div
          key={index}
          className={s.item}
          onClick={() => {
            onTime(item.days);
          }}
        >
          <div className={s.title}>{item.title}</div>
          {item.showDesc ? (
            <div className={s.desc}>{dayjs().add(item.days, 'day').format(dddd)}</div>
          ) : null}
        </div>
      ))}
      <Divider type="horizontal" className={s.divider}></Divider>
      <div className={s.item} onClick={onShowDateTimePanel}>
        <div className={s.title}>{I18N.auto.dateAndTime}</div>
      </div>
      {showDelete ? (
        <>
          <Divider type="horizontal" className={s.divider}></Divider>
          <div className={s.item} onClick={onDeleteTime}>
            <div className={s.title}>{I18N.auto.deleteDeadline}</div>
          </div>
        </>
      ) : null}
    </div>
  );
}
