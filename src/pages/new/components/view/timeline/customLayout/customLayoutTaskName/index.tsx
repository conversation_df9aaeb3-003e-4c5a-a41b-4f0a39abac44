import { VGroup, VImage, VText } from '@visactor/vtable'
import * as VTable from '@visactor/vtable'
import { BaseTableAPI } from '@visactor/vtable/es/ts-types/base-table'
import { FederatedPointerEvent } from '@visactor/vtable/es/vrender'
import ReactDom from 'react-dom/client'

import { getVTableIcons } from '@/common/VTableIcons'
import { OneAndMoreType, TaskCompleteStatus, TaskStatusMenu } from '@/components/basic-task/task-status'
import {
  getTaskStatusFn,
  isThatLastOneFn,
  selectOperationMethodFn,
} from '@/components/basic-task/task-status/use-complete-task-status'
import { OneAndMoreServerParamsType, AddMenuId, TaskTableRowTypeAdd, TaskTableRowType } from '@/utils/const'
import I18N from '@/utils/I18N'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'
import { CorlorMode } from '@/utils/platform'

import { getTreeIndent, hideTooltip, mapValue, showTooltip } from '../../utils'
import { store } from '@/models/store'
import { QueryGroupBy, ViewTab } from '@/types'
import renderAvatar from '../customRenderAvatar'
import { EnumGroupBy } from '@/utils/fields'
import { FieldTypeEnum } from '@/types/custom-field'
import * as _ from 'lodash';
import { useTimelineDrag } from '../../hooks/useTimelineDrag';

const getIcon = (finished: 0 | 1, disabled: boolean, theme = CorlorMode.light) => {
  let iconName = finished ? 'done' : 'normal'

  if (disabled) {
    iconName = `${iconName}-disabled`
  }

  return getVTableIcons('task-status', theme, iconName)
}

const getPermissions = (taskId: number) => {
  if (!taskId) {
    return [false, false]
  }
  const permissions = store.getState().permissions.permissionsMap[taskId]?.permissions
  return validatesPermission({
    permissions: permissions,
    key: [TaskPermissionEnum.CAN_SET_COMPLETE_MODE, TaskPermissionEnum.CAN_COMPLETE],
  }) as boolean[]
}

export const createTaskName = (
  args: VTable.TYPES.CustomRenderFunctionArg<BaseTableAPI>,
  { themeTokensRef, hasDragIcon }: { themeTokensRef?: any, hasDragIcon?: boolean} = {}
) => {
  const { table, row, col, rect } = args
  const { height, width } = rect || table.getCellRect(col, row)
  const record = table.getRecordByCell(col, row)

  const hierarchyState = table.getHierarchyState(col, row) || 'collapse'

  console.log('hierarchyState', hierarchyState, col, row, record)

  const isGroupRecord = !!record?.isGroup
  const isParent = !!record?.isParent

  const { finished, selfFinished, editable, title, taskId } = record || {}

  // const rowIndex = table.getRecordShowIndexByCell(col, row);
  // const visibleRowRange = table.getBodyVisibleRowRange() || { rowStart: 0, rowEnd: 50 };
  let rowPermission: any[] = getPermissions(Number(taskId))

  // canComplete: 是否有完成权限 -> isManager
  let canComplete = false
  // if (visibleRowRange) {
  //   if (rowIndex + 1 >= visibleRowRange.rowStart && rowIndex + 1 <= visibleRowRange.rowEnd + 5) {
  //     console.log('visible', record.title);
  //     rowPermission = getPermissions(Number(taskId));
  //   }
  // }
  canComplete = rowPermission[1]

  const indent = getTreeIndent(col, row, table) + 4

  const isFinished = finished || selfFinished

  const countTitle = `${record.finishedCount}/${record.totalCount}`
  const countWidth = isGroupRecord ? countTitle.length * 7 + 4 : 0

  const subtaskCount = `${record.subtaskFinishedCount || 0}/${record.subtask?.length || 0}`
  const subtaskCountWidth = isParent ? subtaskCount.length * 7 + 30 : 0

  // 是否是新建任务行
  const isCreateRow = record._rowType === TaskTableRowType.addBtn

  const renderTaskStatus = () => {
    if (isCreateRow) {
      return (
        <VGroup
          attribute={{
            width: width - indent,
            height,
            display: 'flex',
            flexWrap: 'nowrap',
            justifyContent: 'flex-start',
            alignItems: 'center',
            alignContent: 'center',
          }}
          onClick={() => {
            window.gantt?.taskListTableInstance?.startEditCell(1, row);
          }}
        >
          <VTable.VRect
            attribute={{
              width: 23,
            }}
          />
          <VText
            attribute={{
              text: I18N.auto.newTask,
              fontSize: 13,
              fill: themeTokensRef.current.TextTertiary,
              textAlign: 'left',
              textBaseline: 'top',
              boundsPadding: [0, 0, 0, 0],
              cursor: 'pointer',
            }}
          />
        </VGroup>
      )
    }

    const foldNode =
      isGroupRecord || isParent ? (
        <VImage
          attribute={{
            id: 'fold-icon',
            width: 15,
            height: 15,
            image: getVTableIcons('task-fold', themeTokensRef.current._name, hierarchyState),
            boundsPadding: [0, 0, 0, 0],
            cursor: 'pointer',
          }}
          onMouseDown={(e: FederatedPointerEvent, ...rest) => {
            table.toggleHierarchyState?.(col, row)
            // NOTE: 手动折叠后 有些树的col、row不会更新，在此强制触发更新，后续寻找更高效的方式
            table.setRecords(table.records)
          }}
        />
      ) : (
        <VTable.VRect
          attribute={{
            width: 17,
          }}
        />
      )

    const statusNode = !isGroupRecord ? (
      <VGroup
        attribute={{
          width: 16,
          height: 18,
        }}
      >
        <VImage
          attribute={{
            width: 15,
            height: 15,
            image: getIcon(isFinished, !editable && !canComplete, themeTokensRef.current._name),
            boundsPadding: [0, 0, 0, 4],
            cursor: 'pointer',
            dy: 2,
            id: 'status-icon',
          }}
          stakeProxy={(stateName: string) => {
            if (stateName === 'hover' && editable && !isFinished) {
              return {
                image: getVTableIcons('task-status', themeTokensRef.current._name, 'normal-hover'),
              }
            }
            return {}
          }}
          onMouseEnter={event => {
            const getTipText = () => {
              if (!canComplete) {
                return I18N.auto.noEditingPermission
              }
              return isFinished ? I18N.auto.clickToRebuildTheTask : I18N.auto.clickToCompleteTheTask
            }

            const rect = table.getCellRect(col, row)

            const { top } = rect
            const scrollTop = table?.scrollTop || 0

            const container = document.getElementById('gantt-view')
            const containerRect = container?.getBoundingClientRect() || { left: 0, top: 0 }
            const htmlFontSize = window.getComputedStyle(document.documentElement)?.fontSize

            showTooltip(
              getTipText(),
              26 + containerRect.left + indent + 40,
              top +
              containerRect.top -
              scrollTop -
              36 -
              Math.round(mapValue(Number(htmlFontSize?.replace('px', ''))))
            );

            event.currentTarget.addState('hover', true, false)
            event.currentTarget.stage.renderNextFrame()
          }}
          onMouseLeave={event => {
            event.currentTarget.removeState('hover', false)
            event.currentTarget.stage.renderNextFrame()
            hideTooltip()
          }}
          onMouseDown={(e: FederatedPointerEvent, ...rest) => {
            hideTooltip()
          }}
        />
      </VGroup>
    ) : null

    return (
      <VGroup
        attribute={{
          display: 'flex',
          flexWrap: 'nowrap',
          justifyContent: 'flex-start',
          alignItems: 'center',
          alignContent: 'center',
          width: isGroupRecord ? 15 : 36,
          height: 18,
        }}
      >
        {foldNode}
        {statusNode}
      </VGroup>
    )
  }

  const renderTaskNameContent = () => {
    const queryGroupBys = store.getState().viewSetting.currentViewTab.queryGroupBys
    const queryGroupBy = queryGroupBys?.[record?.groupLevel || 0] as QueryGroupBy
    const customFieldUsers = record?.users
    if (queryGroupBy?.customFieldId) {
      const customFields = store.getState().viewSetting.customFields
      const field = customFields.find(v => v.fieldId === record?.groupBy)
      if (field?.type === FieldTypeEnum.user && customFieldUsers?.length) {
        return renderAvatar({
          avatars: customFieldUsers,
          themeTokensRef,
          length: record?.value?.length,
        })
      }
    }
    if (record?.groupBy === EnumGroupBy.ASSIGNEE && customFieldUsers?.length) {
      return renderAvatar({
        avatars: customFieldUsers,
        themeTokensRef,
        length: record?.value?.length,
      })
    }
    return (
      <VText
        attribute={{
          id: 'title',
          text: title,
          fontSize: 14,
          fontWeight: isGroupRecord ? 600 : 400,
          fill: isFinished ? themeTokensRef.current.TextTertiary : themeTokensRef.current['TextPrimary-strong'],
          textAlign: 'left',
          textBaseline: 'top',
          boundsPadding: [0, 0, 0, 5],
          maxLineWidth: width - 44 - indent - countWidth - subtaskCountWidth,
          cursor: 'pointer',
        }}
      />
    )
  }

  const container = (
    <VGroup
      attribute={{
        width,
        height,
        display: 'flex',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'center',
        alignContent: 'center',
        id: 'container',
      }}
    >
      <VTable.VRect
        attribute={{
          width: indent,
        }}
      />
      {renderTaskStatus()}
      {renderTaskNameContent()}
      {/* <VText
        attribute={{
          id: 'title',
          text: title,
          fontSize: 14,
          fontWeight: isGroupRecord ? 600 : 400,
          fill: isFinished ? themeTokensRef.current.TextTertiary : themeTokensRef.current['TextPrimary-strong'],
          textAlign: 'left',
          textBaseline: 'top',
          boundsPadding: [0, 0, 0, 5],
          maxLineWidth: width - 44 - indent - countWidth - subtaskCountWidth,
          cursor: 'pointer',
        }}
      /> */}
      {isParent && (
        <VGroup
          attribute={{
            width: 30,
            height,
            display: 'flex',
            flexWrap: 'nowrap',
            justifyContent: 'flex-start',
            alignItems: 'center',
            alignContent: 'center',
            id: 'container',
          }}
        >
          <VImage
            attribute={{
              width: 16,
              height: 16,
              image: `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/subtask/sub.png`,
              boundsPadding: [0, 0, 0, 5],
              id: 'subtask_icon',
            }}
          />
          <VText
            attribute={{
              text: subtaskCount,
              fontSize: 12,
              fill: themeTokensRef.current.TextTertiary,
              textAlign: 'left',
              textBaseline: 'top',
              boundsPadding: [0, 0, 0, 3],
            }}
          ></VText>
        </VGroup>
      )}
      {/* 展示分组数量 */}
      {isGroupRecord && (
        <VText
          attribute={{
            text: `${record.finishedCount}/${record.totalCount}`,
            fontSize: 13,
            fill: themeTokensRef.current.TextTertiary,
            textAlign: 'left',
            textBaseline: 'top',
            boundsPadding: [0, 0, 0, 8],
          }}
        ></VText>
      )}
    </VGroup>
  )
  return {
    rootContainer: container,
    renderDefault: false,
  }
}

let prevMenuRoot: ReactDom.Root | null = null
export const onTaskNameCellClick = (
  args: VTable.TYPES.MousePointerCellEvent,
  {
    listTableInstance,
    userInfo,
    dispatch,
    hasDragIconRef ,
  }: { listTableInstance?: VTable.ListTable; userInfo?: any; dispatch?: any, hasDragIconRef?: React.MutableRefObject<boolean> }
) => {
  const { col, row, target } = args
  const record = listTableInstance?.getRecordByRowCol(col, row)
  // 完成状态 icon
  if (col === (hasDragIconRef?.current ? 1 : 0) && row >= 1 && target?.id === 'status-icon') {

    const rect = listTableInstance?.getCellRect(col, row) || { top: 0, bottom: 0 };
    const [canSetComplete, canComplete] = getPermissions(record?.taskId);

    const { completeCondition, participantCount, finishedParticipantCount, selfFinished, finished } = record

    const _oneAndMoreType =
      completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one

    const isThatLastOne = isThatLastOneFn({
      oneAndMoreType: _oneAndMoreType,
      participantCount: participantCount || 0,
      finishedParticipantCount: finishedParticipantCount || 0,
      selfFinished: selfFinished,
      finished: finished,
    })

    const status = getTaskStatusFn({
      oneAndMoreType: _oneAndMoreType,
      userInfo: userInfo!,
      isThatLastOne,
      isManager: !!canSetComplete,
      ...record,
    })

    if (!canComplete) {
      return
    }

    const visibleDetail = dispatch?.detail?.getVisibleDetail()

    const selectOperationMethod = (params?: any) => {
      selectOperationMethodFn({
        loadingRef: { current: false },
        dispatch,
        isThatLastOne,
        userInfo: userInfo!,
        visibleDetail: visibleDetail,
        taskState: status,
        isManager: !!canSetComplete,
        ...record,
        ...params,
      })

      setTimeout(() => {
        const vtableMenu = document.querySelector('.vtable__menu-element')
        if (vtableMenu) {
          vtableMenu.classList.remove('vtable__menu-element--shown')
          vtableMenu.classList.add('vtable__menu-element--hidden')
        }
      }, 320)
    }

    if (status === TaskCompleteStatus.InstantaneousFinished || status === TaskCompleteStatus.InstantaneousUnfinished) {
      selectOperationMethod()
      return { stopNext: true }
    }

    const indent = getTreeIndent(col, row, listTableInstance)

    const scrollTop = listTableInstance?.scrollTop || 0

    if (prevMenuRoot) {
      prevMenuRoot.unmount()
      prevMenuRoot = null
    }
    const div = document.createElement('div')
    const root = ReactDom.createRoot(div)
    prevMenuRoot = root
    root.render(
      <TaskStatusMenu
        loadingRef={{ current: false }}
        taskState={status}
        selectOperationMethod={selectOperationMethod}
        dispatch
        isThatLastOne
        userInfo={userInfo}
        visibleDetail={visibleDetail}
        {...record}
      />,
    )

    listTableInstance?.showDropDownMenu(col, row, {
      content: div,
      referencePosition: {
        rect: {
          left: 14 + indent,
          top: rect?.top - scrollTop + 3,
          width: 15,
          height: 15,
          right: 30 + indent,
          bottom: rect?.bottom - 4 - scrollTop,
        },
      },
    })
    return { stopNext: true }
  }
}
