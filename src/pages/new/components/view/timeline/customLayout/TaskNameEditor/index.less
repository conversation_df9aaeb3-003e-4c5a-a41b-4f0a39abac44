.vtable__task-name-editor {
  position: absolute;
  top: 1px;
  width: 100%;
  min-height: 36px;
  padding: 1px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  height: auto;
  opacity: 0;
  background-color: var(--bgBottom);
}

.table__textarea {
  padding: 0 !important;
  border-radius: 0;
  background-color: var(--bgTop) !important;
  border: 1px solid var(--Brand500) !important;
  box-sizing: border-box !important;
  box-shadow: 0px 0.03rem 0.04rem 0px var(--absaBlack12) !important;
  width: 100%;
  max-width: unset !important;
  margin-right: -1px !important;
  line-height: 0.2rem;
  font-weight: 400;
  font-size: 0.14rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  color: var(--TextPrimary-strong);
  outline: none;
  user-select: none !important;
  -webkit-user-select: none !important;
  min-height: 100% !important;
  height: fit-content;

  &::before {
    border-radius: 0;
    display: none;
  }

  .rock-input-textarea {
    padding: 0.07rem !important;
    min-height: 36px;
    border-radius: 0;
    padding: 0;
    display: flex;
    align-items: center;
  }
}

.create__status--icon {
  position: relative;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
}
