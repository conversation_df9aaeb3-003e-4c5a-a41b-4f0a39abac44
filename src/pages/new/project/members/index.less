.projectList {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.list {
  flex: 1;
  padding: 0 20px 8px 20px;
  .colum {
    height: 60px;
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 32px;
    margin-right: 16px;
  }
  .name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .createTag {
    line-height: 22px;
    height: 22px;
    margin-left: 16px;
    padding: 0 6px;
    border-radius: 6px;
    color: var(--Brand700);
    font-size: 12px;
    background-color: var(--cardblue2);
  }
  .time {
    line-height: 22px;
    font-size: 13px;
    color: var(--TextSecondary);
  }
  :global {
    .todo-thead {
      .todo-th {
        border-top: none;
      }
    }
    .todo-table .todo-tr .todo-th,
    .todo-table .todo-tr .todo-td {
      border-right: none;
    }
  }
}
