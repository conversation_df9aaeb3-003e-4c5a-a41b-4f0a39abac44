import { ViewGantt, ViewMore } from '@babylon/popo-icons';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import { FC, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Icon, Input, Message, Modal, Tooltip } from '@/components/basic';
import { RootState } from '@/models/store';
import { ViewTab } from '@/types';
import I18N from '@/utils/I18N';
import { validatesPermission, ViewPermissionEnum, ViewTypeEnum } from '@/utils/permission';

import { EditType, ViewType } from '../interface';
import ViewTabMoreOption from '../view-tab-more';
import s from './index.less';
interface TabItemProps {
  viewTab: ViewTab;
  viewList?: ViewTab[];
  className?: string;
  activeTabId: number;
  idPrefix?: string;
  hiddenInMore?: boolean;
  changeCurrentViewTab?: (item: ViewTab) => void;
  onDelete?: (id: number) => void;
  isProjectTab?: boolean;
  renameViewTab?: (name: string, viewId: number) => void;
}

const TabItem = (props: TabItemProps) => {
  const {
    viewTab,
    activeTabId,
    idPrefix = '',
    hiddenInMore = false,
    changeCurrentViewTab,
    onDelete,
    renameViewTab,
    className,
    isProjectTab,
    viewList,
  } = props;

  const { currentViewTab } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab!,
  }));

  const isCommonView = [ViewTypeEnum.Common, ViewTypeEnum.System].includes(
    viewTab.viewDistribution!
  );

  const canDeleteView = validatesPermission({
    permissions: viewTab.permissions,
    key: isCommonView
      ? ViewPermissionEnum.CAN_DELETE_COMMON_VIEW
      : ViewPermissionEnum.CAN_DELETE_PERSON_VIEW,
  });

  const canEditView = validatesPermission({
    permissions: viewTab.permissions,
    key: isCommonView
      ? ViewPermissionEnum.CAN_EDIT_COMMON_VIEW
      : ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
  });

  const [isEdit, setIsEdit] = useState(false);

  const [value, setValue] = useState(viewTab.name);

  const viewIconName = useMemo(() => {
    switch (viewTab.viewType) {
      case 1:
        return 'icon-view_list_line';
      case 2:
        return 'icon-view_kanban_line';
      case 3:
        return 'icon-view_calendar_line';
      case 4:
        return <ViewGantt />;
    }
  }, [viewTab.viewType]);

  const onBlur = (e: { target: { value: string } }) => {
    const v = e.target.value;
    setIsEdit(false);
    if (!v) {
      setValue(viewTab.name);
    } else {
      // 添加重命名视图逻辑
      renameViewTab?.(v, viewTab.viewId!);
    }
  };

  const handleChange = useMemoizedFn((type: EditType) => {
    const _comViewList = viewList?.filter((item) =>
      [ViewTypeEnum.Common, ViewTypeEnum.System].includes(item.viewDistribution as ViewTypeEnum)
    );

    if (_comViewList?.length === 1 && _comViewList[0].viewId === viewTab.viewId) {
      Message.text(I18N.auto.atLeastInTheProject);
      return;
    }
    if (type === EditType.rename) {
      setIsEdit(true);
    } else if (type === EditType.remove) {
      Modal.confirm(
        {
          title: I18N.auto.confirmToDeleteThis,
          width: 400,
          scrolled: false,
          centered: true,
          okText: I18N.auto.deleteView,
          content: I18N.auto.theTaskWillNotBeAffected,
          onOk: () => {
            onDelete?.(viewTab.viewId!);
          },
        },
        'warning'
      );
    }
  });

  return (
    <ViewTabMoreOption
      onChange={handleChange}
      trigger={'contextMenu'}
      canDelete={canDeleteView as boolean}
      canEdit={canEditView as boolean}
    >
      <div
        className={classNames(
          s.item,
          {
            [s.activeTabItem]: activeTabId === viewTab.viewId,
            'active__tab--item': activeTabId === viewTab.viewId,
          },
          className
        )}
        id={`${idPrefix}-${viewTab.viewId}`}
        onClick={() => {
          if (viewTab.viewId !== currentViewTab.viewId) changeCurrentViewTab?.(viewTab);
        }}
      >
        <Icon className={s.icon} name={viewIconName} />
        <Tooltip title={viewTab.name} onlyEllipsis>
          <div
            className={s.title}
            onDoubleClick={() => {
              if (canEditView) {
                setIsEdit(true);
              }
            }}
          >
            {isEdit ? (
              <Input
                fill
                autoFocus
                value={value}
                onChange={(e) => {
                  setValue(e.target.value);
                }}
                onBlur={onBlur}
                className={s.input}
              ></Input>
            ) : (
              viewTab.name
            )}
          </div>
        </Tooltip>

        {activeTabId === viewTab.viewId && !hiddenInMore && (
          <>
            {isProjectTab && (canEditView || canDeleteView) ? (
              <div className={s.itemRight}>
                <ViewTabMoreOption
                  onChange={handleChange}
                  canDelete={canDeleteView as boolean}
                  canEdit={canEditView as boolean}
                >
                  <ViewMore className={classNames(s.icon, 'fs-16')} />
                </ViewTabMoreOption>
              </div>
            ) : (
              <></>
            )}

            <div className={classNames(s.inkbar)}></div>
          </>
        )}

        {activeTabId === viewTab.viewId && hiddenInMore ? (
          <Icon className={s.icon} name="icon-sys_check"></Icon>
        ) : null}
      </div>
    </ViewTabMoreOption>
  );
};

export default TabItem;
