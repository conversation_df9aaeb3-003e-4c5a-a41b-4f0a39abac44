.editor {
  display: inline-flex;
  position: relative;
  width: 100%;
  min-height: 22px;
  outline: none;
  white-space: pre-line;
  cursor: pointer;
  .editableWrap {
    width: 100%;
    :global {
      .rock-input-wrapper.rock-input-wrapper-inline {
        .rock-input {
          height: 22px;
          // font-size: 13px;
          &::placeholder {
            // font-size: 13px;
            height: 22px;
          }
        }
      }
    }
  }
  .content {
    display: inline-block;
    max-width: 100%;
    width: auto;
    outline: none;
    white-space: pre-wrap;
    word-break: break-all;
    // user-select: text;
    caret-color: transparent;
    // max-height: calc(100vh - 300px);
    // overflow: hidden auto;
  }

  :global {
    a[data-popo='tag'] {
      color: var(--LinkNormal);
      cursor: pointer;
    }
  }
  :global {
    .rock-input-wrapper.rock-input-wrapper-inline {
      width: 100%;
      padding: 0;
      margin: 0;
      background-color: transparent;
      .rock-input-textarea {
        padding: 0;
        margin: 0;
        min-height: 26px;
        // line-height: 26px;
        white-space: pre-line;
        word-break: break-all;
        user-select: none !important;
        border-radius: 0;
        color: var(--TextPrimary);
        scrollbar-color: rgba(153, 153, 153, 0.4) transparent;

        font-family: -apple-system, 'Microsoft Yahei', 'PingFang SC', BlinkMacSystemFont,
          'Helvetica Neue', Tahoma, Arial, 'Hiragino Sans GB', sans-serif, 'Apple Color Emoji',
          Roboto, RobotoDraft;
        &::-webkit-resizer {
          background: none;
        }
        &::placeholder {
          font-size: 13px;
          color: var(--TextTertiary);
        }
      }
    }
    .rock-input-affix-wrapper {
      resize: none !important;
    }
  }
}

.normal {
  :global {
    .rock-input-wrapper.rock-input-wrapper-inline {
      padding: 8px 6px;
      // margin-left: -7px; //扣除边框1px
      // margin-top: -9px; //扣除边框1px
      .rock-input-textarea {
        min-height: 100px !important;
      }
    }
  }
}
.unnormal {
  :global {
    .rock-input-wrapper.rock-input-wrapper-inline {
      border: none;
    }
  }
}

.placeholder {
  display: flex;
  align-items: center;
  flex: 1;
  color: var(--TextTertiary);
  font-size: 13px;
  line-height: 22px;
  //padding-left: 8px;
}

.disabled {
  cursor: default;
  // pointer-events: none;
}

.barrier {
  width: 1px;
}

.editorTextArea {
  :global {
    textarea {
      // max-height: calc(100vh - 300px);
      // overflow: hidden auto !important;
    }
  }
}
