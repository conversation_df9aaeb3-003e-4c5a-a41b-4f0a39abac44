import { TaskInfo } from '@/types'
import { TaskTableRowType } from '@/utils/const'
import { get } from 'lodash'
import { GroupByField, GroupConfigMap, GroupOptions } from './types'
import { GroupFieldProcessor } from './GroupFieldProcessor'
import { TaskGroupCollector } from './TaskGroupCollector'
import { UserGroupManager } from './UserGroupManager'
import { buildGroupedStructure, findGroupId, getAddLine, shouldKeepEmptyGroup } from './utils'
import { store } from '@/models/store'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'

// 主要分组逻辑
export const getGroupedList = (
  list: TaskInfo[],
  queryGroupBys?: GroupByField[],
  options: GroupOptions = {} as GroupOptions,
) => {
  const viewPermissions = store.getState().viewSetting.permissions
  const canCreateTask = validatesPermission({
    permissions: viewPermissions,
    key: ProjectPermissionEnum.CAN_CREATE_TASK,
  })

  if (!queryGroupBys?.length) {
    if (canCreateTask) {
      const lastItem = list[list.length - 1]
      if (lastItem?._rowType !== TaskTableRowType.addBtn) {
        const index = list.length
        const addLine = getAddLine({
          index,
          id: index,
        })

        return [...list, addLine]
      }
    }
    return list
  }
  const [firstGroupBy, secondGroupBy] = queryGroupBys
  const collector = new TaskGroupCollector()

  // 清理之前的用户分组
  if (GroupFieldProcessor.isUserField(firstGroupBy)) {
    UserGroupManager.clearUserGroups(firstGroupBy.fieldName || firstGroupBy.customFieldId!)
  }
  if (secondGroupBy && GroupFieldProcessor.isUserField(secondGroupBy)) {
    UserGroupManager.clearUserGroups(secondGroupBy.fieldName || secondGroupBy.customFieldId!)
  }

  // 预先初始化静态分组配置
  let firstConfigs = GroupFieldProcessor.getStaticConfigs(firstGroupBy, 0)
  let secondConfigs: GroupConfigMap | null = secondGroupBy
    ? GroupFieldProcessor.getStaticConfigs(secondGroupBy, 1)
    : null

  // 获取字段键
  const firstFieldKey = GroupFieldProcessor.getFieldKey(firstGroupBy)
  const secondFieldKey = secondGroupBy ? GroupFieldProcessor.getFieldKey(secondGroupBy) : ''

  // 判断是否为用户字段
  const isFirstUserField = GroupFieldProcessor.isUserField(firstGroupBy)
  const isSecondUserField = secondGroupBy ? GroupFieldProcessor.isUserField(secondGroupBy) : false

  // 遍历任务进行分组
  list?.forEach(task => {
    // 处理用户字段的动态分组
    if (isFirstUserField) {
      GroupFieldProcessor.processUserField(task, firstGroupBy, 0)
      firstConfigs = UserGroupManager.getUserConfigs(firstGroupBy.customFieldId || firstGroupBy.fieldName || '', 0)
    }

    if (isSecondUserField) {
      GroupFieldProcessor.processUserField(task, secondGroupBy!, 1)
      secondConfigs = UserGroupManager.getUserConfigs(secondGroupBy!.customFieldId || secondGroupBy!.fieldName || '', 1)
    }

    const firstValue = get(task, firstFieldKey)
    const firstGroupId = findGroupId(firstConfigs, firstValue)

    if (firstGroupId !== null) {
      task.groupedIds = [firstGroupId]

      if (secondGroupBy) {
        const secondValue = get(task, secondFieldKey)
        const secondGroupId = findGroupId(secondConfigs!, secondValue)

        if (secondGroupId !== null) {
          task.groupedIds.push(secondGroupId)
          collector.setDuration(`${firstGroupId}`, task)
          collector.setDuration(`${firstGroupId}-${secondGroupId}`, task)
          collector.addTask(`${firstGroupId}-${secondGroupId}`, task)
        }
      } else {
        collector.setDuration(`${firstGroupId}`, task)
        collector.addTask(`${firstGroupId}`, task)
      }
    }
  })

  const keepEmptyGroupFields = new Set([])

  const shouldKeepFirstEmpty = shouldKeepEmptyGroup(firstGroupBy, keepEmptyGroupFields)
  const shouldKeepSecondEmpty = shouldKeepEmptyGroup(secondGroupBy, keepEmptyGroupFields)

  // 构建分组数据
  return buildGroupedStructure(firstConfigs, secondConfigs, {
    ...options,
    groupMap: collector.getGroups(),
    groupStats: collector.getAllGroupStats(),
    groupDuration: collector.getAllGroupDuration(),
    keepFirstEmpty: shouldKeepFirstEmpty,
    keepSecondEmpty: shouldKeepSecondEmpty,
    canCreateTask,
  })
}
