.item {
  display: flex;
  width: 100%;
  background-color: var(--TasksTagbg);
  color: var(--TasksText0);
  padding: 6px 8px;
  border-radius: 6px;
  margin-bottom: 1px;
  border: 1px solid transparent;

  &.completed {
    .right {
      opacity: 0.5;
    }
  }
  // &:not(.completed):hover {
  &:hover {
    border-color: var(--aBlack16);
    background-color: var(--aBlack10);
  }
}

.height_1 {
  // height: 30px;
}
.height_2 {
  height: 52px;
}
.height_3 {
  height: 84px;
}
.height_4 {
  height: 96px;
}
.height_5 {
  height: 118px;
}

.left {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  box-sizing: content-box;
  margin-right: 6px;

  :global {
  }
}

.right {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  // height: 82px;
  :global {
    .com-level-bg {
      height: 20px;
      padding: 0 6px;
    }
    .com-level-icon {
      width: 12px;
      height: 12px;
      font-size: 12px;
    }
    .com-level-label {
      line-height: 11px;
      font-size: 11px;
    }
  }
}

.title {
  color: var(--TasksText0);
  font-size: 13px;
  font-weight: 600;
  line-height: 16px;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-word;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.completedTitle {
  // color: var(--TasksText0);
  // opacity: 0.5;
  text-decoration-line: line-through;
}
.level,
.time {
  margin-top: 4px;
}
.time {
  display: flex;
  align-items: center;
  line-height: 18px;
  height: 18px;
  span {
    font-size: 11px;
    line-height: 12px;
    margin-left: 2px;
  }
  &.expired {
    color: var(--R600);
  }
  &.today {
    color: var(--Brand600);
  }
}
