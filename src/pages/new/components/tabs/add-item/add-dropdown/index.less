.list {
  background: var(--bgBottom);
}
.descTitle {
  padding: 14px 12px 8px 12px;
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 22px;
  font-weight: bold;
}
.back {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
.item {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 8px;
  width: 170px;
  border-radius: 4px;
  cursor: pointer;
  i {
    margin-right: 8px;
  }
  .name {
    color: var(--TextPrimary);
    font-size: 13px;
  }
  &:hover {
    background-color: var(--aBlack6);
  }
}
.disableItem {
  i {
    color: var(--IconQuartus);
  }
  .name {
    color: var(--TextQuartus);
  }
  &:hover {
    background-color: transparent;
  }
}
