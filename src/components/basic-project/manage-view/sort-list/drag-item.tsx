import { ViewGantt, ViewMore, ViewOpenview } from '@babylon/popo-icons';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import { useMemo, useState } from 'react';

import { Icon, Input, Message, Modal, Tooltip } from '@/components/basic';
import { EditType } from '@/pages/new/components/tabs/interface';
import ViewTabMoreOption from '@/pages/new/components/tabs/view-tab-more';
import { ViewTab } from '@/types';
import I18N from '@/utils/I18N';
import { validatesPermission, ViewPermissionEnum, ViewTypeEnum } from '@/utils/permission';

import s from './index.less';

interface DragItemProps {
  viewTab: ViewTab;
  viewList?: ViewTab[];
  className?: string;
  onDelete?: (id: number) => void;
  renameViewTab?: (name: string, viewId: number) => void;
}
const DragItem = (props: DragItemProps) => {
  const { viewTab, viewList, className, onDelete, renameViewTab } = props;

  const isSystemView = viewTab.viewDistribution === ViewTypeEnum.System;
  const isCommonView = [ViewTypeEnum.Common, ViewTypeEnum.System].includes(
    viewTab.viewDistribution!
  );

  const canDeleteView = isSystemView
    ? false
    : validatesPermission({
        permissions: viewTab.permissions,
        key: isCommonView
          ? ViewPermissionEnum.CAN_DELETE_COMMON_VIEW
          : ViewPermissionEnum.CAN_DELETE_PERSON_VIEW,
      });

  const canEditView = isSystemView
    ? false
    : validatesPermission({
        permissions: viewTab.permissions,
        key: isCommonView
          ? ViewPermissionEnum.CAN_EDIT_COMMON_VIEW
          : ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
      });

  const [isEdit, setIsEdit] = useState(false);

  const [value, setValue] = useState(viewTab.name);

  const viewIconName = useMemo(() => {
    switch (viewTab.viewType) {
      case 1:
        return 'icon-view_list_line';
      case 2:
        return 'icon-view_kanban_line';
      case 3:
        return 'icon-view_calendar_line';
      case 4:
        return <ViewGantt />;
    }
  }, [viewTab.viewType]);

  const onBlur = (e: { target: { value: string } }) => {
    const v = e.target.value;
    setIsEdit(false);
    if (!v) {
      setValue(viewTab.name);
    } else {
      // 添加重命名视图逻辑
      renameViewTab?.(v, viewTab.viewId!);
    }
  };

  const handleChange = useMemoizedFn((type: EditType) => {
    const _comViewList = viewList?.filter((item) =>
      [ViewTypeEnum.Common, ViewTypeEnum.System].includes(item.viewDistribution as ViewTypeEnum)
    );

    if (_comViewList?.length === 1 && _comViewList[0].viewId === viewTab.viewId) {
      Message.text(I18N.auto.atLeastInTheProject);
      return;
    }
    if (type === EditType.rename) {
      setIsEdit(true);
    } else if (type === EditType.remove) {
      Modal.confirm(
        {
          title: I18N.auto.confirmToDeleteThis,
          width: 400,
          scrolled: false,
          centered: true,
          okText: I18N.auto.deleteView,
          content: I18N.auto.theTaskWillNotBeAffected,
          onOk: () => {
            onDelete?.(viewTab.viewId!);
          },
        },
        'warning'
      );
    }
  });

  return (
    <ViewTabMoreOption
      onChange={handleChange}
      trigger={'contextMenu'}
      canDelete={canDeleteView as boolean}
      canEdit={canEditView as boolean}
    >
      <div className={classNames(s.item, className)}>
        <div className={s.itemLeft}>
          <Icon className={s.icon} name="icon-pc_plane_move"></Icon>
          <div className={s.content}>
            <Icon className={s.icon} name={viewIconName!}></Icon>
            <div className={s.title}>
              {isEdit ? (
                <Input
                  fill
                  autoFocus
                  value={value}
                  onChange={(e) => {
                    setValue(e.target.value);
                  }}
                  onBlur={onBlur}
                  className={s.input}
                ></Input>
              ) : (
                viewTab.name
              )}
            </div>
          </div>
        </div>
        <div className={s.itemRight}>
          {isCommonView && (
            <Tooltip
              title={
                isSystemView ? I18N.auto.thisViewIsForTheSystem : I18N.auto.thisViewIsForPublic
              }
            >
              <div className={s.sysIcon}>
                <ViewOpenview className={classNames(s.icon, 'fs-16')} />
              </div>
            </Tooltip>
          )}

          {canEditView || canDeleteView ? (
            <ViewTabMoreOption
              onChange={handleChange}
              canDelete={canDeleteView as boolean}
              canEdit={canEditView as boolean}
            >
              <ViewMore className={classNames(s.icon, 'fs-16')} />
            </ViewTabMoreOption>
          ) : (
            <></>
          )}
        </div>
      </div>
    </ViewTabMoreOption>
  );
};

export default DragItem;
