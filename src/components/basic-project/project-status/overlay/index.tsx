import React from 'react';

import { Menu } from '@/components/basic';
import { EnumProjectStatus } from '@/utils/const';

import ProjectStatusRecord from '../project-status-record';
import ProjectStatusTag from '../project-status-tag';
import useProjectStatus, { ProjectRecord } from '../use-project-status';
import s from './index.less';

export type Props = {
  className?: string;
  value?: ProjectRecord;
  onChange?: (k: EnumProjectStatus) => void;
};

const Overlay: React.FC<Props> = (props) => {
  const { className, value, onChange } = props;
  const { changeProjectStatus } = useProjectStatus({
    onChange,
  });
  return (
    <div className={s.overlay}>
      <Menu>
        <Menu.Item
          key={EnumProjectStatus.ongoing}
          onClick={() => {
            changeProjectStatus(EnumProjectStatus.ongoing);
          }}
        >
          <ProjectStatusTag status={EnumProjectStatus.ongoing}></ProjectStatusTag>
        </Menu.Item>
        <Menu.Item
          key={EnumProjectStatus.pause}
          onClick={() => {
            changeProjectStatus(EnumProjectStatus.pause);
          }}
        >
          <ProjectStatusTag status={EnumProjectStatus.pause}></ProjectStatusTag>
        </Menu.Item>
        <Menu.Item
          key={EnumProjectStatus.risk}
          onClick={() => {
            changeProjectStatus(EnumProjectStatus.risk);
          }}
        >
          <ProjectStatusTag status={EnumProjectStatus.risk}></ProjectStatusTag>
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item
          key={EnumProjectStatus.done}
          onClick={() => {
            changeProjectStatus(EnumProjectStatus.done);
          }}
        >
          <ProjectStatusTag status={EnumProjectStatus.done}></ProjectStatusTag>
        </Menu.Item>
        <Menu.Item
          key={EnumProjectStatus.archive}
          onClick={() => {
            changeProjectStatus(EnumProjectStatus.archive);
          }}
        >
          <ProjectStatusTag status={EnumProjectStatus.archive}></ProjectStatusTag>
        </Menu.Item>

        {value?.state ? (
          <>
            <Menu.Divider />
            <ProjectStatusRecord value={value}></ProjectStatusRecord>
          </>
        ) : null}
      </Menu>
    </div>
  );
};

export default Overlay;
