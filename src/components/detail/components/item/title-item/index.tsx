import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { apiTodoUpdateTitlePost } from '@/api';
import ContentEditableEditor from '@/components/basic/content-editable-editor';
import { Dispatch } from '@/models/store';
import { TITLE_MAX_LENGTH } from '@/utils/const';

import s from './index.less';
export type Props = {
  className?: string;
  value?: string;
  onChange?: (v?: string) => void;
  isFinished?: boolean;
  disabled?: boolean;
  taskId: number;
};

const TitleItemBasic: React.FC<Props> = (props) => {
  const { value: baseValue = '', isFinished, disabled, taskId } = props;
  const dispatch = useDispatch<Dispatch>();
  const [value, setValue] = useState<string>(baseValue);

  useEffect(() => {
    setValue(baseValue);
  }, [baseValue]);

  const change = (v: string) => {
    if (v.length > TITLE_MAX_LENGTH) {
      return;
    }
    if (v && v !== value) {
      setValue(v);
      apiTodoUpdateTitlePost({
        taskId: taskId,
        title: v,
      })
        .finally(() => {
          dispatch.viewSetting.updateItemByDetail({ taskId });
        })
        .catch((error) => {
          setValue(baseValue);
        });
    }
  };
  return (
    <ContentEditableEditor
      disabled={disabled}
      className={classNames(s.detailTitle, { [s.finished]: isFinished })}
      contentClassName={s.title}
      value={value}
      onChange={change}
      //maxLength={10}
      maxLength={300}
      allowedEmpty={false}
    ></ContentEditableEditor>
  );
};

export default TitleItemBasic;
