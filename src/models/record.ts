import { createModel } from '@rematch/core';
import Quill from 'quill';

import {
  apiRecordQueryGet,
  ApiRecordQueryGetRequest,
  ApiRecordQueryGetResponse,
  apiTodoCommentAddPost,
  ApiTodoCommentAddPostRequest,
  apiTodoCommentDeleteDelete,
  apiTodoCommentReadPost,
  ApiTodoCommentReadPostRequest,
  apiTodoCommentUnreadGet,
} from '@/api';
import { ReadComment, RecordItem } from '@/types';

import type { RootModel } from '.';

const initdirection = 1;
const initeventCategory = 2;
interface ReplyCommentInfo {
  commentId?: number;
  operatorName?: string;
}

export const record = createModel<RootModel>()({
  state: {
    recordList: [] as RecordItem[],
    hasMore: false as boolean,
    eventCategory: initdirection, //编辑记录类型：0-全部，1-评论，2-编辑记录。默认 0
    size: 20,
    replyCommentInfo: {} as ReplyCommentInfo,
    readComment: {} as ReadComment,
    quill: undefined as Quill | undefined,
    bedrockEditor: undefined as any,
  },
  reducers: {
    initData(state) {
      return {
        recordList: [] as RecordItem[],
        hasMore: false as boolean,
        eventCategory: initdirection,
        size: 20,
        replyCommentInfo: {} as ReplyCommentInfo,
        readComment: {} as ReadComment,
        quill: state.quill,
        bedrockEditor: state.bedrockEditor,
      };
    },
    setQuill(state, payload: Quill) {
      return {
        ...state,
        quill: payload,
      };
    },
    setRecordList(state, payload: ApiRecordQueryGetResponse) {
      const { list = [], hasMore = false } = payload || {};
      return {
        ...state,
        recordList: list,
        hasMore,
      };
    },
    updateRecordList(state, payload: RecordItem[]) {
      return {
        ...state,
        recordList: payload,
      };
    },
    setUpwardRecordList(state, payload: ApiRecordQueryGetResponse) {
      const { list = [] } = payload || {};
      const { recordList } = state;
      return {
        ...state,
        recordList: list.concat(recordList),
      };
    },
    settaskId(state, payload: string) {
      return {
        ...state,
        taskId: payload,
      };
    },
    setInitData(state, payload: any) {
      return {
        ...state,
        hasMore: false,
        recordList: [],
        direction: initdirection,
        ...payload,
      };
    },
    setEventCategory(state, payload: number) {
      return {
        ...state,
        eventCategory: payload,
      };
    },
    setReplyCommentId(state, payload: ReplyCommentInfo) {
      return {
        ...state,
        replyCommentInfo: payload,
      };
    },
    setUnreadComment(state, payload: ReadComment) {
      return {
        ...state,
        readComment: payload,
      };
    },
  },
  effects: (dispatch) => ({
    //查询待办编辑记录，向后加载数据
    async getRecordList(
      options: ApiRecordQueryGetRequest & { page?: number },
      state
    ): Promise<ApiRecordQueryGetResponse> {
      const { page } = options;
      const { size, eventCategory, recordList } = state.record;
      const { taskId } = state.detail;
      let scrollId = '0';
      if (recordList.length && page !== 1) {
        scrollId = recordList[recordList.length - 1].scrollId!;
      }
      if (taskId) {
        return apiRecordQueryGet(
          {
            direction: String(initdirection),
            taskId: String(taskId),
            size: String(size),
            eventCategory: String(eventCategory),
            scrollId: scrollId,
          },
          { errorSilent: true }
        ).then((ret) => {
          const { recordList } = state.record;
          dispatch.record.setRecordList(
            page === 1
              ? ret
              : {
                  ...ret,
                  list: recordList.concat(ret.list!),
                }
          );
          return ret;
        });
      } else {
        return new Promise((resolve) => {
          resolve({
            hasMore: false,
            list: [],
          });
        });
      }
    },
    //向前加载数据 向上
    async getUpwardRecordList(options: ApiRecordQueryGetRequest, state) {
      const { size, eventCategory, recordList } = state.record;
      const { taskId } = state.detail;
      if (taskId) {
        let scrollId = recordList[0]?.scrollId || 0;
        return apiRecordQueryGet(
          {
            direction: '2', //时间距离现在最近的 向上加载
            taskId: String(taskId),
            size: String(size),
            eventCategory: String(eventCategory),
            scrollId: String(scrollId),
          },
          { errorSilent: true }
        ).then((ret) => {
          dispatch.record.setUpwardRecordList(ret);
          return ret;
        });
      }
    },
    async updateUnreadComment(taskId: number, state) {
      return apiTodoCommentUnreadGet({ taskId: String(taskId) }).then(async (res) => {
        //设置详情的未读状态
        dispatch.record.setUnreadComment(res);
      });
    },
    async addComment(opt: ApiTodoCommentAddPostRequest, state) {
      return apiTodoCommentAddPost({
        ...opt,
      });
      // .then(async () => {
      //   const { taskId } = opt;
      //   //更新最新评论未读状态之后在请求列表
      //   dispatch.record.updateUnreadComment(taskId!);
      //   await dispatch.record.getUpwardRecordList({});
      //   const { dataList } = state.task;
      //   const item = dataList.filter((item) => item.taskId === Number(taskId))[0];
      //   if (item) {
      //     item.commentCount = item.commentCount || 0;
      //     item.commentCount += 1;
      //     dispatch.task.updateTodoList(item);
      //   }
      // });
    },
    async deleteComment(opt: { commentId?: string; taskId?: string; scrollId?: string }, state) {
      const { commentId, taskId, scrollId } = opt;
      return apiTodoCommentDeleteDelete({
        taskId: taskId,
        commentId,
      }).then(() => {
        const { recordList } = state.record;
        const _recordList = recordList.filter((item) => item.scrollId !== scrollId);
        dispatch.record.updateRecordList(_recordList);
      });
    },
    async commentRead(opt: ApiTodoCommentReadPostRequest) {
      const { commentId, taskId } = opt;
      return apiTodoCommentReadPost({
        commentId: commentId,
        taskId: taskId,
      });
    },
  }),
});
