.item {
  display: flex;
  align-items: center;

  padding: 4px 6px;
  border-radius: 4px;
  background-color: var(--aBlack4);
  cursor: pointer;

  .icon {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    margin-right: 6px;
    img {
      width: 100%;
      height: 100%;
      border-radius: 2.33px;
    }
  }
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .content {
    flex: 1;
    color: var(--TextPrimary-strong);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .delete {
    width: 20px;
    height: 20px;
    color: var(--IconPrimary);
    i {
      color: var(--IconTertiary);
    }
    flex-shrink: 0;
    &:hover {
      background-color: var(--aBlack10);
    }
  }
}
