.select {
  display: flex;
  //padding-top: 8px;
  flex-direction: column;
  overflow: hidden !important;
}

.tabs {
  flex: 1;
  min-height: 1px;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 6px;
}

.tab {
  display: flex;
  flex-direction: row;
  margin: 0px 0 12px 0;
  padding: 0 12px;
  .groupTitle {
    display: flex;
    align-items: center;
    padding-bottom: 4px;

    font-size: 12px;
    line-height: 22px;
    color: var(--TextTertiary);
    cursor: pointer;
    &:hover {
      color: var(--Brand600);
    }
  }
  .active {
    position: relative;
    font-weight: 600;
    color: var(--Brand600);
    &::after {
      content: '';
      position: absolute;
      bottom: 0px;
      left: 0;
      right: 0;
      width: 100%;
      height: 2px;
      background-color: var(--Brand500);
    }
  }
}

.tabBox {
  padding: 0 4px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
}
.emptyTip {
  display: flex;
  width: 100%;
  height: 100%;
  font-size: 12px;
  align-items: center;
  color: var(--TextTertiary);
  justify-content: center;
}

.tabBoxIem {
  display: none;
}
.show {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.executorList {
  flex: 1;
  overflow-y: auto;
}
.executorTab {
  flex-shrink: 0;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
  color: var(--TextSecondary);
  margin-bottom: 2px;
  // font-weight: 500;
}
