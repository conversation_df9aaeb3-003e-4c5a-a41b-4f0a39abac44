import './index.less';

import Quill from 'quill';

import { POPOEmbedClass, POPOEmojiProps } from '@/components/basic-task/comment/editor/types';
interface PopoEmojiBlotProps extends POPOEmojiProps {}

class PopoEmojiBlot extends Quill.import('blots/embed') {
  static create(value: PopoEmojiBlotProps) {
    let node = super.create();
    node.innerHTML = `<img src="${value.src}" alt="${value.text}" class="ql-popo-emoji-img"></img>`;
    //node.contentEditable = false;
    node.setAttribute('data-src', value.src);
    node.setAttribute('data-text', value.text);
    node.setAttribute('data-key', value.key);
    return node;
  }

  static value(node: any) {
    return {
      src: node.getAttribute('data-src'),
      text: node.getAttribute('data-text'),
      key: node.getAttribute('data-key'),
    };
  }
}
PopoEmojiBlot.blotName = 'popoEmoji';
PopoEmojiBlot.tagName = 'span';
PopoEmojiBlot.className = POPOEmbedClass.emoji;

Quill.register(PopoEmojiBlot);
