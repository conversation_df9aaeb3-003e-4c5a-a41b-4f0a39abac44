.emojiPopover {
  padding: 0;
  margin: 0;
  :global {
    .rock-popover-inner {
      border-width: 0;
      border-radius: 12px;
      background-color: var(--bgMiddle);
    }
    .rock-popover-inner-content {
      padding: 0;
    }
    .rock-popover-arrow {
      background-color: var(--bgMiddle);
      border-color: var(--bgMiddle) !important;
    }
  }
  &:global(.rock-popover-placement-topRight > .rock-popover-content > .rock-popover-arrow) {
    bottom: -4.5px;
    right: 29px;
  }
  &:global(.rock-popover-placement-topLeft > .rock-popover-content > .rock-popover-arrow) {
    left: 29px;
    bottom: -4.5px;
  }
}
.emoji {
  max-width: 550px;
  min-width: 350px;
  padding: 12px 12px 0 12px;
  background-color: var(--bgMiddle);
  border-radius: 12px;
  .emojiList {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .emojiItem {
    display: inline-block;
    width: 28px;
    height: 28px;
    margin: 0 6px 12px 6px;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
