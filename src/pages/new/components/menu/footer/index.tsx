import { HelpBook, HelpRepo, OperateCloseS, SystemOpen } from '@babylon/popo-icons';
import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Popover, Tooltip } from '@/components/basic';
import { SurveyUrl } from '@/utils/const';
import { getFileByCDN } from '@/utils/getFileByCDN';
import I18N from '@/utils/I18N';

import s from './index.less';

const Footer: React.FC = () => {
  const [footerData, setFooterData] = useState<any>({});
  const [type, setType] = useState<'expand' | 'fold'>('fold'); // ['', 'userManual', 'userFeedback']
  const [visible, setVisible] = useState<boolean>(false);
  const handleJumpSurvey = () => {
    pp.openSysBrowser({
      url: SurveyUrl,
    });
    setVisible(false);
  };

  const { language } = useSelector((state: any) => ({ language: state.user.language }));

  const handleToggleExpand = (e?: React.MouseEvent) => {
    e?.stopPropagation?.();
    setType(type === 'fold' ? 'expand' : 'fold');
    localStorage.setItem(`FOOTER-MARK-V${footerData?.v || 1}`, type);
  };

  const handleOpenLink = (e: React.MouseEvent) => {
    e.stopPropagation();
    const link = footerData?.[type]?.link;
    if (link) {
      pp.openSysBrowser({
        url: link,
      });
    }
    handleToggleExpand();
  };

  useEffect(() => {
    getFileByCDN('json', 'footer-config.json').then((res) => {
      if (res) {
        const { v = 1 } = res;
        const footerMark = localStorage.getItem(`FOOTER-MARK-V${v}`);
        if (footerMark) {
          setType('fold');
        } else {
          setType('expand');
        }
        setFooterData(res);
      }
    });
  }, []);

  let content;
  if (type === 'expand') {
    let showBorder = true;
    if ('show-border' in footerData[type]) {
      showBorder = footerData[type]['show-border'];
    }

    let borderColor = footerData[type]?.['border-color'];
    borderColor = borderColor?.startsWith('--') ? `var(${borderColor})` : borderColor;

    let bg = '';
    if (typeof footerData[type]?.bg === 'object') {
      bg = footerData[type]?.bg?.[language];
    } else {
      bg = footerData[type]?.bg;
    }
    content = (
      <div
        className={`${s.footerBox} ${s[type]}`}
        style={{
          backgroundImage: `url(${bg}?v=${footerData.v})`,
          border: showBorder ? `1px solid ${borderColor}` : 'none',
        }}
        onClick={handleOpenLink}
      >
        <Tooltip title={I18N.auto.putAwayTheDayAndWait}>
          <OperateCloseS
            onClick={handleToggleExpand}
            className={s['fold__icon']}
            style={{ color: `var(${footerData[type]?.['close-icon-color']})` }}
          />
        </Tooltip>
      </div>
    );
  } else {
    content = (
      <Popover
        visible={visible}
        onVisibleChange={(v) => {
          setTimeout(() => {
            setVisible(v);
          }, 32);
        }}
        overlayClassName={s.popover}
        title=""
        showArrow={false}
        content={
          <div className={s.popoverContent}>
            <div className={s.popoverItem} onClick={handleJumpSurvey}>
              <HelpBook className={s.popoverIcon}></HelpBook> {I18N.auto.userManual}
            </div>
            <div
              className={s.popoverItem}
              onClick={() => {
                pp.openMessageSession({
                  id: '6400356',
                  type: 2, //此桥 群的type为2
                });
                setVisible(false);
              }}
            >
              <HelpRepo className={s.popoverIcon}></HelpRepo> {I18N.auto.userFeedback}
            </div>
          </div>
        }
        placement="rightBottom"
      >
        <div
          className={`${s.footerBox} ${s[type]}`}
          style={{
            backgroundImage: `url(${footerData[type]?.bg}?v=${footerData.v})`,
            borderColor: `var(${footerData[type]?.['border-color']})`,
          }}
        >
          <div className={classNames(s.footerIcon, 'mr-6')}></div>
          <span className={classNames(s.footerText, 'mr-6')}>{I18N.auto.easyToLearnItems}</span>
          <SystemOpen className={s.arrowIcon}></SystemOpen>
        </div>
      </Popover>
    );
  }

  return <div className={s.footer}>{content}</div>;
};
export default Footer;
