import dayjs from 'dayjs';

import { TaskInfo } from '@/types';
import { CustomField } from '@/types/custom-field';
import { EnumField, Priority } from '@/utils/fields';
import I18N from '@/utils/I18N';

export const getMonthScales = (themeTokens: any) => {
  return [
    {
      unit: 'month',
      step: 1,
      format(date) {
        return `    ${dayjs(date.startDate).year()}${I18N.auto.year}${date.dateIndex}${I18N.auto.month
          }`;
      },
      rowHeight: 32,
      style: {
        fontSize: 14,
        fontWeight: 'normal',
        color: themeTokens['TextPrimary-strong'],
        textAlign: 'left',
        textBaseline: 'middle',
        textStick: false,
        padding: [0, 0, 0, 8],
      },
    },
    {
      unit: 'day',
      step: 1,
      rowHeight: 32,
      format(date) {
        return date.dateIndex.toString();
      },
      style: {
        fontSize: 12,
        fontWeight: 'normal',
        color: themeTokens['TextSecondary-ongrey'],
        textAlign: 'center',
        textBaseline: 'middle',
      },
    },
  ];
};

export const getYearScales = (themeTokens: any) => {
  return [
    {
      unit: 'quarter',
      step: 1,
      rowHeight: 32,
      format(date) {
        return `    ${dayjs(date.startDate).year()}${I18N.auto.year}Q${date.dateIndex}`;
      },
      style: {
        fontSize: 14,
        fontWeight: 'normal',
        color: themeTokens['TextPrimary-strong'],
        textAlign: 'center',
        textBaseline: 'middle',
        textStick: false,
        padding: [0, 0, 0, 8],
      },
    },
    {
      unit: 'month',
      step: 1,
      rowHeight: 32,
      format(date) {
        return `${date.dateIndex}${I18N.auto.month}`;
      },
      style: {
        fontSize: 12,
        fontWeight: 'normal',
        color: themeTokens['TextSecondary-ongrey'],
        textAlign: 'center',
        textBaseline: 'middle',
        padding: [0, 0, 0, 8],
      },
    },
  ];
};

export enum ScaleType {
  month = 'month',
  year = 'year',
}

export const ModeOptions = [
  { label: I18N.auto.month, value: ScaleType.month },
  { label: I18N.auto.year, value: ScaleType.year },
];

export type IGanttTaskInfo = TaskInfo & {
  children?: TaskInfo[];
  finishedCount?: number;
  totalCount?: number;
  hierarchyState?: string;
  $startTime?: string;
  $deadline?: string;
  // [index: string]: any;
};

export const FieldLabelMap: Record<string, Record<string, any>> = {
  [EnumField.finished]: {
    0: I18N.auto.hangInTheAir,
    1: I18N.auto.completed,
  },
  [EnumField.priority]: {
    [Priority.Urgent]: I18N.auto.urgent,
    [Priority.High]: I18N.auto.high,
    [Priority.Medium]: I18N.auto.in,
    [Priority.Low]: I18N.auto.low,
  },
};

export interface IGanttTaskGroupInfo extends Omit<IGanttTaskInfo, 'taskId' | 'children'> {
  taskId: string | number;
  isGroup?: boolean;
  isGroupUnknown?: boolean;
  // 现阶段只用于内部分组的时候，时间类型分组使用
  groupType?: 'date';
  groupLevel?: number;
  groupField?: string;
  groupValue?: any;
  customField?: CustomField;
  children: (IGanttTaskGroupInfo | IGanttTaskInfo)[];
  groupOrder: number;
}

export function isGroupRecordFn(record: IGanttTaskInfo) {
  return !!record.children?.length && !record.isParent;
}

// 单例模式：全局共享的一个离屏 Canvas 上下文
let measureCtx: CanvasRenderingContext2D | null = null;
function getSharedContext() {
  if (!measureCtx) {
    const canvas = document.createElement('canvas');
    measureCtx = canvas.getContext('2d');
  }
  return measureCtx!;
}

const defaultFontFamily = `'-apple-system','Microsoft Yahei','PingFang SC'`;

/**
 * 高性能文字测量（快速版，误差约±1px）
 * @param {string} text 待测量文字
 * @param {{ fontSize: number, fontWeight: string }}
 * @returns {number} 近似宽度（像素）
 */
export function fastMeasureText(
  text: string | undefined,
  font: { fontSize: number; fontWeight?: string }
) {
  if (!text) {
    return 0;
  }
  const ctx = getSharedContext();
  ctx.font = `${font.fontWeight || ''} ${font.fontSize}px ${defaultFontFamily}`; // 直接复用上下文
  return ctx.measureText(text).width;
}

export const FinishedIcon = 'https://popo.gsf.netease.com/task-status/done.png';
