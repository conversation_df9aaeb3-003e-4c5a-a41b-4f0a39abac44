.config__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  font-size: 13px;
  line-height: 20px;

  border-radius: 4px;

  transition: background-color 0.2s;

  &--label {
    color: var(--TextPrimary);
  }

  .sub__title {
    display: flex;
    align-items: center;
    color: var(--TextTertiary);
  }

  &.primary {
    --bgColor: var(--aBlack6);
  }

  &.secondary {
    --bgColor: var(--aBlack4);
  }

  &.interactive {
    cursor: pointer;
  }

  &:hover {
    background-color: var(--bgColor);
  }

  &:global(.rock-popover-open) {
    background-color: var(--bgColor);
  }
}
