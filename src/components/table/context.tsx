import { createContext } from 'react';

import { TableNoDataProp, TaskColumnsType } from '@/types';
import { Order } from '@/utils/const';

interface ContextShape<RecordType = any> {
  columnsWidths: (string | number)[];
  onSelect?: (v: number[], rows: RecordType[]) => void;
  onSortChange?: (key: string, sortOrder: Order) => void;
  selectedRowKeys?: any[];
  columns: TaskColumnsType<RecordType>;
  dark?: boolean;
  theadRef?: React.MutableRefObject<null>;
  noDataProps?: TableNoDataProp;
  noSearchDataProps?: TableNoDataProp;
}

export const TableContext = createContext<ContextShape>({
  columnsWidths: [],
  selectedRowKeys: [],
  columns: [],
  dark: false,
  noDataProps: {},
  noSearchDataProps: {},
});
