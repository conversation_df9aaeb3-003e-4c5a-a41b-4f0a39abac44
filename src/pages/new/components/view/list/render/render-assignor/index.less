.assignerBox {
  cursor: unset;
}
.assigner {
  display: flex;
  align-items: center;
  // width: 100%;
  // height: 100%;
  cursor: pointer;
}

.deleteTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--TextPrimary);
}

.confirm {
  :global {
    .rock-btn-primary {
      background-color: var(--R600) !important;
      &:hover {
        background-color: var(--R500) !important;
      }
    }
  }
}
.avatar {
  width: 22px;
  height: 22px;
}
// .disabled {
//   cursor: not-allowed;
// }
