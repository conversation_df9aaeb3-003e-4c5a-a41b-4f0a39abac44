import { IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';
import { RockEditorPlugin, Plugin<PERSON><PERSON>, EditorState } from '@bedrock/editor';
import Emoji, { IEmoji } from '@/components/basic-task/emoji';
import EmojiNodeSpecs from './EmojiNodeSpecs';
import EmojiNodeView from './EmojiNodeView';
import classNames from 'classnames';
import s from './index.less';

const EmojiToolBar = (props) => {
  const handleEmojiClick = (emoji: IEmoji) => {
    const command = insertEmoji(emoji);
    command(props.editorState, props.dispatch);
  };

  const insertEmoji = (emoji: IEmoji) => {
    return (editorState, dispatch) => {
      const { tr, selection } = editorState as EditorState;
      const emojiType = editorState.schema.nodes.popoEmoji;
      let { $from, from } = selection,
        index = $from.index();
      if (!$from.parent.canReplaceWith(index, index, emojiType)) return false;
      const node = emojiType.create({
        src: emoji.dynamicPath,
        text: emoji.name,
        alt: emoji.name,
        key: emoji.key
      });
      if (dispatch) {
        dispatch(tr.insert(from, node));
      }
    };
  };

  return (
    <div className={classNames(s.emojiWrapper)}>
      <Emoji onClickEmoji={handleEmojiClick}>
        <IconBtn
          iconName="icon-pc_comment_emoji"
          title={I18N.auto.expression}
          iconClassName={s.emojiIcon}
        />
      </Emoji>
    </div>
  );
};

export default function createEmojiPlugin() {
  return new RockEditorPlugin({
    key: new PluginKey('emojiPlugin'),
    nodeSpecs: EmojiNodeSpecs,
    nodeViews: EmojiNodeView,
    getToolbarConfig: (plugins) => {
      return [...plugins, [EmojiToolBar]];
    },
  });
}
