import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Order } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  headClassName?: string;
  className?: string;
  title: string;
  onClear?: () => void;
  showClear?: boolean;
  footer?: React.ReactNode;
};

export interface Sorted {
  sortOrder: Order;
  key: string;
}

const Container: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, title, className, showClear = false, onClear, headClassName, footer } = props;

  return (
    <div className={classNames(s.overlay, className)}>
      <div className={classNames(s.head, headClassName)}>
        <div className={s.headLeft}>{title}</div>
        {showClear ? (
          <div className={s.headRight} onClick={onClear}>
            {I18N.auto.empty}
          </div>
        ) : null}
      </div>
      <div className={s.body}>{children}</div>
      {!!footer && <div className={s.footer}>{footer}</div>}
    </div>
  );
};

export default Container;
