import { Dispatch, RootState } from '@/models/store';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'umi';
import PageRight from '../../new/components/page-tasks';
import ViewSelector from './components/ViewSelector';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import s from './index.less';
import Filter from '@/pages/new/components/options/filter';
import CalendarConfig from './components/CalendarConfig';
import { ViewType } from '@/utils/const';
import { CalendarSaveViewBtn } from '@/pages/new/components/view/calendar/calendar-save-view-btn';
import CalendarFieldsSetting from './components/CalendarFieldsSetting';
import style from '../index.less';
import LoadingPage from '@/components/loading-page';

const SessionProject = () => {
  const { id } = useParams();
  const dispatch = useDispatch<Dispatch>();

  const [loading, setLoading] = useState(true);

  const { currentViewTab } = useSelector((state: RootState) => {
    return {
      currentViewTab: state.viewSetting.currentViewTab,
    };
  });

  useEffect(() => {
    dispatch.viewSetting.setData({ navigatorId: id });

    dispatch.viewSetting.updatePermissions({}).then((permissions) => {
      const [CAN_VIEW] = validatesPermission({
        permissions,
        key: [ProjectPermissionEnum.CAN_VIEW],
      }) as boolean[];
      if (CAN_VIEW) {
        setLoading(true);
        dispatch.viewSetting
          .fetchViewList(id)
          .then((viewList) => {
            const activeView = dispatch.viewSetting.getDefaultView({ viewList });
            if (activeView) {
              dispatch.viewSetting.toggeleView(activeView);
            }
            dispatch.viewSetting.refreshDataByDataChange({
              refreshCount: false,
              refreshList: true,
            });
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    });
  }, [id]);

  const renderCalendarOptions = () => {
    if (currentViewTab.viewType === ViewType.calendar) {
      return (
        <div className={`${s.option__container} flex-y-center`}>
          <ViewSelector />
          <Filter />
          <CalendarFieldsSetting />
          <CalendarConfig />
          <CalendarSaveViewBtn />
        </div>
      );
    }
    return null;
  };

  return loading ? (
    <LoadingPage showHeader={false} className={s.loading}></LoadingPage>
  ) : (
    <PageRight
      options={renderCalendarOptions()}
      className={style.session__container}
      prefixOption={<ViewSelector />}
      showTabs={false}
    />
  );
};

export default SessionProject;
