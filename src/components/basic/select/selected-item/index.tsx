import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import Icon from '../../Icon';
import s from './index.less';
interface SelectedItemProps {
  onClose: (e: React.MouseEvent<HTMLSpanElement, MouseEvent>) => void;
  showClose?: boolean;
  closeClass?: string;
}
const SelectedItem: React.FC<PropsWithChildren<SelectedItemProps>> = (props) => {
  const { onClose, children, showClose = true, closeClass } = props;
  return (
    <>
      {children}
      {showClose ? (
        <div className={classNames(s.optionClose, closeClass)}>
          <Icon
            onClick={(e) => {
              onClose(e);
            }}
            name="icon-sys_close"
          ></Icon>
        </div>
      ) : null}
    </>
  );
};

export default SelectedItem;
