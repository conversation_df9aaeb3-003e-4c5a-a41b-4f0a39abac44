import classNames from 'classnames';
import React, { FC, memo, PropsWithChildren } from 'react';

import { IconProject, Input, Tooltip } from '@/components/basic';
import { ProjectInfo, ProjectItemStatus } from '@/types';

import More from '../more';
import s from './index.less';
import { InputElement } from '@bedrock/components/lib/Input/InputBase';
import { useDispatch } from 'react-redux';
import { Dispatch } from '@/models/store';
import { Over } from '@dnd-kit/core';
import I18N from '@/utils/I18N';
import { OperateCaogao } from '@babylon/popo-icons';
import GroupFoldIcon from '../GroupFoldIcon';
import { CompositionInput } from '@/components/basic/input';

interface Props {
  active?: boolean;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  item: ProjectInfo;
  // 拖拽过程中是否可排序
  isSortable?: boolean;
  activeIndex?: number;
  depth?: number;
}

const RenderItem: React.FC<Props> = (props) => {
  const { item, depth = 0 } = props;
  if (item.isGroup) {
    return <GroupItem {...props} depth={depth}></GroupItem>;
  }
  return <ProjectItem {...props} depth={depth} />;
};

const ProjectItem: React.FC<Props> = (props) => {
  const { item, onClick, className } = props;
  const isPlaceholder = item.status === ProjectItemStatus.placeholder;

  const getName = () => {
    if (item.status === ProjectItemStatus.project_creating) {
      return item.name || I18N.auto.unnamedProject;
    }
    return item.name;
  };

  const renderContent = () => {
    if (isPlaceholder) {
      return <div className={s.placeholder}>{item.name}</div>;
    }
    return (
      <>
        <IconProject
          className={s.projectIcon}
          fontSize={16}
          name={item.icon}
          bgColor={item.iconColor}
          active
        />
        <Tooltip title={item.name} onlyEllipsis>
          <div className={s.menuName}>{getName()}</div>
        </Tooltip>
      </>
    );
  };

  const renderRight = () => {
    if (isPlaceholder) {
      return null;
    }
    return (
      <div className={s.right}>
        {item.status === ProjectItemStatus.project_creating ? (
          <OperateCaogao className={s.editIcon}></OperateCaogao>
        ) : (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <More projectInfo={item}></More>
          </div>
        )}
      </div>
    );
  };

  return (
    <ItemContainer {...props} onClick={isPlaceholder ? undefined : onClick} key={item.projectId}>
      <div className={s.left}>{renderContent()}</div>
      {renderRight()}
    </ItemContainer>
  );
};

export const GroupItem: React.FC<Props> = (props) => {
  const { onClick, ...rest } = props;
  const { item, depth } = props;
  const dispatch = useDispatch<Dispatch>();

  const handleConfirm = (e: any) => {
    e.stopPropagation();
    const input = e.target as InputElement;
    const name = input.value?.trim();
    if (name) {
      if (item.groupId) {
        dispatch.project.updateGroupName({
          projectId: item.projectId,
          name,
        });
      } else {
        dispatch.project.createProjectGroup({
          name,
          isPin: true,
        });
      }
    } else {
      if (item.groupId) {
        dispatch.project.updateGroupName({
          projectId: item.projectId,
          name: item.name,
        });
      } else {
        dispatch.project.removeItem({ projectInfo: item, isPin: true });
      }
    }
  };

  // const fold = projectGroupFoldStatus.isFold(item.id!);

  const renderContent = () => {
    if (item.status === ProjectItemStatus.editing) {
      return (
        <CompositionInput
          size="small"
          defaultValue={item.name}
          onKeyDown={(e) => {
            if (e.code === 'Space') {
              e.stopPropagation();
            }
          }}
          onPressEnter={handleConfirm}
          onBlur={handleConfirm}
          autoFocus
          maxLength={50}
          onClick={(e) => e.stopPropagation()}
          placeholder={I18N.auto.newGroup}
          className={s.editing__input}
        />
      );
    }

    return (
      <Tooltip title={item.name} onlyEllipsis>
        <div
          className={s.menuName}
          onClick={(e) => {
            e.stopPropagation();
            onClick?.(e);
          }}
        >
          {item.name}
        </div>
      </Tooltip>
    );
  };

  return (
    <>
      <ItemContainer depth={depth} {...rest}>
        <div className={s.left}>
          <GroupFoldIcon isGroup={!!item.isGroup} />
          {/* {fold ? <Folder2 className="fs-16" /> : <FolderOpen className="fs-16" />} */}
          {renderContent()}
        </div>
        {item.status !== ProjectItemStatus.editing && (
          <div className={s.right}>
            <div
              className="flex-y-center"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <More projectInfo={item}></More>
            </div>
          </div>
        )}
      </ItemContainer>
      {/* <div ref={childrenContainerRef} style={{ display: fold ? 'none' : 'block' }}>
        {item?.groupProjects?.map((p: any) => {
          p.id = p.projectId;
          return (
            <SortableItem key={p.id || p.projectId} data={p} hasHandle={false}>
              <RenderItem depth={depth + 1} item={p} />
            </SortableItem>
          );
        })}
      </div> */}
    </>
  );
};

const ItemContainer: FC<
  PropsWithChildren<Props & { onOverChange?: (over: Over | null) => void }>
> = ({ active, onClick, depth = 0, children }) => {
  return (
    <div
      className={classNames(s.box, {
        'project__item--active': active,
      })}
      style={{ paddingLeft: `${depth * 15}px` }}
    >
      <div className={classNames(s.menuItem)} onClick={onClick}>
        {children}
      </div>
    </div>
  );
};

export const RenderOverlayItem: React.FC<Props> = (props) => {
  const { item } = props;
  return (
    <div className={s.menuItem}>
      <div className={s.left}>
        <IconProject
          className={s.projectIcon}
          fontSize={16}
          name={item?.icon}
          bgColor={item?.iconColor}
          active
        />
        <div className={s.menuName}>{item?.name}</div>
      </div>
    </div>
  );
};

export default memo(RenderItem);
