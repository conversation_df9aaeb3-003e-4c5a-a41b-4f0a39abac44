import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Icon } from '@/components/basic';

import s from './index.less';

interface Props {
  showArrow?: boolean;
  hasArrow?: boolean;
}

const DropdownIcon: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, showArrow, hasArrow } = props;
  return (
    <div className={s.label}>
      <div className={classNames(s.content, 'picker-label-content')}>{children}</div>
      {hasArrow ? (
        <Icon
          name="icon-sys_Expand"
          className={classNames(s.arrow, 'picker-label-arrow', 'icon-sys_open', {
            [s.down]: showArrow,
          })}
        ></Icon>
      ) : null}
    </div>
  );
};

export default DropdownIcon;
