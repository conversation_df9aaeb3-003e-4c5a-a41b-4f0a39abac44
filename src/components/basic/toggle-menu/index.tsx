import classNames from 'classnames';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import { IconBtn } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  className?: string;
}

const ToggleMenu: React.FC<Props> = (props) => {
  const { className } = props;
  const dispatch = useDispatch<Dispatch>();
  const { showMenu } = useSelector((state: RootState) => ({
    showMenu: state.user.showMenu,
  }));
  const toggleShowMenu = () => {
    dispatch.user.setData({
      showMenu: !showMenu,
    });
  };
  return showMenu ? null : (
    <IconBtn
      iconClassName={s.iconColor}
      className={classNames('mr-8', s.toggleShowMenu)}
      iconName={showMenu ? 'icon-tage_shouqi' : 'icon-tage_zhankai'}
      onClick={toggleShowMenu}
      title={showMenu ? I18N.auto.putAwayTheDayAndWait : I18N.auto.expandTheDayOfWaiting}
    />
  );
};
export default ToggleMenu;
