import { Folder, FolderOpen } from '@bedrock/icons-react';
import { OperateArrowdown, OperateCollapse2, OperateExpand } from '@babylon/popo-icons';
import './index.less';
import { FC } from 'react';

interface IGroupFoldIconProps {
  isGroup: boolean;
}

const GroupFoldIcon: FC<IGroupFoldIconProps> = ({ isGroup }) => {
  if (!isGroup) {
    return null;
  }
  return (
    <div className="icon__wrapper">
      <div className="fold__icon">
        <OperateExpand className="fold__expanded fs-16" />
        <OperateCollapse2 className="fold__collapsed fs-16" />
      </div>
      <div className="arrow__icon">
        <OperateArrowdown className="arrow__down fs-16" />
        <OperateArrowdown className="arrow__right fs-16" />
      </div>
      {/* <div>{defaultIcon}</div> */}
    </div>
  );
};
export default GroupFoldIcon;
