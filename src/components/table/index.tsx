import {
  defaultDropAnimationSideEffects,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DropAnimation,
  MouseSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';

import { TableSortableItem } from '@/components/dndkit';
import { TableNoDataProp, TaskColumnsType } from '@/types';
import { EnumEmitter, Order, TaskTableRowType } from '@/utils/const';

import { RenderAddRow, RenderRow, Tbody, Thead } from './components';
import { TableContext } from './context';
import s from './index.less';
import TableVirtuoso from './table-virtuoso';
import { TableGroupItem, TableItem } from './types';

const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
};

export type Props<RecordType = TableItem> = {
  className?: string;
  data: RecordType[];
  columns: TaskColumnsType<RecordType>;
  selectedRowKeys?: any[];
  onSelect?: (v: number[], rows: RecordType[]) => void;
  onAddBtnClick?: (id: number | string, groupId?: string | number) => void;
  onSortChange?: (dataIndex: string, sortOrder: Order) => void;
  sortOrder: { dataIndex?: string; order?: Order };
  atBottomStateChange?: (atBottom: boolean) => void;
  isGroup?: boolean;
  groupData: TableGroupItem[];
  drag?: boolean;
  wholeRowDrag?: boolean;
  dark?: boolean;
  noDataProps?: TableNoDataProp;
  noSearchDataProps?: TableNoDataProp;
  groupRender?: (index: number) => React.ReactNode;
  onRowClick?: (v: RecordType, e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  onRowMouseDown?: React.MouseEventHandler<HTMLDivElement>;
  onDragEnd?: (v: DragEndEvent) => void;
  rowKey?: ((record: RecordType, index: number) => string) | string;
};
function TodoTable<T = TableItem>(props: Props) {
  const {
    columns,
    data,
    selectedRowKeys,
    onSelect,
    onAddBtnClick,
    onSortChange,
    sortOrder,
    atBottomStateChange,
    isGroup,
    groupData,
    drag,
    className,
    dark,
    noDataProps,
    noSearchDataProps,
    groupRender,
    onRowClick,
    onRowMouseDown,
    onDragEnd,
    rowKey,
    wholeRowDrag,
  } = props;
  const theadRef = useRef(null);
  const [dndActiveId, setDndActiveId] = useState<string | number | undefined>(undefined);

  const activeItem = useMemo(() => {
    return data?.find((item) => item.id === dndActiveId);
  }, [data, dndActiveId]);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 4,
      },
    })
  );
  useEffect(() => {
    if (theadRef) {
      if (theadRef.current) {
        //@ts-ignore
        theadRef.current.scrollLeft = 0;
        POPOBridgeEmitter.emit(EnumEmitter.ListTableSoscrollToLeft, 0);
      }
    }
  }, [columns, theadRef]);

  return (
    <div className={classNames(s.todoTable, className)}>
      <DndContext
        sensors={sensors}
        onDragStart={({ active }) => {
          POPOBridgeEmitter.emit(EnumEmitter.ListTableSoscrollToLeft, 0);
          setDndActiveId(active.id as string);
        }}
        modifiers={[restrictToVerticalAxis]}
        onDragEnd={(opt: DragEndEvent) => {
          const { active, over } = opt;
          if (!active.id || !over?.id) {
            return;
          }
          onDragEnd?.(opt);
          setDndActiveId(undefined);
        }}
      >
        <SortableContext items={data} strategy={verticalListSortingStrategy}>
          <TableContext.Provider
            value={{
              columnsWidths: columns.map((item) => item.width!),
              theadRef: theadRef,
              onSelect: onSelect, // 判断是否有筛选
              onSortChange: onSortChange,
              selectedRowKeys,
              columns,
              dark: dark,
              noDataProps: noDataProps,
              noSearchDataProps: noSearchDataProps,
            }}
          >
            <div
              className={classNames('todo-table', {
                ['todo-table-has-selectkeys']: !!selectedRowKeys?.length,
                ['todo-table-has-select']: !!onSelect,
              })}
            >
              <Thead data={data} columns={columns} sortOrder={sortOrder} ref={theadRef}></Thead>
              <Tbody>
                <TableVirtuoso
                  isGroup={isGroup}
                  data={data}
                  groupData={groupData}
                  atBottomStateChange={atBottomStateChange}
                  itemContent={(index: number) => {
                    const info = data[index];
                    if (!info) {
                      return;
                    }
                    let key: string | number | undefined = undefined;
                    if (rowKey) {
                      if (typeof rowKey === 'string') {
                        key = info?.[rowKey];
                      }
                      if (typeof rowKey === 'function') {
                        key = rowKey(info, index);
                      }
                    }
                    // TODO 这抽离成自定义渲染
                    if (info._rowType === TaskTableRowType.addBtn) {
                      return (
                        <RenderAddRow
                          key={key}
                          data={data}
                          itemData={info}
                          onAddBtnClick={() => {
                            onAddBtnClick?.(info.id!, info.groupId);
                          }}
                        ></RenderAddRow>
                      );
                    }
                    if (info._rowType === TaskTableRowType.add) {
                      return (
                        <RenderRow
                          key={key}
                          onRowClick={(e) => {
                            onRowClick?.(info, e);
                          }}
                          onRowMouseDown={onRowMouseDown}
                          hasCheckbox={false}
                          data={data}
                          itemData={info}
                          className="task-tr-add"
                        ></RenderRow>
                      );
                    }
                    if (drag) {
                      let childrenNodes = [];
                      const { groupProjects } = info;
                      if (groupProjects?.length) {
                        childrenNodes = groupProjects.map((item: any) => {
                          return (
                            <TableSortableItem
                              data={item}
                              key={item.projectId}
                              wholeRowHandle={wholeRowDrag}
                            >
                              {(dom: React.ReactNode) => {
                                return (
                                  <RenderRow
                                    key={item.projectId}
                                    sortableDom={dom}
                                    onRowClick={(e) => {
                                      onRowClick?.(item, e);
                                    }}
                                    className={classNames({
                                      ['todo-tr-open']: item._active,
                                    })}
                                    data={data}
                                    itemData={item}
                                    hasCheckbox
                                  ></RenderRow>
                                );
                              }}
                            </TableSortableItem>
                          );
                        });
                      }

                      return (
                        <>
                          <TableSortableItem data={info} key={key} wholeRowHandle={wholeRowDrag}>
                            {(dom: React.ReactNode) => {
                              return (
                                <RenderRow
                                  key={key}
                                  sortableDom={dom}
                                  onRowClick={(e) => {
                                    onRowClick?.(info, e);
                                  }}
                                  className={classNames({
                                    ['todo-tr-open']: info._active,
                                  })}
                                  data={data}
                                  itemData={info}
                                  hasCheckbox
                                ></RenderRow>
                              );
                            }}
                          </TableSortableItem>
                          {childrenNodes}
                        </>
                      );
                    }
                    return (
                      <RenderRow
                        key={key}
                        onRowClick={(e) => {
                          onRowClick?.(info, e);
                        }}
                        className={classNames({
                          ['todo-tr-open']: info._active,
                        })}
                        data={data}
                        itemData={info}
                        hasCheckbox
                      ></RenderRow>
                    );
                  }}
                  groupContent={(index: number) => {
                    if (groupRender) {
                      return groupRender?.(index);
                    }
                    //TODO 通用渲染 分组头
                    return <div></div>;
                  }}
                ></TableVirtuoso>
              </Tbody>
            </div>
          </TableContext.Provider>
        </SortableContext>
        <DragOverlay adjustScale={false} dropAnimation={dropAnimation} zIndex={9999}>
          {dndActiveId ? (
            <div className={classNames('todo-table', s.dragItem)}>
              <TableContext.Provider
                value={{
                  columnsWidths: columns.map((item) => item.width),
                  columns: columns,
                }}
              >
                <RenderRow data={data} itemData={activeItem!}></RenderRow>
              </TableContext.Provider>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}

export default TodoTable;
