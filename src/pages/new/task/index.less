.page {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
}
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 8px 20px;
  flex-shrink: 0;
  .toggleShowMenu {
    transform: translateX(-2px);
    .iconColor {
      color: var(--IconSecondary);
    }
  }
  .title {
    line-height: 24px;
    font-size: 16px;
    font-weight: 600;
    color: var(--TextPrimary);
  }
  .headLeft {
    display: flex;
  }
  .headRight {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }
}

.loading {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bgTop);
}

.hidden {
  display: none !important;
}
