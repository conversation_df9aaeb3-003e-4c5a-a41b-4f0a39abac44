import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useRef, useState } from 'react';

import { apiTodoAttachmentAddPost, apiTodoAttachmentDeleteDelete } from '@/api';
import I18N from '@/utils/I18N';
import { validatesVersionAndTip } from '@/utils/validate-version';

import { Message } from '..';
import Icon from '../Icon';
import Placeholder from '../placeholder';
import AttachmentFileItem, { ApiAttachment } from './file-item';
import s from './index.less';

export type Props = {
  value?: ApiAttachment[];
  onChange?: (v: ApiAttachment[]) => void;
  className?: string;
  placeholder?: string;
  hasArrow?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  disabled?: boolean;
  taskId: number;
};

const AttachmentSelect: React.FC<PropsWithChildren<Props>> = (props) => {
  const { taskId, value, onChange, className, visible = false, onVisible, disabled } = props;
  const [list, setList] = useState<ApiAttachment[]>(value || []);
  const cancelRef = useRef<(taskId: string) => void>();
  useEffect(() => {
    if (value) {
      setList(value);
    }
  }, [value]);
  const getFileDocIndex = (item: ApiAttachment) => {
    const { info, type } = item;
    let index = -1;
    if (type === 1) {
      index = list.findIndex((v) => v.info?.url === info?.url);
    }
    if (type === 2) {
      index = list.findIndex((v) => v.info?.fileId === info?.fileId);
    }
    if (type === 3) {
      index = list.findIndex((v) => v.info?.docId === info?.docId);
    }
    return index;
  };
  const uploadFile2Todo = (attachments: ApiAttachment[]) => {
    return apiTodoAttachmentAddPost({ taskId, attachments: attachments }).then((retList) => {
      //文件和待办进行关联之后改变状态以及回填attachmentId 删除需要attachmentId
      retList.forEach((item) => {
        const { id } = item;
        const index = getFileDocIndex(item);
        if (index > -1) {
          list[index].id = id;
          list[index].loading = false;
        }
      });
      const _list = [...list];
      setList(_list);
      onChange?.(_list);
    });
  };
  const selectLocalFile = async () => {
    if (disabled) {
      return;
    }
    onVisible?.(false);
    if (validatesVersionAndTip('openTodoLocalDocPicker')) {
      pp.openTodoLocalDocPicker({
        maxFileCount: 5,
        maxFileSize: 200 * 1024 * 1024,
        attachmentsMd5: [],
        onComplete: (e) => {
          const attachments: ApiAttachment[] = [];
          e.forEach((item) => {
            const index = list.findIndex((v) => v.taskId === item.taskId);
            if (index > -1) {
              const attachment = list[index];
              //@ts-ignore
              attachment.info = {
                ...attachment.info,
                ...item,
              };
              attachments.push(attachment);
            }
          });
          uploadFile2Todo(attachments);
        },
      }).then(({ cancelLocalDocUpload, list: dataList, status }) => {
        if (status === 3) {
          Message.error(I18N.auto.thereAreMoreThanM);
        }
        if (status === 2) {
          Message.error(I18N.auto.atMostOnce);
          return;
        }
        cancelRef.current = cancelLocalDocUpload;
        dataList.forEach((item) => {
          list.push({
            ...item,
            icon: '',
            loading: true,
          });
        });
        setList([...list]);
      });
    }
  };
  const selectCloudFile = async () => {
    onVisible?.(false);
    const { data } = await pp.openTodoCloudDocPicker({
      maxFileCount: 5,
      attachmentsUrl: [],
    });
    if (data?.length) {
      const attachments: ApiAttachment[] = [];
      data.forEach((item) => {
        list.push({
          ...item,
          icon: '',
          loading: true,
        });
        attachments.push({
          type: item.type,
          info: {
            docId: item.info.docId,
            docUrl: item.info.docUrl,
          },
        });
      });
      setList([...list]);
      uploadFile2Todo(attachments);
    }
  };
  const onDelete = (attachment: ApiAttachment) => {
    // 如果已经关联过待办 调用接口删除
    if (attachment.id) {
      const _list = list.filter((item) => {
        return attachment.id !== item.id;
      });
      apiTodoAttachmentDeleteDelete({
        taskId: String(taskId),
        attachmentIds: JSON.stringify([attachment.id]),
      }).then(() => {
        setList(_list);
        onChange?.(_list);
      });
    } else {
      //本地文件还在上传状态
      if (attachment.loading && attachment.taskId) {
        cancelRef.current?.(attachment.taskId!);
      }
      const _list = list.filter((item) => {
        if (attachment.info?.docId) {
          // 云空间文件
          return attachment.info?.docId !== item.info?.docId;
        }
        if (attachment.info?.fileId) {
          //本地文件
          return attachment.info?.fileId !== item.info?.fileId;
        }
        return true;
      });
      setList(_list);
      onChange?.(_list);
    }
  };
  const openDocFileDetail = (attachment: ApiAttachment) => {
    const { info, taskId, type } = attachment;
    if (type === 3) {
      //打开云空间文档
      pp.openSysBrowser({ url: info?.url });
    } else {
      // 下载文件
      if (validatesVersionAndTip('downloadTodoAttachment')) {
        if (attachment.type === 1) {
          let name = attachment.info.name?.match(/[^/]+$/)?.[0];
          if (!name?.includes('.')) {
            name = `${name}.png`;
          }
          attachment.info = {
            ...attachment.info,
            name: name,
          };
        }
        pp.downloadTodoAttachment({
          attachment: attachment,
          taskId: taskId,
          openAfterDownload: true,
          // onComplete: () => {
          // },
        });
      }
    }
  };
  return (
    <div
      className={classNames(
        s.attachmentSelecWrap,
        { [s.attachmentDisabledWrapper]: disabled, [s.noAttachmentSelectWrap]: !list?.length },
        className
      )}
    >
      <div className={s.list}>
        {list.map((item, index) => (
          <div key={index} className={s.itemWrapper}>
            <AttachmentFileItem
              key={index}
              type={item.type}
              info={item.info}
              taskId={item.taskId}
              loading={item.loading}
              onDelete={() => {
                onDelete(item);
              }}
              openDocFileDetail={() => {
                openDocFileDetail(item);
              }}
              disabled={disabled}
            ></AttachmentFileItem>
          </div>
        ))}
      </div>
      <div
        className={classNames(
          s.add,
          'detail-attachment-placeholder',
          value?.length && s.hasListAdd,
          disabled && value?.length && s.hasListDisabledAdd
        )}
        onClick={selectLocalFile}
      >
        {value?.length ? (
          disabled ? null : (
            <>
              <Icon name="icon-add" className={s.plus} fontSize={14}></Icon>
              <span>{I18N.auto.addAttachments}</span>
            </>
          )
        ) : (
          <Placeholder text={disabled ? I18N.auto.nothing : I18N.auto.addTo}></Placeholder>
        )}
      </div>

      {/* <Dropdown
             title={<div className={classNames(s.add, 'detail-attachment-placeholder')}>添加附件</div>}
             trigger="click"
             arrow={false}
             disabled={disabled}
             defaultOpen={visible}
             open={visible}
             onOpenChange={(v) => {
               onVisible?.(v);
             }}
             //@ts-ignore
             destroyPopupOnHide
             minOverlayWidthMatchTrigger={false}
             getPopupContainer={(dom) => dom.parentNode}
             overlay={
               <div className={classNames('com-dropdown-select', s.panel)}>
                 <div className={s.panelItem} onClick={selectCloudFile}>
                   云空间文件
                 </div>
                 <div className={s.panelItem} onClick={selectLocalFile}>
                   本地文件
                 </div>
               </div>
             }
            /> */}
    </div>
  );
};

export default AttachmentSelect;
