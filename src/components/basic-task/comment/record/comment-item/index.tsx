import { pp } from '@popo-bridge/web'
import classNames from 'classnames'
import React, { useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { IconBtn, Popconfirm } from '@/components/basic'
import { Dispatch, RootState } from '@/models/store'
import I18N from '@/utils/I18N'

import { POPOEmbedClass } from '../../editor/types'
import {
  dataPOPOtag2Delta,
  dataPOPOtag2Es,
  findIndexDescendantInArray,
  getInitEmptyDelta,
  isAncestorOrSelfWithClass,
} from '../../editor/utils'
import ItemRender from '../render-item'
import s from './index.less'
interface Props {
  operatorAvatarUrl?: string
  operatorUid?: string
  operatorName?: string
  isreply?: boolean
  replyToCommentCreatorName?: string
  content?: string
  contentClassName?: string
  taskId?: number
  commentId?: number
  canReply?: boolean
  event?: string
  scrollId?: string
}

const CommentItemRender: React.FC<Props> = props => {
  const {
    taskId,
    commentId,
    operatorUid,
    operatorName,
    isreply,
    replyToCommentCreatorName,
    content,
    contentClassName,
    canReply = true,
    event,
    scrollId,
  } = props
  const { uid, quill, bedrockEditor } = useSelector((state: RootState) => ({
    uid: state.user.userInfo?.uid,
    quill: state.record.quill,
    bedrockEditor: state.record.bedrockEditor,
  }))
  const dispatch = useDispatch<Dispatch>()
  const [disabled, setDisabled] = useState(false)
  const onDelete = (e: Event) => {
    e.stopPropagation()
    if (disabled) {
      return
    }
    setDisabled(true)
    dispatch.record
      .deleteComment({
        taskId: String(taskId),
        commentId: String(commentId),
        scrollId: scrollId,
      })
      .finally(() => {
        setDisabled(false)
      })
    // 删除评论后，重置评论区输入框
    dispatch.record.setReplyCommentId({
      commentId: 0,
      operatorName: ''!,
    })
  }
  const memoRitchHTML = useMemo(() => {
    // if (quill) {
    //   const a = dataPOPOtag2Delta(content!);
    //   quill.setContents(a);
    //   const html = quill.root.innerHTML;
    //   quill.setContents(getInitEmptyDelta());
    //   return html;
    // }
    if (!content) return undefined
    const html = dataPOPOtag2Es(content)
    return html
  }, [content, bedrockEditor, quill])

  return (
    <div className={s.commentBox}>
      <div className={s.commentHead}>
        {event ? (
          <ItemRender
            className={s.commentHeadLeft}
            right={isreply ? replyToCommentCreatorName : ''}
            event={event}
            linkBreak={!isreply}
          ></ItemRender>
        ) : null}
        <div className={s.commentHeadRight}>
          {canReply ? (
            <IconBtn
              className={classNames(s.icon, 'ml-6', 'mr-6')}
              iconClassName={'replyBtn'}
              fontSize={16}
              title={I18N.auto.reply}
              iconName="icon-pc_comment_reply"
              onClick={() => {
                if (canReply) {
                  dispatch.record.setReplyCommentId({
                    commentId: commentId!,
                    operatorName: operatorName!,
                  })
                }
              }}
            ></IconBtn>
          ) : null}
          {canReply && operatorUid === uid ? (
            <Popconfirm
              title={<div>{I18N.auto.okToDeleteThis}</div>}
              danger
              hasIcon={false}
              placement="bottomRight"
              cancelText={I18N.auto.cancel}
              okText={I18N.auto.delete}
              builtinPlacements={{
                bottomRight: {
                  points: ['tr', 'br'],
                  offset: [15, 0],
                },
              }}
              trigger="click"
              onOk={onDelete}
            >
              <IconBtn
                className={classNames(s.icon)}
                fontSize={16}
                title={<span style={{ whiteSpace: 'nowrap' }}>{I18N.auto.deleteComment}</span>}
                iconName="icon-sys_close"
              ></IconBtn>
            </Popconfirm>
          ) : null}
        </div>
      </div>
      <div
        className={classNames(s.comment, contentClassName, 'popo-qiull-editor ql-container ql-snow')}
        onClick={e => {
          //@ts-ignore
          if (e.target.closest('.ql-popo-mention')) {
            //@ts-ignore
            const popoMentionE = e.target.closest('.ql-popo-mention')
            const uid = popoMentionE.getAttribute('data-uid')
            if (uid) {
              // pp.openMessageSession({
              //   id: uid,
              //   type: 1,
              // });
              pp.openUserProfile({ uid: uid })
            }
          }
          //@ts-ignore
          if (e.target.closest('.ql-popo-doc')) {
            //@ts-ignore
            const popoDocE = e.target.closest('.ql-popo-doc')
            const url = popoDocE.getAttribute('data-docUrl')
            pp.openSysBrowser({ url: url })
          }
          //@ts-ignore
          if (e.target && e.target?.href && e.target.tagName === 'A') {
            //@ts-ignore
            const url = e.target.href
            pp.openSysBrowser({ url: url })
            e.preventDefault()
          }
          // if (e.target.closest('.ql-popo-emoji')) {
          //   console.log('点击了表情');
          // }
          // @ts-ignore
          if (e.target.closest('.prosemirror-popo-doc')) {
            //@ts-ignore
            const popoDocE = e.target.closest('.prosemirror-popo-doc')
            const url = popoDocE.getAttribute('data-src')
            pp.openSysBrowser({ url: url })
          }
          if (e.target.closest('.image-wrapper img')) {
            const img = e.target.closest('.image-wrapper img')
            const src = img.getAttribute('src')
            pp.previewImage({ images: [src] })
          }
          e.stopPropagation()
        }}
        onCopy={event => {
          const selection = window.getSelection()
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const classList = [POPOEmbedClass.emoji, POPOEmbedClass.doc, POPOEmbedClass.mention]
            //判断当前元素是不是自身或者祖先是不是自定义的embed 最高遍历到popo-qiull-editor结束
            const alone = isAncestorOrSelfWithClass(
              range.commonAncestorContainer as HTMLElement,
              classList,
              'popo-qiull-editor',
            )
            let val = {
              plain: '',
              html: '',
            }
            if (alone.v) {
              //如果是@联系人转文本, 无权限的链接转文本, 有权限的文档和表情补全
              const ele = alone.element
              if (alone.className && classList.includes(alone.className)) {
                val.html = alone.element.outerHTML
              } else {
                // 未识别默认
                val.html = range.commonAncestorContainer.nodeValue || ''
              }
            } else {
              //组合数据  判断当前的子元素
              const childNodes = range.commonAncestorContainer.childNodes
              if (childNodes.length) {
                //range.startContainer range.endContainer
                const startIndex = findIndexDescendantInArray(range.startContainer as HTMLElement, childNodes)
                const endIndex = findIndexDescendantInArray(range.endContainer as HTMLElement, childNodes)
                if (startIndex > -1 && endIndex > -1) {
                  for (let index = 0; index < childNodes.length; index++) {
                    if (index < startIndex) {
                      continue
                    }
                    if (index > endIndex) {
                      break
                    }
                    if (index === startIndex) {
                      const alone = isAncestorOrSelfWithClass(
                        range.startContainer as HTMLElement,
                        classList,
                        'popo-qiull-editor',
                      )
                      if (range.startContainer.nodeType === 3 && !alone.v) {
                        val.html += range.startContainer.nodeValue?.substring(range.startOffset)
                      } else {
                        //@ts-ignore
                        val.html += childNodes[index].outerHTML || childNodes[index].nodeValue
                      }
                    } else if (index === endIndex) {
                      const alone = isAncestorOrSelfWithClass(
                        range.endContainer as HTMLElement,
                        classList,
                        'popo-qiull-editor',
                      )
                      if (range.endContainer.nodeType === 3 && !alone.v) {
                        val.html += range.endContainer.nodeValue?.substring(0, range.endOffset)
                      } else {
                        //@ts-ignore
                        val.html += childNodes[index].outerHTML || childNodes[index].nodeValue
                      }
                    } else {
                      //@ts-ignore
                      val.html += childNodes[index].outerHTML || childNodes[index].nodeValue
                    }
                  }
                }
              } else {
                if (range.commonAncestorContainer.nodeType === 3) {
                  val.html += range.commonAncestorContainer.nodeValue?.substring(range.startOffset, range.endOffset)
                } else {
                  val.html = range.commonAncestorContainer.nodeValue || ''
                }
              }
            }
            event.preventDefault() // 阻止默认复制行为
            // event.clipboardData.setData('text/plain', '测试');
            const plainText = val.html.replace(/<[^>]+>/g, '')
            event.clipboardData.setData('text/html', val.html) // 将修改后的内容添加到剪贴板
            event.clipboardData.setData('text/plain', plainText)
          }
        }}
        dangerouslySetInnerHTML={{ __html: memoRitchHTML }}
      ></div>
    </div>
  )
}

export default CommentItemRender
