import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import PeopleList, { TagData } from '../people-list';
import s from './index.less';
import { ArrowDown, ArrowUp } from '@bedrock/icons-react';

export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  list: UserInfo[];
  value?: UserInfo[];
  onChange?: (v: UserInfo[]) => void;
  loading?: boolean;
  multiple?: boolean;
  emptyTip?: React.ReactNode;
  currentUser?: UserInfo;
  extraItemTag?: (item: UserInfo) => TagData | undefined;
  searchNoDataTip?: string;
};
const SearchList: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    className,
    list,
    value,
    onChange,
    loading,
    multiple,
    emptyTip,
    extraItemTag,
    currentUser,
    searchNoDataTip,
  } = props;
  return (
    <div className={classNames(s.search, className)}>
      {loading || searchNoDataTip ? (
        <div className={s.text}>{searchNoDataTip || I18N.auto.searching}</div>
      ) : (
        <>
          <PeopleList
            list={list}
            value={value}
            multiple={multiple}
            onChange={onChange}
            canSelect={true}
            emptyTip={emptyTip}
            extraItemTag={extraItemTag}
            currentUser={currentUser}
          />
          <div className={s.search__footer}>
            <div>{I18N.auto.switch}</div>
            <div>{I18N.auto.enter}</div>
          </div>
        </>
      )}
    </div>
  );
};

export default SearchList;
