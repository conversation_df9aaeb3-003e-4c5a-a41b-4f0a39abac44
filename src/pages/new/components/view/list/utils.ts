import { Message } from '@/components/basic'
import { store } from '@/models/store'
import { isProjectId } from '@/models/utils'
import { TaskInfo } from '@/types'
import { OneAndMoreServerParamsType, SUBTASK_MAX_COUNT, TaskTableRowType } from '@/utils/const'
import I18N from '@/utils/I18N'
export const removeByKey = (data: any[], keyToRemove: string, index = 0): any => {
  for (let i = 0; i < data.length; i++) {
    index++
    if (data[i].key === keyToRemove) {
      const ret = data[i]
      data.splice(i, 1) // 找到指定的 key 并将其从数组中删除
      return {
        data: ret,
        index: index,
      }
    } else if (data[i].children) {
      const item = removeByKey(data[i].children, keyToRemove, index)
      if (item) {
        return item
      }
    }
  }
  return undefined // 返回 false 表示未找到指定的 key
}
export const addByKey = (data: any[], keyToRemove: string, item: any, index = 0): any => {
  for (let i = 0; i < data.length; i++) {
    index++
    if (data[i].key === keyToRemove) {
      const { data: dataItem, index: _index } = item
      //const _i = _index > index ? i : i + 1; // 判断了相对位置, 这个应该用dndkit内置sensors判断相对方向会比较好
      if (_index > index || data[i].type === 2) {
        // 碰到新增类型也不往后添加
        data.splice(i, 0, dataItem) //
      } else {
        data.splice(i + 1, 0, dataItem) //
      }

      return true
    } else if (data[i].children) {
      const ret = addByKey(data[i].children, keyToRemove, item, index)
      if (ret) {
        return true
      }
    }
  }
  return false // 返回 false 表示未找到指定的 key
}

export const getAllKeys = (data: any[], attr = 'id') => {
  let keys: string[] | number[] = []
  data.forEach(item => {
    if (item[attr]) {
      keys.push(item[attr])
    }
    if (item.hasOwnProperty('children') && Array.isArray(item.children)) {
      keys = keys.concat(getAllKeys(item.children))
    }
  })
  return keys
}

export const tableCodeMap = {
  taskName: {
    name: I18N.auto.taskName,
    dataIndex: 'taskName',
  },
  assigner: {
    name: I18N.auto.assignPerson,
    dataIndex: 'assigner',
  },
  assignee: {
    name: I18N.auto.personLiable,
    dataIndex: 'assignee',
  },
  createTime: {
    name: I18N.auto.creationTime,
    dataIndex: 'createTime',
  },
  startTime: {
    name: I18N.auto.startTime,
    dataIndex: 'startTime',
  },
  deadline: {
    name: I18N.auto.deadline,
    dataIndex: 'deadline',
  },
  alarmTime: {
    name: I18N.auto.reminderTime,
    dataIndex: 'alarmTime',
  },
  project: {
    name: I18N.auto.belongingProject,
    dataIndex: 'project',
  },
  priority: {
    name: I18N.auto.priority,
    dataIndex: 'priority',
  },
}

/**
 * 校验是否是一个有效的待办数据, 排除新建按钮行、新建中的编辑态行、群组的行
 * @param taskInfo
 * @returns
 */
export const validateTask = (taskInfo: TaskInfo) => {
  return (
    taskInfo?._rowType !== TaskTableRowType.add &&
    taskInfo?._rowType !== TaskTableRowType.addBtn &&
    taskInfo?._rowType !== TaskTableRowType.group
  )
}

export const checkSubtaskOverLimit = (subtasks?: TaskInfo[], subCount?: number) => {
  // 传入数量表示【当前子任务数量 + 要创建的任务数量】，判断 > 即可
  const overLimit = subCount ? subCount > SUBTASK_MAX_COUNT : (subtasks?.length || 0) >= SUBTASK_MAX_COUNT

  if (overLimit) {
    Message.warn(
      {
        content: I18N.template(I18N.auto.subtaskLimit, { val1: SUBTASK_MAX_COUNT }),
      },
      {
        zIndex: Number.MAX_SAFE_INTEGER,
        getContainer: () => document.body,
      },
    )
  }
  return overLimit
}

export const getFinishStatus = ({ completeCondition, selfFinished, finished }: Partial<TaskInfo>) => {
  const isProject = isProjectId(store.getState().viewSetting.navigatorId)
  if (isProject) {
    if (completeCondition === OneAndMoreServerParamsType.all) {
      return finished
    }
    return selfFinished || finished
  }
  return selfFinished || finished
}

export const stopClickWhenDropdownPending = (e: React.MouseEvent<HTMLElement>, container = '#tanstack-table') => {
  const dropdownEls = document.querySelectorAll(`${container} .rock-dropdown`)
  const target = e.target as HTMLElement

  if (Array.from(dropdownEls).some(el => el.contains(target))) {
    return
  }

  const confirm__btn = document.querySelector('.confirm__btn:not([disabled])')
  if (confirm__btn) {
    e.stopPropagation()
  }
}
