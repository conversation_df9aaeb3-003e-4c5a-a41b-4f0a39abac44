import { RootState } from '@/models/store'
import { TaskInfo } from '@/types'
import { useMemo } from 'react'
import { useSelector } from 'react-redux'
import { getGroupedList } from './groupedList'
import { GroupOptions } from './types'
import { getAddLine } from './utils'

// Hook
const useGetGroupedList = (list: TaskInfo[], options: GroupOptions = {} as GroupOptions) => {
  const { queryGroupBys } = useSelector((state: RootState) => ({
    queryGroupBys: state.viewSetting.currentViewTab?.queryGroupBys,
  }))

  const groupedList = useMemo(() => {
    if (!queryGroupBys?.length || !list.length) {
      return [...list, getAddLine({ index: list?.length || 0, id: 1 })]
    }
    return getGroupedList(list, queryGroupBys, options)
  }, [list, queryGroupBys])

  return groupedList
}

export default useGetGroupedList

export { getGroupedList }
// 重新导出需要的工具函数和类型
export { getAddLine, normalizeTimestamp } from './utils'
export type { GroupConfig, GroupConfigMap, GroupedItem, GroupByField, GroupStats, GroupOptions } from './types'
