{"private": true, "author": "zhouqing01 <<EMAIL>>", "scripts": {"dev": "cross-env umi dev", "build": "cross-env umi build", "build:test": "cross-env UMI_ENV=test umi build", "build:staging": "cross-env UMI_ENV=staging umi build", "build:production": "cross-env UMI_ENV=production umi build", "build:production-canary": "cross-env UMI_ENV=production umi build", "publishCdn": "node ./publish/publish-cdn.js", "genNginxConf": "node ./publish/build-nginx.js", "postinstall": "umi setup", "setup": "umi setup", "start": "cross-env npm run dev", "prepare": "husky install", "analyz": "cross-env ANALYZE=1 UMI_ENV=production umi build", "ytt": "ytt -c ytt.config.ts", "lint": "eslint src --fix --ext .ts,.tsx,.js,.jsx", "lint:css": "stylelint **/*.{css,less,sass,scss}", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\" \"!src/api\" \"!src/assets\" ", "genI18n": "ppi18n extract -d src -p auto", "icons": "pnpm i @babylon/popo-icons@latest"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix"], "*.{js,jsx,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@babylon/popo-icons": "^2.0.78", "@bedrock/components": "2.2.104", "@bedrock/editor": "^1.0.125", "@bedrock/guide": "2.0.17", "@bedrock/icons-react": "2.2.27", "@bedrock/icons-svg": "2.2.7", "@bedrock/utils": "^0.0.20", "@dnd-kit/core": "6.0.8", "@dnd-kit/modifiers": "7.0.0", "@dnd-kit/sortable": "7.0.2", "@dnd-kit/utilities": "3.2.1", "@fullcalendar/core": "6.1.9", "@fullcalendar/daygrid": "6.1.9", "@fullcalendar/interaction": "6.1.9", "@fullcalendar/react": "6.1.9", "@linaria/core": "^6.3.0", "@popo-bridge/web": "0.2.51", "@popo/fontsize2rem": "^0.0.6", "@popo/quill": "2.0.3-beta.5", "@popo/skyline-browser": "1.0.1", "@popoDrive/gdlc": "1.2.7", "@pp-i18n/cli": "^1.0.23", "@pp-i18n/intl": "1.0.4", "@rematch/core": "2.2.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.6", "@types/js-cookie": "3.0.6", "@types/quill": "2.0.14", "@types/react-transition-group": "^4.4.6", "@visactor/vrender-kits": "0.21.9-alpha.1", "@visactor/vtable": "1.17.0", "@visactor/vtable-editors": "^1.17.0", "@visactor/vtable-gantt": "1.17.0", "ahooks": "3.7.8", "axios": "1.3.3", "classnames": "2.3.2", "compare-versions": "6.1.0", "copy-to-clipboard": "3.3.3", "dayjs": "1.11.9", "fs-extra": "^11.2.0", "html-minifier-terser": "7.2.0", "js-cookie": "3.0.5", "lodash": "4.17.21", "minimist": "1.2.8", "overlayscrollbars": "1.13.3", "overlayscrollbars-react": "0.3.0", "prosemirror-keymap": "^1.2.3", "prosemirror-model": "^1.18.3", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.3", "quill": "1.3.7", "quill-delta": "5.1.0", "quill-mention": "4.1.0", "re-resizable": "^6.11.2", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-dom": "18.2.0", "react-error-boundary": "4.0.11", "react-quill": "2.0.0", "react-redux": "8.0.5", "react-tiny-virtual-list": "^2.2.0", "react-transition-group": "^4.4.5", "react-virtuoso": "4.4.1", "redux": "4.2.1", "rrule": "2.7.2", "umi": "^4.0.63", "webpack": "5.86.0", "yapi-to-typescript": "3.37.0"}, "pnpm": {"overrides": {"@bedrock/tooltip": "0.2.6", "click-to-react-component": "1.0.8"}, "patchedDependencies": {"@visactor/vtable-gantt@1.17.0": "patches/@<EMAIL>", "@visactor/vrender-kits@0.21.9-alpha.1": "patches/@<EMAIL>", "@visactor/vtable@1.17.0": "patches/@<EMAIL>"}}, "devDependencies": {"@bedrock/async-script-plugin": "0.0.11", "@scout/webpack-plugin": "0.0.24", "@types/lodash": "4.14.191", "@types/react": "^18.0.30", "@types/react-beautiful-dnd": "13.1.3", "@types/react-dom": "^18.0.11", "@types/react-redux": "7.1.25", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "cross-env": "7.0.3", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "8.0.0", "husky": "^8.0.3", "lint": "0.7.0", "lint-staged": "^13.2.0", "postcss-plugin-px2rem": "0.8.1", "prettier": "^2.8.7", "typescript": "^4.9.5", "workbox-webpack-plugin": "7.0.0"}, "peerDependencies": {"@visactor/vrender-kits": "0.21.9-alpha.1"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "resolutions": {"prosemirror-commands": "1.3.1", "prosemirror-history": "1.3.0", "prosemirror-inputrules": "1.2.0", "prosemirror-keymap": "1.2.0", "prosemirror-model": "1.18.3", "prosemirror-schema-list": "1.2.2", "prosemirror-state": "1.4.2", "prosemirror-tables": "1.3.4", "prosemirror-transform": "1.6.0", "prosemirror-view": "1.29.1"}}