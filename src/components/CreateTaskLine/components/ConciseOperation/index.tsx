import { Space } from '@bedrock/components';
import { SubTaskDeadline, SubTaskExecutor } from '..';
import { FC, useCallback } from 'react';
import { TaskTime, TodoInfo } from '@/types';

interface IConciseOperationProps {
  taskInfo: TodoInfo;
  onChange: (newTaskInfo: TodoInfo, type?: 'local') => void;
  onSuccess?: () => void;
}

const ConciseOperation: FC<IConciseOperationProps> = ({ taskInfo, onChange, onSuccess }) => {
  const handleTimeChange = useCallback(
    (time: TaskTime) => {
      onChange(time);
    },
    [taskInfo]
  );

  // 负责人变更处理
  const handleExecutorChange = useCallback(
    (assignees: TodoInfo['assignees']) => {
      let updateData: any = {};
      if (Array.isArray(assignees)) {
        updateData.assignees = assignees;
      } else {
        updateData = assignees;
      }
      onChange(updateData, 'local');
    },
    [taskInfo]
  );

  // 完成条件(个人/所有人完成)变更处理
  const handleExecuteTypeChange = useCallback(
    (completeCondition) => {
      onChange({ completeCondition }, 'local');
    },
    [taskInfo]
  );

  return (
    <Space className="ml-8" size={4}>
      <SubTaskDeadline taskInfo={taskInfo} onSuccess={onSuccess} onChange={handleTimeChange} />
      <SubTaskExecutor
        taskInfo={taskInfo}
        onChange={handleExecutorChange}
        handleExecuteTypeChange={handleExecuteTypeChange}
        onSuccess={onSuccess}
      />
    </Space>
  );
};

export default ConciseOperation;
