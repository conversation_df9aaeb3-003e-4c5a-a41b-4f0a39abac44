import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { AvatarPeople, Icon, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  avatarUrl?: string;
  finished?: boolean;
  name?: string;
  onClick?: () => void;
  onRemove?: () => void;
  selected?: boolean;
  canSelect?: boolean;
  showDelete?: boolean;
  tagName?: string;
  tooltipTitle?: string;
  disabled?: boolean;
};
const PeopleItem: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    name,
    avatarUrl,
    onClick,
    finished,
    className,
    onRemove,
    canSelect = true,
    showDelete = false,
    selected,
    tagName,
    tooltipTitle = '',
    disabled,
  } = props;
  return (
    <Tooltip title={tooltipTitle}>
      <div
        className={classNames(
          s.peopleItem,
          { [s.hasTag]: tooltipTitle, [s.disabled]: disabled },
          className
        )}
        onClick={(e) => {
          e.stopPropagation();
          if (canSelect && !disabled) {
            onClick?.();
          }
        }}
      >
        <div className={s.avatarBox}>
          <AvatarPeople className={classNames(s.avatar)} avatarUrl={avatarUrl}></AvatarPeople>
          {finished ? <i className={classNames(s.finishedIcon)}></i> : null}
        </div>
        <div className={s.name}>
          <div className={'ellipsis'}>{name}</div>
        </div>
        {tagName ? <div className={`${s.tag} tag`}>{tagName}</div> : null}
        {canSelect && selected ? (
          <Icon
            name="icon-sys_check"
            className={s.check}
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
          ></Icon>
        ) : null}
        {showDelete ? (
          <Tooltip title={I18N.auto.remove}>
            <Icon
              name="icon-close"
              className={s.delete}
              onClick={(e) => {
                e.stopPropagation();
                onRemove?.();
              }}
            ></Icon>
          </Tooltip>
        ) : null}
      </div>
    </Tooltip>
  );
};

export default PeopleItem;
