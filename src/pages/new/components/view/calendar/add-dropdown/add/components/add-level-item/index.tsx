import React, { useState } from 'react';

import { LevelSelect } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  className?: string;
  value?: number;
  onChange: (v: number) => void;
  onOpen?: (v: boolean, itemName: string) => void;
};

const AddRow: React.FC<Props> = (props) => {
  const { value, onChange, onOpen } = props;

  const [visible, setVisible] = useState<boolean>(false);
  return (
    <LevelSelect
      placeholder={I18N.auto.addTo}
      visible={visible}
      onVisible={(v) => {
        setVisible(v);
        onOpen?.(v, 'level');
      }}
      value={value}
      onChange={onChange}
      showLabelDeleteIcon={false}
      className={s.addLevelSelect}
    ></LevelSelect>
  );
};

export default AddRow;
