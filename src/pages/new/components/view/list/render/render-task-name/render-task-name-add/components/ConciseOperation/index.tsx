import React from 'react';
import { NewTaskDeadline, NewTaskExecutor } from '..';
import { TodoInfo } from '@/types';

interface IConciseOperationProps {
  taskInfo: TodoInfo;
  onChange?: (newTaskInfo: TodoInfo) => void;
}

const ConciseOperation: React.FC<IConciseOperationProps> = ({ taskInfo, onChange }) => {
  const handleChange = (newTaskInfo: TodoInfo) => {
    onChange?.(newTaskInfo);
  };
  return (
    <div className="flex-y-center" style={{ gap: '4px' }}>
      <NewTaskDeadline taskInfo={taskInfo} onChange={handleChange} />
      <NewTaskExecutor taskInfo={taskInfo} onChange={handleChange} />
    </div>
  );
};

export default ConciseOperation;
