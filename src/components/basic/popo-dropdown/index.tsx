import { Placement } from '@bedrock/components/lib/Dropdown/interface';
import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode, useMemo, useState } from 'react';

import { Dropdown } from '@/components/basic';
import { isControlled } from '@/utils';

import s from './index.less';
export type Props = {
  className?: string;
  trigger?: 'click' | 'hover';
  title: ReactNode;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  placement?: Placement;
  clickItemCloseDropdown?: boolean;
};

const FieldSetting: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    title,
    children,
    trigger = 'click',
    className,
    onVisible,
    clickItemCloseDropdown = false,
    visible: propVisible,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const memoVisible = useMemo(() => {
    return isControlled(propVisible) ? propVisible : visible;
  }, [propVisible, visible]);

  const changeVisible = (v: boolean) => {
    if (isControlled(propVisible)) {
      onVisible?.(v);
    } else {
      setVisible(v);
    }
  };
  return (
    <div className={s.popoDropdown}>
      <Dropdown
        trigger={trigger}
        arrow={false}
        title={title}
        open={memoVisible}
        onOpenChange={(open) => {
          changeVisible(open);
        }}
        overlay={
          <div
            className={classNames(s.box, className)}
            onClick={() => {
              if (clickItemCloseDropdown) {
                changeVisible(false);
              }
            }}
          >
            {children}
          </div>
        }
        placement={props.placement ?? 'bottomRight'}
      ></Dropdown>
    </div>
  );
};

export default FieldSetting;
