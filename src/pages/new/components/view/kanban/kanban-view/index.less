.kanban {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px 12px 0 12px;
  border-top: 1px solid var(--aBlack6);
  :global {
    .os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
      background-color: var(--aBlack12);
    }
  }
  > :global(.rock-scrollbar > .rock-scrollbar-track-horizontal) {
    padding: 2px;
  }
}
.lane {
  display: flex;
  column-gap: 4px;
  overflow-x: auto;
  //看板泳道内部overlayscrollbars-react滚动条组件回动态计算实际区域, 用height: 100%;导致高度不生效,缩放大小后页面在不停在计算实际高度
  height: calc(100vh - 154px);
  :global {
    .os-content > div > div > div {
      padding: 0 8px !important;
    }
  }
}

.laneBody {
  height: 100%;
  //margin-top: 8px;
}

.noDataLaneBody {
  background: linear-gradient(180deg, var(--TasksTagbg) 0%, transparent 100%);
  border-radius: 8px;
  padding: 0 8px;
  margin: 0 8px;
}

.add {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 60px;
  padding: 10px;
}

.dragCard {
  background-color: var(--bgBottom) !important;
  border-color: var(--Brand600) !important;
  box-shadow: 0px 1px 6px 0px var(--absaBlack12) !important;
}

.statusModalRadioGroup {
  display: flex;
  flex-direction: column;
}

:global {
  .statusModal {
    .rock-modal-content {
      .rock-modal-confirm-header {
        display: flex !important;
      }
      .rock-radio-group {
        margin-top: 20px;
        .rock-radio-wrapper {
          font-size: 13px;
          color: var(--TextPrimary);
          &:first-child {
            margin-bottom: 10px;
          }
        }
      }
    }
    .rock-modal-body {
      padding: 16px 20px 20px 20px !important;
    }
  }
}
