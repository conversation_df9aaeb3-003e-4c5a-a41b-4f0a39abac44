import classNames from 'classnames';
import { PropsWithChildren, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { Button, Dropdown, Icon } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { FieldTypeEnum } from '@/types/custom-field';
import { Order, OrderCustom, ViewType } from '@/utils/const';
import { CustomSort, SortListData } from '@/utils/fields';
import I18N from '@/utils/I18N';

import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
}

const Sort: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled, className = '', ...rest } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { order, orderBy, customFields, navigatorId, currentViewTab } = useSelector(
    (state: RootState) => {
      let querySort = state.viewSetting.currentViewTab.querySort;
      return {
        order: querySort?.order,
        orderBy: querySort?.customFieldId ? querySort.customFieldId : querySort?.fieldName,
        customFields: state.viewSetting.customFields,
        navigatorId: state.viewSetting.navigatorId,
        currentViewTab: state.viewSetting.currentViewTab,
      };
    }
  );
  const dispatch = useDispatch<Dispatch>();

  const sortList = useMemo(() => {
    const _SortListData = [...SortListData];
    // if (isTaskMenuId(navigatorId)) {
    //   _SortListData.unshift(CustomSort);
    // }
    return _SortListData.concat(
      customFields
        .filter((f) =>
          [FieldTypeEnum.text, FieldTypeEnum.datetime, FieldTypeEnum.number].includes(f.type)
        )
        .map((f) => {
          return {
            name: f.name,
            value: f.fieldId,
            customFieldId: f.fieldId,
            icon: '',
          };
        })
    );
  }, [customFields, navigatorId, currentViewTab.type]);

  const activeItem = useMemo(() => {
    return sortList.find((item) => item.value === orderBy);
  }, [sortList, orderBy]);

  const orderName = useMemo(() => {
    if (order === Order.desc) {
      return I18N.auto.reverseOrder;
    } else if (order === Order.asc) {
      return I18N.auto.positiveSequence;
    } else {
      return '';
    }
  }, [order]);
  return (
    <Dropdown
      className={`${s.dropdown} ${className}`}
      title={
        <Button
          className={classNames('mr-8', s.order)}
          type="text-subtle"
          icon={<Icon name="icon-soft_px" fontSize={16}></Icon>}
        >
          <div className={s.sortDsc}>
            {I18N.auto.sort_2} {activeItem?.name}
            {orderBy === OrderCustom ? '' : orderName}
          </div>
        </Button>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      disabled={disabled}
      open={visible}
      onOpenChange={(v) => {
        setVisible(v);
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Overlay
          list={sortList}
          onChange={(v) => {
            const { order: _order, orderBy: _orderBy } = v;
            if (_order && _orderBy && !(_orderBy === orderBy && _order === order)) {
              dispatch.viewSetting.sort({ order: _order, orderBy: _orderBy });
            }
            setVisible(false);
          }}
          order={order}
          orderBy={orderBy}
        ></Overlay>
      }
      {...rest}
    />
  );
};

export default Sort;
