import dayjs, { Dayjs } from 'dayjs';

import I18N, { getCurrentLang } from '@/utils/I18N';
interface FormatOptProps {
  hasHHmm?: boolean;
  todayStr: string;
  tomorrowStr: string;
  yearStr: string;
  time: Dayjs;
}

export const getCalendarFormat = (opt: FormatOptProps) => {
  const language = getCurrentLang();
  const { hasHHmm, todayStr, tomorrowStr, time, yearStr } = opt;
  const isThisYear = dayjs().isSame(time, 'year');
  let formatStr = `${isThisYear ? '' : `YYYY${yearStr ? yearStr : ''}`}MMMDo ${
    hasHHmm ? ' HH:mm' : ''
  }`;
  // let formatStr = `${isThisYear ? '' : `YYYY${yearStr ? yearStr : ''}`}MMMDo dddd ${
  //   hasHHmm ? ' HH:mm' : ''
  // }`;
  if (language === 'en-US') {
    formatStr = `MMM Do ${isThisYear ? '' : 'YYYY'} ${hasHHmm ? ' HH:mm' : ''}`;
  }

  return {
    lastDay: formatStr,
    sameDay: `[${todayStr}] ${hasHHmm ? ' HH:mm' : ''}`,
    nextDay: `[${tomorrowStr}] ${hasHHmm ? ' HH:mm' : ''}`,
    lastWeek: formatStr,
    nextWeek: formatStr,
    sameElse: formatStr,
  };
};

export const getCalendarFormatDay = (opt: FormatOptProps) => {
  const { todayStr, tomorrowStr, time, yearStr } = opt;
  const language = getCurrentLang();
  const isThisYear = dayjs().isSame(time, 'year');
  let formatStr = `${isThisYear ? '' : `YYYY${yearStr ? yearStr : ''}`}MMMDo dddd`;
  let sameDay = `MMMDo [${todayStr}] dddd `;
  let nextDay = `MMMDo [${tomorrowStr}] dddd `;
  if (language === 'en-US') {
    formatStr = `dddd MMM Do ${isThisYear ? '' : 'YYYY'}`;
    sameDay = `[${todayStr}] dddd MMM Do`;
    nextDay = `[${tomorrowStr}] dddd MMM Do`;
  }
  return {
    lastDay: formatStr,
    sameDay: sameDay,
    nextDay: nextDay,
    lastWeek: formatStr,
    nextWeek: formatStr,
    sameElse: formatStr,
  };
};

export const getComCalendarFormat = () => {
  const language = getCurrentLang();
  let formatStr = `${`YYYY${I18N.auto.year}`}MMMDo HH:mm`;
  if (language === 'en-US') {
    formatStr = 'MMM Do YYYY HH:mm';
  }
  return {
    lastDay: formatStr,
    sameDay: formatStr,
    nextDay: formatStr,
    lastWeek: formatStr,
    nextWeek: formatStr,
    sameElse: formatStr,
  };
};

type ComFormatOpt = {
  time: Dayjs;
  diffYear?: boolean;
  hasHHmm?: boolean;
};

export const getComFormat = (opt: ComFormatOpt) => {
  const { time, diffYear, hasHHmm = true } = opt;
  const language = getCurrentLang();
  const isThisYear = dayjs().isSame(time, 'year');
  const HM = hasHHmm ? ' HH:mm' : '';
  let formatStr = `MMMDo${HM}`;
  if (isThisYear && diffYear) {
    if (language === 'en-US') {
      formatStr = `MMM Do${HM}`;
    }
  } else {
    formatStr = `${`YYYY${I18N.auto.year}`}MMMDo${HM}`;
    if (language === 'en-US') {
      formatStr = `MMM Do YYYY${HM}`;
    }
  }

  return formatStr;
};
