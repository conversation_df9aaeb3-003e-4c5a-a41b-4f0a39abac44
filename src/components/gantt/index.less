.ganttView {
  position: relative;
  height: 100%;
  user-select: none;
  -webkit-user-select: none;
  padding-left: 20px;
  border-top: 1px solid var(--aBlack8);

  &::before {
    content: ' ';
    display: block;
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    background-color: transparent;
    width: 2px;
    z-index: 1;
  }

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: var(--bgTop);
    width: 2px;
    z-index: 1;
  }

  .leftToolbar {
    position: absolute;
    top: 1px;
    display: inline-flex;
    align-items: center;
    height: 33px;
    z-index: 1;
    padding: 4px 0;
    z-index: 1;
  }

  .toggle__container {
    position: absolute;
    top: 4px;
    height: 24px;
    border-radius: 4px;
    background-color: var(--bgBottom);

    .toggle {
      display: inline-flex;
      cursor: pointer;
      padding: 4px;
      background-color: var(--bgBottom);
      transition: background-color 0.2s;
      border-radius: 4px;

      &:hover {
        background-color: var(--aBlack6);
      }

      .toggleIcon {
        display: block;
        width: 16px;
        height: 16px;
        transform: rotate(180deg);
      }
    }
  }

  .resize__line {
    & > div:first-child {
      width: 1px !important;
      background-color: var(--aBlack12) !important;
    }
  }

  .list__hide {
    pointer-events: none;

    & > div:not(.toggle__container) {
      display: none;
    }
  }

  .view {
    height: 100%;
    position: relative;
    &:global(#gantt-view) {
      & > div:nth-child(3) {
        cursor: e-resize !important;
      }
    }
  }

  :global {
    .vtable__menu-element {
      padding: 4px;
      border-radius: 6px;
      // min-width: 82px;

      .rock-menu {
        margin: 0;
        padding: 0;
        color: rgba(17, 17, 17, 0.88);

        &-item {
          padding: 0.08rem;
          margin: 0 !important;
          height: 36px;

          &:hover {
            background-color: rgba(0, 0, 0, 0.06);
          }
        }
      }
    }
  }
}
