import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';

import { apiTodoParticipantCandidateSearchGet } from '@/api';
import { CommonSearch, Divider, IconBtn, Tooltip } from '@/components/basic';
import SearchList from '@/components/basic/people-picker/components/search-list';
import SelectedList from '@/components/basic/people-picker/components/selected-list';
import { UserInfo } from '@/types';
import { PEOPLE_MAX_LENGTH } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  value?: UserInfo[];
  onChange?: (users: UserInfo[]) => void;
  hasMinimum?: boolean;
  selectedPanelUsers: UserInfo[];
  canSelect?: boolean; //全部 通过select 选择和取消
  canDelete?: boolean; //单独的删除
  canDeleteSelf?: boolean; //能否单独删除自己
};

enum Panel {
  search,
  select,
}

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    search = true,
    value,
    onChange,
    canSelect,
    canDelete,
    canDeleteSelf,
    selectedPanelUsers,
  } = props;

  //面板类型
  const [panelType, setPanelType] = useState<Panel>();
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchList, setSearchList] = useState<UserInfo[]>([]);
  // 最近联系人
  const [loading, setLoading] = useState<boolean>(true);
  const commonSearchRef = useRef<any>();
  // 禁止搜索 人数超限
  const disabledSearch = useMemo(() => {
    return (value || [])?.length >= PEOPLE_MAX_LENGTH;
  }, [value]);
  /**
   * 搜索接口
   * @param v
   */
  const searchPeople = (v: string) => {
    setLoading(true);
    apiTodoParticipantCandidateSearchGet({ keyword: v })
      .then((res) => {
        // 遍历数据 设置被选中的数据selected为1, 并且排序按照selected排序
        const ret = res
          .map((item) => ({
            ...item,
            selected: (value || []).findIndex((v) => v.uid === item.uid) > -1 ? 1 : 0,
          }))
          .sort((a, b) => a.selected - b.selected);
        setSearchList(ret);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleChangeUserList = (userList: UserInfo[]) => {
    onChange?.([...userList]);
    const copySelectedList = [...selectedPanelUsers];
    userList.forEach((item) => {
      const index = selectedPanelUsers.findIndex((v) => v.uid === item.uid);
      if (index === -1) {
        copySelectedList.push(item);
      }
    });
    setSearchValue('');
  };
  const change = (userList: UserInfo[], isSelfDelete?: boolean) => {
    if (!canSelect && !isSelfDelete) {
      return;
    }
    handleChangeUserList(userList);
  };
  const onSearchChange = (userList: UserInfo[]) => {
    change(userList);
    setPanelType(Panel.select);
  };
  // 调用im组件批量选择
  const openIMContacts = () => {
    const checkableDefaultItems = value?.map((item) => ({
      id: item.uid!,
      type: 1,
    }));
    pp.chooseIMContacts({
      groupCollapse: true,
      uncheckableDefaultItems: checkableDefaultItems,
      title: I18N.auto.addPersonnel,
      disabledItems: [],

      maxCount: 120,
    })
      .then((res) => {
        if (Array.isArray(res.data)) {
          const obj: Record<string, UserInfo> = {};
          const _list = res.data.map((item) => ({ ...item, uid: item.id }));
          [...(value || [])].concat(_list).forEach((item) => {
            if (!obj[item.uid!]) {
              obj[item.uid!] = item;
            }
          });
          if (Object.values(obj).length) {
            onChange?.(Object.values(obj));
          }
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        });
      });
  };
  // 设置当前的面板类型
  const setPanelTypebyData = () => {
    setPanelType(Panel.select);
  };

  useEffect(() => {
    setPanelType(Panel.select);
  }, []);
  return (
    <div
      className={classNames('com-dropdown-select', s.panel, s.searchingPanel)}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {search ? (
        <>
          {disabledSearch ? (
            <Tooltip title={I18N.template(I18N.auto.addUpToPeople, { val1: PEOPLE_MAX_LENGTH })}>
              <div className={classNames(s.input, s.disabledSearch)}>{I18N.auto.addTo}</div>
            </Tooltip>
          ) : (
            <>
              <div className={s.panelSearchPd}>
                <CommonSearch
                  autoFocus
                  ref={commonSearchRef}
                  className={classNames(s.input)}
                  placeholder={I18N.auto.addTo}
                  onSearch={(v) => {
                    if (!v) {
                      setPanelTypebyData();
                    } else {
                      setLoading(true);
                      setPanelType(Panel.search);
                      searchPeople(v);
                    }
                  }}
                  value={searchValue}
                  onChange={(v) => {
                    setSearchValue(v);
                  }}
                  fill={false}
                  border={false}
                  round={false}
                  debounceTime={350}
                  disabled={disabledSearch}
                ></CommonSearch>
                <Divider type="vertical" className={s.verticalLine}></Divider>
                <IconBtn
                  className={s.iconAdd}
                  iconName="icon-kit_user_add"
                  onClick={openIMContacts}
                  title={I18N.auto.batchAdd}
                ></IconBtn>
              </div>
              {!canSelect && value?.length ? null : (
                <Divider type="horizontal" className={s.divider}></Divider>
              )}
            </>
          )}
        </>
      ) : null}
      <div className={s.container}>
        {panelType === Panel.search ? (
          <SearchList
            value={value}
            loading={loading}
            list={searchList}
            onChange={onSearchChange}
            className={s.list}
            multiple={true}
          ></SearchList>
        ) : null}
        {panelType === Panel.select ? (
          <SelectedList
            value={value}
            onChange={change}
            list={selectedPanelUsers || []}
            className={s.list}
            multiple={true}
            canSelect={canSelect}
            canDelete={canDelete}
            canDeleteSelf={canDeleteSelf}
          ></SelectedList>
        ) : null}
      </div>
    </div>
  );
};

PeoplePicker.defaultProps = {
  value: [],
};
export default PeoplePicker;
