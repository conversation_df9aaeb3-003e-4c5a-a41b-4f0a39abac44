import classNames from 'classnames';
import { PropsWithChildren } from 'react';

import { Input } from '@/components/basic';
import IconSelect from '@/components/basic/icon-select';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  disabled?: boolean;
  className?: string;
  iconName?: string;
  onChangeIcon?: (v: string) => void;
  iconBgColor?: string;
  onChangeColor?: (v: string) => void;
  projectName?: string;
  onChangeProjectName?: (v: string) => void;
  autoFocus?: boolean;
}

const ProjectName: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    iconName,
    iconBgColor,
    onChangeIcon,
    onChangeColor,
    projectName,
    onChangeProjectName,
    autoFocus,
    className,
  } = props;

  return (
    <div className={classNames(s.nameAndIcon, className)}>
      <div className={s.label}>
        {I18N.auto.projectIconAnd}
        <span>*</span>
      </div>
      <div className={s.value}>
        <IconSelect
          className={s.projectIcon}
          iconName={iconName}
          iconBgColor={iconBgColor}
          onChangeIcon={onChangeIcon}
          onChangeColor={onChangeColor}
          fontSize={36}
        ></IconSelect>
        <Input
          value={projectName}
          onChange={(e) => {
            onChangeProjectName?.(e.target.value);
          }}
          className={s.projectName}
          size="large"
          placeholder={I18N.auto.pleaseEnterTheProject}
          maxLength={100}
          autoFocus={autoFocus}
        ></Input>
      </div>
    </div>
  );
};

export default ProjectName;
