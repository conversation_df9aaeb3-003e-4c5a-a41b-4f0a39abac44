import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { taskUpdateAlarm, taskUpdateDeadline } from '@/api-common';
import { POPOConvergenceDatePicker } from '@/components/basic';
import { PopoConvergenceDatePickerContext } from '@/components/basic/popo-convergence-date-picker/context';
import { TimeInputType } from '@/components/basic/popo-convergence-date-picker/convergence-date-picker';
import { getTimePickerFormat, PPTimeFormat } from '@/components/basic/popo-date-picker/utils';
import { Dispatch } from '@/models/store';
import { TaskTime, TodoInfo } from '@/types';
import { EnumTimePickerType, TaskTableRowType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import AddRowBox from '../row-box';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Params {
  deadline: number;
  deadlineFormat: PPTimeFormat | undefined;
  rrule: string;
}

interface Props {
  onChange?: (v: Params) => void;
  taskInfo: TodoInfo;
}

const RenderStartTime: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const {
    taskId,
    _rowType: rowType,
    memoAlarm: alarm,
    deadline,
    rrule,
    deadlineFormat: timeFormat,
    startTime,
    selfFinished,
    isExpired,
  } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();
  const memoDeadlineTime = useMemo(() => {
    return {
      startTime: startTime,
      deadline: deadline,
      rrule: rrule,
      timeFormat: timeFormat,
      alarm: alarm,
    };
  }, [startTime, deadline, timeFormat, rrule, alarm]);

  const permissions = useGetPermissions({ taskId });

  const change = async (v: TaskTime) => {
    const copyValue = memoDeadlineTime;
    const { startTime, deadline, timeFormat, rrule, alarm } = v;
    if (
      startTime === copyValue.startTime &&
      deadline === copyValue.deadline &&
      rrule === copyValue.rrule &&
      timeFormat === copyValue.timeFormat &&
      alarm === copyValue.alarm
    ) {
      return;
    }
    const params = {
      taskId: taskId,
      startTime: startTime || 0,
      deadline: deadline || 0,
      deadlineFormat: timeFormat,
      rrule: rrule,
    };
    if (rowType === TaskTableRowType.add) {
      dispatch.task.updateTodoList(Object.assign(taskInfo, params));
      return null;
    }
    //TODO 服务端不改接口合并 ,还有并发场景 只能先后改
    await taskUpdateDeadline(params);
    await taskUpdateAlarm({
      taskId: taskId,
      alarm: {
        alarmTimestamp: alarm?.time,
        alarmRrule: alarm?.rrule,
        selectedOption: alarm?.selectedOption,
      },
    });
    dispatch.detail.getTodoDetail({ taskId, onlyDetail: true }).then((res) => {
      dispatch.viewSetting.refreshDataByDataChange({
        refreshList: true,
        refreshCount: false,
        detail: res,
      });
    });
  };

  const changeVisible = (v: boolean) => {
    setVisible(v);
  };
  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    });
    return !CAN_EDIT && rowType !== TaskTableRowType.add;
  }, [permissions, rowType]);

  return (
    <AddRowBox
      iconName="icon-Soft_qk_td"
      tipTitle={memoDeadlineTime.startTime ? '' : I18N.auto.atTheBeginningOfAdding}
      taskId={taskInfo.taskId}
      disabled={disabled}
      hasDelete={!!startTime}
      focused={visible}
      onClick={(e) => {
        changeVisible(!visible);
      }}
      onClear={() => {
        change({
          startTime: 0, //只清除开始时间
          deadline: deadline,
          rrule: rrule,
          timeFormat: timeFormat,
          alarm: alarm,
        });
      }}
    >
      <PopoConvergenceDatePickerContext.Provider
        value={{
          timePickerType: EnumTimePickerType.start,
        }}
      >
        <POPOConvergenceDatePicker
          disabled={disabled}
          timeInputType={TimeInputType.start}
          hasQuick={false}
          value={memoDeadlineTime}
          placeholder=" "
          onChange={change}
          visible={visible}
          onVisible={changeVisible}
          labelClassName={classNames({ [s.expired]: isExpired && selfFinished !== 1 })}
          hasArrow={false}
          showLabelRRuleIcon={false}
          renderLabel={(time: Dayjs, value?: TaskTime) => {
            const { startTime } = value!;
            if (!startTime) {
              return null;
            }
            let hasHHmm = false;
            if (timeFormat === PPTimeFormat.dateAndTime) {
              hasHHmm = true;
            }
            const timeStr = getTimePickerFormat({
              time: dayjs(startTime),
              hasHHmm,
            });
            const node = [timeStr];

            return node;
          }}
        ></POPOConvergenceDatePicker>
      </PopoConvergenceDatePickerContext.Provider>
    </AddRowBox>
  );
};

export default RenderStartTime;
