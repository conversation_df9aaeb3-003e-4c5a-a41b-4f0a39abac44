import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode, useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Dropdown, Placeholder } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { RootState } from '@/models/store';
import { UserInfo } from '@/types';
import { OneAndMoreType } from '@/utils/const';

import { PeoplePickerContext } from '../context';
import { mergeArrays } from '../utils';
import s from './index.less';
import Overlay from './overlay';
export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  value: UserInfo[];
  hasMinimum?: boolean;
  onOneAndMoreChange?: (oneAndMoreType: OneAndMoreType) => void;
  assignerUid?: string;
  assigner?: UserInfo;
  onChange: (users: UserInfo[]) => void;
  hasArrow?: boolean;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  placeholder?: ReactNode;
  groupId?: string;
  taskId: number;
  oneAndMoreType?: OneAndMoreType;
  getPopupContainer?: () => HTMLElement;
};

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    search,
    children,
    value,
    onChange,
    onOneAndMoreChange,
    hasMinimum,
    hasArrow,
    disabled,
    visible = false,
    onVisible,
    placeholder,
    className,
    taskId,
    oneAndMoreType,
  } = props;
  const { assigneeUids } = useContext(PeoplePickerContext);
  // // 最近联系人
  // const [recentContacts, setRecentContacts] = useState<UserInfo[]>([]);
  // 我(选中) + 已选人人员 + 我(未选中) + 已选人员(取消选中的) + 最近联系人
  const [selectedPanelUsers, setSelectedPanelUsers] = useState<UserInfo[]>([]);
  const { recentContactsList, userInfo } = useSelector((state: RootState) => ({
    recentContactsList: state.user.recentContactsList,
    userInfo: state.user.userInfo,
  }));

  const changeselectedPanelUsers = (opt: { recentContacts: UserInfo[] }) => {
    const { recentContacts } = opt;
    let topUnSelect: UserInfo[] = [];
    let topSelect =
      [userInfo!].filter((item) => {
        if (value.findIndex((v) => v.uid === item.uid) > -1) {
          return true;
        } else {
          topUnSelect.push({
            ...item,
          });
          return false;
        }
      }) || [];
    //已选人员value
    topSelect = topSelect.map((item) => ({
      ...item,
      selected: true,
    }));

    const selectValue = value.map((item) => ({
      ...item,
      selected: true,
    }));
    const _selectedPanelUsers = selectedPanelUsers.map((item) => ({
      ...item,
      selected: false,
    }));
    //我(选中) + 已选人人员 + 我(未选中) + 已选人员(取消选中的) + 最近联系人
    const list = [...topSelect, ...selectValue, ..._selectedPanelUsers];
    if (!list.length && !disabled) {
      list.push(...[...topUnSelect, ...recentContacts]);
    }
    // 针对list 去重

    const data = mergeArrays(list) as UserInfo[];
    setSelectedPanelUsers(data);
  };

  const handleOpenChange = (open: boolean, force?: boolean) => {
    // if (!open && isAssigneeUpdated && !force) {
    //   const btn = document.body.querySelector(`.confirm__btn`);
    //   if (btn) {
    //     btn?.classList.add('blink__animation');
    //     setTimeout(() => {
    //       btn?.classList.remove('blink__animation');
    //     }, 200);
    //   }
    // } else {
    //   onVisible?.(open);
    // }
    onVisible?.(open);
  };

  useEffect(() => {
    // 打开面板用户不存在时获取最近联系人
    if (visible) {
      changeselectedPanelUsers({ recentContacts: recentContactsList || [] });
    }
  }, [visible, assigneeUids, value.length]);

  return (
    <Dropdown
      disabled={disabled}
      className={classNames(s.peoplePickerDropdown, className)}
      title={
        <DropdownIcon showArrow={visible} hasArrow={hasArrow}>
          {children ? (
            children
          ) : placeholder !== undefined ? (
            <Placeholder text={placeholder}></Placeholder>
          ) : null}
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      open={visible}
      onOpenChange={(open) => handleOpenChange(open)}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Overlay
          search={search}
          value={value}
          oneAndMoreType={oneAndMoreType}
          onOneAndMoreChange={onOneAndMoreChange}
          onChange={onChange}
          hasMinimum={hasMinimum}
          selectedPanelUsers={selectedPanelUsers}
          taskId={taskId}
          disabled={disabled}
          handleOpenChange={handleOpenChange}
        ></Overlay>
      }
    />
  );
};

export default PeoplePicker;
