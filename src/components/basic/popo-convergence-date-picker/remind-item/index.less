.clear {
  margin-left: 4px;
  width: 22px;
  height: 22px;
  i {
    color: var(--IconTertiary);
  }
}
.startTime {
  margin-left: 10px;
}

.remindLabel {
  display: flex;
  min-width: 0;
  // color: var(--TextPrimary); 不在这里加颜色 哪里用哪里加
}

.remindItemWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  :global {
    .todo-item-bg {
      &:hover {
        .picker-label-arrow {
          display: block;
        }
      }
    }
  }
}

.detailRemindItemWrapper {
  min-height: 30px;
  display: flex;
  align-items: center;
  .suffixIcon {
    line-height: 30px;
    i {
      margin-right: 8px;
      color: var(--IconTertiary);
    }
  }
  .showBg {
    max-width: calc(100% - 52px);
  }
  .clear {
    width: 24px !important;
    height: 24px !important;
    margin-left: 8px !important;
  }
  :global {
    .todo-base-label {
      color: var(--TextPrimary);
    }

    .todo-base-label > div > div {
      text-wrap: wrap;
    }
  }
}
