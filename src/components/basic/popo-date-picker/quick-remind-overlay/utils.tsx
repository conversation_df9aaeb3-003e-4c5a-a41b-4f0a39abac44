import dayjs, { Dayjs } from 'dayjs';

import { dddd_HH_mm } from '@/utils/const';
import I18N from '@/utils/I18N';

import { getRuleStrByRepeatType, RepeatType, TimeValue } from '../utils';

export type RemindTimeValue = TimeValue;

export type ItemTimeOption = {
  text: string;
  showDate?: (time: Dayjs) => string | React.ReactNode;
  getTime?: (time: Dayjs) => RemindTimeValue;
};

const getLaterTodayTime = (time: Dayjs) => {
  const todayTime = time.startOf('date');
  const is9 = todayTime.add(9, 'hour');
  const is12 = todayTime.add(12, 'hour');
  const is14 = todayTime.add(12, 'hour');
  //在午餐前3小时 9点前 正常+3小时
  let remindTime = undefined;
  if (time.isBefore(is9)) {
    remindTime = time.add(3, 'hour');
  } else if (time.isBefore(is12)) {
    //9点到12点间 扣除2小时 当前时间多加5小时
    remindTime = time.add(5, 'hour');
  } else if (time.isBefore(is14)) {
    //12点到14点间  从2点开始加3小时 固定17:00
    remindTime = todayTime.add(17, 'hour');
  } else {
    //14点以后 正常+3小时
    remindTime = time.add(3, 'hour');
  }
  return remindTime;
};

export const Later_Today: ItemTimeOption = {
  text: I18N.auto.laterToday,
  showDate: (time: Dayjs) => {
    const remindTime = getLaterTodayTime(time);
    return remindTime.format(dddd_HH_mm);
  },
  getTime: (time: Dayjs) => {
    const remindTime = getLaterTodayTime(time);
    return {
      time: remindTime.valueOf(),
      rrule: undefined,
      selectedOption: 'LATER_TODAY',
    };
  },
};

export const None: ItemTimeOption = {
  text: I18N.auto.nothing,
};

export const Tomorrow: ItemTimeOption = {
  text: I18N.auto.tomorrow,
  showDate: (time: Dayjs) => {
    return time.add(1, 'day').format(dddd_HH_mm);
  },
  getTime: (time: Dayjs) => {
    return {
      time: time.add(1, 'day').valueOf(),
      rrule: undefined,
      selectedOption: 'SAME_TIME_TOMORROW',
    };
  },
};

export const Next_Week: ItemTimeOption = {
  text: I18N.auto.nextWeek,
  showDate: (time: Dayjs) => {
    return time.add(7, 'day').format(dddd_HH_mm);
  },
  getTime: (time: Dayjs) => {
    return {
      time: time.add(7, 'day').valueOf(),
      rrule: undefined,
      selectedOption: 'SAME_TIME_NEXT_WEEK',
    };
  },
};

export const Every_Day: ItemTimeOption = {
  text: I18N.auto.everyDay,
  getTime: (time: Dayjs) => {
    const rrule = getRuleStrByRepeatType(RepeatType.Dayly, time);
    return {
      time: time.valueOf(),
      rrule: rrule,
      selectedOption: 'SAME_TIME_EVERYDAY',
    };
  },
};

export const Every_3_Day: ItemTimeOption = {
  text: I18N.auto.everyDay_2,
  getTime: (time: Dayjs) => {
    const rrule = getRuleStrByRepeatType(RepeatType.ThreeDays, time);
    return {
      time: time.valueOf(),
      rrule: rrule,
      selectedOption: 'SAME_TIME_EVERY_THREE_DAY',
    };
  },
};

export const Every_Weeks: ItemTimeOption = {
  text: I18N.auto.weekly,
  getTime: (time: Dayjs) => {
    const rrule = getRuleStrByRepeatType(RepeatType.Weekly, time);
    return {
      time: time.valueOf(),
      rrule: rrule,
      selectedOption: 'SAME_TIME_EVERY_WEEK',
    };
  },
};
