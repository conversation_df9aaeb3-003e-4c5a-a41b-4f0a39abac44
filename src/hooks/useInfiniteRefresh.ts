import { useCallback, useEffect, useRef } from 'react';

import useForceUpdate from './useForceUpdate';

export interface Option {
  request(option: {
    searchId?: string;
    scrollId?: string;
    size: number;
    [key: string]: any;
  }): Promise<{ list?: any[]; hasMore?: boolean; [k: string]: any }>;
  size?: number;
  scrollId?: string;
}

export default function useInfiniteRefresh<T = any>(options: Option) {
  const optRef = useRef<Option>({ size: 30, ...options });
  optRef.current = { size: 30, ...options };
  const forceUpdate = useForceUpdate();

  const dataRef = useRef({
    searchId: undefined,
    scrollId: undefined,
    total: 0,
    list: [] as T[],
    hasMore: false,
  });
  const setData = useCallback(
    (list: T[] | ((val: T[]) => T[])) => {
      if (typeof list === 'function') {
        dataRef.current.list = list(dataRef.current.list);
        forceUpdate();
      } else {
        dataRef.current.list = list;
        forceUpdate();
      }
    },
    [forceUpdate]
  );

  const refresh = useCallback(
    (args: Record<string, any> = {}) => {
      return optRef.current.request({ size: optRef.current.size!, ...args }).then((res) => {
        const { scrollId, searchId, list = [] } = res;
        dataRef.current = {
          scrollId: scrollId,
          searchId: searchId,
          total: res.total!,
          list: list,
          hasMore: !!res.hasMore,
        };
        forceUpdate();
      });
    },
    [forceUpdate]
  );

  const loadMore = useCallback(
    (args: Record<string, any> = {}) => {
      return new Promise<void>((resolve, reject) => {
        if (dataRef.current.hasMore) {
          optRef.current
            .request({
              scrollId: dataRef.current.scrollId,
              searchId: dataRef.current.searchId,
              size: optRef.current.size!,
              ...args,
            })
            .then((res) => {
              const { scrollId, searchId, list = [] } = res;
              let newList = dataRef.current.list.concat(list);
              dataRef.current = {
                scrollId: scrollId,
                searchId: searchId,
                total: res.total!,
                list: newList,
                hasMore: !!res.hasMore,
              };
              forceUpdate();
            })
            .finally(() => {
              resolve();
            });
        } else {
          reject();
        }
      });
    },
    [forceUpdate]
  );

  useEffect(() => {
    refresh();
  }, []);

  return {
    refresh,
    loadMore,
    setData,
    ...dataRef.current,
  };
}
