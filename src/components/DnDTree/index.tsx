import { Tree } from '@bedrock/components';
import { TreeProps } from '@bedrock/components/lib/Tree';
import { useState, useCallback, useEffect } from 'react';
import projectGroupFoldStatus from '@/utils/projectGroupFoldStatus';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import { EnumEmitter } from '@/utils/const';

const DnDTree = (props: TreeProps & { setTreeRef: (node: any) => void; isPin: boolean }) => {
  const [expandedKeys, setExpandedKeys] = useState<(string | number)[]>([]);

  const onDragEnter = useCallback((info) => {
    // console.log(info);
    setExpandedKeys(info.expandedKeys);
  }, []);

  const handleExpand = (keys: any[]) => {
    projectGroupFoldStatus.setFoldStatus({ ids: keys as (string | number)[] });
    setExpandedKeys([...keys]);
  };

  useEffect(() => {
    function handleExpandById(
      { id, fromPin }: { id: string | number; fromPin?: boolean } = {} as any
    ) {
      const index = expandedKeys.indexOf(id);
      let newExpandedKeys = expandedKeys;
      if (index === -1) {
        newExpandedKeys.push(id);
      } else {
        newExpandedKeys.splice(index, 1);
      }
      handleExpand(newExpandedKeys);
    }
    console.log('expandedKeys', expandedKeys);
    POPOBridgeEmitter.addListener(EnumEmitter.TreeExpandById, handleExpandById);

    return () => {
      POPOBridgeEmitter.removeAllListeners(EnumEmitter.TreeExpandById);
    };
  }, [expandedKeys]);

  useEffect(() => {
    setExpandedKeys(projectGroupFoldStatus.getFoldStatus() || []);
  }, []);

  return (
    <Tree
      ref={props.setTreeRef}
      showTitle={false}
      // height={400}
      showIcon={false}
      onExpand={(keys, info = {}) => {
        const { node } = info;
        POPOBridgeEmitter.emit(EnumEmitter.TreeExpandById, { id: node?.id, fromPin: props.isPin });

        handleExpand(keys);
      }}
      onDragEnter={onDragEnter}
      // virtual
      fieldnames={{
        key: 'projectId',
        title: 'name',
        children: 'groupProjects',
      }}
      expandedKeys={expandedKeys}
      {...props}
    />
  );
};

export default DnDTree;
