import dayjs from 'dayjs';
import React, { CSSProperties, useMemo, useState } from 'react';

import { IconBtn, Itembg, POPODatePicker, Tooltip } from '@/components/basic';
import { QuickType, TimeValue } from '@/components/basic/popo-date-picker';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import SelectItemContainer from '../select-item-container';
import s from './index.less';
import RemindStr from './remind-label';
export type Props = {
  className?: string;
  value: TimeValue;
  suffixIcon?: React.ReactNode;
  showSource?: string;
  onChange?: (v: TimeValue) => void;
  disabled?: boolean;
  loopInducedDisabled?: boolean;
  deadlineTime?: TimeValue;
};

const RemindItemBasic: React.FC<Props> = (props) => {
  const { value, onChange, disabled, deadlineTime, loopInducedDisabled, suffixIcon, showSource } =
    props;
  const [visible, setVisible] = useState<boolean>(false);

  const style: CSSProperties = {};
  if (showSource === 'detail') {
    style.maxWidth = '100%';
  }

  return (
    <Tooltip
      title={loopInducedDisabled ? I18N.auto.theNextOneHasBeenGenerated : ''}
      placement="topLeft"
    >
      <SelectItemContainer icon={suffixIcon} title={I18N.auto.remind}>
        <Itembg disabled={disabled || loopInducedDisabled} style={style}>
          <POPODatePicker
            visible={visible}
            onVisible={(v) => {
              setVisible(v);
            }}
            disabled={disabled || loopInducedDisabled}
            value={value}
            onChange={onChange}
            placeholder={I18N.auto.nothing}
            quickType={QuickType.remind}
            hasRRule={true}
            hasArrow={true}
            createTime={dayjs()}
            deadlineTime={deadlineTime}
            className={showSource === 'detail' ? s.detailRemindItemPicker : ''}
            renderLabel={(time, rrule) => {
              return (
                <RemindStr time={time} timeFormat={value.timeFormat} rrule={rrule}></RemindStr>
              );
            }}
          ></POPODatePicker>
        </Itembg>
      </SelectItemContainer>
      {/* <div
        className={`${s.remindItemWrapper} ${showSource === 'detail' && s.detailRemindItemWrapper}`}
      >
        <span className={s.suffixIcon}>{suffixIcon}</span>
        <Itembg
          showbg={true}
          disabled={disabled || loopInducedDisabled}
          style={showSource === 'detail' ? { maxWidth: 'calc(100% - 52px)' } : {}}
        >
          <POPODatePicker
            visible={visible}
            onVisible={(v) => {
              setVisible(v);
            }}
            disabled={disabled || loopInducedDisabled}
            value={value}
            onChange={onChange}
            placeholder={I18N.auto.whenSettingReminders}
            quickType={QuickType.remind}
            hasRRule={true}
            hasArrow={true}
            createTime={dayjs()}
            deadlineTime={deadlineTime}
            className={showSource === 'detail' ? s.detailRemindItemPicker : ''}
            renderLabel={(time, rrule) => {
              return (
                <RemindStr time={time} timeFormat={value.timeFormat} rrule={rrule}></RemindStr>
              );
            }}
          ></POPODatePicker>
        </Itembg>

        {memoShowDelete ? (
          <div className={s.clear}>
            <IconBtn
              fontSize={16}
              title={I18N.auto.delete}
              iconName="icon-sys_close"
              onClick={() => {
                onChange?.({
                  time: 0,
                  timeFormat: PPTimeFormat.noDate,
                  rrule: '',
                });
              }}
            ></IconBtn>
          </div>
        ) : null}
      </div> */}
    </Tooltip>
  );
};

export default RemindItemBasic;
