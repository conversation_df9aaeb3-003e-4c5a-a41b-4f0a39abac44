import {
  RockEditorPlugin,
  Plugin<PERSON>ey,
  RockEditorView,
  RockNodeView,
  Decoration,
} from '@bedrock/editor';
import axios from 'axios';
import LinkRecognitionView from './LinkRecognitionView';
import { LinkRecognitionNodeSpec } from './LinkRecognitionNodeSpec';

const getAppendixNodeView = () => {
  return class extends RockNodeView {
    constructor(
      node: Node,
      editorView: RockEditorView,
      getPos: () => number,
      decorations: Decoration[]
    ) {
      super(node, editorView, getPos, decorations);
    }

    // @override
    createDOMElement(): HTMLElement {
      const el = document.createElement('div');
      el.className = 'rock-editor__appendix-view';
      return el;
    }

    // @override
    renderReactComponent(): React.ReactElement<any> {
      return <span>Appendix</span>;
    }
  };
};
// 链接识别的正则表达式，与你现有的DocsRegex类似
const LinkRegex = /((?:https|http):\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]+)/g;
// 创建链接识别插件
export default function createLinkRecognitionPlugin(options = {}) {
  return new RockEditorPlugin({
    key: new PluginKey('linkRecognitionPlugin'),
    nodeSpecs: {
      ['linkRecognition']: LinkRecognitionNodeSpec,
    },
    // nodeViews: {
    //   ['linkRecognitionPlugin']: getAppendixNodeView(),
    // },
    view(editorView: RockEditorView) {
      return new LinkRecognitionView(editorView);
    },
    priority: 9999,
    // patchNormalizeHTML(doc, config, options) {
    //   const aEls = doc.querySelectorAll('a[href]');
    //   console.log('aEls', aEls);
    //   for (let i = 0; i < aEls.length; i++) {
    //     const el = aEls[i];
    //     const href = el.getAttribute('href');
    //     if (LinkRegex.test(href)) {
    //       // 请求接口获取链接信息
    //       const spanEl = document.createElement('span');
    //       spanEl.innerText = 'link';
    //       el.parentElement?.replaceChild(el, spanEl);
    //     }
    //   }
    // },
  });
}

// 处理粘贴事件中的链接
async function handleLinks(links, text, view) {
  const { state, dispatch } = view;

  try {
    // 调用API获取链接信息
    const linkInfos = await fetchLinkInfo(links);

    // 创建新的文档内容
    let newContent = text;
    for (const link of links) {
      if (linkInfos[link]) {
        const linkName = linkInfos[link].name || link;
        // 替换链接为带名称的a标签
        newContent = newContent.replace(link, `<a href="${link}" target="_blank">${linkName}</a>`);
      }
    }

    // 插入处理后的内容
    const { from } = state.selection;
    dispatch(state.tr.insertText(newContent, from));
  } catch (error) {
    console.error('Error processing links:', error);
  }
}

// 处理文档变化中的链接
function handleLinksInTransaction(links, state) {
  const tr = state.tr;

  // 这里我们只标记需要处理的链接位置
  // 实际的API调用和替换会在异步函数中完成
  for (const link of links) {
    let index = 0;
    let pos = state.doc.textContent.indexOf(link, index);

    while (pos !== -1) {
      // 标记链接位置，后续可以通过API处理
      // 这里只是示例，实际实现可能需要更复杂的逻辑
      index = pos + link.length;
      pos = state.doc.textContent.indexOf(link, index);
    }
  }

  // 异步处理链接
  setTimeout(() => {
    fetchAndReplaceLinks(links, state);
  }, 0);

  return null; // 不直接修改文档
}

// 调用API获取链接信息
async function fetchLinkInfo(links) {
  try {
    // 这里替换为你的实际API调用
    const response = await axios.post('/api/link-info', { links });
    return response.data.reduce((acc, item) => {
      acc[item.url] = item;
      return acc;
    }, {});
  } catch (error) {
    console.error('Error fetching link info:', error);
    return {};
  }
}

// 异步获取链接信息并替换
async function fetchAndReplaceLinks(links, state) {
  try {
    const linkInfos = await fetchLinkInfo(links);

    // 这里需要获取最新的编辑器状态和视图
    // 实际实现中需要通过某种方式获取当前的编辑器视图
    // 例如通过全局变量或事件系统

    // 替换链接为带名称的a标签
    // 这部分需要根据你的编辑器实现来调整
  } catch (error) {
    console.error('Error replacing links:', error);
  }
}
