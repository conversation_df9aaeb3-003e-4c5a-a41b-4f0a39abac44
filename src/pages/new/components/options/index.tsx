import { SoftZd } from '@babylon/popo-icons'
import classNames from 'classnames'
import { useEffect, useMemo, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import * as _ from 'lodash'

import { Button, Icon, Popover } from '@/components/basic'
import { FilterGuide } from '@/components/guide/filter-guide'
import useGuide from '@/components/guide/useGuide'
import { Dispatch, RootState } from '@/models/store'
import { isProjectId, isTaskMenuId } from '@/models/utils'
import { EnumKanbanAddSource, EnumListAddSource, GuideType, ViewAddSource, ViewType } from '@/utils/const'
import I18N from '@/utils/I18N'
import { ProjectPermissionEnum, validatesPermission, ViewPermissionEnum, ViewTypeEnum } from '@/utils/permission'

import CardConfig from './card-config'
import FieldsSetting from './fields-setting'
import Filter from './filter'
import Group from './group'
import s from './index.less'
import Sort from './sort'
import SaveViewBtn from './save-view-btn'
import { useParams } from 'umi'

import { ChevronDown, Save } from '@bedrock/icons-react'
export interface IOptionProps {
  className?: string
  prefixOption?: React.ReactNode
}

const Options: React.FC<IOptionProps> = props => {
  const { className, prefixOption = null } = props
  const dispatch = useDispatch<Dispatch>()

  const { id: projectId } = useParams()
  const { currentViewTab, permissions, viewTabList, projectInfo, navigatorId } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
    permissions: state.viewSetting.permissions,
    viewTabList: state.viewSetting.viewTabList,
    projectInfo: state.project.projectInfo,
    navigatorId: state.viewSetting.navigatorId,
  }))
  const { changeSearchParams, hasViewChanged, parsedViewConfig } = currentViewTab

  const isProjectTab = useMemo(() => {
    return isProjectId(navigatorId as number)
  }, [navigatorId])
  const isTaskMenuTab = useMemo(() => {
    return isTaskMenuId(navigatorId as number)
  }, [navigatorId])

  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.filter,
  })
  const ref = useRef<{ changeVisible: (v: boolean) => void }>({
    changeVisible: () => {},
  })

  const [canEditView] = useMemo(() => {
    const isCommonView = currentViewTab.viewDistribution !== ViewTypeEnum.Personal

    const canEditView = validatesPermission({
      permissions: currentViewTab.permissions,
      key: isCommonView ? ViewPermissionEnum.CAN_EDIT_COMMON_VIEW : ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
    })
    return [canEditView]
  }, [currentViewTab])

  const [canCreateCommonView, canCreatePersonView] = validatesPermission({
    permissions,
    key: [ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW, ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW],
  }) as boolean[]

  const showSaveNewBtn = useMemo(() => {
    return changeSearchParams && hasViewChanged && (canCreateCommonView || canCreatePersonView)
  }, [changeSearchParams, hasViewChanged, canCreateCommonView, canCreatePersonView])

  const onAdd = () => {
    //e.stopPropagation();
    setTimeout(() => {
      if (currentViewTab?.viewType === ViewType.list) {
        // 标注最新新建来源
        ViewAddSource.list = EnumListAddSource.globalCreate
        dispatch.task.tableAddTaskItemFromOptions({
          groupId: undefined,
          isHeader: true,
          defaultParams: {},
          isSubtask: true,
        })
      } else if (currentViewTab?.viewType === ViewType.kanban) {
        // 标注最新新建来源
        ViewAddSource.kanban = EnumKanbanAddSource.globalCreate
        dispatch.kanban.tableAddTaskItem({ groupId: undefined, isHeader: true, defaultParams: {} })
      } else if (currentViewTab?.viewType === ViewType.timeline) {
        // dispatch.timeline.tableAddTaskItem({
        //   groupId: undefined,
        //   isHeader: true,
        //   defaultParams: {},
        // });
        window.postMessage({ type: 'timeline-add-task' }, '*')
      }
    }, 0)
  }
  const showAddBtn = useMemo(() => {
    return validatesPermission({
      permissions: permissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    })
  }, [permissions])

  // const handleSaveView = useMemoizedFn(() => {
  //   Modal.confirm({
  //     title: I18N.auto.saveAsDefault,
  //     content: I18N.auto.afterSavingItWillBeMade,
  //     okText: I18N.auto.cover,
  //     className: s.saveViewModal,
  //     onOk: () => {
  //       dispatch.viewSetting
  //         .updateSetting({})
  //         .then(() => {
  //           Message.success(I18N.auto.saveViewAs);
  //           // 更新viewTabList对应这个视图的值，让保存视图以及另存为新视图按钮消失
  //           dispatch.viewSetting.fetchViewList(projectInfo.projectId);
  //           dispatch.viewSetting.setData({
  //             currentViewTab: {
  //               ...currentViewTab,
  //               changeSearchParams: false,
  //             },
  //           });
  //         })
  //         .catch(() => {
  //           Message.success(I18N.auto.saveViewLost);
  //         });
  //       const list = viewTabList.map((view) => {
  //         if (view.viewId === currentViewTab.viewId) {
  //           return {
  //             ...currentViewTab,
  //             changeSearchParams: false,
  //           };
  //         }
  //         return view;
  //       });
  //       dispatch.viewSetting.setViewTabList([...(list as ViewTab[])]);
  //       //保存视图不需要刷新当前数据
  //       //dispatch.viewSetting.toggeleView(currentViewTab as ViewTab);
  //     },
  //     centered: true,
  //     width: 400,
  //   });
  // });
  useEffect(() => {
    //筛选引导目前只放在任务快捷视图下
    if (isTaskMenuTab) {
      getGuideData()
    }
  }, [isTaskMenuTab])

  // const renderSave = () => {
  //   if (showSaveNewBtn && isProjectTab) {
  //     const getSaveAsView = ({ className = '' }) => (
  //       <SaveAsView>
  //         {/* <div className={s.overlay__content}>{I18N.auto.saveAsNewVision_2}</div> */}
  //         <div className={className}>{I18N.auto.saveAsNewVision_2}</div>
  //       </SaveAsView>
  //     );

  //     if (canEditView && currentViewTab.viewDistribution !== ViewTypeEnum.Personal) {
  //       return (
  //         <div className={s.saveBtnWrapper}>
  //           <div className={`${s.save__btn} flex-y-center`}>
  //             <div onClick={handleSaveView} className={`${s['save__btn--left']} flex-y-center`}>
  //               <SoftSave className="fs-16" />
  //               {I18N.auto.saveView}
  //             </div>
  //             <Dropdown
  //               align={{
  //                 offset: [-2, 4],
  //               }}
  //               placement="bottomRight"
  //               trigger="hover"
  //               overlay={
  //                 // <div
  //                 //   className={s.overlay__wrapper}
  //                 //   onClick={() => {
  //                 //     console.log('dropdownRef.current', dropdownRef.current);
  //                 //     dropdownRef.current.onBlur();
  //                 //   }}
  //                 // >
  //                 //   {getSaveAsView({ className: s.overlay__content })}
  //                 // </div>
  //                 <SaveAsView>
  //                   <Menu className={s['save__view--menu']}>
  //                     <Menu.Item key={1}>{I18N.auto.saveAsNewVision_2}</Menu.Item>
  //                   </Menu>
  //                 </SaveAsView>
  //               }
  //             >
  //               <div className={`${s['save__btn--right']} flex-y-center`}>
  //                 <ChevronDown className="rock-dropdown-down-arrow-icon" />
  //               </div>
  //             </Dropdown>
  //           </div>
  //         </div>
  //       );
  //     }
  //     return (
  //       <div className={s.saveBtnWrapper}>
  //         <div className={`${s.save__btn} flex-y-center`}>
  //           <div
  //             style={{ paddingRight: '0.06rem' }}
  //             className={`${s['save__btn--left']} flex-y-center`}
  //           >
  //             <SoftSave className="fs-16" />
  //             {getSaveAsView({})}
  //           </div>
  //         </div>
  //       </div>
  //     );
  //   }
  //   return null;
  // };

  return currentViewTab?.type != ViewType.calendar ? (
    <div className={classNames(s.optionsWrapper, className)}>
      <div className={s.options}>
        {showAddBtn ? (
          <Button
            className={classNames(s.add, 'option__add--task')}
            icon={<Icon name="icon-sys_add" fontSize={16}></Icon>}
            type="checked-neutral"
            onClick={onAdd}
          >
            {I18N.auto.newTask}
          </Button>
        ) : null}
        {prefixOption}
        <div className="task-fields-select">
          <Popover
            overlayClassName={s.guidePopover}
            arrowPointAtCenter
            placement={'bottomLeft'}
            trigger={'click'}
            visible={showGuide}
            content={
              <FilterGuide
                onClick={() => {
                  handleHideGuide()
                }}
                onExpClick={() => {
                  handleHideGuide().then(() => {
                    ref.current.changeVisible(true)
                  })
                }}
              />
            }
          >
            <Filter ref={ref}></Filter>
          </Popover>
          <Sort></Sort>
          <Group></Group>
          {currentViewTab.viewType !== ViewType.timeline && (
            <FieldsSetting>
              <Button
                className={classNames('mr-8', s.hidden, 'task-fields-setting')}
                type="text-subtle"
                icon={<SoftZd className="fs-16" />}
              >
                {I18N.auto.fieldConfiguration}
              </Button>
            </FieldsSetting>
          )}
          {/* 看板 => 卡片配置 */}
          {currentViewTab.type === ViewType.kanban && <CardConfig />}
        </div>
        {/* {renderSave()} */}
        <SaveViewBtn
          showSaveNewBtn={showSaveNewBtn}
          isProjectTab={isProjectTab}
          canEditView={canEditView}
          currentViewTab={currentViewTab}
          projectId={projectInfo.projectId || projectId}
        />
      </div>
      {/* {showSaveNewBtn && isProjectTab && (
        <div className={s.saveBtnWrapper}>
          {canEditView && currentViewTab.viewDistribution !== ViewTypeEnum.Personal && (
            <Button className="mr-8" type="checked" onClick={handleSaveView}>
              {I18N.auto.saveView}
            </Button>
          )}

          <SaveAsView>
            <Button type="checked">{I18N.auto.saveAsNewVision_2}</Button>
          </SaveAsView>
        </div>
      )} */}
    </div>
  ) : null
}
export default Options
