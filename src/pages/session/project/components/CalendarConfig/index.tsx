import { Switch } from '@/components/basic';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';
import s from './index.less';

const CalendarConfig = () => {
  const { showWeekend } = useSelector((state: RootState) => {
    return {
      showWeekend: state.viewSetting.currentViewTab.showWeekend,
    };
  });

  const dispatch = useDispatch<Dispatch>();

  const handleSwitch = () => {
    dispatch.viewSetting.updateCurrentViewAndUpdateServer({
      showWeekend: !showWeekend,
    });
  };

  return (
    <div className={`${s.switch} flex-y-center`} onClick={handleSwitch}>
      <span className={s.switch__label}>{I18N.auto.showWeekends}</span>
      <Switch value={showWeekend} />
    </div>
  );
};

export default CalendarConfig;
