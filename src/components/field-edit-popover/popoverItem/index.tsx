import classNames from 'classnames';
import { FC, ReactNode } from 'react';

import s from './index.less';

interface ConfigItemProps extends React.HTMLAttributes<HTMLDivElement> {
  left: ReactNode;
  right?: ReactNode;
  level?: 'primary' | 'secondary';
  interactive?: boolean;
}

export const PopoverItem: FC<ConfigItemProps> = ({
  left,
  right,
  level = 'primary',
  className = '',
  interactive = false,
  ...props
}) => {
  return (
    <div
      className={classNames(`${s.config__item} ${s[level]} ${className}`, {
        [s.interactive]: interactive,
      })}
      {...props}
    >
      {left}
      {right}
    </div>
  );
};
