import React, {
  useMemo,
  useRef,
  useCallback,
  useEffect,
  memo,
  CSSProperties,
  useState,
  FC,
  PropsWithChildren,
  ReactNode,
} from 'react'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  type ColumnDef,
  type Row,
  type Header,
  type Cell,
  type Table,
  type Column,
  getExpandedRowModel,
  ExpandedState,
} from '@tanstack/react-table'
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  type DragEndEvent,
  useSensor,
  useSensors,
  MeasuringStrategy,
  DragOverlay,
  DragStartEvent,
  DragOverEvent,
  pointerWithin,
} from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useVirtualizer, VirtualItem, type Virtualizer } from '@tanstack/react-virtual'
import './index.less'
import { HeadCell } from '../table/components'
import classNames from 'classnames'
import { EnumEmitter, Order, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import { TanstackTableContext, useTanstackTableContext } from './tanstackTableContext'
import { TodoInfo } from '@/types'
import { getExpandedIndexMap, getTaskByIndexes, setExpandedIndexMap } from '@/models/utils'
import { getInsertPosition } from '@/utils/dndkitUtils'
import { ListTableNoData } from '../table/components/no-data'
import { POPOBridgeEmitter } from '@popo-bridge/web'
import { getColumnsWidth } from './utils'
import { Platform } from '@/utils/platform'
import { store } from '@/models/store'
import { OperateMove } from '@babylon/popo-icons'
import { useSelector } from 'react-redux'
import * as _ from 'lodash'
import autoExpandData from '@/hooks/useGetGroupedList/AutoExpandData'

// 类型定义集中管理
export type TodoInfo = {
  firstName: string
  lastName: string
  age: number
  visits: number
  progress: number
  status: 'relationship' | 'complicated' | 'single'
  children?: TodoInfo[]
}

type TanstackTableProps<TData> = {
  columns: ColumnDef<TData, any>[]
  data: TData[]
  initialPinLeft?: string[]
  tableClassName?: string
  wrapperClassName?: string
  onSortChange?: (dataIndex: string, sortOrder: Order) => void
  sortOrder: { dataIndex?: string; order?: Order }
  taskIds?: number[]
  expandedKeysMap?: Record<number, boolean>
  navigatorId?: string | number
  rowsDraggable?: boolean
  onExpandedChange?: (expanded: ExpandedState) => void
  emptyNodeProps?: any
  emptySearchDataProps?: any
  total?: number
  onDragEnd?: (params: {
    list: TodoInfo[]
    activeIndex: number
    overIndex: number
    preTaskId?: number
    tailTaskId?: number
    activeTaskId: number
    parentId?: number
  }) => void
  extra?: ReactNode
}

const getCommonPinningStyles = (column: Column<any>): React.CSSProperties => {
  const isPinned = column.getIsPinned()
  return {
    left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
    right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
    position: isPinned ? 'sticky' : 'relative',
    // width: column.getSize(),
    zIndex: isPinned ? 1 : 0,
    // boxShadow: isPinned ? '4px 0px 8px 0px #00000014' : 'none',
  }
}

window.fontSizeScaleRatio = window.fontSizeScaleRatio || 1

const DraggableTableHeader = memo(({ header }: { header: Header<any, unknown> }) => {
  const id = header.id
  const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
    id,
  })

  const { sortOrder, onSortChange } = useTanstackTableContext()

  const style: React.CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform),
    transition: 'width transform 0.2s ease-in-out',
    whiteSpace: 'nowrap',
    width: header.column.getSize(),
    zIndex: isDragging ? 2 : 0,
  }

  const content = header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())

  const columnDef = header.column.columnDef

  const { meta } = columnDef

  const isResizing = header.column.getIsResizing()

  useEffect(() => {
    if (isResizing) {
      document.body.style.userSelect = 'none'
      document.body.style.webkitUserSelect = 'none'
    } else {
      document.body.style.userSelect = ''
      document.body.style.webkitUserSelect = ''
    }
  }, [isResizing])

  return (
    <th
      colSpan={header.colSpan}
      ref={setNodeRef}
      onClick={header.column.getToggleSortingHandler()}
      style={{
        ...style,
        width: meta?.width || `calc(var(--header-${header?.id}-size) * 1px)`,
        ...getCommonPinningStyles(header.column),
      }}
    >
      <div {...attributes} {...listeners} className="th__content">
        <HeadCell
          className={classNames(
            'todo-th',
            'flex-y-center',
            meta?.className || '',
            // _className,
            // {
            //   ['todo-td-right-no-border']: key === columns.length - 1,
            // },
            // item.className
          )}
          key={id}
          style={{ textAlign: columnDef.align }}
          sortable={meta?.sortable}
          active={sortOrder?.orderBy == columnDef.accessorKey}
          order={sortOrder?.orderBy == columnDef.accessorKey ? sortOrder?.order : undefined}
          onSortChange={sort => {
            onSortChange?.(columnDef.accessorKey as string, sort)
          }}
          prefix={columnDef.prefix}
          suffix={meta?.suffix}
        >
          {content}
        </HeadCell>
      </div>
      {meta?.resizable !== false && (
        <div
          onDoubleClick={() => header.column.resetSize()}
          onMouseDown={header.getResizeHandler()}
          onTouchStart={header.getResizeHandler()}
          className={`resizer ${header.column.getIsResizing() ? 'isResizing' : ''}`}
          style={{ cursor: Platform.OS === 'windows' ? 'e-resize' : 'col-resize' }}
        />
      )}
    </th>
  )
})

// 同时实现行列拖拽，性能跟不上，因为列拖拽会影响每个单元格的计算
const DragAlongCell = memo(
  ({
    cell,
    style = {},
    dragHandler,
    className = '',
  }: {
    cell: Cell<TodoInfo, unknown>
    style?: CSSProperties
    dragHandler?: ReactNode
    className?: string
  }) => {
    // const { isDragging, setNodeRef, transform } = useSortable({
    //   id: cell.column.id,
    // });

    const { meta } = cell.column.columnDef
    // 如果是新增的行,需要覆盖整行
    const calculatedWidth = meta?.width || `calc(var(--header-${cell.column?.id}-size) * 1px)`

    const styles: CSSProperties = {
      // opacity: isDragging ? 0.8 : 1,
      // transform: CSS.Translate.toString(transform),
      // transition: 'width transform 0.2s ease-in-out',
      width: calculatedWidth,
      // width: `var(--header-${cell.column?.id}-size)`,
      ...getCommonPinningStyles(cell.column),
      // ...(isDragging ? { position: 'relative', zIndex: 2 } : {}),
      ...style,
    }

    const isAddRow = cell.row.original._rowType === TaskTableRowType.addBtn

    return (
      // <td key={cell.column.id} style={styles} className="flex-y-center" ref={setNodeRef}>
      <td style={styles} className={`table__cell flex-y-center ${className}`}>
        {!isAddRow && dragHandler}
        {flexRender(cell.column.columnDef.cell, cell.getContext())}
      </td>
    )
  },
)

interface TableBodyProps {
  table: Table<any>
  tableContainerRef: React.RefObject<HTMLDivElement>
  scaleRatio: number
  tableRows: Row<TodoInfo>[]
}

function TableBody({ table, tableContainerRef, scaleRatio, tableRows: rows }: TableBodyProps) {
  const count = rows.length
  // Important: Keep the row virtualizer in the lowest component possible to avoid unnecessary re-renders.
  const rowVirtualizer = useVirtualizer<HTMLDivElement, HTMLTableRowElement>({
    count: rows.length,
    estimateSize: index => {
      return 40 * window.fontSizeScaleRatio
    },
    getScrollElement: () => tableContainerRef.current,
    measureElement:
      typeof window !== 'undefined' && navigator.userAgent.indexOf('Firefox') === -1
        ? element => element?.getBoundingClientRect().height
        : undefined,
    overscan: 10,
  })

  useEffect(() => {
    rowVirtualizer.measure()
  }, [scaleRatio])

  useEffect(() => {
    window.virtualizer = rowVirtualizer

    POPOBridgeEmitter.addListener(EnumEmitter.TanstackSoscrollToIndex, index => {
      if (index <= 2) {
        rowVirtualizer.scrollToIndex?.(0, {
          align: 'end',
        })
      } else {
        const addLine = document.querySelector('.task-tr-add')
        if (addLine) {
          addLine.scrollIntoView({ block: 'nearest' })
        } else {
          rowVirtualizer.scrollToIndex?.(index + 3, {
            align: 'end',
          })
        }
      }
    })

    return () => {
      POPOBridgeEmitter.removeAllListeners(EnumEmitter.TanstackSoscrollToIndex)
    }
  }, [rowVirtualizer, rows])

  const virtualRows = rowVirtualizer.getVirtualItems()

  return (
    <tbody
      id="tanstack-table__body"
      className="tanstack-table__tbody"
      style={{
        display: 'grid',
        height: `${rowVirtualizer.getTotalSize() + 1}px`,
        position: 'relative',
      }}
    >
      {virtualRows.map(virtualRow => {
        const row = rows[virtualRow?.index] as Row<TodoInfo>

        const original = row.original

        // if (!isTaskGotPermission(original.taskId)) {
        //   batchRequestRef.current.add(original);
        // }

        // Use a stable key that won't change when rows are inserted
        // For add rows, use a special prefix to ensure they get a fresh render
        const rowKey = original._rowType === TaskTableRowType.add ? `add-row-${original.taskId}` : original.taskId

        return (
          <MemoizedTableBodyRow
            table={table}
            row={row}
            count={count}
            key={rowKey}
            virtualRow={virtualRow}
            rowVirtualizer={rowVirtualizer}
          />
        )
      })}
    </tbody>
  )
}

interface TableBodyRowProps {
  table: Table<any>
  row: Row<TodoInfo>
  virtualRow?: VirtualItem
  rowVirtualizer: Virtualizer<HTMLDivElement, HTMLTableRowElement>
  count: number
}

function TableBodyRow({ row, virtualRow = {}, count }: TableBodyRowProps) {
  const { rowsDraggable, onRowClick, onRowMouseDown, containerWidth } = useTanstackTableContext()
  const { currentViewTab } = useSelector((state: any) => {
    return {
      currentViewTab: state.viewSetting.currentViewTab,
    }
  })
  const taskId = row.original.taskId

  const currId = taskId || row.original.groupId

  const parentId = row.original.parentId
  const dragDisabled = !rowsDraggable && !parentId
  const { over, index, activeIndex, setNodeRef, isDragging, attributes, listeners } = useSortable({
    id: currId,
    data: {
      type: 'row',
      row: row,
      depth: row.depth,
      parentId,
    },
    disabled: dragDisabled,
    strategy: verticalListSortingStrategy,
  })

  const insertPosition = !dragDisabled
    ? getInsertPosition({
        overItemData: over,
        currentId: taskId,
        currentIndex: index,
        activeIndex,
      })
    : ''

  const style: CSSProperties = {
    position: 'absolute',
    top: `${virtualRow.start}px`,
    zIndex: Math.max(1, (count || 1) - (virtualRow?.index || 0) + 2),
    opacity: isDragging ? 0.4 : 1,
    width: '100%',
  }

  const isGroupRow = !!(row.original as TodoInfo).groupBy
  const isAddBtnRow = row.original._rowType === TaskTableRowType.addBtn
  const isAddRow = row.original._rowType === TaskTableRowType.add

  const isExpanded = row.getIsExpanded()

  const { querySort, queryGroupBy } = currentViewTab
  const calculatedPaddingLeft = () => {
    if (!dragDisabled && !parentId) return 24
    // 因为自定义筛选会在前面加一个拖拽区域
    if (!dragDisabled && parentId && querySort?.fieldName === 'customize' && !_.isEmpty(queryGroupBy?.fieldName))
      return 0
    if (!dragDisabled && parentId && querySort?.fieldName === 'customize') return 24
    return 0
  }

  const renderCells = () => {
    if (isGroupRow || isAddBtnRow || isAddRow) {
      const cell = row.getVisibleCells()[0]
      return (
        <DragAlongCell
          key={`${cell.row.original.taskId}-${cell.column.id}`}
          cell={cell}
          className="single__cell"
          style={{
            width: (containerWidth || 120) - 34 * window.fontSizeScaleRatio,
            position: 'sticky',
            left: 0,
            paddingLeft: calculatedPaddingLeft(),
          }}
        />
      )
      // return (
      //   <SortableContext
      //     key={cell.id}
      //     items={columnOrder || []}
      //     strategy={horizontalListSortingStrategy}
      //   >
      //     <DragAlongCell key={cell.id} cell={cell} style={{ width: '100%' }} />
      //   </SortableContext>
      // );
    }
    return row.getVisibleCells().map(cell => {
      const original = cell.row.original
      let dragHandler = null
      if (cell.column.id === 'taskName') {
        if (!dragDisabled) {
          dragHandler = (
            <div
              {...attributes}
              {...listeners}
              className={classNames('drag__handler flex-y-center', {
                only_subtasks: parentId && !rowsDraggable,
              })}
            >
              <OperateMove className="fs-16" />
            </div>
          )
        }
      }
      return <DragAlongCell key={`${original.taskId}-${cell.column.id}`} cell={cell} dragHandler={dragHandler} />
      // return (
      //   <SortableContext
      //     key={cell.id}
      //     items={columnOrder || []}
      //     strategy={horizontalListSortingStrategy}
      //   >
      //     <DragAlongCell key={cell.id} cell={cell} dragHandler={dragHandler} />
      //   </SortableContext>
      // );
    })
  }

  const isAddingRow = row.original._rowType === TaskTableRowType.add

  return (
    <tr
      id={currId + ''}
      data-index={virtualRow?.index}
      ref={setNodeRef}
      className={classNames('tanstack-table__tr', 'flex-y-center', insertPosition, {
        dragging: isDragging,
        'task-tr-add': isAddingRow,
        active: isAddingRow,
        'task-tr-add-btn': row.original._rowType === TaskTableRowType.addBtn,
        'tanstack-table__tr--expanded': isExpanded,
      })}
      key={currId}
      style={{
        position: 'absolute',
        width: '100%',
        height: 40 * window.fontSizeScaleRatio,
        ...style,
      }}
      onClick={onRowClick}
      onMouseDown={onRowMouseDown}
    >
      {renderCells()}
    </tr>
  )
}

const MemoizedTableBodyRow = React.memo(TableBodyRow)

// 主表格组件
function TanstackTable({
  columns,
  data,
  initialPinLeft = ['taskName'],
  tableClassName = 'tanstack-table',
  wrapperClassName = 'tanstack-table__wrapper',
  sortOrder,
  onSortChange,
  expandedKeysMap,
  rowsDraggable = false,
  onDragEnd,
  emptyNodeProps,
  emptySearchDataProps,
  total,
  onRowClick,
  onRowMouseDown,
  onExpandedChange,
  extra,
}: TanstackTableProps<TodoInfo>) {
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [activeItem, setActiveItem] = useState<TodoInfo | null>(null)
  const [dataIds, setDataIds] = useState<number[]>([])
  const navigatorId = store.getState().viewSetting.navigatorId
  const tableRectRef = useRef<DOMRect>(undefined)
  // 获取全局字体大小
  const [scaleRatio, setScaleRatio] = useState(window.fontSizeScaleRatio)

  const columnVisibility = useMemo(() => {
    return columns.reduce((acc, column) => {
      acc[column.accessorKey || column.id] = true
      return acc
    }, {} as Record<string, boolean>)
  }, [columns])

  const [expanded, setExpanded] = React.useState<ExpandedState>(expandedKeysMap || {})

  const table = useReactTable({
    data,
    columns,
    state: { expanded },
    initialState: {
      columnPinning: { left: initialPinLeft },
      sorting: [],
      columnVisibility,
      columnSizing: {},
    },
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: newExpanded => {
      const expandedState = typeof newExpanded === 'function' ? newExpanded(expanded) : newExpanded

      setExpanded(expandedState)

      // 更新localStorage
      onExpandedChange ? onExpandedChange(expandedState) : setExpandedIndexMap(expandedState as Record<string, boolean>)

      return expandedState
    },
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
    enableColumnPinning: true,
    getSubRows: row => {
      return (row.subtask || []) as TodoInfo[]
    },
  })

  useEffect(() => {
    const autoExpandIndexes = autoExpandData.getExpandIndexes()
    const prevExpanded = getExpandedIndexMap()
    const newExpanded = { ...(expandedKeysMap || {}), ...prevExpanded, ...autoExpandIndexes }

    setExpandedIndexMap(newExpanded)

    setExpanded(newExpanded)
  }, [expandedKeysMap, data])

  useEffect(() => {
    window.tanstackTable = table
  }, [table])

  const columnSizeVars = useMemo(() => {
    const headers = table.getFlatHeaders()
    const columnsWidth = getColumnsWidth({
      headers,
      containerEl: tableContainerRef.current,
      columnSizingInfo: table.getState().columnSizingInfo,
      actionType: !table.getState().columnSizingInfo.isResizingColumn ? 'init' : undefined,
      navigatorId,
    })
    return columnsWidth
  }, [table.getState().columnSizingInfo, table.getState().columnSizing, columns, tableContainerRef.current])

  // 根据窗口大小动态设置columnSizeVars
  useEffect(() => {
    const headers = table.getFlatHeaders()
    const handleResize = () => {
      const columnsWidth = getColumnsWidth({
        headers,
        containerEl: tableContainerRef.current,
        actionType: 'window_resize',
        navigatorId,
      })
      // 通过dom api的方式更新
      if (tableContainerRef.current) {
        Object.keys(columnsWidth).forEach(key => {
          tableContainerRef.current.style.setProperty(key, columnsWidth[key])
        })
      }

      tableRectRef.current = tableContainerRef.current?.getBoundingClientRect()
      if (window.fontSizeScaleRatio !== scaleRatio) {
        setScaleRatio(window.fontSizeScaleRatio)
      }
    }
    tableRectRef.current = tableContainerRef.current?.getBoundingClientRect()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [table, navigatorId, scaleRatio])

  const containerWidth = tableRectRef.current?.width || 1200

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event
      const rowData = active?.data.current?.row as Row<TodoInfo>
      const original = rowData?.original
      if (original?.parentId) {
        const parent = getTaskByIndexes({
          indexes: rowData.id.split('.'),
          id: rowData.id,
          list: data,
          secondLast: true,
        })
        const subtaskIds = parent?.subtask?.map(item => item.taskId)
        setDataIds(subtaskIds || [])
      } else {
        const ids: number[] = []
        data.forEach(item => {
          if (typeof item.taskId === 'number') {
            ids.push(item.taskId)
          }
        })
        setDataIds(ids)
      }
      setActiveItem(active.data.current?.row)
    },
    [data],
  )

  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { active, over } = event
      const activeOriginal = active?.data.current?.row?.original
      const rowData = over?.data.current?.row as Row<TodoInfo>
      if (activeOriginal && !activeOriginal.parentId && rowData && rowData.getIsExpanded()) {
        rowData.toggleExpanded(false)
      }
    },
    [expandedKeysMap],
  )

  const handleTableScroll = useCallback((e: React.UIEvent<HTMLTableElement>) => {
    requestAnimationFrame(() => {
      const scrollLeft = e.target.scrollLeft
      const tableEl = tableContainerRef.current?.querySelector('#tanstack-table')
      if (tableEl) {
        tableEl.classList.toggle('horizon-scrolled', scrollLeft > 0)
      }
    })
  }, [])

  const tableRows = table.getRowModel().rows
  const newTotal = total || tableRows.length

  const content = (
    <div
      id="tanstack-table__wrapper_id"
      ref={tableContainerRef}
      className={wrapperClassName}
      style={{
        ...columnSizeVars,
        // width: table.getTotalSize(),
      }}
      onScroll={handleTableScroll}
    >
      <table className={classNames(tableClassName, 'tanstack-table')} id="tanstack-table">
        <thead className="tanstack-table__thead" style={{ zIndex: (newTotal || 0) + 2 }}>
          {table.getHeaderGroups().map(headerGroup => (
            <tr className="tanstack-table__tr" key={headerGroup.id}>
              {/* <SortableContext items={columnOrder} strategy={horizontalListSortingStrategy}> */}
              {headerGroup.headers.map(header => (
                <DraggableTableHeader key={header.id} header={header} />
              ))}
              {/* </SortableContext> */}
            </tr>
          ))}
        </thead>
        <SortableContext
          // disabled={!rowsDraggable}
          items={dataIds || []}
          strategy={verticalListSortingStrategy}
        >
          <TableBody
            tableRows={tableRows}
            table={table}
            tableContainerRef={tableContainerRef}
            scaleRatio={scaleRatio}
          />
        </SortableContext>
        <ListTableNoData noDataProps={emptyNodeProps} noSearchDataProps={emptySearchDataProps} />
      </table>
      {activeItem && (
        <DragOverlay
          dropAnimation={null}
          zIndex={10000}
          style={{ left: tableRectRef.current?.left, width: containerWidth, overflow: 'hidden' }}
        >
          <div
            style={{
              pointerEvents: 'none',
            }}
          >
            <MemoizedTableBodyRow table={table} row={activeItem as Row<TodoInfo>} rowVirtualizer={null as any} />
          </div>
        </DragOverlay>
      )}
      {extra}
    </div>
  )

  return (
    <TanstackTableContext.Provider
      value={{
        sortOrder,
        onSortChange,
        columnOrder: [],
        rowsDraggable,
        total: newTotal,
        onRowClick,
        onRowMouseDown,
        containerWidth,
      }}
    >
      <SortableContainer onDragStart={handleDragStart} onDragEnd={onDragEnd} onDragOver={handleDragOver} data={data}>
        {content}
      </SortableContainer>
    </TanstackTableContext.Provider>
  )
}

export default memo(TanstackTable)

interface ISortableContainerProps {
  onDragStart?: (event: DragStartEvent) => void
  onDragEnd?: TanstackTableProps<TodoInfo>['onDragEnd']
  onDragOver?: (event: DragOverEvent) => void
  data?: TodoInfo[]
}

const SortableContainer: FC<PropsWithChildren<ISortableContainerProps>> = ({
  onDragStart,
  onDragOver,
  onDragEnd,
  children,
  data,
}) => {
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event
      if (active && over && active.id !== over.id) {
        if (`${over.id}`.includes(TaskTableRowTypeAdd)) {
          return
        }
        const activeRow = active.data.current?.row as Row<TodoInfo>
        const overRow = over.data.current?.row as Row<TodoInfo>
        const activeIndex = Number(activeRow?.index)
        const overIndex = Number(overRow?.index)
        let preIndex
        let nextIndex
        if (activeIndex > overIndex) {
          preIndex = overIndex - 1
          nextIndex = overIndex
        } else {
          preIndex = overIndex
          nextIndex = overIndex + 1
        }

        let moveList = data
        const isSubtask = typeof activeRow.original.parentId === 'number'
        let parent = null
        if (isSubtask) {
          parent = getTaskByIndexes({
            indexes: activeRow.id.split('.'),
            list: data,
            id: activeRow.id,
            secondLast: true,
          })
          moveList = parent?.subtask as TodoInfo[]
        }

        const preTaskId = moveList![preIndex]?.taskId!
        const tailTaskId = moveList![nextIndex]?.taskId!

        let newList = arrayMove(moveList || [], activeIndex, overIndex)
        if (isSubtask) {
          if (parent?.subtask) {
            parent.subtask = [...newList]
            newList = data || []
          }
        }

        onDragEnd?.({
          list: newList,
          activeIndex,
          overIndex,
          preTaskId: typeof preTaskId === 'number' ? preTaskId : undefined,
          tailTaskId: typeof tailTaskId === 'number' ? tailTaskId : undefined,
          activeTaskId: active.id as number,
          parentId: isSubtask ? parent?.taskId : undefined,
          activeParent: parent,
        })
      }
    },
    [data],
  )

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 1,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        distance: 1,
      },
    }),
    useSensor(KeyboardSensor, {}),
  )

  const sameParentCollisionDetection = useCallback((args: any) => {
    // 使用默认的碰撞检测算法获取所有可能的碰撞
    const collisions = pointerWithin(args)

    if (!collisions.length) return collisions

    // 获取当前拖拽项的数据
    const activeData = args.active.data.current
    if (!activeData) return collisions

    const activeDepth = activeData.depth

    // 过滤出只有相同父级ID的碰撞项
    return collisions.filter(collision => {
      const overData = args.droppableContainers.find(item => item.id === collision.id)?.data.current
      if (!overData) return false

      return overData.depth === activeDepth && overData.parentId === activeData.parentId
    })
  }, [])

  return (
    <DndContext
      collisionDetection={sameParentCollisionDetection}
      modifiers={[restrictToVerticalAxis]}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragEnd={handleDragEnd}
      sensors={sensors}
      // 添加测量策略以确保正确检测碰撞
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.WhileDragging,
        },
      }}
    >
      {children}
    </DndContext>
  )
}
