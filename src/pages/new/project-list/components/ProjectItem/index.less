.item__container {
  display: grid;
  grid-template-columns: minmax(3.2rem, 1fr) 1.6rem 1.6rem 1.8rem 0.8rem;
  align-items: center;
  column-gap: 16px;
  row-gap: 8px;
  padding-right: 20px;
  background-color: transparent;
  flex: 1;
  height: 50px;
}

.columName {
  display: flex;
  align-items: center;
  column-gap: 12px;
  overflow: hidden;
  height: 50px;

  .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--TextPrimary-strong);
  }

  .creating__input {
    position: absolute;
    z-index: 1;
    height: 100%;
    width: 100%;

    :global {
      .rock-input {
        color: var(--TextPrimary-strong);
        font-size: 14px;
      }
    }

    &:global(.rock-input-wrapper) {
      border-radius: 0;
    }

    &::before {
      border-radius: 0;
    }
  }

  &.editing {
    position: relative;
    width: 100%;
  }
}

.item__placeholder {
  font-size: 12px;
  line-height: 18px;
  color: var(--TextTertiary, #11111180);
  height: 34px;
}

.projectIcon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}
.time {
  line-height: 22px;
  font-size: 13px;
  color: var(--TextSecondary);
}

.members {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  cursor: pointer;
  padding-left: 6px;
}

.keyword {
  font-weight: bold;
  color: var(--TextPrimary);
}
