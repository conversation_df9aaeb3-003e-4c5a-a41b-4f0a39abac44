import { Permission, TaskPermission, ViewPermission } from '@/types';

import { EnumRole } from './const';
import I18N from './I18N';

export enum PermissionObjectType {
  TASK = 'TASK',
  PROJECT = 'PROJECT',
  VIEW = 'VIEW',
}

/**
 * 项目权限枚举
 */
export enum ProjectPermissionEnum {
  /**
   * 可查看项目
   */
  CAN_VIEW = 'CAN_VIEW',
  /**
   * 可编辑项目
   */
  CAN_EDIT = 'CAN_EDIT',
  /**
   * 可增加管理员
   */
  CAN_ADD_MANAGER = 'CAN_ADD_MANAGER',
  /**
   * 可增加成员
   */
  CAN_ADD_EDITOR = 'CAN_ADD_EDITOR',
  /**
   * 可增加仅查看
   */
  CAN_ADD_VIEWER = 'CAN_ADD_VIEWER',
  /**
   * 可移除管理员
   */
  CAN_REMOVE_MANAGER = 'CAN_REMOVE_MANAGER',
  /**
   * 可移除成员
   */
  CAN_REMOVE_EDITOR = 'CAN_REMOVE_EDITOR',
  /**
   * 可移除仅查看
   */
  CAN_REMOVE_VIEWER = 'CAN_REMOVE_VIEWER',
  /**
   * 可退出项目
   */
  CAN_EXIT = 'CAN_EXIT',
  /**
   * 可删除项目
   */
  CAN_DELETE = 'CAN_DELETE',
  /**
   * 可编辑成员
   */
  CAN_EDIT_MEMBER = 'CAN_EDIT_MEMBER',
  /**
   * 可分享项目
   */
  CAN_SHARE = 'CAN_SHARE',
  /**
   * 可创建群聊
   */
  CAN_CREATE_IM_TEAM = 'CAN_CREATE_IM_TEAM',
  /**
   * 可进入群聊
   */
  CAN_ENTER_IM_TEAM = 'CAN_ENTER_IM_TEAM',
  /**
   * 可新建日程
   */
  CAN_CREATE_SCHEDULE = 'CAN_CREATE_SCHEDULE',
  /**
   * 可变更项目状态
   */
  CAN_SET_STATE = 'CAN_SET_STATE',
  /**
   * 可查看任务列表
   */
  CAN_VIEW_TASK_LIST = 'CAN_VIEW_TASK_LIST',
  /**
   * 可查看任务
   */
  CAN_VIEW_TASK = 'CAN_VIEW_TASK',
  /**
   * 可编辑任务
   */
  CAN_EDIT_TASK = 'CAN_EDIT_TASK',
  /**
   * 可修改任务完成方式
   */
  CAN_SET_TASK_COMPLETE_MODE = 'CAN_SET_TASK_COMPLETE_MODE',
  /**
   * 可完成任务
   */
  CAN_COMPLETE_TASK = 'CAN_COMPLETE_TASK',
  /**
   * 可分享任务
   */
  CAN_SHARE_TASK = 'CAN_SHARE_TASK',
  /**
   * 可评论任务
   */
  CAN_COMMENT_TASK = 'CAN_COMMENT_TASK',
  /**
   * 可创建任务
   */
  CAN_CREATE_TASK = 'CAN_CREATE_TASK',
  /**
   * 可删除任务
   */
  CAN_REMOVE_TASK = 'CAN_REMOVE_TASK',
  /**
   * 可新增任务
   */
  CAN_ADD_TASK = 'CAN_ADD_TASK',
  /**
   * 管理字段权限
   */
  CAN_MANAGE_FIELD = 'CAN_MANAGE_FIELD',
  /**
   * 管理视图权限
   */
  CAN_MANAGE_VIEW = 'CAN_MANAGE_VIEW',
  /**
   * 创建公共视图权限
   */
  CAN_CREATE_COMMON_VIEW = 'CAN_CREATE_COMMON_VIEW',
  /**
   * 管理公共视图权限
   */
  CAN_MANAGE_COMMON_VIEW = 'CAN_MANAGE_COMMON_VIEW',
  /**
   * 创建个人视图权限
   */
  CAN_CREATE_PERSON_VIEW = 'CAN_CREATE_PERSON_VIEW',
  /**
   * 管理个人视图权限
   */
  CAN_MANAGE_PERSON_VIEW = 'CAN_MANAGE_PERSON_VIEW',
  /**
   * 管理消息通知
   */
  //CAN_MANAGE_NOTIFICATION = 'CAN_MANAGE_NOTIFICATION',
}

//项目详情界面使用
export const ProjectDetailPermissions = [
  // 查看项目
  ProjectPermissionEnum.CAN_VIEW,
  // 编辑项目
  ProjectPermissionEnum.CAN_EDIT,
  // 管理字段
  ProjectPermissionEnum.CAN_MANAGE_FIELD,
  // 变更项目状态
  ProjectPermissionEnum.CAN_SET_STATE,
  // 可创建任务
  ProjectPermissionEnum.CAN_CREATE_TASK,
  // 可分享项目
  ProjectPermissionEnum.CAN_SHARE,
  // 创建公共视图权限
  ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
  // 创建个人视图权限
  ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW,
  //项目详情头部 更多按钮需要校验权限,展示提示
  // 可创建群聊
  ProjectPermissionEnum.CAN_CREATE_IM_TEAM,
  //可进入群聊
  ProjectPermissionEnum.CAN_ENTER_IM_TEAM,
  //管理消息通知
  //ProjectPermissionEnum.CAN_MANAGE_NOTIFICATION,
  //退出项目
  ProjectPermissionEnum.CAN_EXIT,
  ProjectPermissionEnum.CAN_ADD_MANAGER,
  ProjectPermissionEnum.CAN_ADD_EDITOR,
  ProjectPermissionEnum.CAN_ADD_VIEWER,
  ProjectPermissionEnum.CAN_REMOVE_MANAGER,
  ProjectPermissionEnum.CAN_REMOVE_EDITOR,
  ProjectPermissionEnum.CAN_REMOVE_VIEWER,
  ProjectPermissionEnum.CAN_EDIT_MEMBER,
];

//左侧导航项目更多操作使用
export const ProjectMenuPermissions = [
  // 编辑项目
  ProjectPermissionEnum.CAN_EDIT,
  // 变更项目状态
  ProjectPermissionEnum.CAN_SET_STATE,
  //邀请成员 管理员
  ProjectPermissionEnum.CAN_ADD_MANAGER,
  //邀请成员 项目成员
  ProjectPermissionEnum.CAN_ADD_EDITOR,
  //邀请成员 仅查看
  ProjectPermissionEnum.CAN_ADD_VIEWER,
  //移除 管理员
  ProjectPermissionEnum.CAN_REMOVE_MANAGER,
  //移除 项目成员
  ProjectPermissionEnum.CAN_REMOVE_EDITOR,
  //移除 仅查看
  ProjectPermissionEnum.CAN_REMOVE_VIEWER,
  // 分享
  ProjectPermissionEnum.CAN_SHARE,
];
//项目更多操作使用
export const ProjectListPermissions = [
  // 编辑项目
  ProjectPermissionEnum.CAN_EDIT,
  // 变更项目状态
  ProjectPermissionEnum.CAN_SET_STATE,
  //邀请成员 管理员
  ProjectPermissionEnum.CAN_ADD_MANAGER,
  //邀请成员 项目成员
  ProjectPermissionEnum.CAN_ADD_EDITOR,
  //邀请成员 仅查看
  ProjectPermissionEnum.CAN_ADD_VIEWER,
  //移除 管理员
  ProjectPermissionEnum.CAN_REMOVE_MANAGER,
  //移除 项目成员
  ProjectPermissionEnum.CAN_REMOVE_EDITOR,
  //移除 仅查看
  ProjectPermissionEnum.CAN_REMOVE_VIEWER,
  // 分享
  ProjectPermissionEnum.CAN_SHARE,
  //退出项目
  ProjectPermissionEnum.CAN_EXIT,
  //删除项目
  ProjectPermissionEnum.CAN_DELETE,
  //管理消息通知
  //ProjectPermissionEnum.CAN_MANAGE_NOTIFICATION,
];

/**
 * 任务对象枚举
 */
export enum TaskPermissionEnum {
  /**
   * 可查看任务
   */
  CAN_VIEW = 'CAN_VIEW',
  /**
   * 可编辑任务
   */
  CAN_EDIT = 'CAN_EDIT',
  /**
   * 可删除任务
   */
  CAN_DELETE = 'CAN_DELETE',
  /**
   * 转移任务所有者权限
   */
  CAN_TRANSFER = 'CAN_TRANSFER',
  /**
   * 设置任务完成方式权限
   */
  CAN_SET_COMPLETE_MODE = 'CAN_SET_COMPLETE_MODE',
  /**
   * 完成任务权限
   */
  CAN_COMPLETE = 'CAN_COMPLETE',
  /**
   * 添加到项目权限
   */
  CAN_ADD_TO_PROJECT = 'CAN_ADD_TO_PROJECT',
  /**
   * 移除项目
   */
  CAN_REMOVE_FROM_PROJECT = 'CAN_REMOVE_FROM_PROJECT',
  /**
   * 分享任务权限
   */
  CAN_SHARE = 'CAN_SHARE',
  /**
   * 评论任务权限
   */
  CAN_COMMENT = 'CAN_COMMENT',
  /**
   * 同步任务到日历权限
   */
  CAN_SYNC_TO_CALENDAR = 'CAN_SYNC_TO_CALENDAR',
  /**
   * 权限: 不能关注
   */
  CAN_NOT_OBSERVER = 'CAN_NOT_OBSERVER',
  /**
   * 取消关注权限
   */
  CAN_CANCEL_OBSERVER = 'CAN_CANCEL_OBSERVER',
  /**
   * 从项目中查看任务的权限
   */
  CAN_VIEW_FROM_PROJECT = 'CAN_VIEW_FROM_PROJECT',
  /**
   * 从项目中编辑任务的权限
   */
  CAN_EDIT_FROM_PROJECT = 'CAN_EDIT_FROM_PROJECT',
}

export const TaskDetailPermissions = [
  // 可查看任务
  // TaskPermissionEnum.CAN_VIEW,
  // 能否编辑任务
  TaskPermissionEnum.CAN_EDIT,
  // 能否删除任务
  TaskPermissionEnum.CAN_DELETE,
  // 是否有完成任务权限
  TaskPermissionEnum.CAN_COMPLETE,
  // 设置任务完成方式权限
  TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
  // 转移任务所有者权限
  TaskPermissionEnum.CAN_TRANSFER,
  // 评论任务权限
  TaskPermissionEnum.CAN_COMMENT,
  // 同步任务到日历权限
  TaskPermissionEnum.CAN_SYNC_TO_CALENDAR,
  // 添加到项目权限
  TaskPermissionEnum.CAN_ADD_TO_PROJECT,
  // 移除项目
  TaskPermissionEnum.CAN_REMOVE_FROM_PROJECT,
  // 分享任务权限
  TaskPermissionEnum.CAN_SHARE,
  //权限: 不能关注
  TaskPermissionEnum.CAN_NOT_OBSERVER,
  // 取消关注权限
  TaskPermissionEnum.CAN_CANCEL_OBSERVER,
  TaskPermissionEnum.CAN_VIEW,
];

export const TaskListPermissions = [
  //是否有完成任务权限
  TaskPermissionEnum.CAN_COMPLETE,
  //能否编辑任务
  TaskPermissionEnum.CAN_EDIT,
  //可查看任务
  //TaskPermissionEnum.CAN_VIEW,
];

export enum ViewTypeEnum {
  System = 1,
  Personal = 2,
  Common = 3,
}

/**
 * 视图对象枚举
 */
export enum ViewPermissionEnum {
  /**
   * 公共视图编辑权限
   */
  CAN_EDIT_COMMON_VIEW = 'CAN_EDIT_COMMON_VIEW',
  /**
   * 公共视图删除权限
   */
  CAN_DELETE_COMMON_VIEW = 'CAN_DELETE_COMMON_VIEW',
  /**
   * 个人视图编辑权限
   */
  CAN_EDIT_PERSON_VIEW = 'CAN_EDIT_PERSON_VIEW',
  /**
   * 个人视图删除权限
   */
  CAN_DELETE_PERSON_VIEW = 'CAN_DELETE_PERSON_VIEW',
}

type ValidatesPermissionParams = {
  permissions?: Permission[] | TaskPermission[] | ViewPermission[];
  key:
    | ProjectPermissionEnum
    | ProjectPermissionEnum[]
    | TaskPermissionEnum
    | TaskPermissionEnum[]
    | ViewPermissionEnum
    | ViewPermissionEnum[];
  anyOne?: boolean;
};

/**
 * 校验是否拥有某个权限 或者一批权限中的所有权限
 * @param permissions 当前拥有的权限列表
 * @param key 需要校验的权限  ProjectPermissionEnum | ProjectPermissionEnum[]
 * @returns
 */
export const validatesPermission = (opt: ValidatesPermissionParams) => {
  const { permissions, key } = opt;
  const permissionsMap =
    permissions?.reduce((pre, cur) => {
      pre[cur.name] = cur.value;
      return pre;
    }, {} as Record<string, boolean>) || {};
  if (Array.isArray(key)) {
    return key.map((item) => !!permissionsMap[item]);
  } else {
    return !!permissionsMap[key];
  }
};

export const Role_Admin = {
  name: I18N.auto.administrators,
  value: EnumRole.admin,
  desc: I18N.auto.canModifyAll,
};

export const Role_Members = {
  name: I18N.auto.projectMembers,
  value: EnumRole.members,
  desc: I18N.auto.modifyExceptProject,
};

export const Role_ViewOnly = {
  name: I18N.auto.viewOnly,
  value: EnumRole.viewOnly,
  desc: I18N.auto.onlyViewingIsNotAllowed,
};

export const RoleMemberMap: Record<EnumRole, { name: string }> = {
  [EnumRole.admin]: Role_Admin,
  [EnumRole.members]: Role_Members,
  [EnumRole.viewOnly]: Role_ViewOnly,
};

export const RoleList = [Role_Admin, Role_Members, Role_ViewOnly];
