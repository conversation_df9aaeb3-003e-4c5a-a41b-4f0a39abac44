import { Plus } from '@bedrock/icons-react'
import s from '../index.less'
import { OperateOpentask14 } from '@babylon/popo-icons'
import I18N from '@/utils/I18N'
import { TodoInfo } from '@/types'
import { FC } from 'react'
import { useDispatch } from 'react-redux'
import { Dispatch } from '@/models/store'
import { Row } from '@tanstack/react-table'
import { Tooltip } from '@/components/basic'
import { checkSubtaskOverLimit } from '../../../utils'
import { getIsInSession, getIsSessionTasks } from '@/models/utils'
import { TASK_BASE_INFO } from '@/hooks/useGetGroupedList/utils'

interface IRenderOperationProps {
  taskInfo?: TodoInfo
  tableRowData?: Row<any>
  showViewBtn?: boolean
  disabledView?: boolean
  disabled?: boolean
  showViewText?: boolean
}
const RenderOperation: FC<IRenderOperationProps> = props => {
  const { showViewBtn = true, disabledView, disabled, showViewText = true } = props

  if (disabledView && disabled) {
    return null
  }

  return (
    <div className={`${s.taskname__operatoin} taskname__operatoin`}>
      {!disabled && <AddSubRow {...props} />}
      {!disabledView && showViewBtn && (
        <div className={s.titleWarpRight} style={{ padding: `0.03rem ${showViewText ? '0.06rem' : '0.03rem'}` }}>
          <OperateOpentask14 className={s.openIcon} />
          {showViewText && I18N.auto.view}
        </div>
      )}
    </div>
  )
}

// 子任务不能新增，即有parentId的不展示新增
const AddSubRow: FC<IRenderOperationProps> = ({ taskInfo, tableRowData }) => {
  if (taskInfo?.parentId) {
    return null
  }

  const dispatch = useDispatch<Dispatch>()

  const handleAddSubRow = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    if (checkSubtaskOverLimit(taskInfo?.subtask as TodoInfo[])) {
      return
    }
    tableRowData?.toggleExpanded(true)

    const model = getIsSessionTasks() ? dispatch.imTodoList : dispatch.task

    // 新增子任务
    model.addSubLine({
      taskInfo: { ...taskInfo, ...TASK_BASE_INFO },
      tableRowData,
      isSubTask: true,
      addItemData: {
        placeholder: I18N.auto.subTaskPlaceholder_2,
      },
    })
  }
  return (
    <Tooltip
      mouseLeaveDelay={0}
      mouseEnterDelay={0}
      title={I18N.auto.newSubtask}
      placement="top"
      trigger="hover"
      className={s.addSubRow}
      transitionName=""
    >
      <div className={`${s.titleWarpRight} ${s.square} add_subtask`} onClick={handleAddSubRow}>
        <Plus className={`${s.openIcon} add_subtask--icon`}></Plus>
      </div>
    </Tooltip>
  )
}

export default RenderOperation
