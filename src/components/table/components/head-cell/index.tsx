import classNames from 'classnames';
import React, { CSSProperties, PropsWithChildren, useMemo } from 'react';

import { Icon, Tooltip } from '@/components/basic';
import { Order } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  style?: CSSProperties;
  sortable?: boolean;
  onChange?: () => void;
  order?: Order;
  active?: boolean;
  onSortChange?: (order: Order | undefined) => void;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  tooltip?: React.ReactNode;
};
const TableCell: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    children,
    className = 'todo-th',
    style,
    order,
    sortable,
    onSortChange,
    active,
    prefix,
    suffix,
    tooltip,
  } = props;
  const changeOrder = () => {
    if (!sortable) {
      return;
    }
    let newValue;
    if (order === undefined) {
      newValue = Order.asc;
    }
    if (order === Order.asc) {
      newValue = Order.desc;
    }
    if (order === Order.desc) {
      newValue = Order.asc;
    }
    onSortChange?.(newValue);
  };
  const tip = useMemo(() => {
    return order === Order.asc ? I18N.auto.clickOnDescendingOrder : I18N.auto.clickOnAscendingOrder;
  }, [order]);
  return (
    <div
      className={classNames({ [s.headSort]: sortable }, className)}
      style={style}
      onClick={changeOrder}
    >
      <span className={classNames(s.wrap, { [s.sortableWrap]: sortable })}>
        <div className={s.prefix}>{prefix}</div>
        <div className={s.content}>
          <span className={s.title}>
            {tooltip ? (
              <Tooltip title={tooltip}>
                <div className={s.name}>{children}</div>
              </Tooltip>
            ) : (
              <div className={s.name}>{children}</div>
            )}
            {sortable ? (
              <div className={s.sortBox}>
                <div className={classNames('todo-sort', s.sort, { [s.show]: order && active })}>
                  <span className={classNames({ [s.active]: order === Order.asc }, s.sortIcon)}>
                    <Icon name="icon-sort_up1"></Icon>
                  </span>
                  <span className={classNames({ [s.active]: order === Order.desc }, s.sortIcon)}>
                    <Icon name="icon-sort_down1"></Icon>
                  </span>
                </div>
              </div>
            ) : null}
          </span>
        </div>
        <div className={s.suffix}>{suffix}</div>
      </span>
    </div>
  );
};

export default TableCell;
