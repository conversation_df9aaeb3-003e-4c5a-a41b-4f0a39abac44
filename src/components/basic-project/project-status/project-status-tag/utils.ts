import { ProjectStatus } from '@/types';
import { EnumProjectStatus } from '@/utils/const';
import I18N from '@/utils/I18N';

export const projectStatusOngoing = {
  name: I18N.auto.haveInHand,
  key: EnumProjectStatus.ongoing,
  className: 'com-project-status-ongoing',
  icon: 'icon-pjstate_ing',
};

export const projectStatusPause = {
  name: I18N.auto.suspend,
  key: EnumProjectStatus.pause,
  className: 'com-project-status-pause',
  icon: 'icon-pjstate_ing',
};
export const projectStatusRisk = {
  name: I18N.auto.thereIsARiskInvolved,
  key: EnumProjectStatus.risk,
  className: 'com-project-status-risk',
  icon: 'icon-pjstate_ing',
};
export const projectStatusDone = {
  name: I18N.auto.completed,
  key: EnumProjectStatus.done,
  className: 'com-project-status-done',
  icon: 'icon-pjstate_wancheng',
};
export const projectStatusArchive = {
  name: I18N.auto.file,
  key: EnumProjectStatus.archive,
  className: 'com-project-status-archive',
  icon: 'icon-pjstate_Archive',
};

export const projectStatusList: ProjectStatus[] = [
  projectStatusOngoing,
  projectStatusPause,
  projectStatusRisk,
  projectStatusDone,
  projectStatusArchive,
];
