.icon__wrapper {
  width: 16px;
  height: 16px;
  .arrow__right {
    transform: rotate(-90deg);
  }
}
.fold__collapsed {
  display: none;
}

.arrow__icon {
  display: none;
}

.sortable-tree-item {
  .arrow__down,
  .arrow__right,
  .fold__icon,
  .fold__expanded {
    display: flex;
    align-items: center;
    color: var(--IconPrimary, #1111119e);
  }
  &:not(:has(.rock-input-wrapper)):hover {
    .arrow__icon {
      display: block;
    }
    .fold__icon {
      display: none;
    }
  }
}

.sortable-tree-item__expanded {
  .fold__expanded,
  .arrow__down {
    display: block;
  }

  .fold__collapsed,
  .arrow__right {
    display: none;
  }
}

.sortable-tree-item__collapsed {
  .fold__expanded,
  .arrow__down {
    display: none;
  }
  .fold__collapsed,
  .arrow__right {
    display: block;
  }
}
