const path = require('path');
const fsExtra = require('fs-extra');
const { setConfigPath, onekeypubFileOrFolderListToPath } = require('@popoDrive/gdlc');
const { dist, CDN } = require('./const');
const minimist = require('minimist');
const argv = minimist(process.argv.slice(2));
const { version_tag } = argv;

const projectJson = require('../f2econfig.json');

const rootDir = path.join(__dirname, '../');
let tempDir = resolvePath('.temp');

let CDNTemp = path.join(tempDir, CDN.projectName); // .temp/{projectName}
let assetsTemp = path.join(CDNTemp, version_tag, CDN.assetsDir); // .temp/{projectName}/{versionTag}/dist

function resolvePath(str) {
  return path.join(rootDir, str);
}

const configPath = resolvePath(path.join('publish', 'gdlc_config'));

setConfigPath(configPath);

async function prepareTempDir() {
  await fsExtra.remove(tempDir); //清除旧的编译产物
  await fsExtra.ensureDir(tempDir);
  //js等资源移动至tempdir/assets/
  await fsExtra.copy(resolvePath(`${dist}`), assetsTemp);
}

function publish({ src, publichPath, zipName }) {
  return onekeypubFileOrFolderListToPath({
    filePathList: [src],
    targetPath: publichPath,
    //zip包文件名
    versionName: zipName,
    publishConfig: { yes: true },
  });
}
function publishBundle() {
  return publish({
    src: CDNTemp,
    publichPath: ``,
    zipName: `${projectJson.projectName}-${version_tag}`,
  });
}

//修改dist的目录结构，保持跟cdn的目录结构一致，方便nginx访问
async function copyToDist() {
  await fsExtra.remove(resolvePath(dist));
  await fsExtra.copy(tempDir, resolvePath(dist));
}

async function Run() {
  await prepareTempDir();
  await publishBundle();
  await copyToDist();
}

Run();

process.on('unhandledRejection', (err) => {
  console.log('未处理reject', err);
  process.exit(2);
});
