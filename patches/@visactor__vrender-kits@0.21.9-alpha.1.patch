diff --git a/es/canvas/contributions/browser/context.js b/es/canvas/contributions/browser/context.js
index 6c911d718d06e571be9fef34333845a04798ed83..0da941ebefa042f49a0a2de02164748a2e32ede9 100644
--- a/es/canvas/contributions/browser/context.js
+++ b/es/canvas/contributions/browser/context.js
@@ -17,7 +17,7 @@ const outP = [ 0, 0, 0 ], addArcToBezierPath = (bezierPath, startAngle, endAngle
     for (;ea !== endAngle; ) {
         ea = step > 0 ? Math.min(ea + step, endAngle) : Math.max(ea + step, endAngle);
         const delta = Math.abs(ea - sa), len = 4 * Math.tan(delta / 4) / 3, dir = ea < sa ? -1 : 1, c1 = Math.cos(sa), s1 = Math.sin(sa), c2 = Math.cos(ea), s2 = Math.sin(ea), x1 = c1 * rx + cx, y1 = s1 * ry + cy, x4 = c2 * rx + cx, y4 = s2 * ry + cy, hx = rx * len * dir, hy = ry * len * dir;
-        bezierPath.push([ x1 - hx * s1, y1 + hy * c1, x4 + hx * s2, y4 - hy * c2, x4, y4 ]), 
+        bezierPath.push([ x1 - hx * s1, y1 + hy * c1, x4 + hx * s2, y4 - hy * c2, x4, y4 ]),
         sa = ea;
     }
 };
@@ -129,12 +129,12 @@ let BrowserContext2d = class {
         }), this._clearShadowStyle = !1, this._clearFilterStyle = !1, this._clearGlobalCompositeOperationStyle = !1;
         const context = canvas.nativeCanvas.getContext("2d");
         if (!context) throw new Error("发生错误，获取2d上下文失败");
-        this.nativeContext = context, this.canvas = canvas, this.matrix = new Matrix(1, 0, 0, 1, 0, 0), 
-        this.stack = [], this.dpr = dpr, this.applyedMatrix = new Matrix(1, 0, 0, 1, 0, 0), 
+        this.nativeContext = context, this.canvas = canvas, this.matrix = new Matrix(1, 0, 0, 1, 0, 0),
+        this.stack = [], this.dpr = dpr, this.applyedMatrix = new Matrix(1, 0, 0, 1, 0, 0),
         this._clearMatrix = new Matrix(1, 0, 0, 1, 0, 0), this.baseGlobalAlpha = 1;
     }
     reset() {
-        this.stack.length && Logger.getInstance().warn("可能存在bug，matrix没有清空"), this.matrix.setValue(1, 0, 0, 1, 0, 0), 
+        this.stack.length && Logger.getInstance().warn("可能存在bug，matrix没有清空"), this.matrix.setValue(1, 0, 0, 1, 0, 0),
         this.applyedMatrix = new Matrix(1, 0, 0, 1, 0, 0), this.stack.length = 0, this.nativeContext.setTransform(1, 0, 0, 1, 0, 0);
     }
     getCanvas() {
@@ -144,7 +144,7 @@ let BrowserContext2d = class {
         return this.nativeContext;
     }
     setTransformForCurrent(force = !1) {
-        !force && this.applyedMatrix.equalToMatrix(this.matrix) || (this.applyedMatrix.setValue(this.matrix.a, this.matrix.b, this.matrix.c, this.matrix.d, this.matrix.e, this.matrix.f), 
+        !force && this.applyedMatrix.equalToMatrix(this.matrix) || (this.applyedMatrix.setValue(this.matrix.a, this.matrix.b, this.matrix.c, this.matrix.d, this.matrix.e, this.matrix.f),
         this.nativeContext.setTransform(this.matrix.a, this.matrix.b, this.matrix.c, this.matrix.d, this.matrix.e, this.matrix.f));
     }
     get currentMatrix() {
@@ -154,11 +154,11 @@ let BrowserContext2d = class {
         return matrixAllocate.allocateByObj(m);
     }
     clear() {
-        this.save(), this.resetTransform(), this.nativeContext.clearRect(0, 0, this.canvas.width, this.canvas.height), 
+        this.save(), this.resetTransform(), this.nativeContext.clearRect(0, 0, this.canvas.width, this.canvas.height),
         this.restore();
     }
     restore() {
-        this.nativeContext.restore(), this.stack.length > 0 && (matrixAllocate.free(this.matrix), 
+        this.nativeContext.restore(), this.stack.length > 0 && (matrixAllocate.free(this.matrix),
         this.matrix = this.stack.pop(), this.setTransformForCurrent(!0));
     }
     highPerformanceRestore() {
@@ -182,14 +182,14 @@ let BrowserContext2d = class {
         this.matrix.setScale(sx, sy), setTransform && this.setTransformForCurrent();
     }
     scalePoint(sx, sy, px, py, setTransform = !0) {
-        this.translate(px, py, !1), this.scale(sx, sy, !1), this.translate(-px, -py, !1), 
+        this.translate(px, py, !1), this.scale(sx, sy, !1), this.translate(-px, -py, !1),
         setTransform && this.setTransformForCurrent();
     }
     setTransform(a, b, c, d, e, f, setTransform = !0, dpr = this.dpr) {
         this.matrix.setValue(dpr * a, dpr * b, dpr * c, dpr * d, dpr * e, dpr * f), setTransform && this.setTransformForCurrent();
     }
     setTransformFromMatrix(matrix, setTransform = !0, dpr = this.dpr) {
-        this.matrix.setValue(matrix.a * dpr, matrix.b * dpr, matrix.c * dpr, matrix.d * dpr, matrix.e * dpr, matrix.f * dpr), 
+        this.matrix.setValue(matrix.a * dpr, matrix.b * dpr, matrix.c * dpr, matrix.d * dpr, matrix.e * dpr, matrix.f * dpr),
         setTransform && this.setTransformForCurrent();
     }
     resetTransform(setTransform = !0, dpr = this.dpr) {
@@ -199,7 +199,7 @@ let BrowserContext2d = class {
         this.matrix.multiply(a, b, c, d, e, f), setTransform && this.setTransformForCurrent();
     }
     transformFromMatrix(matrix, setTransform) {
-        this.matrix.multiply(matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f), 
+        this.matrix.multiply(matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f),
         setTransform && this.setTransformForCurrent();
     }
     translate(x, y, setTransform = !0) {
@@ -213,7 +213,7 @@ let BrowserContext2d = class {
         this.translate(x, y, !1), this.rotate(rad, !1), this.translate(-x, -y, !1), setTransform && this.setTransformForCurrent();
     }
     rotateDegreesAbout(deg, x, y, setTransform = !0) {
-        this.translate(x, y, !1), this.rotateDegrees(deg, !1), this.translate(-x, -y, !1), 
+        this.translate(x, y, !1), this.rotateDegrees(deg, !1), this.translate(-x, -y, !1),
         setTransform && this.setTransformForCurrent();
     }
     beginPath() {
@@ -238,12 +238,12 @@ let BrowserContext2d = class {
     bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y, z) {
         if (z = z || 0, this.camera) {
             let cp1z = z, cp2z = z;
-            this.modelMatrix && (transformMat4(outP, [ cp1x, cp1y, z ], this.modelMatrix), cp1x = outP[0], 
-            cp1y = outP[1], cp1z = outP[2], transformMat4(outP, [ cp2x, cp2y, z ], this.modelMatrix), 
-            cp2x = outP[0], cp2y = outP[1], cp2z = outP[2], transformMat4(outP, [ x, y, z ], this.modelMatrix), 
+            this.modelMatrix && (transformMat4(outP, [ cp1x, cp1y, z ], this.modelMatrix), cp1x = outP[0],
+            cp1y = outP[1], cp1z = outP[2], transformMat4(outP, [ cp2x, cp2y, z ], this.modelMatrix),
+            cp2x = outP[0], cp2y = outP[1], cp2z = outP[2], transformMat4(outP, [ x, y, z ], this.modelMatrix),
             x = outP[0], y = outP[1], z = outP[2]);
             let data = this.camera.vp(x, y, z);
-            x = data.x, y = data.y, data = this.camera.vp(cp1x, cp1y, cp1z), cp1x = data.x, 
+            x = data.x, y = data.y, data = this.camera.vp(cp1x, cp1y, cp1z), cp1x = data.x,
             cp1y = data.y, data = this.camera.vp(cp2x, cp2y, cp2z), cp2x = data.x, cp2y = data.y;
         }
         this.nativeContext.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);
@@ -256,7 +256,7 @@ let BrowserContext2d = class {
     }
     lineTo(x, y, z) {
         if (z = z || 0, this.camera) {
-            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0], 
+            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0],
             y = outP[1], z = outP[2]);
             const data = this.camera.vp(x, y, z);
             x = data.x, y = data.y;
@@ -265,7 +265,7 @@ let BrowserContext2d = class {
     }
     moveTo(x, y, z) {
         if (z = z || 0, this.camera) {
-            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0], 
+            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0],
             y = outP[1], z = outP[2]);
             const data = this.camera.vp(x, y, z);
             x = data.x, y = data.y;
@@ -275,8 +275,8 @@ let BrowserContext2d = class {
     quadraticCurveTo(cpx, cpy, x, y, z) {
         if (z = z || 0, this.camera) {
             let cpz = z;
-            this.modelMatrix && (transformMat4(outP, [ cpx, cpy, z ], this.modelMatrix), cpx = outP[0], 
-            cpy = outP[1], cpz = outP[2], transformMat4(outP, [ x, y, z ], this.modelMatrix), 
+            this.modelMatrix && (transformMat4(outP, [ cpx, cpy, z ], this.modelMatrix), cpx = outP[0],
+            cpy = outP[1], cpz = outP[2], transformMat4(outP, [ x, y, z ], this.modelMatrix),
             x = outP[0], y = outP[1], z = outP[2]);
             let data = this.camera.vp(x, y, z);
             x = data.x, y = data.y, data = this.camera.vp(cpx, cpy, cpz), cpx = data.x, cpy = data.y;
@@ -284,7 +284,7 @@ let BrowserContext2d = class {
         this.nativeContext.quadraticCurveTo(cpx, cpy, x, y);
     }
     rect(x, y, w, h, z) {
-        z = z || 0, this.camera ? (this.moveTo(x, y, z), this.lineTo(x + w, y, z), this.lineTo(x + w, y + h, z), 
+        z = z || 0, this.camera ? (this.moveTo(x, y, z), this.lineTo(x + w, y, z), this.lineTo(x + w, y + h, z),
         this.lineTo(x, y + h, z), this.closePath()) : this.nativeContext.rect(x, y, w, h);
     }
     createImageData() {
@@ -309,7 +309,7 @@ let BrowserContext2d = class {
                 this.stops.push([ offset, color ]), edit = !0;
             },
             GetPattern(minW, minH, deltaAngle) {
-                return edit && (deltaAngle || (deltaAngle = endAngle - startAngle), pattern = createConicalGradient(ctx, this.stops, x, y, deltaAngle, startAngle, endAngle, minW, minH), 
+                return edit && (deltaAngle || (deltaAngle = endAngle - startAngle), pattern = createConicalGradient(ctx, this.stops, x, y, deltaAngle, startAngle, endAngle, minW, minH),
                 edit = !1), pattern;
             }
         };
@@ -325,7 +325,7 @@ let BrowserContext2d = class {
     }
     project(x, y, z) {
         if (z = z || 0, this.camera) {
-            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0], 
+            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0],
             y = outP[1], z = outP[2]);
             const data = this.camera.vp(x, y, z);
             x = data.x, y = data.y;
@@ -336,12 +336,12 @@ let BrowserContext2d = class {
         };
     }
     view(x, y, z) {
-        return z = z || 0, this.camera ? (this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), 
+        return z = z || 0, this.camera ? (this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix),
         x = outP[0], y = outP[1], z = outP[2]), this.camera.view(x, y, z)) : [ x, y, z ];
     }
     fillText(text, x, y, z) {
         if (z = z || 0, this.camera) {
-            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0], 
+            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0],
             y = outP[1], z = outP[2]);
             const data = this.camera.vp(x, y, z);
             x = data.x, y = data.y;
@@ -365,9 +365,9 @@ let BrowserContext2d = class {
         if (!method || "native" === method) return this.nativeContext.measureText(text);
         this.mathTextMeasure || (this.mathTextMeasure = application.graphicUtil.createTextMeasureInstance({}, {}, (() => this.canvas.nativeCanvas)));
         const fontFamily = null !== (_a = this.fontFamily) && void 0 !== _a ? _a : DefaultTextStyle.fontFamily, fontSize = null !== (_b = this.fontSize) && void 0 !== _b ? _b : DefaultTextStyle.fontSize;
-        return this.mathTextMeasure.textSpec.fontFamily === fontFamily && this.mathTextMeasure.textSpec.fontSize === fontSize || (this.mathTextMeasure.textSpec.fontFamily = fontFamily, 
-        this.mathTextMeasure.textSpec.fontSize = fontSize, this.mathTextMeasure._numberCharSize = null, 
-        this.mathTextMeasure._fullCharSize = null, this.mathTextMeasure._letterCharSize = null, 
+        return this.mathTextMeasure.textSpec.fontFamily === fontFamily && this.mathTextMeasure.textSpec.fontSize === fontSize || (this.mathTextMeasure.textSpec.fontFamily = fontFamily,
+        this.mathTextMeasure.textSpec.fontSize = fontSize, this.mathTextMeasure._numberCharSize = null,
+        this.mathTextMeasure._fullCharSize = null, this.mathTextMeasure._letterCharSize = null,
         this.mathTextMeasure._specialCharSizeMap = {}), this.mathTextMeasure.measure(text, method);
     }
     putImageData(imagedata, dx, dy) {
@@ -385,7 +385,7 @@ let BrowserContext2d = class {
     }
     strokeText(text, x, y, z) {
         if (z = z || 0, this.camera) {
-            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0], 
+            this.modelMatrix && (transformMat4(outP, [ x, y, z ], this.modelMatrix), x = outP[0],
             y = outP[1], z = outP[2]);
             const data = this.camera.vp(x, y, z);
             x = data.x, y = data.y;
@@ -393,8 +393,12 @@ let BrowserContext2d = class {
         this.nativeContext.strokeText(text, x, y);
     }
     drawImage() {
+      try {
         const _context = this.nativeContext, a = arguments;
         3 === a.length ? _context.drawImage(a[0], a[1], a[2]) : 5 === a.length ? _context.drawImage(a[0], a[1], a[2], a[3], a[4]) : 9 === a.length && _context.drawImage(a[0], a[1], a[2], a[3], a[4], a[5], a[6], a[7], a[8]);
+      } catch (error) {
+        console.error(error);
+      }
     }
     setCommonStyle(params, attribute, offsetX, offsetY, defaultParams) {
         if (Array.isArray(defaultParams)) {
@@ -426,13 +430,13 @@ let BrowserContext2d = class {
         const _context = this.nativeContext;
         defaultParams || (defaultParams = this.fillAttributes);
         const {opacity: opacity = defaultParams.opacity, shadowBlur: shadowBlur = defaultParams.shadowBlur, shadowColor: shadowColor = defaultParams.shadowColor, shadowOffsetX: shadowOffsetX = defaultParams.shadowOffsetX, shadowOffsetY: shadowOffsetY = defaultParams.shadowOffsetY, blur: blur = defaultParams.blur, globalCompositeOperation: globalCompositeOperation = defaultParams.globalCompositeOperation} = attribute;
-        opacity <= 1e-12 || (shadowBlur || shadowOffsetX || shadowOffsetY ? (_context.shadowBlur = shadowBlur * this.dpr, 
-        _context.shadowColor = shadowColor, _context.shadowOffsetX = shadowOffsetX * this.dpr, 
-        _context.shadowOffsetY = shadowOffsetY * this.dpr, this._clearShadowStyle = !0) : this._clearShadowStyle && (_context.shadowBlur = 0, 
-        _context.shadowOffsetX = 0, _context.shadowOffsetY = 0), blur ? (_context.filter = `blur(${blur}px)`, 
-        this._clearFilterStyle = !0) : this._clearFilterStyle && (_context.filter = "blur(0px)", 
-        this._clearFilterStyle = !1), globalCompositeOperation ? (_context.globalCompositeOperation = globalCompositeOperation, 
-        this._clearGlobalCompositeOperationStyle = !0) : this._clearGlobalCompositeOperationStyle && (_context.globalCompositeOperation = "source-over", 
+        opacity <= 1e-12 || (shadowBlur || shadowOffsetX || shadowOffsetY ? (_context.shadowBlur = shadowBlur * this.dpr,
+        _context.shadowColor = shadowColor, _context.shadowOffsetX = shadowOffsetX * this.dpr,
+        _context.shadowOffsetY = shadowOffsetY * this.dpr, this._clearShadowStyle = !0) : this._clearShadowStyle && (_context.shadowBlur = 0,
+        _context.shadowOffsetX = 0, _context.shadowOffsetY = 0), blur ? (_context.filter = `blur(${blur}px)`,
+        this._clearFilterStyle = !0) : this._clearFilterStyle && (_context.filter = "blur(0px)",
+        this._clearFilterStyle = !1), globalCompositeOperation ? (_context.globalCompositeOperation = globalCompositeOperation,
+        this._clearGlobalCompositeOperationStyle = !0) : this._clearGlobalCompositeOperationStyle && (_context.globalCompositeOperation = "source-over",
         this._clearGlobalCompositeOperationStyle = !1));
     }
     setStrokeStyle(params, attribute, offsetX, offsetY, defaultParams) {
@@ -451,8 +455,8 @@ let BrowserContext2d = class {
         const {strokeOpacity: strokeOpacity = defaultParams.strokeOpacity, opacity: opacity = defaultParams.opacity} = attribute;
         if (_context.globalAlpha = strokeOpacity * opacity * this.baseGlobalAlpha, strokeOpacity > 1e-12 && opacity > 1e-12) {
             const {lineWidth: lineWidth = defaultParams.lineWidth, stroke: stroke = defaultParams.stroke, lineJoin: lineJoin = defaultParams.lineJoin, lineDash: lineDash = defaultParams.lineDash, lineCap: lineCap = defaultParams.lineCap, miterLimit: miterLimit = defaultParams.miterLimit, keepStrokeScale: keepStrokeScale = defaultParams.keepStrokeScale} = attribute;
-            _context.lineWidth = keepStrokeScale ? lineWidth : getScaledStroke(this, lineWidth, this.dpr), 
-            _context.strokeStyle = createColor(this, stroke, params, offsetX, offsetY), _context.lineJoin = lineJoin, 
+            _context.lineWidth = keepStrokeScale ? lineWidth : getScaledStroke(this, lineWidth, this.dpr),
+            _context.strokeStyle = createColor(this, stroke, params, offsetX, offsetY), _context.lineJoin = lineJoin,
             lineDash && _context.setLineDash(lineDash), _context.lineCap = lineCap, _context.miterLimit = miterLimit;
         }
     }
@@ -462,7 +466,7 @@ let BrowserContext2d = class {
         const {scaleIn3d: scaleIn3d = defaultParams.scaleIn3d} = params;
         params.font ? _context.font = params.font : _context.font = getContextFont(params, defaultParams, scaleIn3d && this.camera && this.camera.getProjectionScale(z));
         const {fontFamily: fontFamily = defaultParams.fontFamily, fontSize: fontSize = defaultParams.fontSize} = params;
-        this.fontFamily = fontFamily, this.fontSize = fontSize, _context.textAlign = "left", 
+        this.fontFamily = fontFamily, this.fontSize = fontSize, _context.textAlign = "left",
         _context.textBaseline = "alphabetic";
     }
     setTextStyle(params, defaultParams, z) {
@@ -470,7 +474,7 @@ let BrowserContext2d = class {
         const _context = this.nativeContext;
         defaultParams || (defaultParams = this.textAttributes), params.font ? _context.font = params.font : _context.font = getContextFont(params, defaultParams, this.camera && this.camera.getProjectionScale(z));
         const {fontFamily: fontFamily = defaultParams.fontFamily, fontSize: fontSize = defaultParams.fontSize} = params;
-        this.fontFamily = fontFamily, this.fontSize = fontSize, _context.textAlign = null !== (_a = params.textAlign) && void 0 !== _a ? _a : defaultParams.textAlign, 
+        this.fontFamily = fontFamily, this.fontSize = fontSize, _context.textAlign = null !== (_a = params.textAlign) && void 0 !== _a ? _a : defaultParams.textAlign,
         _context.textBaseline = null !== (_b = params.textBaseline) && void 0 !== _b ? _b : defaultParams.textBaseline;
     }
     draw() {}
