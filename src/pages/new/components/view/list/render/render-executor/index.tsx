import { useMemoizedFn, useWhyDidYouUpdate } from 'ahooks'
import React, { FC, memo, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'

import { apiTaskUpdateCompleteConditionPost, apiTodoParticipantListGet, apiTodoParticipantUpdatePost } from '@/api'
import { PeoplePicker, RenderPeoples } from '@/components/basic'
import { PeoplePickerContext } from '@/components/basic/people-picker'
import { useExecutorRemoveSelf } from '@/components/basic-task/use-executor-remove-self'
import { Dispatch, RootState } from '@/models/store'
import { TodoInfo, UserInfo } from '@/types'
import { arraysEqualIgnoreOrder, onPageVisibleChange } from '@/utils'
import {
  OneAndMoreServerParamsType,
  OneAndMoreType,
  PeoplePickerType,
  TaskTableRowType,
  TaskTableRowTypeAdd,
} from '@/utils/const'
import I18N from '@/utils/I18N'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import AddRowBox from '../row-box'
import s from './index.less'
import useGetPermissions from '@/hooks/useGetPermissions'
import * as _ from 'lodash'
import { useCurrentModel } from '@/hooks'

interface Props {
  onChange?: (params: { assignees: UserInfo[] }) => void
  onSuccess?: (params?: { assignees: UserInfo[] }) => void
  oneAndMoreType?: OneAndMoreType
  taskInfo: TodoInfo
  /** 是否展示外层AddRowBox组件，主要用在列表 */
  showBox?: boolean
  emptyNode?: React.ReactNode
  className?: string
  size?: 'small' | 'large' | 'default'
  canOpenUserProfile?: boolean
  maxShowCount?: number
}

const RenderExecutor: React.FC<Props> = props => {
  return <ExecutorContent showBox {...props} />
}

export const ExecutorContent: FC<Props> = (props, ref) => {
  const {
    oneAndMoreType,
    taskInfo,
    showBox,
    emptyNode = null,
    className = '',
    onChange,
    onSuccess,
    size = 'default',
    canOpenUserProfile = true,
    maxShowCount = 3,
  } = props
  const {
    taskId,
    assigner,
    _rowType: rowType,
    assigneeUids,
    followerUids,
    assigneeCount,
    assignees,
    followers,
  } = taskInfo
  const { assignerUid } = assigner || {}
  const { userInfo } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
  }))

  const [list, setList] = useState<UserInfo[]>([])
  const [visible, setVisible] = useState<boolean>(false)
  const latestUidsRef = useRef({ updated: false, uids: [], srcAssignees: [] as UserInfo[] })
  const assigneesUidsRef = useRef<string[]>(assignees?.map(item => item.uid!) || [])

  const { removeSelf } = useExecutorRemoveSelf({
    className: s.confirm,
    taskId: taskId,
  })

  const dispatch = useDispatch<Dispatch>()

  const currTaskModel = useCurrentModel()

  const permission = useGetPermissions({ taskId })
  // const permission = taskInfo.permissions;

  const uidsSubmit = (userList: UserInfo[]) => {
    if (latestUidsRef.current.updated) {
      const uids = latestUidsRef.current.uids
      apiTodoParticipantUpdatePost({
        taskId: taskId,
        uids: uids,
      })
        .then(() => {
          latestUidsRef.current.updated = false
          assigneesUidsRef.current = userList.map(item => item.uid!)
          if (onSuccess) {
            onSuccess()
          } else {
            dispatch.viewSetting.updateItemByDetail({ taskId: taskId })
          }
          //更新列表权限
          // return currTaskModel.updatePermissionsWithTask({ taskId });
        })
        .finally(() => {
          // // 更新项目成员数量
          // dispatch.project.getProject({
          //   id: String(taskInfo.projectId),
          //   hideLoading: true,
          // });
          // currTaskModel.updateTodoList({
          //   taskId,
          //   assignees: users,
          //   assigneeUids: uids,
          //   assigneeCount: users.length,
          // });
        })
    }
  }

  const isAdd = rowType === TaskTableRowType.add
  const updateExecutor = useMemoizedFn((users: UserInfo[], updated: boolean = true) => {
    const uids: string[] = users.map(item => item.uid!)
    const _followers = followers?.filter(item => !uids.includes(item.uid as string)) || []
    const updateParams = {
      taskId: taskId,
      assignees: users,
      assigneeUids: uids,
      followers: _followers,
      followerUids: _followers.map(item => item.uid!),
      followerCount: _followers.length,
    }

    onChange?.(updateParams)

    latestUidsRef.current = {
      ...latestUidsRef.current,
      updated,
      uids: uids,
    }
    currTaskModel.updateTodoList(Object.assign(taskInfo, updateParams))
    if (isAdd) {
    } else {
      // apiTodoParticipantUpdatePost({
      //   taskId: taskId,
      //   uids: uids,
      // })
      //   .then(() => {
      //     //更新列表权限
      //     return currTaskModel.updatePermissionsWithTask({ taskId });
      //   })
      //   .finally(() => {
      //     dispatch.viewSetting.refreshDataByDataChange({
      //       refreshList: true,
      //       refreshCount: true,
      //     });
      //     // currTaskModel.updateTodoList({
      //     //   taskId,
      //     //   assignees: users,
      //     //   assigneeUids: uids,
      //     //   assigneeCount: users.length,
      //     // });
      //   });
    }
  })
  const change = async (users: UserInfo[]) => {
    // 判断执行人员是否有更新
    const updated = !arraysEqualIgnoreOrder(
      assigneesUidsRef.current,
      users?.map(item => item.uid!),
    )
    updateExecutor(users, updated)
  }

  const onOneAndMoreChange = useMemoizedFn((val: number) => {
    let _completeCondition =
      val === OneAndMoreType.all ? OneAndMoreServerParamsType.all : OneAndMoreServerParamsType.one
    if (isAdd) {
      currTaskModel.updateTodoList({
        taskId,
        ...taskInfo,
        completeCondition: _completeCondition,
      })
      return null
    }
    currTaskModel.updateTodoList({
      taskId,
      ...taskInfo,
      completeCondition: _completeCondition,
    })
    // 调用更改任务完成方式接口逻辑
    taskId &&
      apiTaskUpdateCompleteConditionPost({
        taskId,
        completeCondition: _completeCondition,
      }).catch(() => {
        currTaskModel.updateTodoList(taskInfo)
      })
  })

  useEffect(() => {
    if (taskId && visible) {
      // 判断当前是不是新增
      if (String(taskId).includes(TaskTableRowTypeAdd)) {
        latestUidsRef.current.srcAssignees = assignees || []
        return
      }
      apiTodoParticipantListGet({ todoId: String(taskId) }).then(res => {
        const list = [...(res || [])]
        latestUidsRef.current.srcAssignees = list
        setList(list)
      })
    }
  }, [taskId, visible])

  // 修改详情的时候会重新刷新列表
  useEffect(() => {
    // if (assignees?.length) {
    // }
    setList(assignees || [])
  }, [assignees])

  useEffect(() => {
    onPageVisibleChange(pageShow => {
      if (!pageShow) {
        setVisible(false)
      }
    })
  }, [])

  useEffect(() => {
    const scrollEl = document.querySelector('#virtuoso-table .os-viewport')
    if (!scrollEl) {
      return
    }
    const lastScrollTop = scrollEl.scrollTop
    const handleScroll = () => {
      const diff = scrollEl.scrollTop - lastScrollTop

      if (Math.abs(diff) > 80) {
        handleCancelUpdate()
      }
    }
    if (visible) {
      scrollEl.addEventListener('scroll', handleScroll)
    } else {
      scrollEl.removeEventListener('scroll', handleScroll)
    }

    return () => {
      scrollEl.removeEventListener('scroll', handleScroll)
    }
  }, [visible])

  const changeVisible = (v: boolean) => {
    if (latestUidsRef.current.updated) {
      const btn = document.body.querySelector(`.confirm__btn`)
      if (btn) {
        btn?.classList.add('blink__animation')
        setTimeout(() => {
          btn?.classList.remove('blink__animation')
        }, 200)
      }
    } else {
      latestUidsRef.current.updated = false
      setVisible(v)
    }
  }

  const updateInfo = (userList: UserInfo[]) => {
    const uids = userList.map(item => item.uid!)

    assigneesUidsRef.current = uids
    setVisible(false)
    if (!isAdd) {
      uidsSubmit(userList)
    }
    latestUidsRef.current = {
      ...latestUidsRef.current,
      srcAssignees: userList,
      updated: false,
      uids,
    }
  }

  /**
   * 新增行的情况下触发视图更新
   * 非新增的情况下还会额外进行接口调用
   * @param userList 用户列表
   */
  const handleConfirmUpdate = async (userList: UserInfo[]) => {
    const oldSelf = latestUidsRef.current.srcAssignees.findIndex(item => item.uid === userInfo?.uid) > -1
    const moveSelf = userList.findIndex(item => item.uid === userInfo?.uid) === -1
    //移除自己
    if (moveSelf && oldSelf) {
      return removeSelf()
        .then((persist) => {
          if (!persist) {
            updateInfo(userList)
            dispatch.detail.closeDetail({})
          }
        })
        .catch(() => {
          updateExecutor(latestUidsRef.current.srcAssignees, false)
        })
    }
    updateInfo(userList)
  }

  const handleCancelUpdate = async () => {
    updateExecutor(latestUidsRef.current.srcAssignees, false)
    setVisible(false)
  }

  // 点击单元格clear按钮只更新页面数据，最终需要走确认按钮
  const handleClear = async e => {
    e.stopPropagation()
    await change([])
  }

  const memoPermissions = useMemo(() => {
    const [CAN_VIEW, CAN_EDIT, CAN_SET_COMPLETE_MODE, CAN_CANCEL_OBSERVER] = validatesPermission({
      permissions: permission,
      key: [
        TaskPermissionEnum.CAN_VIEW,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[]
    return {
      CAN_VIEW: CAN_VIEW || rowType === TaskTableRowType.add,
      CAN_EDIT: CAN_EDIT || rowType === TaskTableRowType.add,
      CAN_SET_COMPLETE_MODE: CAN_SET_COMPLETE_MODE || rowType === TaskTableRowType.add,
      CAN_CANCEL_OBSERVER: CAN_CANCEL_OBSERVER || rowType === TaskTableRowType.add,
      disabled: !CAN_EDIT && rowType !== TaskTableRowType.add,
    }
  }, [permission, rowType])

  const content = useMemo(() => {
    return (
      <PeoplePickerContext.Provider
        value={{
          assignerUid: assignerUid,
          assigneeUids: assigneeUids,
          followerUids: followerUids,
          pickerType: PeoplePickerType.executor,
          currentUser: userInfo,
          canEditCompleteCondition: memoPermissions.CAN_SET_COMPLETE_MODE,
          isAssigneeUpdated: latestUidsRef.current.updated,
          onCancel: handleCancelUpdate,
          onConfirm: handleConfirmUpdate,
        }}
      >
        <PeoplePicker
          taskId={taskId!}
          disabled={memoPermissions.disabled}
          assignerUid={assignerUid}
          search
          hasArrow={false}
          value={[...list]}
          onChange={change}
          onOneAndMoreChange={onOneAndMoreChange}
          hasMinimum
          visible={visible}
          onVisible={changeVisible}
          placeholder=" "
          oneAndMoreType={oneAndMoreType}
          className={className}
        >
          {list.length ? (
            <>
              {/* <div className={s.listPeopleItem}></div> */}
              <RenderPeoples
                showFinishedIcon={oneAndMoreType === OneAndMoreType.all}
                list={list || []}
                count={assigneeCount || list.length}
                maxShowCount={maxShowCount}
                avatarClassName="mr-4"
                canOpenUserProfile={canOpenUserProfile}
                size={size}
              ></RenderPeoples>
            </>
          ) : (
            emptyNode
          )}
        </PeoplePicker>
      </PeoplePickerContext.Provider>
    )
  }, [
    list,
    visible,
    memoPermissions.disabled,
    memoPermissions.CAN_SET_COMPLETE_MODE,
    oneAndMoreType,
    assigneeCount,
    size,
  ])

  if (!showBox) {
    return content
  }
  return (
    <AddRowBox
      iconName="icon-kit_user_add"
      tipTitle={list.length ? '' : I18N.auto.addParticipants}
      taskId={taskInfo.taskId}
      hasDelete={!!list.length && memoPermissions.CAN_EDIT && visible}
      disabled={memoPermissions.disabled}
      focused={visible}
      onClick={e => {
        changeVisible(!visible)
      }}
      onClear={handleClear}
    >
      {content}
    </AddRowBox>
  )
}

export default memo(RenderExecutor)
