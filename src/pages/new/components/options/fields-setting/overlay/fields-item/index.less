.list {
  background: var(--bgBottom);
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 38px;
  padding: 0 8px;
  border-radius: 4px;
  padding-right: 72px;
  position: relative;

  &:hover {
    background-color: var(--aBlack6);
  }
  .right {
    position: absolute;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    right: 8px;
    top: 0;
    height: 100%;
  }
  .fieldName {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--TextPrimary);
    flex: 1;
    width: 100%;
    overflow: hidden;

    .name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .icon {
    margin-right: 4px;
    color: var(--IconTertiary);
    cursor: pointer;
    flex: none;
  }
}
.dangerItem {
  color: var(--R600);
}

.attrIcon {
  margin-right: 8px;
  font-size: 16px;
  flex: 0;
}
