body {
  .rock-input-number-wrapper {
    width: 100%;
    &::before {
      // 不能在这里统一去除, 影响面太多
      opacity: 0;
    }
    &.rock-input-wrapper-inline {
      background-color: var(--bgTop);
      border-color: var(--aBlack12);
      &.rock-input-wrapper-focused {
        border-color: var(--Brand500);
      }
      &:not(.rock-input-wrapper-disabled):active {
        border-color: var(--Brand500);
      }
      .rock-input {
        color: var(--TextPrimary);
      }
      &.rock-input-wrapper-no-border {
        border-color: transparent;
      }
    }
    &.rock-input-wrapper:focus,
    &.rock-input-wrapper-focused {
      border: none;
    }
  }
}
