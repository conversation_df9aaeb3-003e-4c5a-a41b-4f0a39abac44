import { Popover } from '@bedrock/components';
import { TooltipPlacement } from '@bedrock/components/lib/Tooltip';
import _chunk from 'lodash/chunk';
import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react';

import { getEmoji, getEmojis, IEmoji } from '@/components/basic-task/emoji/util';

import s from './index.less';
export type { IEmoji };
interface EmojiProps {
  placement?: TooltipPlacement;
  tooltipPlacement?: TooltipPlacement;
  onClickEmoji?: (data: IEmoji) => void;
}

const ALIGN = { offset: [-20, 0] };

const Emoji: React.FC<PropsWithChildren<EmojiProps>> = (props) => {
  const { placement, onClickEmoji, children } = props;
  const [activeIndex, setActiveIndex] = useState(-1);
  const [visible, setVisible] = useState(false);
  const emoji = useMemo(() => getEmojis(), []);
  const getDataIndex = (e: React.MouseEvent<HTMLElement>) => {
    const target = e.target as HTMLSpanElement;
    let dataIndex;
    if (target) {
      if (target.nodeName === 'IMG') {
        dataIndex = target.parentElement?.getAttribute('data-index');
      } else {
        dataIndex = target.getAttribute('data-index');
      }
    }
    return dataIndex;
  };

  const onClick = (e: React.MouseEvent<HTMLElement>) => {
    const dataIndex = getDataIndex(e);
    if (dataIndex) {
      const emoji = getEmoji(+dataIndex);
      if (emoji) {
        onClickEmoji?.(emoji);
      }
      setVisible(false);
    }
    e.stopPropagation();
  };

  const onMouseEnter = (e: React.MouseEvent<HTMLSpanElement>) => {
    const dataIndex = getDataIndex(e);

    if (dataIndex) {
      setActiveIndex(+dataIndex);
    }
  };

  const onMouseLeave = () => {
    if (~activeIndex) {
      setActiveIndex(-1);
    }
  };
  useEffect(() => {
    const listener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setVisible(false);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
  }, []);
  return (
    <Popover
      align={ALIGN}
      className={s.emojiPopover}
      content={
        <div className={s.emoji} onClick={onClick}>
          <ul className={s.emojiList}>
            {_chunk(emoji, emoji.length).map((row, index) => (
              <li className={s.emojiRow} key={index}>
                {row.map((item) => {
                  const { dynamicPath, id, name, staticPath } = item;
                  const path = id === activeIndex ? dynamicPath : staticPath;
                  //const path = dynamicPath;
                  return (
                    <span
                      className={s.emojiItem}
                      data-index={id}
                      key={id}
                      title={name}
                      onMouseEnter={onMouseEnter}
                      onMouseLeave={onMouseLeave}
                    >
                      {/* <Tooltip title={item.name} destroyTooltipOnHide>
                        <img alt={name} src={path} />
                      </Tooltip> */}
                      <img alt={name} src={path} />
                    </span>
                  );
                })}
              </li>
            ))}
          </ul>
        </div>
      }
      overlayClassName={s.emojiPopover}
      placement={placement}
      trigger="click"
      visible={visible}
      onVisibleChange={setVisible}
    >
      {React.cloneElement(children, {
        onClick: () => {
          setVisible(!visible);
        },
      })}
    </Popover>
  );
};
Emoji.defaultProps = {
  placement: 'bottomLeft',
  tooltipPlacement: 'top',
};
export default Emoji;
