import { ChangeParams, Options, SelectValue } from '@bedrock/components/lib/Select';
import classNames from 'classnames';

import { Icon, Select } from '@/components/basic';
import SelectedItem from '@/components/basic/select/selected-item';
import { SelectType } from '@/utils/fields';

import s from './index.less';

interface TabsProps {
  value: any;
  onChange?: (value: any, params?: Partial<ChangeParams>) => void;
  options: Options;
  type: SelectType;
}

const FieldSelect: React.FC<TabsProps> = (props) => {
  const { value, onChange, options } = props;
  return (
    <Select
      dropdownClassName={s.fieldSelect}
      multipleCheckedType={'check-icon'}
      multiple
      value={value}
      onChange={onChange}
      options={options}
      renderLabel={(option) => {
        return <div>{option.name}</div>;
      }}
      renderSelectedItem={(option, { onClose }) => {
        return {
          content: (
            <div className={s.tag}>
              <SelectedItem
                onClose={(e) => {
                  onClose(e, option);
                }}
              >
                {option.name}
              </SelectedItem>
            </div>
          ),
          isRenderInTag: false,
        };
      }}
      zIndex={999999}
      //@ts-ignore
      // getPopupContainer={(dom) => dom.parentNode}
    />
  );
};
export default FieldSelect;
