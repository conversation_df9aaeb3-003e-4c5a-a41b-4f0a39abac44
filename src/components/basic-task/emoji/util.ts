import data from './emojidata.json';

export const EMOJIS = data;
export const IMGALT = 'popo-emoji';
export const EMOJISKEYPRE = '_popo_emoticon_sys_';
const EMOJI_PATH = 'https://popo.res.netease.com/emoji';

export interface IEmoji {
  id: number;
  key: string;
  name: string;
  enname: string;
  staticPath: string;
  dynamicPath: string;
}

export const getEmojiPath = (value: number, isDynamic = true) => {
  const type = isDynamic ? 'gif' : 'png';

  return `${EMOJI_PATH}/${type}/${value}.${type}`;
};
export const getEmoji: (value: number) => IEmoji | undefined = (value: number) => {
  const item = EMOJIS.find((item) => item.id === value)!;
  if (!item) {
    return {
      id: value,
      key: value,
      name: '',
      enname: '',
      dynamicPath: '',
      staticPath: '',
    };
  }
  return {
    ...item,
    dynamicPath: getEmojiPath(item.id),
    staticPath: getEmojiPath(item.id, false),
  };
};

// 获取所有表情
export const getEmojis = () => {
  const data = EMOJIS.map((value) => ({
    dynamicPath: getEmojiPath(value.id),
    id: value.id,
    key: value.key,
    name: value.name,
    enname: value.enname,
    staticPath: getEmojiPath(value.id, false),
  }));
  return data;
};

export const getEmojisMap = () => {
  const obj: any = {};
  EMOJIS.forEach((value) => {
    obj[value.name] = {
      dynamicPath: getEmojiPath(value.id),
      id: value.id,
      key: value.key,
      name: value.name,
      enname: value.enname,
      staticPath: getEmojiPath(value.id, false),
    };
  });
  return obj;
};

export const getEmojisEnMap = () => {
  const obj: any = {};
  EMOJIS.forEach((value) => {
    obj[value.enname] = {
      dynamicPath: getEmojiPath(value.id),
      id: value.id,
      key: value.key,
      name: value.name,
      enname: value.enname,
      staticPath: getEmojiPath(value.id, false),
    };
  });
  return obj;
};

const emojisMap = getEmojisMap();

export const emojiStr2Image = (str: string, width = 20) => {
  return str.replace(/\[(.+?)\]/g, (word, $1) => {
    const emoji = emojisMap[$1];
    if (emoji) {
      return `<img  width="${width}" src='${emoji.dynamicPath}' alt='${IMGALT}' referrerPolicy="no-referrer"/>`;
    }
    return `[${$1}]`;
  });
};

// [emts]_popo_emoticon_sys_00103_ 吐舌 [吐舌][/emts] 转成 <img src="${path}" alt="emoji" />
export const emojiServer2EmojiStr = (str: string) => {
  const reg = new RegExp(
    `\\[emts\\]_popo_emoticon_sys_(?:0)+(\\d+)_(?:.|\\r|\\n|\\u2028|\\u2029)*?(\\[.*\\])\\[\\/emts\\]`,
    'g'
  );
  return str.replace(reg, '$2');
};
