import { TooltipPlacement } from '@bedrock/components/lib/Tooltip';
import classNames from 'classnames';
import React, { PropsWithChildren, useMemo, useState } from 'react';

import { Tooltip } from '@/components/basic';
import {
  OneAndMoreType,
  TaskCompleteStatus,
  TaskStatus,
} from '@/components/basic-task/task-status';
import { TodoInfo } from '@/types';
import { OneAndMoreServerParamsType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';
import { getFinishStatus } from '@/pages/new/components/view/list/utils';
export interface Props {
  className?: string;
  disabled?: boolean;
  onChange?: () => void;
  onTaskStateChange?: (taskState: TaskCompleteStatus, type: string) => void;
  placement?: TooltipPlacement;
  taskInfo?: TodoInfo;
}
const TodoStatus: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    className = '',
    disabled,
    onChange,
    placement = 'top',
    taskInfo,
    onTaskStateChange,
  } = props;
  const {
    taskId,
    participantCount = 0,
    finishedParticipantCount = 0,
    completeCondition,
    isCoordinator,
    selfFinished,
    finished,
  } = taskInfo || {};

  const permissions = useGetPermissions({ taskId });

  const [taskState, setTaskState] = useState<TaskCompleteStatus>(
    TaskCompleteStatus.CompleteMeOrCompleteAll
  );

  const memoFinished = useMemo(() => {
    return getFinishStatus({ completeCondition, selfFinished, finished });
  }, [selfFinished, finished, taskState]);

  const showTip = useMemo(() => {
    if (disabled) {
      return I18N.auto.noEditingPermission;
    }
    return finished ? I18N.auto.clickToRebuildTheTask : I18N.auto.clickToCompleteTheTask;
  }, [disabled, finished]);

  const isManager = useMemo(() => {
    const [CAN_SET_COMPLETE_MODE] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_SET_COMPLETE_MODE],
    }) as boolean[];
    return !!CAN_SET_COMPLETE_MODE;
  }, [permissions]);

  return (
    <Tooltip arrowPointAtCenter title={showTip} placement={placement}>
      <div
        className={`${s.statusWrapper} status__wrapper`}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <TaskStatus
          {...taskInfo}
          taskId={taskInfo?.taskId}
          disabled={disabled}
          isCoordinator={!!isCoordinator}
          isManager={isManager}
          oneAndMoreType={
            completeCondition === OneAndMoreServerParamsType.all
              ? OneAndMoreType.all
              : OneAndMoreType.one
          }
          taskState={taskState}
          onChange={(v) => {
            if (!disabled) {
              onChange?.();
            }
          }}
          onTaskStateChange={(v, type) => {
            setTaskState(v);
          }}
          finishedParticipantCount={finishedParticipantCount}
          participantCount={participantCount}
        >
          <div
            className={classNames(s.statusIcon, className, {
              [s.disabled]: disabled && !isManager,
              [s.isCreator]: isManager,
            })}
          >
            <div className={s.statusBox}>
              <i
                className={classNames(
                  s.icon,
                  'icon iconfont',
                  memoFinished ? 'icon-taskstate_down' : 'icon-taskstate_nodown'
                )}
              ></i>
            </div>
          </div>
        </TaskStatus>
      </div>
    </Tooltip>
  );
};

export default TodoStatus;
