import classNames from 'classnames'
import React, { PropsWithChildren, useContext } from 'react'
import { useSelector } from 'react-redux'

import { IconBtn, RenderLevel, RenderPeoples, Tooltip } from '@/components/basic'
import { Level } from '@/components/basic/level-picker/utils'
import { RootState } from '@/models/store'
import { QueryGroupBy, TaskGroupItem, UserInfo } from '@/types'
import { FieldTypeEnum } from '@/types/custom-field'
import { EnumGroupBy } from '@/utils/fields'
import I18N from '@/utils/I18N'

import { TableContext } from '../../context'
import { Cell, Row } from '..'
import s from './index.less'
export type Props = {
  className?: string
  onCollapse?: (unfold: boolean, info: TaskGroupItem) => void
  groupInfo?: TaskGroupItem
  assignees?: UserInfo[]
}
const TableCell: React.FC<PropsWithChildren<Props>> = React.forwardRef((props, refs) => {
  const { onCollapse, groupInfo, assignees, className } = props
  const { columns } = useContext(TableContext)

  const { customFields, queryGroupBys, groupCountMap } = useSelector((state: RootState) => ({
    customFields: state.viewSetting.customFields,
    // queryGroupBy: state.viewSetting.currentViewTab.queryGroupBy,
    queryGroupBys: state.viewSetting.currentViewTab.queryGroupBys,
    groupCountMap: state.task.groupCountMap,
  }))

  const queryGroupBy = queryGroupBys?.[groupInfo?.groupLevel || 0] as QueryGroupBy

  const renderGroupTitle = () => {
    if (queryGroupBy?.customFieldId) {
      const field = customFields.find(v => v.fieldId === groupInfo?.groupBy)
      if (field?.type === FieldTypeEnum.user) {
        const customFieldUsers = groupInfo?.users
        return customFieldUsers?.length ? (
          <RenderPeoples
            showFinishedIcon={false}
            list={customFieldUsers || []}
            count={customFieldUsers.length}
            maxShowCount={5}
            avatarClassName="mr-4"
          ></RenderPeoples>
        ) : (
          I18N.auto.nothing
        )
      }
      if (field?.type === FieldTypeEnum.option) {
        return (
          <div className={s.ellipsis_2}>
            <Tooltip title={groupInfo?.title} onlyEllipsis>
              <div>{groupInfo?.title}</div>
            </Tooltip>
          </div>
        )
      }

      return (
        <div className={s.ellipsis_2}>
          <Tooltip title={groupInfo?.title} onlyEllipsis>
            <div>{groupInfo?.title}</div>
          </Tooltip>
        </div>
      )
    } else {
      if (groupInfo?.groupBy === EnumGroupBy.ASSIGNEE) {
        return assignees?.length ? (
          <RenderPeoples
            showFinishedIcon={false}
            list={assignees || []}
            count={groupInfo?.assigneeCount || assignees.length}
            maxShowCount={5}
            avatarClassName="mr-8"
          ></RenderPeoples>
        ) : (
          I18N.auto.nothing
        )
      }
      if (groupInfo?.groupBy === EnumGroupBy.PRIORITY) {
        return groupInfo?.groupId === Level.default ? (
          I18N.auto.nothing
        ) : (
          <RenderLevel value={groupInfo?.groupId}></RenderLevel>
        )
      }
      return (
        <div className={s.ellipsis_2}>
          <Tooltip title={groupInfo?.title} onlyEllipsis>
            <div>{groupInfo?.title}</div>
          </Tooltip>
        </div>
      )
    }
  }
  return (
    <div className={classNames(s.groupRow, className)}>
      <div className={`${s.groupTitle} flex-y-center`}>
        {renderGroupTitle()}
        <span className={s.groupTaskCount}>
          {groupInfo?.finishedCount ?? 0}/{groupInfo?.totalCount ?? 0}
          {/* {groupCountMap?.[groupInfo?.groupId!] ?? groupInfo?.groupTaskCount ?? 0} */}
        </span>
      </div>
    </div>
  )
  return (
    <Row ref={refs}>
      {columns?.map((item, k: number) => {
        let _className = ''
        if (item.fixed === 'left') {
          _className = 'todo-td-fixed-left'
        }
        if (item.fixed === 'right') {
          _className = 'todo-td-fixed-right'
        }
        if (k === 0) {
          return (
            <Cell key={k} className={_className}>
              <div
                //className={classNames(s.groupRow)}
                onClick={e => {
                  e.stopPropagation()
                }}
              >
                <div
                  className={s.groupBox}
                  onClick={() => {
                    onCollapse?.(!groupInfo?.unfold, groupInfo!)
                  }}
                >
                  <IconBtn
                    className={classNames(s.arrow, {
                      [s.unfold]: groupInfo?.unfold,
                    })}
                    fontSize={14}
                    iconName="icon-sys_Expand"
                  ></IconBtn>
                  <div className={s.groupTitle}>
                    {renderGroupTitle()}
                    <span className={s.groupTaskCount}>{groupInfo?.groupTaskCount ?? 0}</span>
                  </div>
                </div>
              </div>
            </Cell>
          )
        }
        return (
          <Cell key={k} className={classNames('todo-td-right-no-border', _className)}>
            <div
              className={classNames(s.addRowCell)}
              onClick={e => {
                e.stopPropagation()
              }}
            ></div>
          </Cell>
        )
      })}
    </Row>
  )
})

export default TableCell
