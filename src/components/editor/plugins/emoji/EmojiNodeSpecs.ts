import { NodeSpec } from 'prosemirror-model';

const rootClassName = 'prosemirror-emoji';

const EmojiNodeSpecs: NodeSpec = {
  popoEmoji: {
    attrs: {
      src: {},
      text: {},
      key: {},
      alt: {}
    },

    group: 'inline',
    inline: true,

    toDOM: (node) => [
      'img',
      {
        class: rootClassName,
        src: node.attrs.src,
        alt: node.attrs.alt,
        key: node.attrs.key,
        text: node.attrs.text,
      },
    ],
    parseDOM: [
      {
        tag: `img.${rootClassName}`,
        priority: 200,
        getAttrs: (dom: any) => {
          const src = dom.getAttribute('src');
          const alt = dom.getAttribute('alt');
          const key = dom.getAttribute('key');
          const text = dom.getAttribute('text');

          return { src, alt, key, text };
        },
      },
    ],
  },
};
export default EmojiNodeSpecs;
