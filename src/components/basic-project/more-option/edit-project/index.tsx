import React, { PropsWithChildren, useMemo, useState, useEffect } from 'react';

import { Modal } from '@/components/basic';
import I18N from '@/utils/I18N';

import { EditProjectName } from '../..';

export type EditProjectValue = {
  /**
   * 项目 名称
   */
  name?: string;
  /**
   * 项目图标
   */
  icon?: string;
  /**
   * 项目图标底色
   */
  iconColor?: string;
};

interface Props {
  disabled?: boolean;
  className?: string;
  value: EditProjectValue;
  onChange?: (v: EditProjectValue) => void;
}

const EditProject: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, value, onChange } = props;
  const [info, setInfo] = useState<EditProjectValue>(value);
  const { icon, iconColor, name } = info || {};
  const [visible, setVisible] = useState<boolean>(false);
  // 保证状态更新同步
  useEffect(() => {
    setInfo(value);
  }, [value]);
  const onSubmit = () => {
    //比较变化
    onChange?.({
      name: info.name,
      icon: info.icon,
      iconColor: info.iconColor,
    });
    setVisible(false);
  };
  const memoOkButtonProps = useMemo(() => {
    return {
      disabled: !name,
    };
  }, [name]);
  return (
    <>
      {React.cloneElement(children, {
        onClick: () => {
          setVisible(!visible);
        },
      })}
      <div
        onKeyDown={(e) => {
          //Menu组件劫持了方向键,导致Input不能左右移动光标
          e.stopPropagation();
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        onDragStart={(e) => {
          e.stopPropagation();
        }}
        onPointerDown={(e) => {
          e.stopPropagation();
        }}
        onPointerMove={(e) => {
          e.stopPropagation();
        }}
      >
        <Modal
          visible={visible}
          width={480}
          title={I18N.auto.editProject}
          okText={I18N.auto.preservation}
          closable
          centered
          scrolled={false}
          onCancel={() => {
            //还原数据
            setInfo({
              ...value,
            });
            setVisible(false);
          }}
          onOk={onSubmit}
          okButtonProps={memoOkButtonProps}
          getContainer={() => document.body}
        >
          <EditProjectName
            autoFocus
            iconName={icon}
            iconBgColor={iconColor}
            onChangeIcon={(v) => {
              setInfo({
                ...info,
                icon: v,
              });
            }}
            onChangeColor={(v) => {
              setInfo({
                ...info,
                iconColor: v,
              });
            }}
            projectName={name}
            onChangeProjectName={(v) => {
              setInfo({
                ...info,
                name: v,
              });
            }}
          ></EditProjectName>
        </Modal>
      </div>
    </>
  );
};

export default EditProject;
