.members__overlay {
  border: 1px solid var(--aBlack12);
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.08);
  background-color: var(--bgTop);
  border-radius: 12px;
  width: 480px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;

    .title {
      color: var(--TextPrimary);
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;

      :global {
        .babylon-popo-icon {
          color: var(--IconPrimary);
          cursor: pointer;
        }
      }
    }
  }

  .content,
  .footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .content {
    .alert {
      border: 1px solid var(--aBrand20);
      background-color: var(--aBrand8);
      padding: 10px;

      :global {
        .rock-alert-message {
          color: var(--Brand700);
          font-size: 13px;
          line-height: 16px;
        }
      }
    }
  }

  .footer {
    border-top: 1px solid var(--aBlack10);
  }

  .members__footer {
    display: flex;
    column-gap: 12px;
    padding-top: 16px;
    padding-bottom: 16px;

    .footer__desc {
      font-size: 14px;
      line-height: 20px;
      color: var(--TextPrimary);
    }

    .primary__btn {
      background-color: var(--Brand500);
      color: var(--absWhite);
    }

    .footer__btn {
      color: var(--TextPrimary);
      :global {
        .icon,
        .babylon-popo-icon {
          margin-right: 4px;
          color: var(--IconBlack);
        }
      }
    }
  }

  .invite__actions {
    display: flex;
    column-gap: 12px;
    align-items: center;

    :global {
      .rock-btn {
        min-width: 68px;
      }
    }
  }

  .member__list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    margin: 16px 0;
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    width: calc(100% + 16px);
    padding-right: 6px;
  }

  .member {
    justify-content: space-between;

    &__avatar {
      border-radius: 50%;
      overflow: hidden;
      margin-right: 12px;
      flex-shrink: 0;

      .avatar {
        width: 32px;
        height: 32px;
        border: none;
        flex-shrink: 0;
        border-radius: 28px;
        background: url('~@/assets/images/people.png') center center no-repeat;
        background-size: 100%;

        & > img {
          width: 32px;
          height: 32px;
          object-fit: cover;
        }
      }
    }

    &__name {
      font-size: 13px;
      line-height: 22px;
      color: var(--TextPrimary);
      padding-right: 4px;
    }

    .switch__permission {
      :global {
        .rock-dropdown-trigger-default {
          padding-left: 4px;
          padding-right: 4px;
          border-radius: 6px;
          background-color: transparent !important;

          &:not(.disabled) {
            &:hover,
            &.rock-dropdown-open {
              background-color: var(--aBlack6) !important;
            }
          }

          .rock-icon,
          babylon-popo-icon {
            margin-left: 2px;
            color: var(--IconPrimary);
          }
        }
      }
    }
  }

  .invited__list {
    margin-bottom: 16px;

    .desc {
      padding: 16px 0 10px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: var(--TextPrimary);
      border-bottom: 1px solid var(--aBlack8);
      margin-bottom: 16px;
    }

    .list {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      max-height: 200px;
      overflow-y: auto;
      overflow-x: hidden;
      width: calc(100% + 16px);
      padding-right: 6px;
    }
  }

  .people__picker--overlay {
    width: 438px;

    :global {
      .tag {
        background-color: transparent;
        padding: 0;
      }
    }
  }

  .create__tag {
    line-height: 16px;
    height: 16px;
    margin-left: 4px;
    padding: 0 6px;
    border-radius: 3px;
    color: var(--Brand700);
    font-size: 11px;
    font-weight: 500;
    background-color: var(--cardblue2);
    white-space: nowrap;
  }
}
