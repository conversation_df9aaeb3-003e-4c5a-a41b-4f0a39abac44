.fieldSelect {
  :global {
    .rock-select-item.rock-select-item-selected {
      background-color: transparent;
    }
    .rock-select-item-check-icon {
      color: var(--Brand600);
    }
  }
}

.tip {
  :global {
    max-width: 240px !important;
    white-space: normal;
  }
}

.tag {
  display: flex;
  align-items: center;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  font-size: 13px;
  font-weight: 400;
  border-radius: 3px;
  background-color: var(--aBlack6);
  overflow: hidden;
}
.selectedItem {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  border-radius: 3px;
}
