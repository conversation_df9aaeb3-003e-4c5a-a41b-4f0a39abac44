import { VGroup, VImage, VText } from '@visactor/vtable'
import { IGroupAttribute } from '@visactor/vtable/es/vrender'

const centerStyle: Partial<IGroupAttribute> = {
  display: 'flex',
  flexWrap: 'nowrap',
  justifyContent: 'flex-start',
  alignItems: 'center',
  alignContent: 'center',
}

const defaultAvatar = 'https://popo.gsf.netease.com/default_user.png'

const renderAvatar = ({ avatars, length, themeTokensRef }) => {
  let showAvatars = avatars
  if (length > 5) {
    showAvatars = avatars.slice(0, 5)
    showAvatars.push({
      type: 'ellipsis',
      avatarUrl: '',
    })
  }

  return (
    <VGroup
      attribute={{
        ...centerStyle,
        visibleAll: true,
        boundsPadding: [0, 8, 0, 8],
        id: 'container',
      }}
    >
      {showAvatars?.map((item, index) => {
        const isEllipsis = item.type === 'ellipsis'

        const img = new Image()
        img.crossOrigin = 'anonymous'

        img.onerror = () => {
          img.src = defaultAvatar
        }

        if (item.avatarUrl.includes('default_user_icon')) {
          img.src = defaultAvatar
        }

        if (item.avatarUrl) {
          img.src = `${item.avatarUrl}?fop=imageView/0/w/100/h/100`
        } else {
          img.src = defaultAvatar
        }

        return (
          <VGroup
            key={index}
            attribute={{
              width: 20,
              height: 20,
              x: 0,
              y: 0,
              background: isEllipsis ? themeTokensRef.current.aBlack6 : 'transparent',
              // ...centerStyle,
              cornerRadius: 10,
            }}
          >
            {item.type === 'ellipsis' ? (
              <VGroup
                attribute={{
                  width: 20,
                  height: 20,
                  ...centerStyle,
                  justifyContent: 'center',
                }}
              >
                <VText
                  attribute={{
                    text: `+`,
                    fontSize: 13,
                    textAlign: 'center',
                    fontWeight: 500,
                    fill: themeTokensRef.current['TextSecondary'],
                    boundsPadding: [0, 1, 0, 0],
                  }}
                />
                <VText
                  attribute={{
                    text: `${length - 5}`,
                    fontSize: 10,
                    textAlign: 'center',
                    fontWeight: 500,
                    lineHeight: 1.2,
                    fill: themeTokensRef.current['TextSecondary'],
                  }}
                />
              </VGroup>
            ) : (
              <VImage
                attribute={{
                  // id: 'avatar',
                  width: 20,
                  height: 20,
                  image: img,
                  boundsPadding: [0, 3, 0, 0],
                  cornerRadius: 10,
                  cursor: 'pointer',
                }}
              />
            )}
          </VGroup>
        )
      })}
    </VGroup>
  )
}

export default renderAvatar
