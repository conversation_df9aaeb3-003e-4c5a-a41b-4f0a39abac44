import { useMemo, useRef } from 'react';

import { darkTokens, lightTokens } from '@/common/ColorTokens';
import { DARK, LIGHT } from '@/common/VTableThemes';
import { CorlorMode } from '@/utils/platform';

type ThemeConfig = typeof LIGHT | typeof DARK;
type ThemeTokens = typeof lightTokens | typeof darkTokens;

interface IUseGetThemeConfig {
  theme?: CorlorMode;
  onThemeChange?: (themeConfig: ThemeConfig, themeTokens?: ThemeTokens) => void;
}

export const useGetThemeConfig = ({
  theme = CorlorMode.light,
  onThemeChange,
}: IUseGetThemeConfig) => {
  const themeConfigRef = useRef<ThemeConfig>(LIGHT);
  const themeTokensRef = useRef<ThemeTokens>(lightTokens);

  return useMemo(() => {
    const isDark = theme === CorlorMode.dark;
    themeConfigRef.current = isDark ? DARK : LIGHT;
    themeTokensRef.current = isDark ? darkTokens : lightTokens;
    onThemeChange?.(themeConfigRef.current, themeTokensRef.current);
    return {
      themeConfig: themeConfigRef.current,
      themeTokens: themeTokensRef.current,
      themeConfigRef,
      themeTokensRef,
    };
  }, [theme]);
};
