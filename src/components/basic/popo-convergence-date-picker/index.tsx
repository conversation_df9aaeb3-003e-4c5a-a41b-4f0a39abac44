import classNames from 'classnames'
import { Dayjs } from 'dayjs'
import { ReactNode, useCallback, useContext, useMemo, useRef, useState } from 'react'

import { Dropdown, Placeholder, RenderTime } from '@/components/basic'
import DropdownIcon from '@/components/basic/dropdown-icon'
import { TaskTime } from '@/types'
import { isUpdateTime } from '@/utils'
import { EnumTimePickerType } from '@/utils/const'

import { PopoConvergenceDatePickerContext } from './context'
import DatePickerOverlay, { TimeInputType } from './convergence-date-picker'
import s from './index.less'
import QuickOverlay from './quick-overlay'
import type { TimeValue } from './utils'
import { FORMAT_DD, FORMAT_DD_mm, PPTimeFormat } from './utils'
import { ConfigProvider } from '@bedrock/components'

export { TimeValue }

export type Props = {
  className?: string
  placeholder?: ReactNode
  children?: ReactNode
  value?: TaskTime
  onChange?: (time: TaskTime) => void
  disabled?: boolean
  visible?: boolean
  onVisible?: (v: boolean) => void
  renderLabel?: (time: Dayjs, value?: TaskTime) => ReactNode
  quickType?: QuickType
  labelClassName?: string
  hasQuick?: boolean
  hasArrow?: boolean
  isGetEnd?: boolean
  showDelete?: boolean
  showLabelRRuleIcon?: boolean
  loopInducedDisabled?: boolean
  timeInputType: TimeInputType
  /* 是否展示循环任务, 默认展示 */
  showCircle?: boolean
  getPopupContainer?: () => HTMLElement
  panelType?: Overlay
}

export enum Overlay {
  datePanel = 1,
  quick = 2,
  visible = 3,
  quick2datePanel = 4,
}

export enum QuickType {
  deadline,
  remind,
}

export default function POPODatePicker(props: Props) {
  const {
    value,
    onChange,
    placeholder,
    showLabelRRuleIcon,
    hasArrow,
    renderLabel,
    quickType = QuickType.deadline,
    children,
    showDelete,
    className,
    labelClassName,
    disabled,
    visible = false,
    onVisible,
    hasQuick = true,
    isGetEnd,
    loopInducedDisabled,
    timeInputType,
    showCircle = true,
    panelType,
  } = props
  const { timePickerType } = useContext(PopoConvergenceDatePickerContext)
  const [showDateTimePanel, setShowDateTimePanel] = useState<Overlay>(panelType || Overlay.quick)
  const timeRef = useRef<{ getData: () => TaskTime }>()
  const overlayElRef = useRef<HTMLDivElement>()

  // 默认展示下拉组件选中的值
  const onRemove = useCallback(() => {
    onChange?.({
      deadline: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
      startTime: 0,
      selectedOption: 'NONE',
    })
  }, [onChange])

  const showTimeLabel = () => {
    const { deadline, timeFormat, rrule, startTime, alarm } = value || {}
    if (timePickerType === EnumTimePickerType.deadline) {
      if (deadline || alarm?.time) {
        let FORMAT = FORMAT_DD
        if (timeFormat === PPTimeFormat.olayDay) {
          FORMAT = FORMAT_DD
        }
        if (timeFormat === PPTimeFormat.dateAndTime) {
          FORMAT = FORMAT_DD_mm
        }
        return (
          <RenderTime
            className={classNames(s.renderTime, labelClassName)}
            time={deadline}
            format={FORMAT}
            rrule={rrule}
            showLabelRRuleIcon={showLabelRRuleIcon}
            onRemove={() => {
              onVisible?.(false)
              onRemove()
            }}
            renderLabel={
              renderLabel
                ? time => {
                    return renderLabel(time, value)
                  }
                : undefined
            }
          ></RenderTime>
        )
      }
    }
    if (timePickerType === EnumTimePickerType.start) {
      if (startTime && timeFormat) {
        let FORMAT = FORMAT_DD
        if (timeFormat === PPTimeFormat.olayDay) {
          FORMAT = FORMAT_DD
        }
        if (timeFormat === PPTimeFormat.dateAndTime) {
          FORMAT = FORMAT_DD_mm
        }
        return (
          <RenderTime
            className={classNames(s.renderTime, labelClassName)}
            time={deadline}
            format={FORMAT}
            rrule={rrule}
            showLabelRRuleIcon={showLabelRRuleIcon}
            onRemove={() => {
              onVisible?.(false)
              onRemove()
            }}
            renderLabel={
              renderLabel
                ? time => {
                    return renderLabel(time, value)
                  }
                : undefined
            }
          ></RenderTime>
        )
      }
    }

    return <Placeholder text={placeholder}></Placeholder>
  }

  const changeQuick = (v: TimeValue) => {
    setShowDateTimePanel(Overlay.visible)
    onChange?.({
      deadline: v.time,
      startTime: 0,
      alarm: {},
      rrule: v.rrule,
      timeFormat: v.timeFormat,
      //selectedOption: v.selectedOption,
    })
    onVisible?.(false)
  }

  const showDatePicker = useMemo(() => {
    if (panelType === Overlay.datePanel || panelType === Overlay.quick2datePanel) {
      return true
    }
    return (
      showDateTimePanel === Overlay.datePanel ||
      showDateTimePanel === Overlay.quick2datePanel ||
      !hasQuick ||
      (!!value?.deadline && showDateTimePanel !== Overlay.visible)
    )
  }, [showDateTimePanel, hasQuick, value?.deadline])

  const showQuick = useMemo(() => {
    return showDateTimePanel === Overlay.quick && hasQuick && !value?.deadline
  }, [showDateTimePanel, hasQuick, value?.deadline])

  const handleOpenChange = (open: boolean) => {
    if (showDatePicker && open === false) {
      const data = timeRef.current?.getData()
      if (!data) {
        return
      }
      const isUpdate = isUpdateTime(data, value!)
      if (!isUpdate) {
        onVisible?.(false)
      } else {
        const btn = overlayElRef.current?.querySelector(`.confirm__btn`)
        if (btn) {
          btn?.classList.add('blink__animation')
          setTimeout(() => {
            btn?.classList.remove('blink__animation')
          }, 200)
        }
      }
    } else {
      onVisible?.(open)
    }
  }

  // useEffect(() => {
  //   const listener = () => {
  //     onVisible?.(false);
  //   };
  //   if (visible) {
  //     document.querySelector('.rock-table-body')?.addEventListener('scroll', listener);
  //     return () => {
  //       document.querySelector('.rock-table-body')?.removeEventListener('scroll', listener);
  //     };
  //   }
  // }, [visible]);

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext)

  const zIndex = useMemo(() => getGlobalZIndex(), [visible])

  return (
    <Dropdown
      className={classNames(s.timepickerDropdown, className)}
      title={
        <DropdownIcon showArrow={visible} hasArrow={hasArrow}>
          {children ? children : showTimeLabel()}
        </DropdownIcon>
      }
      trigger="click"
      arrow={false}
      defaultOpen={false}
      open={visible && !disabled}
      zIndex={zIndex}
      placement="bottom"
      onOpenChange={open => {
        handleOpenChange(open)
        if (open) {
          if (value?.deadline) {
            setShowDateTimePanel(Overlay.datePanel)
          } else if (!panelType || panelType === Overlay.quick) {
            setShowDateTimePanel(Overlay.quick)
          }
        }
        // else {
        //   //回调 所有操作的值
        //   const data = timeRef.current?.getData();
        //   if (!data) {
        //     return;
        //   }
        //   const isUpdate = isUpdateTime(data, value!);
        //   isUpdate && onChange?.(data!);
        // }
      }}
      disabled={disabled}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlayClassName={classNames(s.picker, { [s.datePicker]: showDatePicker }, { [s.quick]: showQuick })}
      //@ts-ignore
      overlay={
        <div
          ref={overlayElRef}
          onClick={e => {
            e.stopPropagation()
          }}
        >
          {showDatePicker ? (
            <DatePickerOverlay
              ref={timeRef}
              value={value!}
              showCircle={showCircle}
              loopInducedDisabled={loopInducedDisabled}
              timeInputType={timeInputType}
              onChange={v => {
                onVisible?.(false)
                onChange?.(v)
              }}
              onVisible={onVisible}
            ></DatePickerOverlay>
          ) : null}
          {showQuick && quickType === QuickType.deadline ? (
            <QuickOverlay
              onShowDateTimePanel={() => {
                setShowDateTimePanel(Overlay.quick2datePanel)
              }}
              onChange={changeQuick}
              showDelete={showDelete}
              isGetEnd={isGetEnd}
            ></QuickOverlay>
          ) : null}
        </div>
      }
    />
  )
}
