export default {
  associateProjectLimit: '关联项目超过上限',
  selectProjectGroup: '选择分组',
  addProjectGroup: '添加项目分组',
  ungroupedProject: '未分组项目',
  addProject: '添加项目',
  projectGroupGuideDesc: '归类有序，聚焦重点，高效管理。',
  moveToGroup: '移动至分组',
  canDragToGaoup: '可拖拽项目至该分组',
  afterProjectDelGroup: '删除后，不会删除该分组下的项目',
  confirmDelProjectGroup: '确认删除项目分组 {val1}',
  delGroup: '删除分组',
  newGroup: '新建分组',
  newProjectGroup: '新建项目分组',
  new: '新建',
  addTitleEnter: '输入任务名称，回车保存',
  backToParentTask: '返回上级任务',
  editingNotAllowed: '不支持编辑',
  splitSubtaskFail: '拆解子任务失败，请重试',
  regenerate: '重新生成',
  subtaskLimit: '单条任务最多支持添加{val1}个子任务',
  smartSubtask: '支持新建子任务',
  smartSubtaskDesc: '点击 {val1} 新建子任务，还可在任务详情页使用智能拆解，助力任务和项目高效推进！',
  newSubtask: '新建子任务',
  keepSubtasks: '不删除子任务',
  deleteSubtasksConcurrent: '同时删除子任务',
  AISplitSubtask: '智能拆解',
  transferIndependentSuccess: '已转为独立任务',
  transferIndependent: '转为独立任务',
  delSubTask: '删除子任务',
  subTaskPlaceholder: '输入子任务名称，回车确认',
  subTaskPlaceholder_2: '输入子任务名称',
  subTask: '子任务',
  viewSubTask: '查看子任务',
  past: '过去',
  enterGroup: '进入项目群',
  viewersHasNoPermission: '查看者没有邀请成员及分享项目权限，如需邀请他人，请联系项目管理员',
  haveBeenGranted: '已授予{val1}权限',
  membersAuth: '成员与权限',
  nitifyAlert: '关联群聊',
  bindTeam: '关联群聊',
  unbindTeam: '解绑项目群',
  confirmToUnbind: '确认解绑项目群？',
  operateSuccess: '操作成功',
  afterUnbind: '解绑后项目群内将不会收到通知提醒',
  clickJumpGTeam: '点击跳转项目群，开始沟通协作！',
  setTaskNotify: '任务通知可设置，进度随时掌握。',
  members: '成员 {val1}',
  view: '查看',
  onlySupportsAssociation: '仅支持关联已有群',
  successfullyAssociated: '成功关联已有群',
  onlyAssociatedWithYou: '仅可关联你做为群主或管理员的群',
  relatedGroupChat: '关联已有群',
  subscriptionNotification: '订阅通知',
  savedSuccessfully: '保存成功',
  operationFailedPlease_3: '操作失败，请重试',
  event: '事件',
  notificationScenario: '通知场景',
  preview: '预览',
  operation: '操作',
  member: '成员',
  userManual: '使用手册',
  userFeedback: '用户反馈',
  invitationSuccessful: '邀请成功',
  easyToLearnItems: '轻松上手项目',
  changeIcon: '更换图标',
  selectIcon: '选择图标',
  chooseAColor: '选择颜色',
  pleaseEnter: '请输入',
  theProjectGroupHasBeenCreated: '项目群已创建',
  clickHereToStand: '点击这里，立即跳转至项目群，开始沟通协作！',
  createANewProjectGroup: '新建项目群',
  atProjectInitiation: '在项目启动时，建议同时创建一个项目群，方便团队成员即时沟通与协作。',
  taskAssistant: '任务助手',
  inviteMembersToAdd: '邀请成员加入项目',
  theFollowingUsersWill: '以下用户将收到邀请',
  projectIconAnd: '项目图标和名称',
  pleaseEnterTheProject: '请输入项目名称',
  editProject: '编辑项目',
  preservation: '保存',
  confirmExitItem: '确定退出项目「{val1}」？',
  confirmDisbandItem: '确定解散项目「{val1}」？',
  signOut: '退出',
  afterExitingYouWill: '退出后，您将无法查看项目内的任务，但已分配的任务将保留。',
  managementHasBeenGranted: '已授予管理员权限',
  projectAwarded: '已授予项目成员权限',
  awardedForQueryOnly: '已授予仅查看权限',
  inviteTheProjectToBecome: '邀请项目成员',
  invitation: '邀请',
  shareToConversation: '分享至会话',
  copyLink: '复制链接',
  shareTo: '分享至会话',
  sharingSuccess: '分享成功',
  sharingFailed: '分享失败',
  copySuccessful: '复制成功',
  chooseGroupChatOr: '选择群聊或用户',
  searchGroupChatOr: '搜索群聊或用户',
  thereIsCurrentlyNoBelongingItem: '无所属项目',
  searchForProjects: '搜索项目',
  newProjectStatus: '新建项目状态：{val1}',
  updatedTo: '由{val1}更新为：{val2}',
  confirmTheChangeItem: '确认变更项目状态为：{val1}',
  afterTheChangeThereWillBe: '变更后将生成记录并通知项目成员',
  haveInHand: '进行中',
  suspend: '暂停',
  thereIsARiskInvolved: '有风险',
  file: '归档',
  administrators: '管理员',
  canModifyAll: '编辑项目名称、项目状态、管理任务、公共视图、自定义字段',
  projectMembers: '项目成员',
  modifyExceptProject: '编辑和完成任务',
  viewOnly: '查看者',
  onlyViewingIsNotAllowed: '仅查看任务，无法对任务和项目进行修改',
  exitTheProject: '退出项目',
  removePermissions: '移除权限',
  associateTasks: '将任务关联至项目[{val1}]',
  terminateTheTaskAnd: '解除任务与项目[{val1}]的关联',
  project: '项目',
  unnamedProject: '未命名项目',
  newProject: '新建项目',
  inviteTheTeamToBecome: '邀请团队成员，共同创建、分配和跟进任务。',
  viewAllItems: '查看全部项目',
  more: '更多',
  changeProjectStatus: '变更项目状态',
  inviteMembers: '邀请成员',
  memberManagement: '成员管理',
  searchForExistingItems: '搜索已有项目成员',
  fullName: '姓名',
  joinTime: '加入时间',
  role: '角色',
  waitingForPeopleToJoinTheGroup: '{val1}等{val2}人 加入群组已达上限，无法入群',
  inviteToJoinTheGroupApplication: '邀请入群申请',
  addTheFollowingMembers: '以下成员加入该群需要向其主管申请，请填写入群理由',
  reasonMustBeFilledIn: '填写理由（必填）',
  createANewProjectGroup_2: '新建项目群失败，请在“更多设置”中重试。',
  createANewGroupChat: '新建项目群',
  enterGroupChat: '进入群聊',
  newSchedule: '新建日程',
  membersCanCreate: '同时创建项目群',
  allProjects: '全部项目',
  entryName: '项目名称',
  state: '状态',
  thereAreCurrentlyNoProjectsAvailable: '暂无项目',
  thereAreMoreThanM: '所选文件中存在超过 200M 的文件',
  atMostOnce: '一次最多上传 5 个文件',
  afterTheTransferYouWill: '转让后，您将不再是该任务的创建人',
  confirmToRemoveFrom: '确定移除自己?',
  confirmToCancelTheClosure_2: '确定取消关注此任务？',
  theNextOneHasBeenGenerated: '已生成下一个循环任务，当前任务无法编辑该条件',
  thisWeek: '本周',
  notJoined: '未加入',
  participants: '执行人',
  contacts: '联系人',
  unfinishedToDoList: '未完成任务的执行人',
  sortBy: '按 {val1} 排序',
  thisTaskIsCurrentlyUnavailable: '该任务暂无执行人',
  characterCountExceeds: '字符数超过限制',
  completionTime: '完成时间',
  deleteComment: '删除评论',
  okToDeleteThis: '确定删除此评论？',
  me: '@我',
  mentionAllAchievements: '提及所有成员',
  everyone: '所有人',
  cannotSendEmpty: '不能发送空评论',
  nonToDoParticipation: '同时邀请加入任务',
  expression: '表情',
  mention: '提及',
  send: '发送',
  recentContacts: '最近联系人',
  switch: '↑↓ 切换',
  enter: 'Enter︎ 选择',
  escCancel: 'Esc 取消',
  thisIsAlreadyAToDoList: '已是该任务的创建人，无法添加为执行人',
  remove: '移除',
  thisFriday: '本周五',
  nextMonday: '下周一',
  nextFriday: '下周五',
  closeIndependentWindows: '回到主窗口',
  inASeparateWindow: '在独立窗口中打开',
  currentListDefault: '当前列表默认创建人为我',
  currentListDefault_2: '当前列表默认执行人为我',
  ifAddingStarts: '如需添加开始时间，请先设置截止时间',
  batchAdd: '批量添加',
  deadlineNotAvailable: '截止时间不能早于开始时间',
  startTimeNotAvailable: '开始时间不能晚于截止时间',
  deadlineSet: '已设置截止时间的任务将会展示在此处，你当前没有已计划的任务。',
  addAttachments: '添加附件',
  attachment: '附件',
  theCurrentVersionDoesNot: '当前版本不支持该功能，请升级至最新版本',
  currentlyNotSupportedForAdding: '暂不支持将创建人设置为执行人',
  successfulTransferRefersTo: '成功转让创建人，并退出了相关任务',
  successfulTransferRefersTo_2: '成功转让创建人',
  toDoFor: '{val1} 的任务',
  toDoFor_2: '与{val1} 的任务',
  return: '返回',
  sort: '排序',
  unsort: '默认',
  openToDo: '打开任务事项详情',
  start: '{val1} 开始',
  addParticipants: '添加执行人',
  reminderTime: '提醒时间',
  valPersonFinished: '{val1}人完成指派任务，',
  valPersonNotYet: '{val1}人未完成',
  addAComment: '@提及他人，按 Enter 快速发布评论',
  assignedBy: '创建人',
  allParticipants: '执行人 (全部完成 {val1}）',
  participantCompleted: '已完成 {val1}',
  incompleteParticipants: '未完成 {val1}',
  whenDeletingReminders: '删除提醒时间',
  laterToday: '今天晚些时候',
  nextWeek: '下周',
  everyDay_2: '每3天',
  selectItem: '已选{val1}项',
  appointedPersonForAcceptance: '创建人验收',
  assigneeCancellation: '{val1} 已取消任务的验收',
  newNews: '新消息',
  toDoCompleted_2: '已完成任务的验收',
  completedByEveryone: '所有人完成指派任务',
  assignmentCompleted: '已完成指派给我的任务',
  whenSettingReminders: '设置提醒时间',
  valReminder: '{val1} 提醒',
  valReminder_2: '从 {val1} 起 {val2} 提醒',
  assignee: '执行人',
  open: '打开',
  commented: '{val1}发表了评论',
  iHavePublishedAReview: '{val1}...等{val2}人发表了评论',
  allCompleted: '{val1}人全部完成',
  personCompleted_2: '{val1} 人完成',
  toDoList: '任务事项',
  theresNoMeAtTheMoment: '我指派给他人的任务事项将会展示在此处。你当前没有指派给他人的任务。',
  networkRequestLoss: '网络请求失败',
  toDoHasBeenDeleted: '任务已被删除',
  noPermissions: '无权限',
  addDeadline: '设置截止时间',
  addPriority: '设置优先级',
  addDuplicates: '设置循环任务',
  personCompleted: '{val1}人已完成',
  end: '{val1} 截止',
  repeat_2: '{val1} 循环',
  toDoCannotBeDone: '{val1}条任务无法标记完成',
  selectedWaitingItems: '已选择 {val1} 条任务，最多同时处理{val2}条任务',
  close: '关闭',
  allocationFrom: '分配来自',
  canOnlyDeleteMe: '只能删除我创建的任务',
  canOnlyDeleteMe_2: '只能删除我创建的任务，删除 {val1} 条任务',
  deleteToDoItems: '删除 {val1} 条任务',
  afterDeletingOneself: '移除自己后，将无法查看或编辑该任务',
  operationFailedPlease: '操作失败,请稍后重试',
  addUpToPeople: '最多添加{val1}人',
  deletePriority: '删除优先级',
  deleteDeadline: '删除截止日期',
  commentFailedPlease: '评论失败,请稍后重试',
  exitEsc: '退出 (Esc)',
  unableToMarkAll: '无法标记完成该任务',
  searching: '正在搜索中...',
  sendOrNot: '是否发送提醒？',
  hasBeenSent: '已发送',
  remind: '提醒',
  focusOnToday: '专注于今天',
  createAnd: '今天创建和截止的任务将会显示在此处。',
  youCurrentlyDoNotHave: '你当前没有任务事项，请享受你的一天。',
  year: '年',
  addedDeadline: '将任务截止时间设置为 {val1}',
  addedStartTime: '将任务开始时间设置为 {val1}',
  deletedDueDate: '删除了截止时间 {val1}',
  deletedStartTime: '删除了开始时间 {val1}',
  changeDeadline: '更新截止时间为 {val1}',
  changeStartTime: '更新开始时间为 {val1}',
  deletedComment: '删除了评论',
  addedAComment: '添加了评论',
  deletedToDoList: '删除了任务',
  replyTo: '回复 {val1}',
  viewDetails: '查看详情',
  distribution: '分配',
  assignToMe: '分配给我',
  noDataAvailable: '暂无数据',
  viewHere: '在此处查看你所有的任务事项。当前无任务事项，请享受你的一天。',
  markAsUrgent: '标记为 “紧急” 和 “重要” 的事项将会显示在此处。',
  notYet: '暂时没有已完成的任务。可去往 “全部” 内查看自己的任务事项。',
  noPointsForTheTimeBeing: '暂时没有分配给你的任务事项。',
  expiredPending: '已过期的任务事项将会显示在此处。当前没有已逾期的任务事项需要处理。',
  dateAndTime: '自定义',
  confirmToDeletePending: '确定删除任务：',
  search: '搜索',
  urgent: '紧急',
  high: '高',
  in: '中',
  low: '低',
  addTo: '添加',
  delete: '删除',
  noSearchResults: '无匹配结果',
  completed: '已完成',
  hangInTheAir: '未完成',
  time: '时分',
  repeat: '循环',
  determine: '确定',
  everyWorkingDay: '每个工作日',
  everyDay: '每天',
  weekly: '每周',
  biweekly: '每双周',
  monthly: '每月',
  annually: '每年',
  source: '来源：',
  source_1: '来源',
  comment: '评论',
  whole: '全部',
  editRecord: '日志',
  createdToDo: '创建了任务',
  toDoCompleted: '完成了任务',
  rebuildTheToDoList: '重启了任务',
  changeTheTitleTo: '更新标题为 {val1}',
  assignToDoTo: '将 {val1} 添加为任务执行人',
  unassignPending: '移除了任务执行人 {val1} ',
  transferAssignor_2: '转让了任务创建人至 {val1} ',
  reply: '回复',
  deadline: '截止时间',
  priority: '优先级',
  addComments: '添加备注',
  cancel: '取消',
  screen: '筛选',
  empty: '清空',
  throughKeywords: '通过关键词搜索',
  newSuccessfully: '任务新建成功',
  cancelToDo: '取消任务',
  title: '标题',
  assignedTo: '执行人',
  notLoggedIn: '未登陆',
  needToBeDealtWith: '任务',
  checkInTheCalendar: '在日历中查看',
  today: '今天',
  day: '天',
  hour: '小时',
  minute: '分钟',
  showCompleted: '显示已完成任务',
  creationTime: '创建时间',
  createdToday: '今天创建',
  deferred: '逾期',
  tomorrow: '明天',
  inTheFutureAndToday: '将来（今天之后）',
  thereIsADeadline: '有截止时间',
  noDeadline: '无截止时间',
  withPriority: '有优先级',
  noPriority: '无优先级',
  remarks: '备注',
  withRemarks: '有备注',
  noRemarks: '无备注',
  networkException: '网络异常',
  transferAssignor: '转让创建人',
  forwardTo: '转发到',
  share: '分享',
  viewAttachments: '查看附件',
  viewNotes: '查看备注',
  enterTitleBack: '输入标题，回车即可添加任务事项',
  viewPersonalInformation: '查看个人资料',
  assignedBy_2: '(创建人)',
  expandTheDayOfWaiting: '展开',
  putAwayTheDayAndWait: '收起',
  clickToCancelTheSame: '点击取消同步',
  addedToDay: '已添加至日历',
  clickToSynchronize: '点击同步',
  addToCalendar: '添加至日历',
  successfullyAdded: '添加成功',
  cancelledAdding: '已取消添加到日历',
  addToCalendar_2: '添加到日历',
  cancelAddingTo: '取消添加到日历',
  startTime: '开始时间',
  setDuplicateRules: '设置重复规则',
  atTheBeginningOfTheSetting: '设置开始时间',
  clickToRebuildTheTask: '点击重启任务',
  clickToCompleteTheTask: '点击完成任务',
  addTitle: '输入任务名称',
  week: '周',
  month: '月',
  viewLayoutWord: '视图布局，字段配置',
  allocation: '配置',
  showWeekends: '显示周末',
  fieldConfiguration: '字段配置',
  unplannedDeployment: '展开未计划列表',
  collapseUnplanned: '收起未计划列表',
  unplannedAppointment: '未计划的任务',
  list: '列表',
  plan: '计划',
  theCurrentViewDoesNot: '计划视图不支持排序',
  attachments: '附件 {val1} 个',
  executor: '执行人 {val1} 人',
  other: '其他 {val1} 个',
  notSet: '未设置',
  everyoneNeedsTo: '所有执行人均需完成',
  anyoneCanFinishIt: '任意执行人完成即可',
  addFollowers: '添加关注人',
  notCurrentlySupported: '暂不支持将创建人/执行人设置为关注人',
  end_2: '{val1} 截止',
  markingIncomplete: '标记未完成',
  markingCompleted: '标记完成',
  cancelSelection: '取消选择',
  nonTaskResponsible: '@非任务执行人时邀请加入任务',
  peopleFollow: '人关注',
  confirmToCancelTheClosure: '你将无法再查看此任务',
  dontPayAttentionAnymore: '不再关注',
  followedBy: '已关注',
  focusOnTasks: '关注任务',
  taskCompleted: '任务已完成',
  completeTheTask: '完成任务',
  onlyIHaveCompletedIt: '仅我完成',
  allCompleted_2: '全部完成',
  restartMyTask: '重启我的任务',
  restartEverything: '重启全部任务',
  andThePersonInCharge: '还有执行人未完成，确认完成整个任务?',
  confirmCompletion: '确认完成',
  restartTask: '重启任务',
  afterRestartingTheTask: '重启后，所有执行人的任务将回到未完成状态，确认重启？',
  confirmRestart: '确认重启',
  theTaskHasBeenRestarted: '任务已重启',
  startDate: '开始日期',
  specificTime: '具体时分',
  startDateSelection: '开始日期(选填)',
  otherTimes: '其他时间',
  createdInAConversation: '创建于会话：',
  changeToResponsible: '将关注人变更为执行人将自动取消关注人身份',
  time_2: '时间',
  fieldHiding: '字段隐藏',
  addConditions: '添加条件',
  pleaseSearchFirstAndThen: '请先搜索再选择',
  quickFilterBar: '快捷筛选条件',
  allFilteringItems: '全部筛选条件',
  grouping: '分组: {val1}',
  grouping_2: '分组',
  newTask: '新建任务',
  hide: '隐藏',
  reverseOrder: '倒序',
  positiveSequence: '正序',
  sort_2: '排序:',
  task: '任务',
  shortcuts: '快捷方式',
  myTask: '我的任务',
  bulletinBoard: '看板',
  addNewTab: '新增标签页',
  rename: '重命名',
  tabOperation: '标签页操作',
  allocatedTo: '分配至',
  nothing: '无',
  taskName: '任务名称',
  followPeople: '关注人',
  deadline_2: '截止时间',
  clickOnDescendingOrder: '点击倒序',
  clickOnAscendingOrder: '点击正序',
  assignPerson: '分配人',
  personLiable: '责任人',
  belongingProject: '所属项目',
  allTasks: '全部任务',
  whatIAmConcernedAbout: '我关注的',
  iCreatedIt: '我创建的',
  completionStatus: '完成状态',
  deadlineToday: '今天截止',
  deadlineNextWeek: '下周截止',
  beforeToday: '今天之前',
  nextMonth: '下个月',
  customSorting: '自定义排序',
  futureDays: '未来7天',
  inTheFuture: '以后',
  notArranged: '未安排',
  completedTheEntireTask: '完成了整个任务',
  completedTheTask: '完成了任务',
  completedTheTask_2: '完成了任务，整个任务已完成',
  addVal: '将 {val1} 添加为任务关注人',
  removedTask: '移除了任务关注人 {val1}',
  valFollow: '{val1} 关注了该任务',
  cancelVal: '{val1} 取消了对该任务的关注',
  willValBe: '将 {val1} 由关注人变更为执行人',
  iRestartedMyself: '重启了自己的任务，整个任务已重启',
  restartedTheEntireSystem: '重启了整个任务',
  updatedCompleted: '已更新完成方式为 任意执行人完成即可',
  updatedCompleted_2: '已更新完成方式为 所有执行人均需完成',
  updatedCompleted_3: '已更新完成方式为 {val1}',
  peopleFollow_2: '0人关注',
  cancelFollow: '取消关注',
  clickToFollowRen: '点击关注任务',
  addDeadline_2: '添加截止时间',
  thisWeeksDeadline: '本周截止',
  noGrouping: '无分组',
  noEditingPermission: '无编辑权限',
  itIsAlreadyATaskCreation: '已是任务创建人，不支持添加为关注人',
  itIsAlreadyATaskExecution: '已是任务执行人，不支持添加为关注人',
  people: '人',
  noExecutorYet: '尚无执行人完成任务',
  allExecutors: '所有执行人已完成任务',
  closingDate: '截止日期',
  confirmDeletion: '确认删除',
  humanExecution: '{val1}人 执行',
  transferAndTransferFrom: '转让并将自己添加为执行人',
  transferAndExit: '转让并退出任务',
  confirmTheTransferOfResponsibilities: '确认转让任务创建人身份？',
  enterComment: '输入评论',
  iHaveCompletedIt: '我已完成',
  realTimeMonitoringOfAnyTask: '实时掌握任务进展',
  afterFollowingTheTask: '关注任务后，你将通过任务助手机器人收到进展提醒。',
  immediateMedicalExamination: '立即体验',
  kanbanView: '看板视图',
  taskManagementAgain: '任务管理再升级，看板视图带来全新体验',
  iGotIt: '我知道了',
  welcomeToParticipateInTheProduction: '欢迎参与产品调研',
  helpCenter: '帮助中心',
  addPriority_2: '添加优先级',
  atTheBeginningOfAdding: '添加开始时间',
  viewComments: '查看评论',
  humanCompletion: '{val1}人完成',
  addAsFollow: '添加为关注人',
  cutOffBeforeToday: '今天之前 截止',
  deadlineToday_2: '今天 截止',
  deadlineTomorrow: '明天 截止',
  thisWeeksDeadline_2: '本周 截止',
  deadlineNextWeek_2: '下周 截止',
  deadlineNextMonth: '下个月 截止',
  noDeadlineSet: '未设置截止时间',
  unfinishedTasks: '未完成的任务',
  completedTasks: '已完成的任务',
  deleteTask: '删除任务',
  noTasksAvailableAtTheMoment: '暂无任务',
  thereIsCurrentlyNoScreeningThatMeetsTheCriteria: '暂无符合筛选条件的任务，请尝试调整筛选项',
  chooseToCompleteOr: '选择完成或重启任务的方式',
  reloadingInProgress: '重新加载中',
  taskStatusCutoff: '任务状态、截止时间、执行人',
  useCustom: '使用自定义筛选，助你聚焦重点数据！',
  itCanAlsoBeBasedOn: '还可以根据多个条件组合筛选，如 {val1}等。',
  addPersonnel: '添加人员',
  noOptionsAvailable: '没有选项',
  selectDate: '选择日期',
  publicView: '公共视图、个人视图数量均已达上限，移除后可新增',
  numberOfPersonalViews: '个人视图数量已达上限，移除后可新增',
  noPermissionToAdd: '无权限新增视图',
  managementView: '管理视图（{val1}）',
  addView: '新增视图',
  confirmToDeleteThis: '确定删除此视图',
  deleteView: '删除视图',
  theTaskWillNotBeAffected: '任务不会被删除，仅此视图将被移除',
  thisViewIsForTheSystem: '此视图为系统视图',
  thisViewIsForPublic: '此视图为公共视图，管理员可操作修改',
  deleted: '已删除',
  deleteFailed: '删除失败',
  personalView: '个人视图',
  onlyYouCanDoItYourself: '仅你自己可见',
  publicView_2: '公共视图',
  allMembersAre: '所有成员均可见',
  saveViewAs: '保存视图成功',
  viewName: '视图名称',
  enterViewName: '输入视图名称',
  viewMode: '视图模式',
  viewRange: '视图范围',
  thisTypeHasReached: '该类型已达上限，移除后可新增',
  thisViewIsFor: '此视图为个人视图，仅你可见',
  publicView_3: '公共视图、个人视图均已达上限，移除后可新增',
  saveAsNewVision: '另存为新视图失败',
  saveAsNewVision_2: '另存为新视图',
  calendar: '日历',
  addToProject: '添加至项目时，默认将任务协作人添加至项目',
  projectManagementOnly: '仅项目管理员可修改',
  updateStatus: '更新状态',
  editFields: '编辑字段',
  deleteField: '删除字段"{val1}"',
  taskAfterDeletion: '删除后，任务中已填字段内容将被同步删除',
  moveFromProject: '从项目中移除',
  pleaseChoose: '请选择',
  text: '文本',
  singleChoice: '单选',
  multipleChoice: '多选',
  personnel: '人员',
  date: '日期',
  number: '数字',
  integer: '整数',
  retainDecimalPlaces: '保留1位小数',
  retainDecimalPlaces_2: '保留2位小数',
  percentage: '百分比',
  percentageIsSmall: '百分比（2位小数）',
  modifiedSuccessfully: '修改成功',
  newField: '新建字段',
  enterFieldLabels: '输入字段标题',
  fieldType: '字段类型',
  option: '选项',
  includingTimeDivision: '包含时分',
  format: '格式',
  option_2: '选项1',
  option_3: '选项2',
  option_4: '选项{val1}',
  thisItemCannotBe: '该项不可为空',
  notFilledIn: '未填写',
  saveAsDefault: '保存为默认视图',
  afterSavingItWillBeMade: '保存后，将作为当前视图的默认展示范围，同步更新给项目成员',
  cover: '覆盖',
  saveViewLost: '保存视图失败，请重试',
  saveView: '保存视图',
  newView: '新建视图',
  managementView_2: '管理视图',
  more_2: '更多({val1})',
  addTo_2: '添加{val1}',
  createAGroupChat: '创建群聊',
  allWithinTheProject: '项目内所有成员默认同步到群中，立即创建同名项目群？',
  establish: '创建',
  newlyBuiltProject: '[项目]{val1}，新建成功',
  removeFromNavigation: '从导航移出',
  addToNavigation: '添加到导航',
  projectMembersHave: '项目成员已达上限',
  operationFailedPlease_2: '操作失败，请保留至少1位管理员',
  atLeastInTheProject: '项目中至少保留一个公共视图',
  dissolution: '解散',
  afterDisbandingTheProject:
    '解散项目后，项目内的任务将会保留，但任务内的自定义字段将不会被保留；被解散的项目将无法恢复',
  noSearchResultsFound: '无搜索结果',
  notSupportedForModification: '不支持修改字段类型',
  dissolveTheProject: '解散项目',
  startImmediately: '立即开始你的第一个项目',
  notFoundIncludes: '未找到包含关键字 “{val1}” 的项目，请尝试更换关键词',
  next: '下一个',
  addCustom: '添加自定义字段',
  projectManager:
    '项目管理员可以为任务定制专属字段，支持文本、单选、多选、人员、日期、数字，确保每个任务都能符合项目的特定需求',
  personalizedTasks: '个性化任务视图',
  screenBasedOnDemands: '根据诉求筛选、排列任务，项目管理员可以将视图保存为“公共视图”，项目成员可以保存为“个人视图”',
  quickProjectDesign: '项目快捷设置',
  projectManager_2: '项目管理员可以编辑项目图标、名称、状态、邀请成员、分享等操作',
  thePrevious: '上一个',
  newTimelineView: '新增时间线视图',
  trackProgress: '轻松了解项目全貌，直观把控项目进度。',
  setTaskTimeframes: '设置任务的起止时间',
  planSmartWith: '为任务设置开始时间和截止时间，形成时间条',
  setStartDueDate: '设置开始/截止时间',
  dueToday: '今天截止',
  dueTomorrow: '明天截止',
  setSpecificTime: '具体时间',
  timeline: '时间线',
  collapseTaskList: '收起任务列表',
  expandTaskList: '展开任务列表',
  cardConfig: '卡片配置',
  cardSideColor: '卡片侧边颜色',
  completionIcon: '完成图标',
  addGroup: '添加分组',
  previousScreen: '上一屏',
  nextScreen: '下一屏',
  collapseTasksOneClick: '一键收起任务',
  expandTasksOneClick: '一键展开任务',
  assignTasks: '新增任务',
  triggerNotificationAfterFilling: '新建任务并添加执行人，发送消息到项目群',
  triggerNotificationAfterExecuted: '任务完成时，发送消息到项目群',
  taskOverdue: '任务已逾期',
  triggerNotificationOverdue: '截止时间前未完成任务，发送消息到项目群',
  associateGroupFirst: '请先绑定群聊',
  groupNotification: '项目群通知',
  viewPreview: '预览',
  associatedGroup: '已关联项目群:',
  edit: '编辑',
  sendComment: '发送评论',
  transformToSubtask: '转为子任务',
  searchTask: '搜索任务',
  searchNoResult: '搜索无结果',
  recent: '最近',
  setParentTask: '设置父任务',
}
