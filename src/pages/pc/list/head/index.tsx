import { pp } from '@popo-bridge/web';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { history } from 'umi';

import { Button, Icon } from '@/components/basic';
import { RootState } from '@/models/store';
import { getSessionInfo } from '@/utils';
import I18N from '@/utils/I18N';

import s from './index.less';

const Head: React.FC = () => {
  const [title, setTitle] = useState<string>('');

  const { sessionId = '', sessionType } = useSelector((state: RootState) => ({
    language: state.user.language,
    sessionId: state.imTodoList.sessionId,
    sessionType: state.imTodoList.sessionType,
    filters: state.imTodoList.filters,
  }));

  useEffect(() => {
    if (sessionId) {
      getSessionInfo({
        source: sessionType!,
        sourceId: sessionId,
      }).then(({ name }) => {
        setTitle(name);
      });
    }
  }, [sessionType, sessionId]);

  const showTitle = useMemo(() => {
    if (title) {
      if (Number(sessionType) === 1) {
        return I18N.templateNode(I18N.auto.toDoFor_2, {
          val1: <span className={'ellipsis'}>{title}</span>,
        });
      } else {
        return I18N.templateNode(I18N.auto.toDoFor, {
          val1: <span className={'ellipsis'}>{title}</span>,
        });
      }
    }
    return null;
  }, [title, sessionType]);

  return (
    <div className={s.head}>
      <div className={s.title}>{showTitle}</div>
      <div className={s.options}>
        <Button
          className={s.add}
          icon={<Icon name="icon-sys_add" fontSize={16}></Icon>}
          type="checked-neutral"
          onClick={(e) => {
            pp.openTodoCreate({
              sessionId,
              sessionType: sessionType!,
            }).then((res) => {
              console.log('openTodoCreate callback-->:', res);
              const { status, data } = res;
              if (status === 1) {
                // 不要改这个todoId 客户端桥给的
                history.push(
                  `/pc/detail/${data?.todoId}?sessionId=${sessionId}&sessionType=${sessionType}&from=pc-list`
                );
              }
            });
          }}
        >
          {I18N.auto.newTask}
        </Button>
      </div>
    </div>
  );
};
export default Head;
