.editMember {
  .label {
    margin-bottom: 8px;
    font-size: 13px;
    color: var(--TextSecondary);
    line-height: 24px;
    font-weight: 400;
    span {
      margin-left: 4px;
      color: var(--R600);
    }
  }
  .value {
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    .search {
      position: absolute;
      left: 0;
      right: 0;
      top: 38px;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid var(--aBlack12);
      background-color: var(--bgTop);
      box-shadow: var(--ComBoxShadow6);
    }
  }
}

.addPeople {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--aBlack12);
  border-radius: 6px;
  .input {
    flex: 1;
    height: 34px;
  }
  .line {
    flex-shrink: 0;
  }
  .addPeopleBtn {
    flex-shrink: 0;
    margin-right: 6px;
  }
}

.member {
  display: flex;
  flex-direction: column;
  padding: 12px 4px 12px 12px;
  margin-top: 8px;
  border: 1px solid var(--aBlack12);
  border-radius: 6px;
  .dsec {
    font-size: 12px;
    line-height: 18px;
    font-weight: 400;
    color: var(--TextPrimary);
  }
  .list {
    max-height: 160px;
    padding-right: 8px;
    overflow-y: auto;
  }
  .item {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 12px;
    .avatar,
    .permission {
      flex-shrink: 0;
    }
    .avatar {
      margin-right: 8px;
    }
    .permission {
      margin-left: 8px;
      margin-right: 8px;
    }
    .name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      color: var(--TextPrimary);
      font-weight: 400;
      font-size: 14px;
    }
    .close {
      color: var(--IconTertiary);
      font-size: 16px;
    }
  }
}
