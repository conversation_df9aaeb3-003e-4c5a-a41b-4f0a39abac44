.projectList {
  display: flex;
  height: 100%;
  flex-direction: column;
  min-width: 600px;
}
.list {
  position: relative;
  flex: 1;
  padding-bottom: 8px;
  margin-left: 20px;
  margin-right: 20px;
  overflow: auto;

  width: 100%;
  height: calc(100% - 64px);

  .columName {
    display: flex;
    align-items: center;
    overflow: hidden;
    height: 50px;
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .creating__input {
      position: absolute;
      z-index: 1;
      height: 100%;
      width: 100%;

      :global {
        .rock-input {
          color: var(--TextPrimary-strong);
          font-size: 14px;
        }
      }

      &:global(.rock-input-wrapper) {
        border-radius: 0;
      }

      &::before {
        border-radius: 0;
      }
    }

    &.editing {
      position: relative;
      width: 100%;
    }
  }
  .projectIcon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 12px;
  }
  .time {
    line-height: 22px;
    font-size: 13px;
    color: var(--TextSecondary);
  }
  :global {
    .sortable-tree {
      // width: fit-content;
      min-width: 1063px;
    }
    .todo-drag {
      top: 13px;
    }
    .todo-thead {
      .todo-th {
        border-top: none;
      }
    }
    .todo-table .todo-tr .todo-th,
    .todo-table .todo-tr .todo-td {
      border-right: none;
    }
    .todo-table .todo-tr .todo-th:hover {
      background-color: transparent;
    }
  }
}
.members {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  cursor: pointer;
  padding-left: 6px;
}
.keyword {
  font-weight: bold;
  color: var(--TextPrimary);
}

.project__tree {
  :global {
    .rc-tree-title {
      width: 100%;
    }

    .rc-tree-node-content-wrapper {
      position: static !important;
      background-color: transparent !important;
      min-width: 0 !important;
    }

    .rc-tree-treenode {
      position: relative;
      padding: 0 0 0 20px;
      border-bottom: 1px solid var(--aBlack6);

      &:hover {
        background-color: var(--N50);
        .move__handler {
          opacity: 1;
        }
      }
    }
  }
}

.list__header {
  position: sticky;
  top: 0;
  z-index: 11;
  padding-left: 38px;
  border-bottom: 1px solid var(--aBlack6);
  min-width: 1063px;
  background-color: var(--bgBottom);

  .content {
    display: grid;
    grid-template-columns: minmax(3.2rem, 1fr) 1.6rem 1.6rem 1.8rem 0.8rem;
    grid-template-rows: 36px;
    column-gap: 0.16rem;
    align-items: center;
    padding-right: 20px;
    background-color: transparent;
    min-width: 100%;

    .head {
      color: var(--TextSecondary, #111111b8);
      font-size: 13px;
      line-height: 22px;
      padding-left: 6px;
    }
  }
}

.switch__content {
  justify-content: flex-start;
}

.switch__icon {
  color: var(--IconPrimary, #1111119e);

  &.arrow__right {
    transform: rotate(-90deg);
    &.hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
}
