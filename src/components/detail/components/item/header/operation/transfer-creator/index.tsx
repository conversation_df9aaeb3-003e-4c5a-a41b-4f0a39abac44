import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

import { ApiTaskGetGetResponse, apiTodoAssignerTransferToPost } from '@/api';
import { Icon, IconBtn, Modal, Radio, Tooltip } from '@/components/basic';
import NewAssignor from '@/components/basic/people-picker/assignor-new';
import { Dispatch } from '@/models/store';
import { UserInfo } from '@/types';
import { TransferSelectType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { getIsInSession } from '@/models/utils';
export interface Props {
  taskId: number;
  participant?: boolean; //是参与人
  iconBtnClass?: string;
  assigner?: ApiTaskGetGetResponse['assigner'];
  assignerUid?: string;
  executorList?: any;
  assigneeUids?: string[];
  containerElement: HTMLElement;
  onTransferAndExit?: () => void;
}

const TransferCreator: React.FC<Props> = (props) => {
  const {
    taskId,
    assignerUid,
    executorList = [],
    assigneeUids,
    containerElement,
    onTransferAndExit,
  } = props;
  const dispatch = useDispatch<Dispatch>();

  const [value, setValue] = useState(TransferSelectType.transferringAndSelfToExecutor);
  const [visible, setVisible] = useState<boolean>(false);
  const checkRadioRef = useRef(value);

  const options = [
    {
      name: I18N.auto.transferAndTransferFrom,
      value: TransferSelectType.transferringAndSelfToExecutor,
    },
    { name: I18N.auto.transferAndExit, value: TransferSelectType.transferringAndExit },
  ];

  const onAssignorChange = (user: UserInfo, extras: { type: TransferSelectType }) => {
    setVisible(false);
    if (assigneeUids?.includes(assignerUid)) {
      Modal.info({
        getContainer: () => (containerElement ? containerElement : document.body),
        title: I18N.auto.confirmTheTransferOfResponsibilities,
        width: 400,
        mask: !getIsInSession(),
        content: I18N.auto.afterTheTransferYouWill,
        centered: true,
        className: 'transferCreatorModal',
        closable: false,
        onOk: () => {
          apiTodoAssignerTransferToPost({
            exitTask: 0,
            newAssigner: user.uid,
            taskId: taskId,
          }).then(() => {
            pp.showToast({
              title: I18N.auto.successfulTransferRefersTo_2,
            });
            dispatch.viewSetting.updateItemByDetail({ taskId });
          });
        },
        zIndex: 100000000,
      });
    } else {
      Modal.info({
        getContainer: () => (containerElement ? containerElement : document.body),
        title: I18N.auto.confirmTheTransferOfResponsibilities,
        width: 400,
        mask: !getIsInSession(),
        content: (
          <div>
            <Radio.Group
              options={options}
              defaultValue={value}
              onChange={(e) => {
                setValue(e.target.value);
                checkRadioRef.current = e.target.value;
                e.stopPropagation();
                e.preventDefault();
              }}
              className={s.transferRadioGroup}
            />
          </div>
        ),

        centered: true,
        className: 'transferCreatorModal',
        closable: false,
        onOk: () => {
          apiTodoAssignerTransferToPost({
            exitTask:
              checkRadioRef.current === TransferSelectType.transferringAndSelfToExecutor ? 0 : 1,
            newAssigner: user.uid,
            taskId: taskId,
          }).then(() => {
            dispatch.viewSetting.refreshDataByDataChange({
              refreshList: true,
              refreshCount: true,
            });
            if (checkRadioRef.current === TransferSelectType.transferringAndSelfToExecutor) {
              pp.showToast({
                title: I18N.auto.successfulTransferRefersTo_2,
              });
              dispatch.detail.getTodoDetail(taskId);
            } else {
              pp.showToast({
                title: I18N.auto.successfulTransferRefersTo,
              });
              onTransferAndExit?.();
            }
          });
        },
        zIndex: 100000000,
      });
    }
  };

  return (
    <NewAssignor
      value={{ uid: assignerUid }}
      onChange={onAssignorChange}
      className={s.detailTransferCreator}
      assignerUid={assignerUid}
      executorList={executorList} // 用来判断是否要取消执行人身份
      visible={visible}
      onVisibleChange={setVisible}
    >
      <Tooltip title={I18N.auto.transferAssignor}>
        <span className="ml-10">
          <IconBtn
            iconName="icon-pc_details_transfer"
            className={classNames(s.transferIcon, s.onlyIcon, {
              [s.hoverIcon]: visible,
            })}
          ></IconBtn>
        </span>
      </Tooltip>
    </NewAssignor>
  );
};

export default TransferCreator;
