import { useState } from 'react';

import { Dropdown, IconBtn, Menu, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import { EditType } from '../interface';
import s from './index.less';

interface TabsProps {
  onChange: (type: EditType) => void;
  iconClassName?: string;
}

const Edit: React.FC<TabsProps> = (props) => {
  const { onChange, iconClassName } = props;
  const overlayBasic = () => {
    return (
      <Menu
        onClick={(value) => {
          if (value.key === 'rename') {
            onChange(EditType.rename);
          } else {
            onChange(EditType.remove);
          }
        }}
      >
        <Menu.Item key={'rename'}>{I18N.auto.rename}</Menu.Item>
        <Menu.Item key={'remove'}>{I18N.auto.delete}</Menu.Item>
      </Menu>
    );
  };
  return (
    <Dropdown overlay={overlayBasic()} trigger="click">
      <Tooltip title={I18N.auto.tabOperation}>
        <span>
          <IconBtn
            iconClassName={iconClassName}
            className={s.more}
            iconName="icon-pc_plane_more"
          ></IconBtn>
        </span>
      </Tooltip>
    </Dropdown>
  );
};
export default Edit;
