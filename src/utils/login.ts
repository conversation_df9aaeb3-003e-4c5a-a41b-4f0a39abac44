import {
  Method,
  QueryStringArrayFormat,
  RequestBodyType,
  ResponseBodyType,
} from 'yapi-to-typescript';

import request from '@/utils/request';
const apiLoginConfig = {
  rawData: {},
  mockUrl: '',
  devUrl: 'http://**************:8083/api/uinfo/ncc/token',
  prodUrl: '',
  path: '/api/uinfo/ncc/token',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: 'data',
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'apiTodoFinishPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
};

interface Options {
  email: string;
  // password: string;
  // appVersion: string;
  // deviceId: number;
  // device: number;
  // deviceName: string;
}

const devLogin = (data: Options) => {
  return request({
    ...apiLoginConfig,
    data: {},
    path: `${apiLoginConfig.path}?test=${'1'}&u=${data.email}`,
    hasFileData: false,
    fileData: {},
    allData: {},
    queryNames: ['test', 'u'],
    getFormData: () => {
      return new FormData();
    },
  });
};

export { devLogin };
