import React, {
  cloneElement,
  FC,
  isValidElement,
  PropsWithChildren,
  useRef,
  useState,
} from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import {
  apiProjectFieldGet,
  apiProjectFieldPost,
  apiProjectFieldPut,
  ApiProjectFieldPutResponse,
} from '@/api';
import { Dispatch, RootState } from '@/models/store';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import { Checkbox, Form, Input, Message, Modal, Popover, PopoverProps, Select } from '../basic';
import {
  FieldNumberFormatEnum,
  FieldNumberFormatOptions,
  FieldSchema,
  FieldTypeEnum,
  FiledTypeList,
  SelectFieldOption,
} from './const';
import s from './index.less';
import { PopoverItem } from './popoverItem';
import SelectOptionList from './selectOption';
import ToolTipSelect from './tooltip-select';
import { getIsInSession } from '@/models/utils';

interface Props extends Omit<FieldFormProps, 'onSuccess' | 'onCancel' | 'projectId'> {
  children?: React.ReactNode;
  placement?: PopoverProps['placement'];
  visible?: boolean;
  onVisibleChange?: (v: boolean) => any;
  onSuccess?: () => any;
}

function FieldEditPopover({ children, placement, fieldId, onSuccess, ...restProps }: Props) {
  const dispatch = useDispatch<Dispatch>();

  const { openModal } = useFieldForm({ onSuccess, dispatch });

  const handlePreCreate = (item: any) => {
    openModal({
      fieldId,
      initialValues: { type: item.value },
    });
    onSuccess?.();
  };

  const items = FiledTypeList.map((item) => {
    return (
      <PopoverItem
        key={item.value}
        left={
          <span className={s.popover__item}>
            <span className={s['popover__item--icon']}>{item.icon}</span>
            <span className={s['popover__item--name']}>{item.name}</span>
          </span>
        }
        interactive
        onClick={() => {
          handlePreCreate(item);
        }}
        className={s['config__item--label']}
      />
    );
  });

  return (
    <Popover
      placement={placement}
      // visible={visible}
      // onVisibleChange={changeVisible}
      // destroyTooltipOnHide
      showArrow={false}
      overlayClassName={s.popOverlay}
      trigger="hover"
      content={<div className={s.popover__content}>{items}</div>}
    >
      {children}
    </Popover>
  );
}

interface FieldFormProps {
  showTitle?: boolean;
  fieldId?: any;
  onCancel?: () => any;
  onSuccess?: (res: ApiProjectFieldPutResponse) => any;
  onValuesChange?: (value: FieldSchema, hadChanged: boolean) => any;
  initialValues?: any;
}

interface IFieldForm {
  onSuccess?: (res: ApiProjectFieldPutResponse) => any;
  dispatch: Dispatch;
}

export function useFieldForm({ onSuccess, dispatch }: IFieldForm = {} as IFieldForm) {
  const [form] = Form.useForm();
  const [disabled, setDisabled] = useState(true);
  const modalInstance = useRef<any>();

  const { navigatorId } = useSelector((state: RootState) => ({
    navigatorId: state.viewSetting.navigatorId,
  }));

  const onValuesChange = (changedValues: any, values: any) => {
    const isDisabled = !values.name;
    setDisabled(isDisabled);
    modalInstance.current.update({
      okButtonProps: {
        disabled: isDisabled,
      },
    });
  };

  const openModal = ({ initialValues, fieldId }: FieldFormProps) => {
    const handleOk = () => {
      form!.validateFields().then((values) => {
        let data = {
          fieldId,
          projectId: navigatorId,
          fieldType: values.type,
          fieldSchema: JSON.stringify(values),
          fieldOptions: values.options?.map((opt: SelectFieldOption) => opt.value),
        };
        return (isEdit ? apiProjectFieldPut(data) : apiProjectFieldPost(data)).then((res) => {
          Message.success(!fieldId ? I18N.auto.successfullyAdded : I18N.auto.modifiedSuccessfully);

          dispatch.viewSetting.updateCustomField({
            fieldId: res.fieldId,
            schema: JSON.parse(res.fieldSchema!),
            fieldVersion: res.fieldVersion,
          });
          onSuccess?.(res);
          modalInstance.current?.destroy();
        });
      });
    };

    const isEdit = !!fieldId;

    if (isEdit) {
      apiProjectFieldGet({
        projectId: navigatorId,
        fieldId,
      }).then((res) => {
        if (res.fieldSchema) {
          let schema = JSON.parse(res.fieldSchema);

          form.setFieldsValue(schema);
          // initialRef.current = schema;
        }
      });
    }

    const formNode = (
      <Form
        initialValues={initialValues}
        form={form}
        layout="vertical"
        className={s.form}
        onValuesChange={onValuesChange}
      >
        <Form.Item name="name" label={I18N.auto.title} initialValue={''}>
          <Input
            autoComplete="off"
            autoFocus
            placeholder={I18N.auto.enterFieldLabels}
            maxLength={20}
            trimOnBlur
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item name="type" label={I18N.auto.fieldType} initialValue={FieldTypeEnum.text}>
          <ToolTipSelect isEdit={isEdit}></ToolTipSelect>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(preValues, nextValues) => {
            return preValues?.type !== nextValues?.type;
          }}
        >
          {() => {
            switch (form.getFieldValue('type')) {
              case FieldTypeEnum.multiOption:
              case FieldTypeEnum.option:
                return (
                  <Form.Item name="options" noStyle>
                    <SelectOptionList form={form}></SelectOptionList>
                  </Form.Item>
                );

              case FieldTypeEnum.user:
                return <Form.Item name="multip" hidden initialValue={true}></Form.Item>;
              case FieldTypeEnum.datetime:
                return (
                  <Form.Item
                    label={I18N.auto.option}
                    name="format"
                    getValueProps={(val) => {
                      return {
                        checked: val == PPTimeFormat.dateAndTime,
                      };
                    }}
                    valuePropName="checked"
                    normalize={(val) => {
                      return val ? PPTimeFormat.dateAndTime : PPTimeFormat.olayDay;
                    }}
                    initialValue={PPTimeFormat.olayDay}
                  >
                    <Checkbox>{I18N.auto.includingTimeDivision}</Checkbox>
                  </Form.Item>
                );

              case FieldTypeEnum.number:
                return (
                  <Form.Item
                    label={I18N.auto.format}
                    name="format"
                    initialValue={FieldNumberFormatEnum.int}
                  >
                    <Select options={FieldNumberFormatOptions}></Select>
                  </Form.Item>
                );
            }
          }}
        </Form.Item>
      </Form>
    );

    modalInstance.current = Modal.open({
      title: isEdit ? I18N.auto.editFields : I18N.auto.newField,
      content: formNode,
      className: s.modal,
      width: 400,
      centered: true,
      okButtonProps: {
        disabled,
      },
      okText: I18N.auto.determine,
      cancelText: I18N.auto.cancel,
      maskClosable: false,
      mask: !getIsInSession(),
      onOk: handleOk,
      onCancel: () => {
        modalInstance.current?.destroy();
      },
    });
  };

  return {
    openModal,
  };
}

export const CustomFieldCreateWrapper: FC<PropsWithChildren> = ({ children }) => {
  const dispatch = useDispatch<Dispatch>();

  const { openModal } = useFieldForm({ dispatch });

  const handlePreCreate = () => {
    openModal({
      initialValues: { type: FieldTypeEnum.text },
    });
  };

  return isValidElement(children)
    ? cloneElement(children as React.ReactElement<any>, {
        onClick: handlePreCreate,
      })
    : null;
};

export default FieldEditPopover;
