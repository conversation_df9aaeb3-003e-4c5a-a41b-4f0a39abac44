import I18N from '@/utils/I18N';
import { Checkbox } from '@bedrock/components';
import classNames from 'classnames';
import s from './index.less';

const ParentTaskDelContent = ({ title, onChange }) => {
  const extraParams = { cancelType: 0 };

  return (
    <div>
      <div className={classNames(`line-ellipsis-3 ${s.content}`)}>
        {I18N.auto.confirmToDeletePending} {title}
      </div>
      <Checkbox
        className={s.checkbox}
        defaultChecked={extraParams.cancelType === 1}
        onChange={(e) => onChange(e.target.checked ? 1 : 0)}
      >
        {I18N.auto.deleteSubtasksConcurrent}
      </Checkbox>
    </div>
  );
};

export default ParentTaskDelContent;
