.overlay {
  padding: 4px;
  background-color: var(--bgTop);
  border: 1px solid var(--aBlack12);
  box-shadow: var(--ComBoxShadow6);
  border-radius: 8px;
  min-width: 234px;
  max-width: 280px;
  .item {
    position: relative;
    display: flex;
    padding: 6px 12px 6px 32px;
    border-radius: 4px;
    cursor: pointer;
    .name,
    .desc {
      font-weight: 400;
      line-height: 20px;
    }
    .name {
      font-size: 13px;
      color: var(--TextPrimary);
    }
    .desc {
      font-size: 12px;
      color: var(--TextTertiary);
    }
    &:hover {
      background-color: var(--aBlack6);
    }
    .activeIcon {
      position: absolute;
      left: 8px;
      top: 8px;
      color: var(--Brand600);
    }
  }
  .del {
    .name {
      color: var(--R600);
    }
  }
}
