import { Dispatch, RootState, store } from '@/models/store'
import { BatchRequest, isTaskGotPermission } from '@/models/utils'
import { TodoInfo } from '@/types'
import { useSelector } from 'react-redux'

const dispatch = store.dispatch

const batchRequest = new BatchRequest<TodoInfo, any>({
  processCallback: items => dispatch.permissions.setPermissionsMap({ list: items }),
})

const useGetPermissions = ({ taskId }: { taskId?: number }) => {
  const { permissionsMap } = useSelector((state: RootState) => ({
    permissionsMap: state.permissions.permissionsMap,
  }))

  if (!taskId || typeof taskId !== 'number' || !/^\d+$/.test(taskId)) return void 0
  if (!permissionsMap[taskId]) {
    if (!isTaskGotPermission(taskId)) {
      batchRequest.add({ taskId })
    }
  }
  return permissionsMap[taskId]?.permissions
}

export const getPermissionsByTaskId = (taskId?: number) => {
  const permissionsMap = store.getState().permissions.permissionsMap

  // 判断taskId是否是纯数字
  if (taskId === undefined || typeof taskId !== 'number' || !/^\d+$/.test(`${taskId}`)) {
    return void 0
  }

  if (!permissionsMap[taskId]) {
    if (!isTaskGotPermission(taskId)) {
      batchRequest.add({ taskId })
    }
  }
  return permissionsMap[taskId]?.permissions
}

export default useGetPermissions
