import React, { PropsWithChildren, useMemo } from 'react';

import { ApiTodoAttachmentAddPostRequest } from '@/api';
import { IconBtn, Spin } from '@/components/basic';
import { ArrayItem } from '@/types';
import I18N from '@/utils/I18N';
import { validatesVersionAndTip } from '@/utils/validate-version';

import { getFileIcon, IconImg, IconUnknow } from '../utils';
import s from './index.less';

export type Attachment = ArrayItem<ApiTodoAttachmentAddPostRequest['attachments']>;

export interface ApiAttachment extends Attachment {
  id?: string;
  taskId?: string;
  loading?: boolean;
  downloading?: boolean;
}

export interface AttachmentItemProps extends ApiAttachment {
  onDelete?: () => void;
  openDocFileDetail?: () => void;
  disabled?: boolean;
}

const AttachmentFileItem: React.FC<PropsWithChildren<AttachmentItemProps>> = (props) => {
  const { type, info, openDocFileDetail, loading, onDelete, disabled } = props;
  const iconUrl = useMemo(() => {
    if (type === 3) {
      //type等于3的表示是云空间的文档
      return info?.icon || IconUnknow;
    } else if (type === 1) {
      // 服务端说type等于1的是老待办数据 一定是图片
      return IconImg;
    } else {
      return getFileIcon(info?.format || '');
    }
  }, [type, info]);

  const memoDisabled = useMemo(() => {
    return (
      !validatesVersionAndTip({ methods: 'openTodoLocalDocPicker', showTip: false }) || disabled
    );
  }, [disabled]);

  return (
    <div className={s.item} onClick={openDocFileDetail}>
      <div className={s.icon}>
        {loading ? (
          <div className={s.loading}>
            <Spin size={20}></Spin>
          </div>
        ) : (
          <img
            src={iconUrl}
            onError={(e: any) => {
              e.target.src = IconUnknow;
            }}
          ></img>
        )}
      </div>
      <div className={s.content}>{info?.name}</div>
      {memoDisabled ? null : (
        <IconBtn
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.();
          }}
          className={s.delete}
          iconName="icon-close"
          fontSize={12}
          title={I18N.auto.delete}
        ></IconBtn>
      )}
    </div>
  );
};

export default AttachmentFileItem;
