import React from 'react';

import { Icon, Modal, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

export interface Props {
  onFollow?: (isFollower: boolean) => void;
  iconBtnClass?: string;
  isFollower?: boolean;
  containerElement: HTMLElement;
}

const Follow: React.FC<Props> = (props) => {
  const { onFollow, iconBtnClass, isFollower, containerElement } = props;
  return (
    <Tooltip
      title={isFollower ? I18N.auto.cancelFollow : I18N.auto.clickToFollowRen}
      placement="bottom"
    >
      <span className="ml-10">
        <span
          className={`${s.followTaskWrapper} ${isFollower && s.unFollowTaskWrapper}`}
          onClick={() => {
            if (isFollower) {
              Modal.confirm(
                {
                  wrapClassName: 'todo-follow-modal',
                  getContainer: () => (containerElement ? containerElement : document.body),
                  title: I18N.auto.confirmToCancelTheClosure_2,
                  content: '',
                  okText: I18N.auto.dontPayAttentionAnymore,
                  onOk: () => {
                    onFollow?.(!isFollower);
                  },
                  width: 400,
                },
                'warning'
              );
            } else {
              onFollow?.(!isFollower);
            }
          }}
        >
          <Icon
            fontSize={16}
            name={isFollower ? 'icon-tage_pin_fill' : 'icon-tage_pin_line'}
          ></Icon>
          <span>{isFollower ? I18N.auto.followedBy : I18N.auto.focusOnTasks}</span>
        </span>
      </span>
    </Tooltip>
  );
};

export default Follow;
