import { useControllableValue } from 'ahooks';
import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { AssignorPicker } from '@/components/basic';
import { PanelType } from '@/components/basic/people-picker/assignor';
import { UserInfo } from '@/types';
import { TransferSelectType } from '@/utils/const';

import s from './index.less';
export type Props = {
  className?: string;
  value?: UserInfo;
  onChange?: (user: UserInfo, extras: { type: TransferSelectType }) => void;
  // 指派者 id
  assignerUid?: string;
  disabled?: boolean;
  executorList?: UserInfo[]; //执行人列表
  fromCreate?: boolean;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
};

const NewAssignor: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, onChange, disabled, executorList, assignerUid, className, fromCreate, children } =
    props;
  const [visible, setVisible] = useControllableValue<boolean>(props, {
    valuePropName: 'visible',
    trigger: 'onVisibleChange',
  });

  return (
    <div className={classNames(s.detailAssignor, className)}>
      <AssignorPicker
        value={value}
        onChange={onChange}
        visible={visible}
        onVisible={setVisible}
        disabled={disabled}
        executorList={executorList}
        assignerUid={assignerUid}
        initPanelType={PanelType.search}
        fromCreate={fromCreate}
      >
        {children}
      </AssignorPicker>
    </div>
  );
};

export default NewAssignor;
