.overlay {
  width: 210px;
  background-color: var(--bgTop);
  border-radius: 8px;
  box-shadow: var(--ComBoxShadow);
  border: 1px solid var(--aBlack12);
}
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 42px;
  padding: 0 12px;
  color: var(--TextPrimary);
  line-height: 22px;
  border-bottom: 1px solid var(--aBlack10);

  .headLeft {
    font-weight: 600;
    font-size: 14px;
  }
  .headRight {
    cursor: pointer;
    font-size: 13px;
  }
}

.body {
  padding: 4px;
  width: 100%;
  max-height: calc(100vh - 300px);
  //overflow: overlay;
  overflow-y: auto;
  overflow-x: hidden;
}
.footer {
  padding: 4px;
  border-top: 1px solid var(--aBlack10);
  width: 100%;
}
