import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import React, { cloneElement, PropsWithChildren, useMemo } from 'react';

interface SortableItemData extends Record<string, any> {
  id: number | string;
}

export interface SortableItemProps {
  data: SortableItemData;
  hasHandle?: boolean;
  disabled?: boolean;
  handler?: React.ReactNode;
  className?: string;
}

const SortableItem: React.FC<PropsWithChildren<SortableItemProps>> = (props) => {
  const { data, children, hasHandle = true, disabled = false, handler, className = '' } = props;
  const id = data.id || data.taskId;
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
    data: {
      item: data,
    },
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.4 : 1,
  };
  const handleProps = useMemo(() => {
    return hasHandle && !disabled ? { ...attributes, ...listeners } : {};
  }, [hasHandle, attributes, listeners, disabled]);

  let newHand<PERSON> = handler;
  if (newHandler) {
    newHandler = cloneElement(newHandler, {
      ...attributes,
      ...listeners,
    });
  }

  return (
    <div
      className={className}
      style={style}
      ref={setNodeRef}
      data-id={'' + id + ''}
      {...handleProps}
    >
      {newHandler}
      {children}
    </div>
  );
};

export default SortableItem;
