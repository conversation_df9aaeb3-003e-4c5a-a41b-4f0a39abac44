import _ from 'lodash';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { apiTaskUpdateFieldPost } from '@/api';
import { Dispatch } from '@/models/store';
import { TodoInfo } from '@/types';
import { CustomField, FieldTypeEnum } from '@/types/custom-field';
import { TaskTableRowType } from '@/utils/const';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import {
  RenderCustomFieldNumber,
  RenderCustomFieldPeople,
  RenderCustomFieldText,
  RenderCustomFieldTime,
  RenderCustomOther,
  RenderCustomSelect,
} from '..';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

interface Props {
  taskInfo: TodoInfo;
  filedInfo: CustomField;
}

const RenderCustomField: React.FC<Props> = (props) => {
  const { taskInfo, filedInfo } = props;
  const { _rowType: rowType, taskId } = taskInfo;
  const dispatch = useDispatch<Dispatch>();

  const permissions = useGetPermissions({ taskId });

  const change = (v) => {
    if (rowType === TaskTableRowType.add) {
      _.set(taskInfo, ['customFieldValues', 'values', filedInfo.fieldId], {
        fieldId: filedInfo?.fieldId,
        fieldVersion: filedInfo?.fieldVersion,
        value: v,
      });
      dispatch.task.updateTodoList({
        ...taskInfo,
      });
      return null;
    } else {
      apiTaskUpdateFieldPost({
        fieldValue: {
          fieldId: filedInfo?.fieldId,
          fieldVersion: filedInfo?.fieldVersion,
          value: v,
        },
        taskId: taskInfo.taskId,
      })
        .then(() => {
          dispatch.task.updateTodoList({
            ...taskInfo,
          });
        })
        .finally(() => {
          dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
        });
    }
  };
  const value = useMemo(() => {
    let defaultValue = undefined;
    if (filedInfo?.type === FieldTypeEnum.user) {
      defaultValue = [];
    }
    //@ts-ignore
    return taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.value || defaultValue;
  }, [taskInfo.customFieldValues, filedInfo]);

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    });
    return !CAN_EDIT && rowType !== TaskTableRowType.add;
  }, [permissions, rowType]);

  if (filedInfo?.type === FieldTypeEnum.datetime) {
    return (
      <RenderCustomFieldTime
        value={value}
        filedInfo={filedInfo!}
        taskInfo={taskInfo}
        onChange={change}
        disabled={disabled}
      ></RenderCustomFieldTime>
    );
  }
  if (filedInfo?.type === FieldTypeEnum.user) {
    return (
      <RenderCustomFieldPeople
        filedInfo={filedInfo!}
        taskInfo={taskInfo}
        onChange={change}
        disabled={disabled}
      ></RenderCustomFieldPeople>
    );
  }
  if (filedInfo?.type === FieldTypeEnum.multiOption || filedInfo?.type === FieldTypeEnum.option) {
    return (
      <RenderCustomSelect
        value={value}
        filedInfo={filedInfo!}
        taskInfo={taskInfo}
        onChange={change}
        disabled={disabled}
      ></RenderCustomSelect>
    );
  }
  if (filedInfo?.type === FieldTypeEnum.text) {
    return (
      <RenderCustomFieldText
        value={value}
        filedInfo={filedInfo!}
        taskInfo={taskInfo}
        onChange={change}
        disabled={disabled}
      ></RenderCustomFieldText>
    );
  }
  if (filedInfo?.type === FieldTypeEnum.number) {
    return (
      <RenderCustomFieldNumber
        value={value}
        filedInfo={filedInfo}
        taskInfo={taskInfo}
        onChange={change}
        disabled={disabled}
      ></RenderCustomFieldNumber>
    );
  }
  return (
    <RenderCustomOther
      filedInfo={filedInfo!}
      taskInfo={taskInfo}
      disabled={disabled}
    ></RenderCustomOther>
  );
};

export default RenderCustomField;
