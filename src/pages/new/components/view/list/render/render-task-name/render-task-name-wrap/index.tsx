import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode } from 'react';

import s from './index.less';

interface Props {
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  statusNode: ReactNode;
  //禁止点击查看详情
  disabled?: boolean;
  isAdd?: boolean;
}

const RenderTaskNameWrap: React.FC<PropsWithChildren<Props>> = React.forwardRef((props, refs) => {
  const { onClick, statusNode, children, disabled, isAdd = false } = props;
  return (
    <div
      id={isAdd ? "renderTaskNameId" : ''}
      ref={refs}
      className={classNames(s.renderTaskName, {
        ['cursor']: !disabled,
      })}
      onClick={onClick}
    >
      {statusNode}
      <div className={classNames(s.titleItem)}>{children}</div>
    </div>
  );
});

export default RenderTaskNameWrap;
