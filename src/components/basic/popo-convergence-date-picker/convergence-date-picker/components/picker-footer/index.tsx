import { Checkbox, Switch } from '@bedrock/components';

import I18N from '@/utils/I18N';

import s from './index.less';

interface PickerOperationProps {
  showStartTime?: boolean;
  hasTimePoint: boolean;
  onShowStartTimeChange?: (v: boolean) => void;
  onHasTimePointChange: (v: boolean) => void;
}
export const PickerOperation = (props: PickerOperationProps) => {
  const { showStartTime, hasTimePoint, onShowStartTimeChange, onHasTimePointChange } = props;
  return (
    <div className={s.showSection}>
      {/* <div className={s.showStartDate}>
        <span>{I18N.auto.startDate}</span>
        <Switch
          checked={showStartTime}
          onChange={(e) => {
            onShowStartTimeChange(e);
          }}
          //size="small"
        />
      </div> */}
      <div className={s.showConcreteTime}>
        <Checkbox
          id="hasTimePoint"
          checked={hasTimePoint}
          onChange={(e) => {
            const { checked } = e.target;
            onHasTimePointChange(checked);
          }}
        />
        <label htmlFor="hasTimePoint">{I18N.auto.setSpecificTime}</label>
      </div>
    </div>
  );
};
