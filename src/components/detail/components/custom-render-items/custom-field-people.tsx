import classNames from 'classnames';
import { useMemo } from 'react';

import { CustomFieldPeople } from '@/components/basic-project';
import { DetailTodoInfo, UserInfo } from '@/types';
import { CustomField } from '@/types/custom-field';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  taskInfo: DetailTodoInfo;
  filedInfo: CustomField;
  onChange?: (users?: string[]) => void;
  disabled?: boolean;
  placeholder?: string;
}
export const FieldPeople = (props: Props) => {
  const { taskInfo, filedInfo, onChange, disabled, placeholder = I18N.auto.addTo } = props;
  const memoList = useMemo(() => {
    //@ts-ignore
    const list: UserInfo[] = taskInfo?.customFieldValues?.values?.[filedInfo.fieldId]?.users || [];

    return list as UserInfo[];
  }, [taskInfo.customFieldValues, filedInfo]);

  const change = (users: UserInfo[]) => {
    const uids = users.map((item) => item.uid!);
    onChange?.(uids);
  };
  return (
    <CustomFieldPeople
      value={memoList}
      onChange={change}
      className={classNames(s.customPeople, { [s.disabled]: disabled })}
      disabled={disabled}
      placeholder={placeholder}
    />
  );
};
