import classNames from 'classnames';
import React, { PropsWithChildren, useMemo, useState } from 'react';

import { Divider } from '@/components/basic';
import { UserInfo } from '@/types';
import { PeoplePickerType } from '@/utils/const';
import I18N from '@/utils/I18N';

import PeopleList from '../people-list';
import s from './index.less';

type EmptyTipType = {
  finished: string;
  unfinished: string;
};

export type Props = {
  className?: string;
  list: UserInfo[];
  value?: UserInfo[];
  onChange: (users: UserInfo[]) => void;
  disabled?: boolean;
  emptyTip?: EmptyTipType;
  currentUser?: UserInfo;
};

const enum Tab {
  finish = 1,
  unfinish = 2,
}

const SearchList: React.FC<PropsWithChildren<Props>> = (props) => {
  const { list, value, className, onChange, disabled = false, emptyTip, currentUser } = props;

  const [tab, setTab] = useState<Tab>(Tab.unfinish);
  const unfinishedList = useMemo(() => {
    return list.filter((item) => !item.finished);
  }, [list]);

  const finishedList = useMemo(() => {
    return list.filter((item) => item.finished);
  }, [list]);

  return (
    <div className={classNames(s.select, className)}>
      <div className={s.tabs}>
        <div className={s.tab}>
          <div
            className={classNames(s.groupTitle, { [s.active]: tab === Tab.unfinish })}
            onClick={() => {
              setTab(2);
            }}
          >
            {I18N.templateNode(I18N.auto.incompleteParticipants, {
              val1: String(unfinishedList.length),
            })}
          </div>
          <div
            className={classNames(s.groupTitle, 'ml-20', s.finish, {
              [s.active]: tab === Tab.finish,
            })}
            onClick={() => {
              setTab(1);
            }}
          >
            {I18N.templateNode(I18N.auto.participantCompleted, {
              val1: String(finishedList.length),
            })}
          </div>
        </div>
        <div className={s.tabBox}>
          <div className={classNames(s.tabBoxIem, { [s.show]: tab === Tab.unfinish })}>
            <PeopleList
              value={value}
              onChange={onChange}
              list={unfinishedList}
              multiple={true}
              canDelete={!disabled}
              emptyTip={
                emptyTip?.unfinished ? (
                  <div className={s.emptyTip}>{emptyTip.unfinished}</div>
                ) : null
              }
              canSelect={false}
              currentUser={currentUser}
            ></PeopleList>
          </div>
          <div className={classNames(s.tabBoxIem, { [s.show]: tab === Tab.finish })}>
            <PeopleList
              value={value}
              emptyTip={
                emptyTip?.finished ? <div className={s.emptyTip}>{emptyTip.finished}</div> : null
              }
              onChange={onChange}
              list={finishedList}
              finished
              multiple={true}
              canDelete={!disabled}
              canSelect={false}
              currentUser={currentUser}
            ></PeopleList>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchList;
