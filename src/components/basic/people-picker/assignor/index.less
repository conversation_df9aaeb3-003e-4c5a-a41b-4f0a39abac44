.peoplePickerWrap {
  display: flex;
  width: 100%;
  height: 100%;
  :global {
    .rock-dropdown-trigger-default {
      width: 100%;
      height: 100%;
      justify-content: flex-start;
      padding: 0;
      &:hover {
        background-color: unset;
      }
      & > span {
        display: flex;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.searchingPanel {
  max-height: 320px;
  min-height: 320px;
}

.list {
  flex: 1;
  overflow-y: auto;
}

.iconAdd {
  width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  //margin-left: 8px;
  margin-right: -6px;
  font-size: 14px;
  color: var(--IconPrimary);
  cursor: pointer;
  i {
    color: var(--IconPrimary);
  }
}
