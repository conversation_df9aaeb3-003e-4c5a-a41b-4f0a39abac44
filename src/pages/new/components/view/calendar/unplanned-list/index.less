.unplannedList {
  display: flex;
  flex-direction: column;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .title {
    height: 24px;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    flex-shrink: 0;
  }
  .addbox {
    margin: 10px 0;
    &.hidden {
      display: none;
    }
    :global {
      .rock-dropdown-trigger-default {
        width: 100%;
        height: 100%;
        padding: 0;
        &:hover {
          background-color: unset;
        }
        & > span {
          display: flex;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .add {
    display: flex;
    align-items: center;
    flex: 1;
    height: 33px;
    flex-shrink: 0;
    padding: 0 10px;
    border: 1px solid var(--aBlack6);
    border-radius: 4px;
    background-color: var(--bgBottom);
    .icon {
      color: var(--IconSecondary);
    }
    .addStr {
      margin-left: 8px;
      font-size: 13px;
      line-height: 13px;
      color: var(--TextTertiary);
    }
    &:hover {
      background-color: var(--aBlack4);
      .addStr,
      .icon {
        color: var(--Brand500);
      }
    }
  }
}
.list {
  flex: 1;
  padding-top: 2px;
}

.hidden {
  display: none;
}

.dragging {
  border: 1px solid var(--Brand500);
  border-radius: 4px;
  transform: rotate(5deg);
  opacity: 0.7;
  background-color: var(--bgBottom) !important;
}
