.peopleItem {
  display: flex;
  align-items: center;
  //width: 100%;
  height: 38px;
  min-height: 38px;
  padding-right: 6px;
  padding-left: 6px;
  color: var(--TextPrimary);
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    background-color: var(--aBlack6);
    &.hasTag {
      .name {
        color: var(--TextTertiary);
      }
    }
  }
  .avatarBox {
    display: flex;
    position: relative;
    width: 24px;
    height: 24px;
    .avatar {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      border-radius: 28px;
      background: url('~@/assets/images/people.png') center center no-repeat;
      background-size: 100%;
    }
    .finishedIcon {
      display: inline-block;
      position: absolute;
      right: -2px;
      bottom: -1px;
      width: 12px;
      height: 12px;
      background: url('~@/assets/images/finished.png') center center no-repeat;
      background-size: 100%;
    }
  }

  .darkAvatar {
    background-image: url('~@/assets/images/people.png');
  }
  .name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex: 1;
    padding: 0 8px;
    min-width: 0;
  }
  .disabled {
    .name {
      color: var(--TextTertiary);
    }
  }
  .check {
    font-size: 12px;
    color: var(--Brand600);
  }
  .delete {
    flex-shrink: 0;
    width: 22px;
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
    color: var(--IconPrimary);
    cursor: pointer;
    &:hover {
      border-radius: 5px;
      background-color: var(--aBlack6);
    }
  }
  &.disabled {
    opacity: 0.4;
    cursor: default;
    // .avatar {
    //   opacity: 0.4;
    // }
  }
}

.checkbox {
  margin-right: 10px !important;
}

.tag {
  padding: 2px 4px;
  font-size: 11px;
  line-height: 16px;
  background-color: var(--aBlack8);
  color: var(--TextSecondary);
  border-radius: 2px;
}
// .assignerItem {
//   &:hover {
//     background-color: var(--aBlack6);
//   }
// }
