import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode } from 'react';

import { IconBtn, Itembg } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
export type Props = {
  className?: string;
  leftRender: ReactNode;
  showDeleteIcon?: boolean;
  onDelete?: () => void;
  showbg?: boolean;
};

const RenderItem: React.FC<PropsWithChildren<Props>> = (props) => {
  const { leftRender, children, className, showDeleteIcon, onDelete, showbg } = props;
  return (
    <div className={classNames(s.item, className)}>
      <div className={s.left}>{leftRender}</div>
      <div className={classNames(s.infoItem, 'todo-detail-infoItem')}>
        <Itembg showbg={showDeleteIcon} className={s.addItembg}>
          <div className={s.opt}>{children}</div>
          {showDeleteIcon ? (
            <IconBtn
              className={s.icon}
              iconName="icon-close"
              title={I18N.auto.delete}
              onClick={onDelete}
            ></IconBtn>
          ) : null}
        </Itembg>
      </div>
    </div>
  );
};

export default RenderItem;
