import Skyline from '@popo/skyline-browser';

import { UserInfo } from '@/types';
import Const from '@/utils/const';

let skyline = new Skyline({
  serverUrl: 'https://sigma-popopcoming-popo.proxima.nie.easebar.com',
  authUser: 'popo',
  localStorageKey: 'popo_pcoming_proxima',
  authToken:
    'tDm1K8ekgm2P1vjkhEqd3M4si/iDZa8KEZKUfv4mqCaSBj/GMk2rNxrrQaCZxEFDLxiiLACpY4QobMcbuLZWpQkQKEnzuKAzvRK7RULOSAUy1rC0e9mCUTpY1cxuBsBX',
});

interface BuryData {
  event: string;
  detail?: Record<string, any>;
  time?: string;
  timestamp?: number;
  project: string;
  host: string;
}

class Agent {
  env: string;
  send(event: string, data?: Record<string, any>) {
    if (process.env.NODE_ENV !== Const.PRODUCTION) {
      return;
    }
    let sendData: BuryData = {
      event,
      project: 'popo-todo',
      host: location.host,
    };
    if (data) {
      sendData.detail = data;
    }
    skyline.send(sendData);
  }
  getEnv() {
    let env = Const.ENV_TEST;
    if (location.host.includes('staging-todo.popo.netease.com')) {
      env = Const.ENV_STAGING;
    } else if (location.host.includes('test-todo.popo.netease.com')) {
      env = Const.ENV_TEST;
    } else if (location.host.includes('todo.popo.netease.com')) {
      env = Const.ENV_PRODUCTION;
    }
    this.env = env;
    return env;
  }
  initLoginUser(userinfo?: UserInfo) {
    if (userinfo) {
      const env = this.getEnv();
      skyline.setCommonProperty({
        uid: userinfo.uid,
        profile: env,
      });
    }
  }
}

export default new Agent();

export enum EnumTrackeKey {
  Error = 'Task-Error',
  /**
   * 关键加载点位
   */
  LoadingPointsTime = 'LoadingPointsTime',
  /**
   * 页面当前的快捷方式Navigator
   */
  ViewNavigator = 'ViewNavigator',
  /**
   * 点击导航 页面当前的快捷方式Navigator
   */
  ClickNavigator = 'clickNavigator',
  /**
   * 页面进入列表 // 可以点击进入也可以默认打开
   */
  ViewTab = 'viewTab',
  /**
   * 点击tab
   */
  ClickTab = 'clickTab',
  /**
   * 列表拖拽
   */
  ListDrag = 'listDrag',
  /**
   * 主导航展开收起
   */
  MainNavigationExpandCollapse = 'mainNavigationExpandCollapse',
  /**
   * 独立窗口打开
   */
  OpenInIndependentWindow = 'openInIndependentWindow',
  /**
   * 进入任务的次数 //不管是重启还是打开popo左侧的tab切换到待办都属于一次
   */
  TaskApp = 'taskApp',
  /**
   * 列表新建-全局新建
   */
  ListNewGlobalCreate = 'listNewGlobalCreate',
  /**
   * 列表新建-底部新建
   */
  ListNewBottomCreate = 'listNewBottomCreate',
  /**
   * 列表新建-分组后底部新建
   */
  ListNewGroupBottomCreate = 'listNewGroupBottomCreate',
  /**
   * 看板新建-全局新建
   */
  KanbanNewGlobalCreate = 'kanbanNewGlobalCreate',
  /**
   * 看板新建-泳道内顶部新建
   */
  KanbanNewLaneTopCreate = 'kanbanNewLaneTopCreate',
  /**
   * 看板新建-泳道内底部新建
   */
  KanbanNewLaneBottomCreate = 'kanbanNewLaneBottomCreate',
  /**
   * 日历新建-日历内新建
   */
  CalendarNewCalendarCreate = 'calendarNewCalendarCreate',
  /**
   * 日历新建-未计划新建
   */
  CalendarNewUnplannedCreate = 'calendarNewUnplannedCreate',
  /**
   * 快捷筛选
   */
  TaskQuickFilter = 'taskQuickFilter',
  /**
   * 列表-自定义筛选-执行人
   */
  TaskCustomFilter = 'taskCustomFilter',
  /**
   * 列表-排序
   */
  TaskSortOpt = 'taskSortOpt',
  /**
   * 分组
   */
  TaskGroupOpt = 'taskGroupOpt',
  /**
   * 字段配置 显隐字段
   */
  FieldConfig = 'fieldConfig',
  /**
   * 字段配置 字段拖拽
   */
  FieldConfigDrag = 'fieldConfigDrag',
  SubtaskSplit = 'subtaskSplit',
  SubtaskSplitComfirm = 'subtaskSplitConfirm',
}
