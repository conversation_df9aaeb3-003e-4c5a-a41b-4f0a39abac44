import { ConfigProvider } from '@bedrock/components';
import React, { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { apiProjectFieldDelete } from '@/api';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import { Dropdown, Icon, Menu, Modal, PopoverProps } from '../basic';
import { useFieldForm } from '../field-edit-popover';
import s from './index.less';
import { getIsInSession } from '@/models/utils';

interface Props {
  fieldId: any;
  name: string;
  children: React.ReactNode;
  placement?: PopoverProps['placement'];
  onOpenModal?: (fieldId: any) => void;
}

export default function CustomFieldDropdown({
  children,
  fieldId,
  name,
  placement = 'bottomRight',
  onOpenModal,
}: Props) {
  const { navigatorId } = useSelector((state: RootState) => ({
    navigatorId: state.viewSetting.navigatorId,
  }));
  const dispatch = useDispatch<Dispatch>();

  const { openModal } = useFieldForm({ dispatch });

  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext);
  const zIndex = useMemo(() => getGlobalZIndex(), []);

  return (
    <Dropdown
      zIndex={zIndex}
      overlay={
        <Menu>
          <Menu.Item
            onClick={() => {
              onOpenModal?.(fieldId);
              openModal({ projectId: navigatorId, fieldId });
            }}
            icon={<Icon name="icon-details_nav_edit"></Icon>}
          >
            {I18N.auto.editFields}
          </Menu.Item>
          <Menu.Item
            icon={<Icon name="icon-details_nav_delete1"></Icon>}
            className={s.dangerItem}
            onClick={() => {
              onOpenModal?.(fieldId);
              Modal.warning({
                title: I18N.template(I18N.auto.deleteField, { val1: name }),
                content: I18N.auto.taskAfterDeletion,
                centered: true,
                okButtonProps: {
                  danger: true,
                },
                mask: !getIsInSession(),
                cancelButtonProps: {
                  // type: 'checked-neutral',
                },
                okText: I18N.auto.delete,
                onOk() {
                  return apiProjectFieldDelete({
                    fieldId,
                    //@ts-ignore
                    projectId: navigatorId!,
                  }).then(() => {
                    dispatch.viewSetting.deleteCustomField(fieldId);
                  });
                },
              });
            }}
          >
            {I18N.auto.moveFromProject}
          </Menu.Item>
        </Menu>
      }
      placement={placement}
      trigger="click"
    >
      {children}
    </Dropdown>
  );
}
