import { TooltipPlacement } from '@bedrock/components/lib/Tooltip';
import classNames from 'classnames';
import { <PERSON><PERSON><PERSON>, MouseEvent<PERSON>and<PERSON>, PropsWithChildren, ReactNode, useState } from 'react';

import { Icon, Tooltip } from '@/components/basic';
import { BaseIconSizeEnum } from '@/utils/const';

import s from './index.less';

export type Props = {
  title?: ReactNode;
  className?: string;
  onClick?: MouseEventHandler<HTMLDivElement>;
  iconName?: string; //iconfont 名称
  icon?: React.ReactNode; //图标组件
  fontSize?: number;
  placement?: TooltipPlacement;
  iconClassName?: string;
  size?: BaseIconSizeEnum;
  iconId?: string;
};

function BasicIcon(props: PropsWithChildren<Props>) {
  const { iconClassName, iconName, icon, fontSize, onClick, children, iconId } = props;
  return (
    <div id={iconId} className={classNames(s.iconBox, iconClassName)} onClick={onClick}>
      {iconName ? <Icon fontSize={fontSize} name={iconName}></Icon> : icon}
      {children}
    </div>
  );
}

export default function IconBtn(props: PropsWithChildren<Props>) {
  const { title, placement = 'top', className, size, children, onClick } = props;
  const [tooltipDisabled, setTooltipDisabled] = useState(false);

  const handleBackClick = (e: MouseEvent<HTMLDivElement>) => {
    onClick?.(e);
    setTooltipDisabled(true);
  };

  const handleMouseEnter = () => {
    setTooltipDisabled(false);
  };

  return title ? (
    <Tooltip title={title} disabled={tooltipDisabled} placement={placement} arrowPointAtCenter>
      <span
        className={classNames(
          s.iconWrap,
          { [s.largeIconWrap]: size === BaseIconSizeEnum.large, [s.hasChild]: !!children },

          className
        )}
        onMouseEnter={handleMouseEnter}
      >
        <BasicIcon {...props} onClick={handleBackClick}></BasicIcon>
      </span>
    </Tooltip>
  ) : (
    <span
      className={classNames(
        s.iconWrap,
        { [s.largeIconWrap]: size === BaseIconSizeEnum.large, [s.hasChild]: !!children },
        className
      )}
    >
      <BasicIcon {...props}></BasicIcon>
    </span>
  );
}
