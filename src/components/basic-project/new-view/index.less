.modal {
  :global {
    .rock-modal-body {
      .rock-form-item {
        display: flex;
        flex-direction: column;
        align-items: self-start;
        margin-bottom: 20px;
        .rock-form-item-label {
          text-align: left !important;
          line-height: 22px;
          margin-bottom: 4px;
          font-size: 12px;
          color: var(--TextTertiary);
          height: 22px;
          label {
            top: 0px !important;
          }
        }
        .rock-form-item-input {
          width: 100%;
          .rock-form-item-input-content {
            width: 100%;
            .rock-input-wrapper {
              width: 100%;
              padding-left: 8px;
              padding-right: 8px;
            }
          }
        }
        .rock-select-selector {
          padding-left: 8px;
          padding-right: 8px;
          height: 32px;
        }
      }
    }
    .rock-modal-footer {
      padding-top: 0px;
      padding-bottom: 20px;
      .rock-btn {
        height: 32px;
      }
    }
  }
}

.saveAsModal {
  :global {
    .rock-modal-footer {
      border-top: none;
    }
  }
}

.viewModeSelect {
  padding: 4px;
  .option {
    padding-top: 6px;
    padding-bottom: 6px;
    height: 52px;
    .text {
      color: var(--TextPrimary);
      font-size: 13px;
      line-height: 20px;
    }
    .subText {
      color: var(--TextTertiary);
      font-size: 12px;
      line-height: 20px;
    }
  }
  .disabledOption {
    .text {
      color: var(--TextQuartus);
    }
    .subText {
      color: var(--TextQuartus);
    }
  }
  :global {
    .rock-select-item-wrapper {
      height: 52px;
    }
    .rock-select-item {
      height: 52px;
    }
    .rock-select-item-check-icon {
      top: 16px !important;
    }
  }
}

.personalViewTip {
  display: flex;
  align-items: center;
  .tip {
    color: var(--TextPrimary);
    font-size: 13px;
    margin-right: 4px;
  }
  i {
    color: var(--IconSecondary);
    cursor: pointer;
  }
}

.disabled {
  cursor: pointer;
  background-color: var(--aBlack6);
  border: 1px solid var(--aBlack16);
  color: var(--TextQuartus);
  padding: 3px 10px;
  border-radius: 6px;
  margin-left: 8px;
}
