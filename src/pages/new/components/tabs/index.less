.tabBox {
  display: flex;
  padding-bottom: 1px;
  align-items: center;
  transform: translateY(1px);
  .tabs {
    position: relative;
    display: flex;
    //padding-bottom: 2px;
    color: var(--TextSecondary);
    font-weight: 400;
    :global {
      [data-rbd-droppable-id='droppable'] {
        display: flex;
      }
      [role='button'] {
        opacity: 1 !important;
      }
    }
    .tab {
      display: flex;
      align-items: center;
      height: 24px;
      margin-right: 24px;
      cursor: pointer;

      &.active {
        color: var(--Brand600);
        .icon {
          color: var(--Brand600);
        }
      }

      .more {
        width: 16px;
        height: 16px;
        margin-left: 8px;
      }
    }
    .inkbar {
      position: absolute;
      bottom: -1px;
      height: 2px;
      width: 44px;
      background-color: var(--Brand600);
      transition: width 0.3s, transform 0.3s, visibility 0s 0.3s;
      //transform: translateX(-100px);
    }
  }
  .addView {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    cursor: pointer;
    margin-bottom: 2px;
    i {
      color: var(--IconSecondary);
    }
    &:hover {
      background-color: var(--aBlack6);
    }
  }
}
.hidden {
  display: none;
}
