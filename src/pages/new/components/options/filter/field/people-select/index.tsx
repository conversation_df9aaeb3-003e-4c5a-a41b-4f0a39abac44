import { Empty } from '@bedrock/components';
import { ChangeParams } from '@bedrock/components/lib/Select';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { apiTodoParticipantCandidateSearchGet, apiUserListPost } from '@/api';
import { AvatarPeople, Icon, Select, Spin } from '@/components/basic';
import SelectedItem from '@/components/basic/select/selected-item';
import { RootState } from '@/models/store';
import { SelectType } from '@/utils/fields';
import I18N from '@/utils/I18N';

import s from './index.less';

interface TabsProps {
  value: any;
  onChange?: (value: any, params?: Partial<ChangeParams>) => void;
  type: SelectType;
}

const PeopleSelect: React.FC<TabsProps> = (props) => {
  const { value, onChange } = props;
  const { recentContactsList } = useSelector((state: RootState) => ({
    recentContactsList: state.user.recentContactsList,
  }));
  const [loading, setLoading] = useState(false);
  const [fetched, setFetched] = useState(false);
  const [options, setOptions] = useState<any[]>(
    recentContactsList?.map((item) => ({
      ...item,
      value: item.uid,
    })) || []
  );
  const userListRef = useRef<any[]>(options);
  userListRef.current = options;
  const fetchData = useMemo(() => {
    return debounce((v) => {
      if (v === '' || v === undefined) {
        return;
      }
      setFetched(true);
      setLoading(true);
      apiTodoParticipantCandidateSearchGet({ keyword: v })
        .then((res) => {
          const _options = res.map((item) => ({
            name: item.name,
            value: item.uid,
            avatarUrl: item.avatarUrl,
          }));
          setOptions(_options);
        })
        .finally(() => {
          setLoading(false);
          setFetched(false);
        });
    }, 300);
  }, []);
  const setUserList = async () => {
    //TODO value是数组, 依赖触发多次渲染
    if (value?.length) {
      const usersMap = userListRef.current.reduce((pre, item) => {
        pre[item.value] = item;
        return pre;
      }, {} as Record<string, any>);
      const _values = value.filter((uid) => !userListRef.current.find((v) => v.value === uid));
      if (_values.length) {
        const res = await apiUserListPost({
          uids: _values,
        });
        res.forEach((item) => {
          usersMap[item.uid] = {
            name: item.name,
            value: item.uid,
            avatarUrl: item.avatarUrl,
          };
        });
      }
      const _options = value.map((uid) => {
        return usersMap[uid];
      }) as any[];

      const arr = _options.concat(
        recentContactsList?.map((item) => ({
          ...item,
          value: item.uid,
        }))
      );
      let unique = arr.filter(
        (obj, index, self) => index === self.findIndex((t) => t.value === obj.value)
      );
      setOptions(unique);
    }
  };
  useEffect(() => {
    setUserList();
  }, [value]);
  return (
    <Select
      dropdownClassName={s.fieldSelect}
      multipleCheckedType={'check-icon'}
      multiple
      maxTagCount="responsive"
      minTagCount={2}
      value={value}
      onChange={(v) => {
        onChange?.(v);
      }}
      showSearch="inner"
      onSearch={fetchData}
      filterOption={false}
      options={options}
      //@ts-ignore
      zIndex={999999}
      // getPopupContainer={(dom) => dom.parentNode}
      notFoundContent={
        <div className={s.noData}>
          {loading ? (
            <Spin />
          ) : fetched ? null : (
            <Empty description={I18N.auto.pleaseSearchFirstAndThen} />
          )}
        </div>
      }
      renderLabel={(option) => {
        const { avatarUrl, name } = option;
        return (
          <div className={s.label}>
            <AvatarPeople className={classNames(s.avatar)} avatarUrl={avatarUrl}></AvatarPeople>
            <div className={s.name}>{name}</div>
          </div>
        );
      }}
      renderSelectedItem={(option, { onClose }) => {
        const { avatarUrl, name } = option;
        return {
          content: (
            <div className={s.tag}>
              <SelectedItem
                onClose={(e) => {
                  onClose(e, option);
                }}
              >
                <AvatarPeople className={classNames(s.avatar)} avatarUrl={avatarUrl}></AvatarPeople>
                <div className={s.name}>{name}</div>
              </SelectedItem>
            </div>
          ),

          isRenderInTag: false,
        };
      }}
    />
  );
};
export default PeopleSelect;
