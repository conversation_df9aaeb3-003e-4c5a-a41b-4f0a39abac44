import { FC, useCallback, useEffect, useMemo, useReducer, useState } from 'react'
import { Plus, Tool } from '@bedrock/icons-react'
import I18N from '@/utils/I18N'
import { Button, Input, Tooltip } from '../basic'
import { TaskTime, TodoInfo } from '@/types'
import { Dispatch, store } from '@/models/store'
import { ImCheck1, ImClose1 } from '@babylon/popo-icons'
import { useDispatch } from 'react-redux'
import { Priority } from '@/utils/fields'
import s from './index.less'
import { Space } from '@bedrock/components'
import { AddMenuId, SUBTASK_MAX_COUNT, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import ConciseOperation from './components/ConciseOperation'
import { getCreateParams, handleSeverData } from '@/models/utils'
import { checkSubtaskOverLimit } from '@/pages/new/components/view/list/utils'
import classNames from 'classnames'
import { isVerticalInViewport } from '@/utils'

interface ICreateTaskLineProps {
  subTasks?: TodoInfo[]
  canEdit?: boolean
}

/**
 * 创建任务行组件
 * 用于添加新的子任务
 */
const CreateTaskLine: FC<ICreateTaskLineProps> = ({ subTasks, canEdit }) => {
  const [editing, setEditing] = useState(false)

  const turnToEditing = useCallback(() => {
    if (checkSubtaskOverLimit(subTasks) || !canEdit) {
      return
    }
    setEditing(true)
  }, [subTasks])

  const cancelEditing = useCallback(() => {
    setEditing(false)
  }, [])

  return editing ? (
    <CreatingLine cancelEditing={cancelEditing} subTasks={subTasks} />
  ) : (
    <DefaultLine canEdit={canEdit} turnToEditing={turnToEditing} />
  )
}

interface IDefaultLineProps {
  turnToEditing: () => void
  canEdit?: boolean
}

/**
 * 默认状态的任务行
 * 显示"新建任务"的按钮
 */
const DefaultLine: FC<IDefaultLineProps> = ({ turnToEditing, canEdit }) => {
  const content = (
    <div
      onClick={turnToEditing}
      className={classNames(`${s['task__line--default']} ${s.task__line}`, {
        [s.disabled]: !canEdit,
      })}
      role="button"
      aria-label={I18N.auto.newSubtask}
    >
      <div className={`${s.default__create} flex-y-center`}>
        <Plus />
        {I18N.auto.newSubtask}
      </div>
    </div>
  )
  if (!canEdit) {
    return <Tooltip title={I18N.auto.noEditingPermission}>{content}</Tooltip>
  }
  return content
}

interface ICreatingLineProps {
  cancelEditing: () => void
  subTasks?: TodoInfo[]
}

// 任务信息reducer
const taskInfoReducer = (state: TodoInfo, action: { taskInfo: TodoInfo; type: 'merge' | 'replace' }): TodoInfo => {
  if (action.type === 'merge') {
    return {
      ...state,
      ...action.taskInfo,
    }
  } else if (action.type === 'replace') {
    return action.taskInfo
  }
  return state
}

/**
 * 正在创建任务的行组件
 * 显示任务创建表单
 */
const CreatingLine: FC<ICreatingLineProps> = ({ cancelEditing, subTasks }) => {
  const userInfo = store.getState().user.userInfo
  const dispatch = useDispatch<Dispatch>()
  const detailTaskId = store.getState().detail.taskId

  const initialTaskInfo = useMemo(
    () => ({
      title: '',
      assigner: userInfo,
      executor: [],
      parentTaskId: detailTaskId,
      _rowType: TaskTableRowType.add,
      taskId: TaskTableRowTypeAdd + AddMenuId,
      parentId: detailTaskId,
      startTime: 0,
      deadline: 0,
      rrule: '',
      alarm: {
        alarmTimestamp: 0,
        alarmRrule: '',
      },
    }),
    [userInfo, detailTaskId],
  )

  const [taskInfo, dispatchTaskInfo] = useReducer(
    taskInfoReducer,
    handleSeverData(initialTaskInfo, store.getState().user.userInfo),
  )

  // 标题变更处理
  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    dispatchTaskInfo({ taskInfo: { title: e.target.value }, type: 'merge' })
  }, [])

  // 取消创建
  const handleCancel = useCallback(() => {
    const focusEl = document.querySelector('#subtasks .rock-input-focused') as HTMLInputElement
    if (focusEl) {
      focusEl.blur()
    }
    cancelEditing()
  }, [cancelEditing])

  const handleBlur = () => {
    if (!taskInfo.title?.trim()) {
      handleCancel()
    } else {
      handleConfirm({ confirmType: 'blur' })
    }
  }

  // 确认创建任务
  const handleConfirm = useCallback(
    ({ confirmType }) => {
      if (!taskInfo.title?.trim()) {
        handleCancel()
        return
      }

      createSubTask({ taskInfo, parentTaskId: detailTaskId, dispatch })
      // 如果是回撤和点击确认按钮，继续新建
      if (['enter', 'click'].includes(confirmType) && (subTasks?.length || 0) < SUBTASK_MAX_COUNT) {
        // 重置任务信息
        dispatchTaskInfo({ taskInfo: { ...initialTaskInfo }, type: 'replace' })

        setTimeout(() => {
          const creatingLine = document.querySelector('#subtasks .task__line--editing')

          console.log('creatingLine', creatingLine)

          if (creatingLine) {
            const { isInViewport, db } = isVerticalInViewport(
              creatingLine,
              document.querySelector('.detail-scroll-content'),
            )
            if (!isInViewport) {
              const scrollContent = document.querySelector('.detail-scroll-content .rock-scrollbar-view')
              if (scrollContent) {
                scrollContent.scrollTop += db
              }
            }
            setTimeout(() => {
              const inputEl = creatingLine.querySelector('.rock-input') as HTMLInputElement
              inputEl?.focus()
            }, 160)
          }
        }, 160)
      } else {
        // 关闭编辑状态
        handleCancel()
      }
    },
    [taskInfo, handleCancel, detailTaskId, dispatch],
  )

  // 键盘事件处理 - 支持Enter确认，Esc取消
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        handleConfirm({ confirmType: 'enter' })
      } else if (e.key === 'Escape') {
        handleCancel()
      }
    },
    [handleConfirm, handleCancel],
  )

  useEffect(() => {
    function handleClickAway(e: MouseEvent) {
      const tableDropdown =
        document.querySelector('#detail_body .rock-dropdown') ||
        (document.querySelector('#detail_body .rock-select-dropdown') as HTMLDivElement)
      if (tableDropdown) {
        return
      }
      const editingElement = document.querySelector('.task__line--editing')
      if (!editingElement || (e.target instanceof Element && editingElement?.contains(e.target))) {
        return
      }
      handleBlur()
    }
    document.addEventListener('mousedown', handleClickAway)
    return () => {
      document.removeEventListener('mousedown', handleClickAway)
    }
  }, [taskInfo])

  return (
    <div className={`${s.task__line} ${s['task__line--editing']} task__line--editing flex-y-center`}>
      <i className={`${s.status__icon} icon iconfont icon-taskstate_nodown mr-2`} />
      <Input
        onChange={handleTitleChange}
        onKeyDown={handleKeyDown}
        autoFocus
        value={taskInfo.title}
        border={false}
        placeholder={I18N.auto.subTaskPlaceholder}
        className={s.title__input}
        // onBlur={handleBlur}
        size="small"
      />
      <ConciseOperation
        taskInfo={taskInfo}
        onChange={(newTaskInfo: TodoInfo) => {
          if (newTaskInfo.alarm) {
            newTaskInfo.alarm.alarmTimestamp = newTaskInfo.alarm.time
          }
          const newData = handleSeverData(newTaskInfo, store.getState().user.userInfo)
          dispatchTaskInfo({ taskInfo: newData, type: 'merge' })
        }}
      />
      <Space size={4} className="ml-8">
        <Button
          size="xSmall"
          icon={<ImClose1 className="fs-14" />}
          onClick={handleCancel}
          aria-label="取消"
          className={s.cancel__btn}
        />
        <Button
          type="primary"
          size="xSmall"
          icon={<ImCheck1 className="fs-14" />}
          onClick={() => handleConfirm({ confirmType: 'click' })}
          disabled={!taskInfo.title?.trim()}
          aria-label="确认"
        />
      </Space>
    </div>
  )
}

interface ICreateSubTaskParams {
  taskInfo: TodoInfo
  parentTaskId?: number
  dispatch: Dispatch
}

export async function createSubTask({ taskInfo, parentTaskId, dispatch }: ICreateSubTaskParams) {
  const taskParams = getCreateParams(taskInfo, parentTaskId)

  // 乐观更新UI
  dispatch.detail.insertSubTasks({ taskId: taskInfo.taskId || AddMenuId, ...taskParams })

  // 发送请求创建任务
  return dispatch.task
    .addTask(taskParams)
    .then(createdTask => {
      // 更新子任务列表，将乐观更新的数据替换为createdTask
      dispatch.viewSetting.updateItemByDetail({
        taskId: createdTask.taskId,
        parentId: parentTaskId,
        onlyDetail: true,
        actionType: 'replace',
      })
      dispatch.detail.insertSubTasks({ taskId: taskInfo.taskId || AddMenuId, ...createdTask })

      return createdTask
    })
    .catch(() => {
      dispatch.viewSetting.updateItemByDetail({
        taskId: taskInfo.taskId,
        parentId: parentTaskId,
        actionType: 'replace',
      })
    })
}

export default CreateTaskLine
