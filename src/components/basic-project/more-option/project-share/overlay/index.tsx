import React from 'react';

import { Icon, Menu } from '@/components/basic';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';
import useProjectShare from './use-share-project';

export type Props = {
  className?: string;
  projectInfo?: ProjectInfo;
};

const Overlay: React.FC<Props> = (props) => {
  const { className, projectInfo } = props;
  const { onShare, onCopy } = useProjectShare({ projectInfo });
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Menu className={s.overlay}>
        <Menu.Item className={s.item} key="D-1" onClick={onShare}>
          <Icon name="icon-details_nav_share" className="mr-7" />
          {I18N.auto.shareToConversation}
        </Menu.Item>
        <Menu.Item className={s.item} key="D-2" onClick={onCopy}>
          <Icon name="icon-details_data_file" className="mr-7" />
          {I18N.auto.copyLink}
        </Menu.Item>
      </Menu>
    </div>
  );
};

export default Overlay;
