import { Distribution, ViewType } from '@/utils/const';
export { Distribution, ViewType };

export type TabSizeMap = Map<
  React.Key,
  { width: number; height: number; left: number; top: number }
>;

export interface TabOffset {
  width: number;
  height: number;
  left: number;
  top: number;
  isFirst?: boolean;
  isLast?: boolean;
}
export type TabOffsetMap = Map<React.Key, TabOffset>;

export enum EditType {
  rename,
  remove,
}
