import classNames from 'classnames';
import { PropsWithChildren } from 'react';

import { IconProject, Tooltip } from '@/components/basic';
import { ProjectInfo } from '@/types';

import s from './index.less';

interface Props {
  disabled?: boolean;
  className?: string;
  value?: ProjectInfo;
  onClick?: (v: ProjectInfo) => void;
  item: ProjectInfo;
}

const ProjectTag: React.FC<PropsWithChildren<Props>> = (props) => {
  const { className, item, onClick } = props;
  const { icon, iconColor, name } = item || {};
  return (
    <div
      className={classNames(s.projectTag, className)}
      onClick={() => {
        onClick?.(item);
      }}
    >
      <IconProject
        className={s.icon}
        fontSize={16}
        name={icon}
        bgColor={iconColor}
        active
      ></IconProject>
      <div className={s.name}>{name}</div>
    </div>
  );
};

export default ProjectTag;
