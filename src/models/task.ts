import { createModel } from '@rematch/core'
import _ from 'lodash'

import { getGroupsData, getPermissionsByTaskIds } from '@/api-common'
import { validateTask } from '@/pages/new/components/view/list/utils'
import { ActionType, Pagination, PermissionsTasksMap, TaskGroupItem, TaskInfo, TaskPermission, TodoInfo } from '@/types'
import { PPTimeFormat, TaskNavigatorType, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import { EnumDeadlineGroup, EnumFieldNONE, EnumGroupBy, FinishedStatus, Priority } from '@/utils/fields'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'

import type { RootModel } from '.'
import {
  createTask,
  getAddItem,
  getExpandedIndexMap,
  getNoPermissionList,
  getViewIsGroup,
  scrollToIndex,
  setExpandedIndexMap,
  setNewAddItem,
  tfGroups,
} from './utils'
import I18N from '@/utils/I18N'
import { Row } from '@tanstack/react-table'
import { fetchTaskList, updateTaskInfo } from './utility'
import { getTreeList } from './utility/getTodoList'

const DefaultPagination = {
  size: 200,
  more: false,
  order: undefined,
  orderBy: undefined,
}

type PreAddParamsType = {
  taskId?: string | number
  groupId?: string
  isHeader: boolean
  defaultParams: any
}

type CacheCollapse = {
  [k: string]: TaskGroupItem[]
}

interface TaskState {
  filters?: string[]
  searchValue?: ''
  pagination?: Pagination
  newCreatetaskId?: undefined | number //刚改新建待办id
  tableAddTask?: boolean
  showDataList?: TodoInfo[]
  dataList?: TodoInfo[]
  groupList?: TaskGroupItem[]
  showGroupList?: TaskGroupItem[]
  treeList?: TaskInfo[]
  refreshing: boolean
  taskId?: number
  preAddParams?: PreAddParamsType
  cacheCollapse?: CacheCollapse
  hasData?: boolean
  permissionsMap: PermissionsTasksMap
  taskIds?: number[]
  expandedKeysMap?: Record<string, boolean>
}

const initData = {
  filters: [] as string[],
  searchValue: '', //搜索组件在value和onChange时用的值,保证输入正常显示
  pagination: Object.assign({}, DefaultPagination) as Pagination,
  newCreatetaskId: undefined as undefined | number, //刚改新建待办id
  tableAddTask: false as boolean,
  showDataList: [] as TodoInfo[],
  dataList: [] as TodoInfo[], // 有新建任务一行, 数组长度大于1也不一定是待办有数据
  treeList: [] as TodoInfo[],
  refreshing: true,
  hasData: false, //判断是否有数据
  groupList: [] as TaskGroupItem[],
  showGroupList: [] as TaskGroupItem[],
  preAddParams: {} as PreAddParamsType,
  cacheCollapse: {} as CacheCollapse,
  permissionsMap: {} as Record<string, { permissions: TaskPermission[] }>,
  taskIds: [] as number[],
  expandedKeysMap: {} as Record<string, boolean>,
}

export const task = createModel<RootModel>()({
  state: {
    ...initData,
  },
  reducers: {
    initData(state) {
      return {
        ...initData,
        cacheCollapse: state.cacheCollapse,
      }
    },
    setData(state, payload: Partial<TaskState>) {
      return { ...state, ...payload }
    },
    setShowTitle(state, payload: string) {
      return { ...state, showTitle: payload }
    },
    setTableList(state, payload: TaskInfo[]) {
      return { ...state, tableList: payload }
    },

    removeTodoFromListById(state, taskId: number) {
      const { dataList } = state
      const list = dataList.filter(item => item.taskId !== taskId)
      return {
        ...state,
        dataList: [...list],
      }
    },
    /**
     * 切换页面
     * @param state
     * @param pageNo
     * @returns
     */
    setPage(state, payload: Pagination) {
      const { pagination } = state
      return {
        ...state,
        pagination: {
          ...pagination,
          ...payload,
        },
      }
    },
    setLoading(state, payload: boolean) {
      return {
        ...state,
        loading: payload,
      }
    },
    setFirstLoading(state, payload: boolean) {
      return {
        ...state,
        firstLoading: payload,
      }
    },

    setNewCreatetaskId(state, payload: number | undefined) {
      return {
        ...state,
        newCreatetaskId: payload,
      }
    },
    setSearchValue(state, payload: string) {
      return {
        ...state,
        searchValue: payload,
      }
    },
    setEditing(state, payload: boolean) {
      return {
        ...state,
        editing: payload,
      }
    },
  },
  effects: dispatch => ({
    async getGroupList(state) {
      const { cacheCollapse } = state.task
      const { currentViewTab, customFields, navigatorId } = state.viewSetting
      const { groupBy } = currentViewTab
      const { groups, params } = await getGroupsData({ state, dispatch })
      // 增加自定义字段数据
      let cacheGroupUnfoldMap = {} as Record<string, boolean>
      if (
        currentViewTab &&
        cacheCollapse[currentViewTab.viewId!]?.length &&
        cacheCollapse[currentViewTab.viewId!][0].groupBy === groupBy
      ) {
        cacheGroupUnfoldMap = cacheCollapse[currentViewTab.viewId!].reduce((pre, cur) => {
          pre[cur.groupId!] = !!cur.unfold
          return pre
        }, {} as Record<string, boolean>)
      }

      const currExpandedKeysMap: Record<string, boolean> = {}

      const hadndleItem = (item: TaskGroupItem, index: number): TaskGroupItem => {
        currExpandedKeysMap[index] = true
        return {
          ...item,
          groupBy: item.groupBy as EnumGroupBy,
        }
      }

      const list = tfGroups(
        {
          fieldName: params.queryGroupBy?.fieldName,
          customFieldId: params.queryGroupBy?.customFieldId,
          customFields: customFields,
          projectId: navigatorId,
          groups: groups,
        },
        hadndleItem,
      )

      let expandedIdsMap = getExpandedIndexMap()

      const newExpandedKeysMap = {
        ...expandedIdsMap,
        ...currExpandedKeysMap,
      }

      setExpandedIndexMap(newExpandedKeysMap)

      // 默认设置每个组都是默认打开的
      dispatch.task.setData({
        groupList: list,
        expandedKeysMap: newExpandedKeysMap,
      })
    },
    /**
     * @param state
     * @param payload
     * @returns
     */
    async getTodoList(options: { page?: number; scrollId?: string; size?: number; onlyGetData?: boolean }) {
      fetchTaskList({ options, dispatch, modelName: 'task' })
    },

    updateTreeList({ srcList }: { srcList?: TaskInfo[] }) {
      getTreeList({ srcList, dispatch, modelName: 'task' })
    },

    updatePermissionsList(list: TaskInfo[]) {},
    /**
     * 计算分组数据
     * @param opt
     * @param state
     */
    calculatedGroupTodoList(opt: any, state) {
      const { permissions } = state.viewSetting
      const { groupList, dataList } = state.task
      const { userInfo } = state.user

      const canCreateTask = validatesPermission({
        permissions: permissions,
        key: ProjectPermissionEnum.CAN_CREATE_TASK,
      })

      const handleGroupSubRows = canCreateTask
        ? (group, rows) => {
            rows.push(
              getAddItem({
                _rowType: TaskTableRowType.addBtn,
                id: `${TaskTableRowTypeAdd}-${rows.length}-${group.groupId}`,
                taskId: `${TaskTableRowTypeAdd}-${rows.length}-${group.groupId}`,
                defaultParams: group.defaultParams,
                assigner: userInfo,
                groupId: group.groupId,
                title: I18N.auto.newTask,
              }),
            )
            return rows
          }
        : undefined

      // 按照分组调整对应的数据
      // 深拷贝
      const _groupList = _.cloneDeep(groupList)
      // 排除掉已折叠数据
      // _list = getUnfoldedArray(_list, _groupList);
      // let treeList = [];
      // if (
      //   validatesPermission({
      //     permissions: permissions,
      //     key: ProjectPermissionEnum.CAN_CREATE_TASK,
      //   })
      // ) {
      //   //每个组末尾追加新增按钮 数量+1  讲数组切割成 [3,8,16,28] 数字代表下标开始位
      //   let btnCount = 1;
      //   let lastEnd = 0;
      //   const unfoldGroups: { groupId: string; count: number }[] = [];
      //   let totalCount = 0;
      //   treeList = _groupList.map((item, index) => {
      //     if (item.unfold) {
      //       totalCount += item.count || 0;
      //       const count = totalCount + btnCount;
      //       item.count = count;

      //       const children = _list.slice(lastEnd, item.count);
      //       children.push(
      //         getAddItem({
      //           _rowType: TaskTableRowType.addBtn,
      //           id: `${TaskTableRowTypeAdd}-${index}`,
      //           taskId: `${TaskTableRowTypeAdd}-${index}`,
      //           defaultParams: _groupList[index].defaultParams,
      //           assigner: userInfo,
      //           groupId: item.groupId,
      //           title: I18N.auto.newTask,
      //         }) as TodoInfo
      //       );
      //       lastEnd = item.count;
      //       unfoldGroups.push({
      //         count,
      //         groupId: item.groupId!,
      //       });
      //       btnCount += 1;

      //       return {
      //         taskName: item.groupId,
      //         title: item.groupId,
      //         taskId: item.groupId,
      //         ...item,
      //         children: children,
      //       };
      //     }
      //   }, [] as number[]);

      //   console.log('treeList', _list, treeList);
      // }
      // 需要排除只有一条新增任务的的情况
      dispatch.task.setData({
        showGroupList: _groupList,
        // treeList: treeList,
      })
      // if (more) {
      //   dispatch.task.loadMore({});
      //   return;
      // }
      if (dataList.length) {
        //检查没有权限的数据
        dispatch.task.updatePermissionsList(dataList)
      }
    },
    /**
     *
     * @param opt
     * @param state
     */
    calculatedTodoList(opt: { dataList: TodoInfo[] }, state) {
      let { dataList: baseDataList = [] } = opt
      //去除按钮以及编辑台之后的真实数据 多处理一次数据不耗费多少时间 保稳点
      baseDataList = baseDataList.filter(item => validateTask(item))
      //如果传了数据 就默认存起来
      if (baseDataList && baseDataList.length) {
        dispatch.task.setData({
          dataList: baseDataList,
        })
      }
      const {
        dataList,
        pagination: { more },
      } = state.task
      const { permissions } = state.viewSetting
      let _list = baseDataList.length ? baseDataList : dataList
      if (
        validatesPermission({
          permissions: permissions,
          key: ProjectPermissionEnum.CAN_CREATE_TASK,
        })
      ) {
        //默认增加一个底部按钮 按钮加载时机要处理下
        if (!more) {
          const addItem = getAddItem({
            _rowType: TaskTableRowType.addBtn,
            id: `${TaskTableRowTypeAdd}-1`,
          })
          _list = setNewAddItem({
            dataList: [..._list],
            isHeader: false,
            item: addItem,
          })
        }
      }
      dispatch.task.setData({
        showDataList: _list,
      })

      // if (more) {
      //   dispatch.task.loadMore({});
      //   return;
      // }

      if (dataList.length) {
        //检查没有权限的数据
        dispatch.task.updatePermissionsList(dataList)
      }
    },
    //TODO table基于react-virtuoso 分组数据、新增数据、新增任务按钮、分组新增任务按钮都要额外去处理数据源,非常的不友好
    //如果有一个支持children的树结构的table 且支持自定义组的head和组的footer就可以去除复杂的数据处理逻辑 数据处理难度直线下降
    processData(opt: { dataList: TodoInfo[] }) {
      const { queryGroupBy } = dispatch.viewSetting.getComParams({})
      const { dataList } = opt
      dispatch.task.setData({
        dataList: dataList,
      })
      if (queryGroupBy?.fieldName) {
        console.log('jinlaiole')
        //计算分组后的内容
        dispatch.task.calculatedGroupTodoList({})
      } else {
        dispatch.task.setData({
          groupList: [],
          showGroupList: [],
        })
        dispatch.task.calculatedTodoList({})
      }
    },
    // TODO: 嵌套处理
    updateTodoList(payload: TodoInfo) {
      // const { taskId } = payload;
      // const { dataList } = state.task;
      // const { permissionsMap } = state.permissions;
      // const list = dataList.map((item) => {
      //   if (taskId && item.taskId === taskId) {
      //     return {
      //       ...item,
      //       permissions: permissionsMap?.[taskId]?.permissions,
      //       ...payload,
      //     };
      //   } else {
      //     return item;
      //   }
      // });
      // dispatch.task.processData({
      //   dataList: list,
      // });
      dispatch.task.updateTaskInfo({ taskInfo: payload })
    },
    async updatePermissionsWithTask(payload: TodoInfo, state) {
      const { taskId } = payload
      const { userInfo } = state.user
      const { permissionsMap } = state.permissions
      const noPermissionsData = getNoPermissionList({ list: [payload], permissionsMap, userInfo })
      const mapPermissions = (await getPermissionsByTaskIds(noPermissionsData)) as PermissionsTasksMap
      payload.permissions = mapPermissions[String(taskId)]?.permissions
      dispatch.permissions.setData({
        permissionsMap: {
          ...permissionsMap,
          ...mapPermissions,
        },
      })
      dispatch.task.updateTodoList(payload)
      return payload
    },
    tableAddTaskItemFromOptions(payload, state) {
      const treeList = state.task.treeList
      const isGroup = getViewIsGroup()
      if (isGroup) {
        let expandedIds: Record<string, boolean> = {}

        const queryGroupBys = state.viewSetting.currentViewTab?.queryGroupBys
        let list = [...treeList]
        let lastGroup = null

        if (queryGroupBys?.length > 1) {
          let isTask = false
          while (!isTask) {
            list?.some((item, index) => {
              if (item.groupBy) {
                if (item.subtask?.length) {
                  list = item.subtask as TodoInfo[]
                  lastGroup = item
                }
                const keyList = Object.keys(expandedIds)
                keyList.push(`${index}`)
                const newKey = keyList.join('.')
                expandedIds[newKey] = true
                return true
              } else {
                isTask = true
                return false
              }
            })
          }
        } else {
          // 列表上方点击新建按钮，新建行默认插入的分组位置
          const groupTargetMap = {
            [EnumGroupBy.DEADLINE]: EnumDeadlineGroup.TODAY,
            [EnumGroupBy.ASSIGNEE]: EnumFieldNONE.NONE,
            [EnumGroupBy.PRIORITY]: Priority.Unset,
            [EnumGroupBy.FINISHED]: FinishedStatus.UnFinished,
            customer: EnumFieldNONE.NONE,
          }

          const queryGroupBy = queryGroupBys[0]
          list.some((item, index) => {
            if (item.groupBy === (queryGroupBy.customFieldId || queryGroupBy.fieldName)) {
              const targetGroupId = queryGroupBy.customFieldId ? groupTargetMap.customer : groupTargetMap[item.groupBy]
              if (item.groupId === targetGroupId) {
                list = item.subtask as TodoInfo[]
                lastGroup = item
                expandedIds = {
                  [index]: true,
                }
                return true
              }
            }
          })
        }

        const lastItem = list[list.length - 1]
        const newId = `${lastItem.taskId}-end`
        if (lastGroup && lastItem._rowType) {
          const newSubtask = [...lastGroup.subtask]
          const addLine = {
            ...lastItem,
            taskId: newId,
            id: newId,
            _rowType: TaskTableRowType.add,
          }

          dispatch.taskCommon.setTaskMapItem({ task: addLine })

          newSubtask.splice(newSubtask.length - 1, 0, addLine)

          lastGroup.subtask = newSubtask
          scrollToIndex({ taskId: newId })
          setTimeout(() => {
            const expandedIdsMap = window.tanstackTable.getState().expanded
            window.tanstackTable.setExpanded({ ...expandedIdsMap, ...expandedIds })
          }, 16)
        }

        dispatch.task.setData({
          treeList: [...treeList],
        })
      } else {
        const lastTreeItem = treeList[treeList.length - 1]
        const newId = `${lastTreeItem.taskId}-start`
        const addLine = { ...lastTreeItem, isHeader: true, _rowType: TaskTableRowType.add, taskId: newId, id: newId }
        dispatch.taskCommon.setTaskMapItem({ task: addLine })
        dispatch.task.setData({
          treeList: [addLine, ...treeList],
        })
      }
    },
    /**
     * 增加编辑的空任务  分组比较复杂
     * @param opt
     * @param state
     */
    async tableAddTaskItem(
      opt: {
        taskId?: number
        groupId?: string
        isHeader: boolean
        tableRowData?: Row<TodoInfo>
        isSubtask?: boolean
      },
      state,
    ) {
      let { groupId } = opt

      const { dataList } = state.task
      // 先判端下数据是否已处在新建数据
      // setTimeout(() => {
      const hasaddIndex = dataList.findIndex((item: TodoInfo) => item._rowType === TaskTableRowType.add)
      if (hasaddIndex > -1) {
        dispatch.task.tableRemoveTaskItem({
          taskId: dataList[hasaddIndex].taskId,
          groupId,
          tableRowData: opt.tableRowData || { id: '0' },
        })
      }
      // }, 16)

      // 抽离方法可以获取最新的值
      dispatch.task.tableAdd(opt)
    },
    async tableAdd(
      opt: {
        taskId?: number
        groupId?: string | number
        isHeader: boolean
        tableRowData?: Row<TodoInfo>
        [key: string]: any
      },
      state,
    ) {
      let defaultParams = {}
      // 回车创建需要从上次操作中获取来源
      if (opt.actionType === ActionType.enter) {
        opt.isHeader =
          typeof state.task.preAddParams?.isHeader === 'boolean' ? state.task.preAddParams.isHeader : opt.isHeader
      }
      let { groupId, isHeader, tableRowData, isSubtask = true } = opt
      const { queryGroupBy, queryGroupBys, navigatorId } = dispatch.viewSetting.getComParams({})
      const { userInfo } = state.user
      const { treeList } = state.task
      //分组新增
      if (queryGroupBy?.fieldName || queryGroupBys?.length) {
        let insertGroupIndex = -1

        const addOption = {
          _rowType: TaskTableRowType.add,
          id: TaskTableRowTypeAdd + '-2-' + TaskTableRowType.add,
          assigner: userInfo,
          groupId: groupId,
          defaultParams: {
            ...defaultParams,
          },
        }
        if (navigatorId === TaskNavigatorType.assignToMe) {
          //分配给我新建 我是执行人
          addOption.defaultParams = {
            ...addOption.defaultParams,
            assignees: [userInfo],
            assigneeUids: [userInfo?.uid],
            assigneeCount: 1,
          }
        }
        const addItem = getAddItem(addOption)
        // 记忆当前的创建参数,连续创建需要用
        dispatch.task.setData({
          preAddParams: {
            ...opt,
            taskId: addItem.taskId,
            groupId: groupId,
          },
          // treeList: _groupList,
        })

        const newRowData = tableRowData || ({ id: `${insertGroupIndex}` } as Row<TodoInfo>)

        dispatch.task.addSubLine({
          targetId: newRowData.parentId,
          tableRowData: newRowData,
          addItemData: addItem,
          isSubTask: isSubtask,
        })

        scrollToIndex({
          taskId: addItem.taskId || addItem.id,
        })
        // if (isHeader) {
        //   POPOBridgeEmitter.emit(EnumEmitter.VirtuoSoscrollToIndex, showCount + 1);
        // }
      } else {
        const addOption = {
          _rowType: TaskTableRowType.add,
          id: TaskTableRowTypeAdd + '-' + TaskTableRowType.add + '-' + Date.now(),
          assigner: userInfo,
          groupId: groupId,
          defaultParams: {
            ...defaultParams,
          },
        }
        if (navigatorId === TaskNavigatorType.assignToMe) {
          //分配给我新建 我是执行人
          addOption.defaultParams = {
            ...addOption.defaultParams,
            assignees: [userInfo],
            assigneeUids: [userInfo?.uid],
            assigneeCount: 1,
          }
        }
        const addItem = getAddItem(addOption)

        scrollToIndex({
          taskId: addItem.taskId || addItem.id,
        })

        const list = setNewAddItem({
          dataList: treeList,
          isHeader: isHeader,
          //@ts-ignore
          item: addItem,
        })

        dispatch.taskCommon.setTaskMapItem({ task: addItem })

        dispatch.task.setData({
          // 记忆当前的创建参数,连续创建需要用
          preAddParams: {
            ...opt,
            taskId: addItem.taskId,
            groupId: groupId,
          },
          treeList: list,
          // showDataList: list,
          hasData: !!list.length,
        })
        dispatch.task.updateTreeList({ srcList: list })
      }
    },
    /**
     * 列表移除任务
     * @param opt
     * @param state
     */
    tableRemoveTaskItem(
      opt: { taskId?: string | number; groupId?: number | string; tableRowData: Row<TodoInfo> },
      state,
    ) {
      const { taskId, tableRowData } = opt

      const isGroup = getViewIsGroup()

      // const listKey = isGroup ? 'treeList' : 'dataList'
      const listKey = 'treeList'
      const tableData = state.task[listKey]
      const indexes = tableRowData?.id?.split?.('.')
      if (isGroup || indexes?.length > 1) {
        const row = window.tanstackTable.getRow(tableRowData?.parentId || '')

        const item = row?.original as TodoInfo
        if (item) {
          // item.subtask = (item.subtask as TodoInfo[])?.filter(sub => sub.taskId !== taskId)

          item.subtask?.splice(tableRowData.index, 1)
          item.subtask = [...(item.subtask || [])]
          dispatch.task.setData({
            [listKey]: [...tableData],
            hasData: !!tableData.length,
          })
        }

        // const newDataList = state.task.dataList.filter((v) => v.taskId !== taskId);
        // dispatch.task.setData({
        //   dataList: newDataList,
        // });
      } else {
        const _dataList = tableData.filter(v => v.id !== taskId)
        dispatch.task.updateTreeList({ srcList: _dataList })
        dispatch.task.setData({
          [listKey]: [..._dataList],
          hasData: !!_dataList.length,
        })
      }

      // const dataList = state.task.dataList as TodoInfo[]
      // const newList = dataList.filter(v => v.taskId !== taskId)
      // dispatch.task.setData({
      //   dataList: newList,
      // })

      // const _dataList = state.task.dataList.filter((v) => v.id !== taskId);
      // dispatch.task.setData({
      //   dataList: _dataList,
      // });

      // const _dataList = dataList.filter((v) => v.id !== taskId);
      // // 新增的时候会修改分组真实数据 移除的时候也要判断
      // if (groupId) {
      //   const { groupList } = state.task;
      //   const _groupList = groupList.map((item) => {
      //     return {
      //       ...item,
      //       count: item.groupTaskCount,
      //     };
      //   });
      //   dispatch.task.setData({
      //     groupList: _groupList,
      //   });
      // }
      // dispatch.task.processData({
      //   dataList: [..._dataList],
      // });
    },
    collapse(opt: { unfold: boolean; groupId: string }, state) {
      const { unfold, groupId } = opt
      const { groupList, cacheCollapse } = state.task
      const { currentViewTab } = state.viewSetting
      const _groupList = groupList.map(item => {
        if (item.groupId === groupId) {
          return { ...item, unfold: unfold }
        } else {
          return item
        }
      })
      dispatch.task.setData({
        groupList: _groupList,
        //记忆分组折叠
        cacheCollapse: {
          ...cacheCollapse,
          [currentViewTab.viewId!]: _groupList,
        },
      })
      dispatch.task.calculatedGroupTodoList({})
    },
    async loadMore(options: any, state) {
      const { dataList } = state.task
      if (!dataList.length) {
        return undefined
      }
      let scrollId = dataList[dataList.length - 1]?.taskId
      if (`${scrollId}`.includes(TaskTableRowTypeAdd)) {
        scrollId = dataList[dataList.length - 2]?.taskId
      }
      if (scrollId) {
        return dispatch.task.getTodoList({
          //@ts-ignore
          scrollId: scrollId,
        })
      }
      return undefined
    },
    async addTask(opt) {
      return createTask(opt)
        .then(res => {
          const isGroup = getViewIsGroup()
          if (isGroup) {
            // getGroupsData({ state, dispatch }).then(({ groups }) => {
            //   const groupCountMap = groups.reduce((pre, cur) => {
            //     pre[cur.groupId!] = cur.groupTaskCount;
            //     return pre;
            //   }, {} as Record<string, number>);
            //   dispatch.task.setData({
            //     groupCountMap,
            //   });
            // });
            // dispatch.task.getTreeList({})
          }
          // // NOTE: 分组里面数据增加，要同步到dataList中，防止取消分组后，列表展示数据缺失
          // const dataList = state.task.dataList as TodoInfo[]
          // dataList.unshift(res)
          // dispatch.task.setData({
          //   dataList: [...dataList],
          // })
          dispatch.viewSetting.refreshDataByDataChange({
            refreshCount: true,
            refreshList: false,
          })
          return res
        })
        .catch(err => {
          dispatch.viewSetting.refreshDataByDataChange({
            refreshCount: true,
            refreshList: true,
          })
          return err
        })
    },

    addSubLine(opt, state) {
      const { taskInfo, tableRowData, addItemData = {}, isSubTask, targetId } = opt

      const targetItem = window.tanstackTable.getRow(targetId)

      console.log('addSubLine targetId', targetId, targetItem)

      // tanstack table 的 id，包含层级关系, 比如0.2.1
      const { id } = tableRowData

      if (id) {
        // const listKey = isGroup ? 'treeList' : 'dataList'
        const listKey = 'treeList'
        const list = state.task[listKey]

        // const indexes = id.split('.')
        const addTarget = targetItem.original

        if (addTarget) {
          // 对于分组任务，需要确保编辑行只添加到被点击的特定任务实例中
          // 而不是所有相同 taskId 的任务实例
          let prevSubRows = addTarget.subtask ? [...addTarget.subtask] : []
          const { userInfo } = state.user
          const lastItem = prevSubRows[prevSubRows.length - 1] as TodoInfo
          const lastSecondItem = prevSubRows[prevSubRows.length - 2] as TodoInfo
          if (lastSecondItem?._rowType === TaskTableRowType.add) {
            prevSubRows.splice(prevSubRows.length - 2, 1)
          }
          const now = Date.now()
          const { children: addInfoChildren, subtask: addInfoSubtask, deadlineFormat, ...restTaskInfo } = taskInfo

          const addItem = getAddItem({
            ...restTaskInfo,
            ...addItemData,
            _rowType: TaskTableRowType.add,
            id: `${TaskTableRowTypeAdd}-${id}-${TaskTableRowType.add}-${now}`,
            taskId: `${TaskTableRowTypeAdd}-${id}-${TaskTableRowType.add}-${now}`,
            // @ts-ignore
            defaultParams: addTarget.defaultParams,
            assigner: userInfo,
            finished: 0,
            selfFinished: 0,
            deadlineFormat: taskInfo?.deadline ? deadlineFormat : PPTimeFormat.noDate,
            // groupId: addTarget.groupId,
            // 父任务相关
            parentId: typeof addTarget.taskId === 'number' ? addTarget.taskId : taskInfo?.parentId,
            title: '',
          }) as TodoInfo
          if (taskInfo?.isHeader) {
            addTarget.subtask = [addItem, ...(addTarget.subtask || [])]
          } else {
            if (lastItem?._rowType === TaskTableRowType.add) {
              prevSubRows.splice(prevSubRows.length - 1, 1, addItem)
            } else if (lastItem?._rowType === TaskTableRowType.addBtn) {
              prevSubRows.splice(prevSubRows.length - 1, 0, addItem)
            } else {
              prevSubRows.push(addItem)
            }
            addTarget.subtask = prevSubRows
            // if (isSubTask) {
            //   const parentGroup = window.tanstackTable.getRow(targetItem.parentId)
            //   const parentGroupItem = parentGroup.original as TodoInfo
            //   if (parentGroupItem) {
            //     const index = parentGroupItem.subtask?.findIndex(item => item.taskId === addTarget.taskId)
            //     if (index !== undefined && index > -1) {
            //       parentGroupItem.subtask![index] = {
            //         ...addTarget,
            //         subtask: prevSubRows,
            //       }
            //     }
            //   }
            // } else {
            //   addTarget.subtask = prevSubRows
            // }
          }

          dispatch.taskCommon.setTaskMapItem({ task: addItem })

          const updateData: any = {
            [listKey]: [...list],
          }

          if (isSubTask) {
            const newExpandedKeysMap = {
              ...getExpandedIndexMap(),
              [tableRowData.id]: true,
            }
            setExpandedIndexMap(newExpandedKeysMap)
            updateData.expandedKeysMap = newExpandedKeysMap
          }

          dispatch.task.setData(updateData)
          scrollToIndex({
            taskId: addItem.taskId,
            subtaskLength: (addTarget.subtask?.length || 0) - 1,
          })
        }
      }
    },
    /**
     * 更新showDataList中的任务信息，处理逻辑根据是否有parentId区分子任务或者(父任务 | 普通任务)
     *
     */
    updateTaskInfo(opt) {
      updateTaskInfo(opt, { modelName: 'task', dispatch })
    },
  }),
})
