import classNames from 'classnames'
import React from 'react'

import { Button, Dropdown, Icon, Select } from '@/components/basic'
import { Order } from '@/utils/const'
import I18N from '@/utils/I18N'

import Container from '../../overlay-container'
import GroupOverlay, { GroupList } from '../overlay'
import s from './index.less'
export type Props = {
  className?: string
  queryGroupBys?: any[]
  onChange?: (v: any[]) => void
  groupListData: { name: string; fieldName: string }[]
}

export interface Sorted {
  sortOrder: Order
  key: string
}

const MultipleOverlay: React.FC<Props> = props => {
  const { queryGroupBys = [], onChange, groupListData, groupListDataMap } = props

  const handleChange = (v: string | undefined, item, add) => {
    let groups = []
    if (!queryGroupBys.length) {
      groups = [item]
    } else {
      if (add) {
        groups = [...queryGroupBys, item]
      } else {
        groups = queryGroupBys.filter(i => i.fieldName !== item.fieldName)
      }
    }
    onChange?.(groups)
  }

  const handleChangeByIndex = (v: string | undefined, item, index) => {
    if (queryGroupBys?.some(i => i.fieldName === v)) {
      return
    }
    const groups = [...queryGroupBys]
    if (v) {
      groups[index] = groupListDataMap[v]
    } else {
      groups.splice(index, 1)
    }
    onChange?.(groups)
  }

  const handleDelete = (index: number) => {
    const groups = [...queryGroupBys]
    groups.splice(index, 1)
    onChange?.(groups)
  }

  return (
    <Container
      className={s.container}
      headClassName={s.container__header}
      title={I18N.auto.grouping_2}
      showClear
      onClear={() => {
        onChange?.([])
      }}
    >
      <div className={s.content}>
        {queryGroupBys?.map((item, i) => {
          const isCustomField = item?.fieldName === 'customField'
          const selectValue = isCustomField ? item.customFieldId : item.fieldName

          return (
            <div key={i} className={s.selected__item}>
              <Select
                options={groupListData}
                value={selectValue}
                fieldNames={{ value: 'fieldName' }}
                onChange={val => handleChangeByIndex(val, item, i)}
                className={s.select}
                getPopupContainer={() => document.querySelector(`.${s.content}`)!}
              />

              <Button
                type="text-subtle"
                icon={<Icon name="icon-sys_close" />}
                onClick={() => handleDelete(i)}
                className={s.close}
              ></Button>
            </div>
          )
        })}
        {(!queryGroupBys?.length || queryGroupBys?.length <= 1) && (
          <Dropdown
            getPopupContainer={() => document.querySelector(`.${s.content}`)!}
            overlay={
              <div className={s.list__container}>
                <GroupList groupListData={groupListData} activeKey={queryGroupBys} onChange={handleChange} />
              </div>
            }
          >
            <Button type="checked-neutral" icon={<Icon name="icon-add" />}>
              {I18N.auto.addGroup}
            </Button>
          </Dropdown>
        )}
      </div>
    </Container>
  )
}

export default MultipleOverlay
