import { Modal } from '@/components/basic'
import s from './index.less'
import I18N from '@/utils/I18N'
import classNames from 'classnames'
import { apiTaskDeleteTaskDelete, apiTodoCancelGet } from '@/api'
import { Dispatch, store } from '@/models/store'
import { useDispatch } from 'react-redux'
import { useContext } from 'react'
import { Checkbox, ConfigProvider, Radio } from '@bedrock/components'
import { TodoInfo } from '@/types'
import ParentTaskDelContent from './ParentTaskDelContent'
import { getIsInSession } from '@/models/utils'

interface IOpenParams {
  taskId: string
  parentId?: string
  title: string
  onClose?: () => void
  containerElement?: HTMLElement
  taskInfo?: TodoInfo
  /** detail_inner 代表删除父任务详情中的子任务 */
  from?: string
}

interface IUseDelTaskConfirmParams {
  onSuccess?: () => void
}

const useDelTaskConfirm = () => {
  const dispatch = useDispatch<Dispatch>()
  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext)

  const open = ({ taskId, onClose, taskInfo, containerElement, from }: IOpenParams) => {
    const zIndex = getGlobalZIndex() + 100000
    const extraParams = { cancelType: 0, parentTaskId: taskInfo?.parentId }

    let content = (
      <div className={classNames('line-ellipsis-3')}>
        {I18N.auto.confirmToDeletePending} {taskInfo?.title}
      </div>
    )

    if (taskInfo?.isParent) {
      content = <ParentTaskDelContent title={taskInfo?.title} onChange={v => (extraParams.cancelType = v)} />
    }

    // if (taskInfo?.parentId) {
    // 详情中的子任务删除，不关闭详情
    window.detailClosePending = true
    // }
    Modal.confirm(
      {
        rootClassName: s.confirm,
        width: 420,
        title: taskInfo?.parentId ? I18N.auto.delSubTask : I18N.auto.deleteTask,
        content,
        // centered: true,
        cancelText: I18N.auto.cancel,
        okText: I18N.auto.delete,
        mask: !getIsInSession(),
        afterClose: () => {
          delete window.detailClosePending
        },
        onOk: () => {
          // 父子任务走新接口，普通任务走旧接口
          const request = taskInfo?.isParent || taskInfo?.parentId ? apiTaskDeleteTaskDelete : apiTodoCancelGet
          return request({ taskId: String(taskId), todoId: taskId, ...extraParams })
            .then(() => {
              if (store.getState().detail.visibleDetail) {
                if (from !== 'detail_inner') {
                  dispatch.detail.closeDetail({})
                }
                // 如果在详情中删除的子任务，刷新父任务详情并更新大列表
                if (taskInfo?.parentId) {
                  dispatch.viewSetting.updateItemByDetail({ taskId: taskInfo?.parentId })
                  // 更新数量
                  dispatch.viewSetting.refreshDataByDataChange({
                    refreshList: false,
                    refreshCount: true,
                  })
                } else {
                  // 删除父任务有保留和不保留子任务两种情况，前端更新较复杂，直接请求接口更新
                  if (taskInfo?.isParent) {
                    dispatch.viewSetting.refreshDataByDataChange({
                      refreshList: true,
                      refreshCount: true,
                    })
                  } else {
                    // 删除普通任务，前端更新大列表
                    dispatch.viewSetting.refreshDataByDataChange({
                      refreshList: true,
                      refreshCount: true,
                      detail: taskInfo,
                      actionType: 'delete',
                    })
                  }
                  // TODO: 删除任务后将列表的中该项删除;
                  // dispatch.viewSetting.refreshDataByDataChange({
                  //   refreshList: true,
                  //   refreshCount: true,
                  //   detail,
                  //   actionType: 'delete',
                  // });
                }
              }
              onClose?.()
            })
            .catch(() => {
              // 异常强制刷新列表
              dispatch.viewSetting.refreshDataByDataChange({
                refreshList: true,
                refreshCount: true,
              })
            })
        },
        zIndex,
        getContainer: () => (containerElement ? containerElement : document.body),
      },
      'warning',
    )
  }

  return {
    open,
  }
}

export default useDelTaskConfirm
