import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { history, Outlet, useParams } from 'umi';

import { Dispatch } from '@/models/store';
import { getUrlParams } from '@/utils';
// import Tasks from './tasks';

const NewPage: React.FC = () => {
  const params = useParams();
  const { id } = params as { id: string };
  const dispatch = useDispatch<Dispatch>();
  // 如果带id 默认打开详情
  const { location } = history;
  const { id: taskId } = getUrlParams(location.search);
  if (taskId) {
    dispatch.detail.openDetail(Number(taskId));
    if (location.search) {
      history.replace(location.pathname);
    }
  }
  useEffect(() => {
    if (id) {
      dispatch.project.getProject({
        id: String(id),
      });
    }
  }, [id]);
  return <Outlet></Outlet>;
};
export default NewPage;
