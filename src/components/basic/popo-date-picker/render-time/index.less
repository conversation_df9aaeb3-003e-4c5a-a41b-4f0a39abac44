.labelTime {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  //height: 100%;
  // width: 100%;
  // min-height: 26px;
  // line-height: 26px;
  font-size: 13px;
  color: var(--TextPrimary);

  .labelTimeLeft {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
  }

  .icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    line-height: 16px;
    margin-left: 4px;
    font-size: 12px;
    text-align: center;
    color: var(--TextSecondary);
    border-radius: 4px;
  }
  .iconDelete {
    display: none;
    position: absolute;
    top: 50%;
    margin-top: 1px;
    transform: translateY(-50%);
    right: 0px;
    flex-shrink: 0;
  }
  .showDelete {
    display: block;
  }

  &:hover {
    .remove {
      display: flex;
    }
  }
  .remove {
    &:hover {
      background-color: var(--aBlack6);
    }
  }
}
