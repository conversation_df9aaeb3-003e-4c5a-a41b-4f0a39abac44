.time__label {
  position: relative;
  padding: 2.5px 4px;
  border-radius: 6px;
  color: var(--TextSecondary-ongrey);
  transition: background-color 0.2s ease-in-out;

  .clear__icon {
    display: none;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 100%;
    background-color: var(--N200);

    .clear {
      font-size: 12px;
      color: var(--IconQuartus);
      transition: color, width 0.2s ease-in-out;
      &:hover {
        color: var(--Brand500);
      }

      &:active {
        color: var(--Brand600);
      }
    }
  }

  &:hover {
    background-color: var(--aBlack6);
    .clear__icon {
      display: flex;
    }
  }
}

.date__picker {
  width: auto !important;
}

.date__icon {
  color: var(--IconPrimary);
}

.icon {
  width: 22px;
  height: 22px;
}
