import React from 'react';

import { Drawer<PERSON>lick<PERSON>rign<PERSON><PERSON>, DrawerO<PERSON><PERSON> } from '@/utils/const';

import { RenderRow } from '../index';
export type Props<RecordType> = {
  className?: string;
  data: RecordType[];
  itemData: RecordType;
};
function AddRow<RecordType>(props: Props<RecordType>) {
  const { data, itemData } = props;
  const onRowClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    (e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd;
  };
  const onMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    (e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd;
  };

  return (
    <RenderRow
      onRowClick={onRowClick}
      onRowMouseDown={onMouseDown}
      hasCheckbox={false}
      data={data}
      itemData={itemData}
    ></RenderRow>
  );
}

export default AddRow;
