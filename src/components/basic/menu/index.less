body {
  .rock-dropdown-menu {
    --box-shadow-3: var(--ComBoxShadow);
    --primary-1: var(--TextPrimary);
    --primary-5: var(--aBlack6);
    --fill-1: var(--aBlack6);
    --line-height-base: 26px;
    --border-radius-base: 6px;
    --bg-5: var(--bgTop);
    min-width: 140px;
    //background-color: var(--bgTop); 不要在这个地方设置背景 去内部设置

    .rock-dropdown-menu-submenu-arrow {
      --text-color-3: var(--IconTertiary);
    }
    .rock-dropdown-menu-item-divider {
      margin-top: 4px;
      margin-bottom: 4px;
    }
  }
  .rock-dropdown-menu-item-selected {
    font-weight: normal;
  }
  ul.rock-dropdown-menu {
    border: 1px solid var(--aBlack12);
    padding: 4px 0;
  }
  .rock-dropdown-menu-item {
    margin: 0 4px;
    background-color: var(--bgTop);
  }
  .rock-dropdown-menu-item:not(:first-child),
  .rock-dropdown-menu-submenu:not(:first-child) {
    margin-top: 0px;
  }
  .rock-dropdown-menu-item-divider,
  .rock-dropdown-menu-submenu-title-divider {
    background-color: var(--aBlack6);
  }
}
