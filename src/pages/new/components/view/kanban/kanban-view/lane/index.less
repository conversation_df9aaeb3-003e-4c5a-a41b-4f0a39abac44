.laneContainer {
  width: 256px;
  min-width: 256px;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 0 12px 0;
  border-radius: 8px;
  transition: background-color ease 0.25s;
  &.showBg {
    background-color: var(--aBlack4);
    :global {
      .kanban-lane-no-data {
        background: transparent;
      }
    }
  }

  .head {
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 24px;
    line-height: 24px;
    margin-bottom: 6px;
    .left {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
    }
    .title {
      font-weight: 600;
      font-size: 15px;
      margin-right: 2px;
      color: var(--TextPrimary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .count {
      font-size: 14px;
      display: flex;
      align-items: center;
      height: 16px;
      padding: 0 6px;
      color: var(--TextTertiary);
    }
    .addIcon {
      display: none;
    }
    &:hover {
      .addIcon {
        display: block;
        flex-shrink: 0;
      }
    }
  }
  .body {
    flex: 1;
  }
}
