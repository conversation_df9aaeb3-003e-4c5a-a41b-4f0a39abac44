import classNames from 'classnames';
import React, { PropsWithChildren, useState } from 'react';

import { Divider, Dropdown } from '@/components/basic';
import { Icon } from '@/components/basic';
import I18N from '@/utils/I18N';

import { ILevelOption, Level, LevelSelectOptions } from '../utils';
import s from './index.less';

export type Props = {
  value?: number;
  onChange?: (v?: number) => void;
  className?: string;
  showDelete?: boolean;
};

const LevelSelect: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, onChange, children, showDelete } = props;
  const [open, setOpen] = useState(false);
  const change = (item: ILevelOption) => {
    onChange?.(item.value);
    setOpen(false);
  };
  const onRemove = () => {
    onChange?.(Level.default);
    setOpen(false);
  };
  return (
    <Dropdown
      title={children}
      trigger="click"
      arrow={false}
      defaultOpen={false}
      open={open}
      onOpenChange={(v) => {
        setOpen(v);
      }}
      //@ts-ignore
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <div className={classNames('com-dropdown-select', s.panel)}>
          <div className={s.level}>
            {LevelSelectOptions.filter((item) => item.showSelect).map((item, index) => {
              return (
                <div
                  key={index}
                  className={classNames(s.levelItem, item.className, {
                    [s.active]: item.value === value,
                  })}
                  onClick={() => {
                    change(item);
                  }}
                >
                  <Icon
                    className={classNames(s.icon, 'com-level-icon')}
                    name={item.IconName}
                  ></Icon>
                  <span className="com-level-label">{item.name}</span>
                </div>
              );
            })}
            {showDelete ? (
              <>
                <Divider type="horizontal" className={s.divider}></Divider>
                <div className={classNames(s.levelItem, s.delete)} onClick={onRemove}>
                  {I18N.auto.deletePriority}
                </div>
              </>
            ) : null}
          </div>
        </div>
      }
    />
  );
};

export default LevelSelect;
