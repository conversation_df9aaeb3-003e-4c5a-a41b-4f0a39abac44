import {
  defaultDropAnimationSideEffects,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DropAnimation,
  MeasuringConfiguration,
  MeasuringStrategy,
  MouseSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis, restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import SortableItem from '../sortable-item';

const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
};

export const enum InsertPosition {
  before = 'before',
  after = 'after',
}

interface IGetInsertPositionParams {
  /** 被over的数据 */
  overItemData?: any;
  /** 当前数据Id，用来和被over的数据匹配，如果相等，则代表当前数据被over */
  currentId?: number;
  /** 当前数据索引，用来和被激活数据index（activeIndex）对比，如果currentIndex > activeIndex,代表被激活数据要插入到当前数据后面；反之，插入到前面 */
  currentIndex: number;
  /** 被激活数据索引 */
  activeIndex: number;
}
export const getInsertPosition = ({
  overItemData,
  currentId,
  currentIndex,
  activeIndex,
}: IGetInsertPositionParams) => {
  if (overItemData?.id === currentId && currentIndex !== activeIndex) {
    return currentIndex > activeIndex ? InsertPosition.after : InsertPosition.before;
  }
  return void 0;
};

interface Props<T = { id: UniqueIdentifier; [k: string]: any }> {
  list: T[];
  wholeRowHandle?: boolean;
  onDragEnd?: (
    event: DragEndEvent,
    list: any[],
    params?: {
      activeIndex: number;
      overIndex: number;
    }
  ) => void;
  onDragOver?: (event: DragEndEvent) => void;
  renderItem?: (item: T, index: number) => React.ReactNode;
  customRenderItem?: (item: T, index: number, options?: Record<string, any>) => React.ReactNode;
  direction?: 'vertical' | 'horizontal';
  renderOverlayItem?: (item?: T) => React.ReactNode;
  disabled?: boolean;
  ids?: UniqueIdentifier[];
}

function DnDList<T = { id: UniqueIdentifier; [k: string]: any }>(props: Props) {
  const {
    list,
    onDragEnd,
    renderItem,
    customRenderItem,
    renderOverlayItem,
    wholeRowHandle = true,
    disabled = false,
    direction = 'vertical',
    onDragOver,
    ids,
  } = props;
  const activeIndexRef = useRef(-1);
  const [dndActiveId, setDndActiveId] = useState<string | number | undefined>(undefined);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 4,
      },
    })
  );
  const activeItem = useMemo(() => {
    if (dndActiveId) {
      const item = list.find((item) => (item.id || item.taskId) === dndActiveId);
      return item;
    }
    return undefined;
  }, [dndActiveId, list]);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={({ active }) => {
        activeIndexRef.current = list.findIndex((item) => item.id === active.id);
        setDndActiveId(active.id as string);
      }}
      onDragEnd={(opt) => {
        const { active, over } = opt;
        if (!over?.id || !active.id) {
          return;
        }
        const activeIndex = list.findIndex((item) => (item.id || item.taskId) === active.id);
        const overIndex = list.findIndex((item) => (item.id || item.taskId) === over.id);
        if (activeIndex > -1 && overIndex > -1 && activeIndex !== overIndex) {
          const _list = arrayMove(list, activeIndex, overIndex);
          onDragEnd?.(opt, _list, { activeIndex, overIndex });
        }
        activeIndexRef.current = -1;
        setDndActiveId(undefined);
      }}
      onDragOver={onDragOver}
      modifiers={direction === 'horizontal' ? [restrictToHorizontalAxis] : [restrictToVerticalAxis]}
    >
      <SortableContext
        //可拖拽项目 如果是number[] 有值即可, 如果是object[] 则必须含有id字段,才允许拖拽
        items={ids || list}
        strategy={
          direction === 'vertical' ? verticalListSortingStrategy : horizontalListSortingStrategy
        }
        disabled={disabled}
      >
        {list?.map((item, index) => {
          if (customRenderItem) {
            return customRenderItem?.(item, index, { activeIndex: activeIndexRef.current });
          } else {
            return (
              <SortableItem key={item.id || item.taskId} data={item} hasHandle={wholeRowHandle}>
                {renderItem?.(item, index)}
              </SortableItem>
            );
          }
        })}
      </SortableContext>
      {direction === 'vertical' ? (
        <DragOverlay dropAnimation={dropAnimation}>
          {dndActiveId ? renderOverlayItem?.(activeItem) : null}
        </DragOverlay>
      ) : (
        createPortal(
          <DragOverlay dropAnimation={dropAnimation}>
            {dndActiveId ? renderOverlayItem?.(activeItem) : null}
          </DragOverlay>,
          document.body
        )
      )}
    </DndContext>
  );
}
export default DnDList;
