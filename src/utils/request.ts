import Axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { RequestFunctionParams } from 'yapi-to-typescript'
import { RequestBodyType } from 'yapi-to-typescript'

import { Message } from '@/components/basic'
import { Dispatch, store } from '@/models/store'
import { getStorage, setStorage } from '@/utils'
import I18N from '@/utils/I18N'

import Const, { ErrorCode } from './const'
import { Platform } from './platform'

export interface RequestOptions {
  mock?: boolean
  // 接口错误是否弹窗
  errorSilent?: boolean
  // 请求唯一标识，用于取消请求
  requestId?: string
  // 是否可取消，默认为 true
  cancelable?: boolean
}

interface ResBaseBody<T> {
  status: number
  message: string
  data: T
}

// 可取消的请求接口
export interface CancelableRequest<T> extends Promise<T> {
  cancel: (reason?: string) => void
  requestId: string
}

// 请求管理器
class RequestManager {
  private pendingRequests = new Map<string, AbortController>()
  private requestIdCounter = 0

  // 生成请求ID
  generateRequestId(customId?: string): string {
    if (customId) {
      return customId
    }
    return `req_${Date.now()}_${++this.requestIdCounter}`
  }

  // 添加请求
  addRequest(requestId: string, controller: AbortController): void {
    this.pendingRequests.set(requestId, controller)
  }

  // 取消指定请求
  cancelRequest(requestId: string, reason?: string): boolean {
    const controller = this.pendingRequests.get(requestId)
    if (controller) {
      controller.abort(reason)
      this.pendingRequests.delete(requestId)
      return true
    }
    return false
  }

  // 取消所有请求
  cancelAllRequests(reason?: string): void {
    this.pendingRequests.forEach((controller, requestId) => {
      controller.abort(reason)
    })
    this.pendingRequests.clear()
  }

  // 取消匹配模式的请求
  cancelRequestsByPattern(pattern: RegExp, reason?: string): string[] {
    const canceledIds: string[] = []
    this.pendingRequests.forEach((controller, requestId) => {
      if (pattern.test(requestId)) {
        controller.abort(reason)
        this.pendingRequests.delete(requestId)
        canceledIds.push(requestId)
      }
    })
    return canceledIds
  }

  // 清理已完成的请求
  removeRequest(requestId: string): void {
    this.pendingRequests.delete(requestId)
  }

  // 获取待处理请求数量
  getPendingRequestsCount(): number {
    return this.pendingRequests.size
  }

  // 获取所有待处理请求ID
  getPendingRequestIds(): string[] {
    return Array.from(this.pendingRequests.keys())
  }
}

const axios = Axios.create({
  timeout: 10000, // 超时时间
})

// 全局请求管理器实例
export const requestManager = new RequestManager()

export default function request<TResponseData>(
  payload: RequestFunctionParams,
  options: RequestOptions = {
    mock: false,
    errorSilent: false,
    cancelable: true,
  },
): CancelableRequest<TResponseData> {
  const { mock = false, errorSilent = false, cancelable = true, requestId: customRequestId } = options

  // 生成请求ID
  const requestId = requestManager.generateRequestId(customRequestId)

  // 创建 AbortController
  const controller = cancelable ? new AbortController() : undefined

  // 基本地址
  const baseUrl = mock ? payload.mockUrl : location.origin

  // 请求地址
  const url = `${baseUrl}${payload.path}`

  // 具体请求逻辑
  const axiosOptions: AxiosRequestConfig = {
    url,
    method: payload.method,
    signal: controller?.signal, // 添加取消信号
    data: payload.hasFileData
      ? payload.getFormData()
      : payload.requestBodyType === RequestBodyType.json
      ? payload.data
      : payload.requestBodyType === RequestBodyType.form
      ? Object.keys(payload.data)
          .filter(key => payload.data[key] != null)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(payload.data[key])}`)
          .join('&')
      : undefined,
    headers: {
      appVersion: Platform.appVersion,
      deviceType: Platform.deviceType,
      Authorization: localStorage.getItem(Const.LoginToken),
      'Accept-Language': Platform.appLanguage,
    },
  }

  // 如果可取消，添加到请求管理器
  if (cancelable && controller) {
    requestManager.addRequest(requestId, controller)
  }

  // 创建可取消的Promise
  const requestPromise = axios(axiosOptions).then(
    (res: AxiosResponse<ResBaseBody<TResponseData>>) => {
      // 请求完成，从管理器中移除
      if (cancelable) {
        requestManager.removeRequest(requestId)
      }

      const result = res.data
      if (!result || result.status !== 1) {
        const error = new AxiosError<ResBaseBody<TResponseData>>(
          `${result?.message}`,
          res.status + '',
          res.config,
          res.request,
          res,
        )

        //未登录
        //@ts-ignore
        if (result?.status === ErrorCode.NO_LOGIN) {
          const time = getStorage(Const.RetryLoginTimes)
          if (time < 3) {
            setStorage(Const.RetryLoginTimes, time + 1)
            ;(store.dispatch as Dispatch).user.login().then(() => {
              console.log('request 305 reload')
              window.location.reload()
            })
          }
          if (process.env.NODE_ENV === Const.PRODUCTION) {
            throw error
          }
        } else if (result?.status === ErrorCode.PROJECT_USER_LIMIT) {
          Message.error(I18N.auto.projectMembersHave)
        } else if (result?.status === ErrorCode.PROJECT_LAST_ADMINISTRATOR) {
          Message.error(I18N.auto.operationFailedPlease_2)
        } else if (result?.status === ErrorCode.TEAM_ADMINISTRATOR_LIMIT) {
          Message.error(I18N.auto.onlyAssociatedWithYou)
        } else if (result?.status === ErrorCode.TEAM_ASSOCIATE_LIMIT) {
          Message.error(I18N.auto.associateProjectLimit)
        } else if (result?.status === ErrorCode.TEAM_CREATE_USERS_LESS_LIMIT) {
          Message.error(result?.message)
        } else {
          if (!errorSilent) {
            Message.error(I18N.auto.operationFailedPlease)
          }
        }
        throw error
      }
      if (result?.status === 1) {
        setStorage(Const.RetryLoginTimes, 0)
      }
      return result?.data
    },
    err => {
      // 请求失败，从管理器中移除
      if (cancelable) {
        requestManager.removeRequest(requestId)
      }

      // 检查是否是取消请求
      if (err.name === 'AbortError' || err.name === 'CanceledError') {
        console.log(`Request ${requestId} was canceled:`, err.message)
        // return Promise.reject(new Error(`Request canceled: ${err.message || 'User canceled'}`));
        return
      }

      // 网络异常
      if (err instanceof AxiosError && (!err.response || !err.response.status)) {
        err.message = I18N.auto.networkException
      }
      if (!errorSilent) {
        Message.error(err.message)
      }
      return Promise.reject(err)
    },
  ) as CancelableRequest<TResponseData>

  // 添加取消方法和请求ID
  requestPromise.cancel = (reason?: string) => {
    if (cancelable) {
      requestManager.cancelRequest(requestId, reason)
    }
  }
  requestPromise.requestId = requestId

  return requestPromise
}

// 辅助函数：创建带自动取消的请求钩子
export function createCancelableRequest() {
  const activeRequests = new Set<string>()

  const makeRequest = <T>(payload: RequestFunctionParams, options?: RequestOptions): CancelableRequest<T> => {
    const req = request<T>(payload, options)
    activeRequests.add(req.requestId)

    // 请求完成后从集合中移除
    req.finally(() => {
      activeRequests.delete(req.requestId)
    })

    return req
  }

  const cancelAll = (reason?: string) => {
    activeRequests.forEach(requestId => {
      requestManager.cancelRequest(requestId, reason)
    })
    activeRequests.clear()
  }

  return {
    request: makeRequest,
    cancelAll,
    getActiveRequestsCount: () => activeRequests.size,
    getActiveRequestIds: () => Array.from(activeRequests),
  }
}

// 页面卸载时自动取消所有请求的钩子
export function useAutoCancel() {
  const cancelableRequest = createCancelableRequest()

  // 组件卸载时取消所有请求
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      cancelableRequest.cancelAll('Page unload')
    })
  }

  return cancelableRequest
}

// 导出请求管理器的一些快捷方法
export const cancelRequest = (requestId: string, reason?: string) => requestManager.cancelRequest(requestId, reason)

export const cancelAllRequests = (reason?: string) => requestManager.cancelAllRequests(reason)

export const cancelRequestsByPattern = (pattern: RegExp, reason?: string) =>
  requestManager.cancelRequestsByPattern(pattern, reason)

export const getPendingRequestsCount = () => requestManager.getPendingRequestsCount()
