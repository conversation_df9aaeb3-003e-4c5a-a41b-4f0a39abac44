import { FederatedPointerEvent } from '@visactor/vtable/es/vrender'
import * as VTableGantt from '@visactor/vtable-gantt'
import { useLatest, useMemoizedFn } from 'ahooks'
import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom/client'

import { getVTableIcons } from '@/common/VTableIcons'
import { Tooltip } from '@/components/basic'
import { CorlorMode } from '@/utils/platform'

import s from './index.less'
import { useGetThemeConfig } from './useGetThemeConfig'
import {
  FinishedIcon,
  getMonthScales,
  getYearScales,
  IGanttTaskGroupInfo,
  IGanttTaskInfo,
  isGroupRecordFn,
  ScaleType,
} from './utils'
import I18N from '@/utils/I18N'
import { getPermissionsByTaskId } from '@/hooks/useGetPermissions'
import { useSelector } from 'react-redux'
import { Dispatch, RootState } from '@/models/store'
import { taskUpdateDeadline } from '@/api-common'
import { AddMenuId, PPTimeFormat, TaskTableRowTypeAdd } from '@/utils/const'
import * as _ from 'lodash'
import { useTimelineDrag } from '@/pages/new/components/view/timeline/hooks/useTimelineDrag'
import { useDispatch } from 'react-redux'

export interface IGanttProps {
  taskKeyField: string
  startDateField: string
  endDateField: string
  columns: VTableGantt.ColumnsDefine
  records: (IGanttTaskGroupInfo | IGanttTaskInfo)[]
  onReady?: (gantt: VTableGantt.Gantt) => void
  onTaskBarClick?(evt: {
    index: number
    sub_task_index?: number
    record: IGanttTaskInfo
    event: Event
    federatedEvent: FederatedPointerEvent
  }): any
  scaleType?: ScaleType
  onScaleChange?(scaleType: ScaleType): void
  theme?: CorlorMode
  permissionsMap?: Record<string, boolean>
}

function Gantt(props: IGanttProps) {
  const {
    taskKeyField,
    columns,
    records,
    startDateField,
    endDateField,
    onReady,
    onTaskBarClick,
    scaleType = ScaleType.month,
    onScaleChange,
    theme = CorlorMode.light,
    permissionsMap,
  } = props
  const domRef = useRef<HTMLDivElement>(null)
  const ganttRef = useRef<VTableGantt.Gantt>()
  const dispatch = useDispatch<Dispatch>()
  const { querySort, queryGroupBys } = useSelector((state: RootState) => {
    return {
      querySort: state.viewSetting.currentViewTab.querySort,
      queryGroupBys: state.viewSetting.currentViewTab.queryGroupBys,
    }
  })

  const getTimelineHeader = (curScaleType: `${ScaleType}`) => {
    return {
      colWidth: curScaleType === ScaleType.month ? 50 : 94,
      backgroundColor: themeTokens?.bgBottom,
      scales: curScaleType === ScaleType.month ? getMonthScales(themeTokens) : getYearScales(themeTokens),
    }
  }

  const { themeConfig, themeTokens, themeTokensRef } = useGetThemeConfig({
    theme,
    onThemeChange: themeConfig => {
      ganttRef.current?.taskListTableInstance?.updateTheme(themeConfig)
    },
  })

  // const [scaleType, setScaleType] = useState<`${ScaleType}`>(propScaleType);
  const [sidebarExpand, setSidebarExpand] = useState(true)

  const { hasDragIcon } = useTimelineDrag()

  const renderDetailBar = (args: VTableGantt.TYPES.TaskBarCustomLayoutArgumentType) => {
    const { taskRecord, width, height } = args
    const curRecord = taskRecord as IGanttTaskInfo
    const isParent = !!curRecord.isParent
    const isSubTask = !!curRecord.parentId

    const {
      TaskbarIng,
      TaskbarDone,
      TextPrimary,
      'TextSecondary-ongrey': TextSecondaryOngrey,
    } = themeTokensRef.current || {}

    // 减去左右2侧padding
    let availableWidth = width - 16

    const container = new VTableGantt.VRender.Group({
      width: width,
      height: isSubTask ? 18 : 26,
      fill: curRecord.finished ? TaskbarDone : TaskbarIng,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      cornerRadius: 4,
      alignItems: 'center',
      justifyContent: 'space-between',
      cursor: 'pointer',
      y: 6,
    })

    const leftContainer = new VTableGantt.VRender.Group({
      height,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      cornerRadius: 8,
      alignItems: 'center',
      boundsPadding: [0, 8, 0, 8],
    })

    if (curRecord.finished) {
      // 减去 icon  + 右侧 margin
      availableWidth -= 18

      const statusIcon = new VTableGantt.VRender.Image({
        width: 12,
        height: 12,
        image: FinishedIcon,
        cornerRadius: 14,
        boundsPadding: [0, 4, 0, 0],
        cursor: 'pointer',
      })

      leftContainer.add(statusIcon)
    }

    const content = new VTableGantt.VRender.Text({
      text: curRecord.title,
      fontSize: isSubTask ? 11 : 12,
      // fontWeight: 500,
      fill: TextPrimary,
      cursor: 'pointer',
      boundsPadding: [2, 0, 0, 0],
    })

    // 减去文本内容
    // availableWidth -= fastMeasureText(curRecord.title, { fontSize: 12, fontWeight: 'bold' });
    leftContainer.add(content)

    if (isParent) {
      const subtaskIcon = new VTableGantt.VRender.Image({
        width: 16,
        height: 16,
        image: `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/subtask/sub.png`,
        boundsPadding: [0, 0, 0, 5],
        id: 'subtask_icon',
      })
      const subtaskCount = new VTableGantt.VRender.Text({
        text: `${curRecord.subtaskFinishedCount || 0}/${curRecord.subtask?.length || 0}`,
        fontSize: 12,
        fill: TextSecondaryOngrey,
        textAlign: 'left',
        textBaseline: 'top',
        boundsPadding: [0, 0, 0, 3],
      })
      leftContainer.add(subtaskIcon)
      leftContainer.add(subtaskCount)
    }

    // 减去天数
    // const taskSpanText = `${taskDays}天`;
    // availableWidth -= fastMeasureText(taskSpanText, { fontSize: 12, fontWeight: 'bold' });

    // const daysEl = new VTableGantt.VRender.Text({
    //   text: taskSpanText,
    //   fontSize: 12,
    //   fontWeight: 'bold',
    //   fill: 'rgba(17, 17, 17, 0.72)',
    //   maxLineWidth: 40,
    //   boundsPadding: [0, 8, 0, 0],
    // });

    container.add(leftContainer as any)
    return container
  }

  const renderGroupBar = (args: VTableGantt.TYPES.TaskBarCustomLayoutArgumentType) => {
    const { taskRecord, width, height } = args
    console.log('arrrrgs', args)
    const curRecord = taskRecord as IGanttTaskInfo
    const curGroup = taskRecord as IGanttTaskGroupInfo

    const { Brand500, TaskbarIng, TextSecondary, aBlack6, TextPrimary } = themeTokensRef.current || {}

    const container = new VTableGantt.VRender.Group({
      width,
      height,
      fill: 'transparent',
    })

    const textContainer = new VTableGantt.VRender.Group({
      width,
      height,
      fill: 'transparent',
      display: 'flex',
      flexWrap: 'nowrap',
      alignItems: 'center',
      x: 4,
      y: -8,
    })
    const titleEl = new VTableGantt.VRender.Text({
      text: curRecord.title,
      fontSize: 13,
      fill: TextSecondary,
    })
    textContainer.add(titleEl)
    container.add(textContainer)

    const barContainer = new VTableGantt.VRender.Rect({
      width,
      height: curGroup.groupLevel === 0 ? 12 : 8,
      lineWidth: 1,
      stroke: aBlack6,
      fill: TaskbarIng,
      cornerRadius: 2,
      x: 0,
      y: 18,
    })

    const progressContainer = new VTableGantt.VRender.Rect({
      // 最小宽度 8
      width:
        curRecord.finishedCount == 0
          ? 0
          : Math.max(8, Math.floor((curRecord.finishedCount! / curRecord.totalCount!) * width)),
      height: curGroup.groupLevel === 0 ? 12 : 8,
      fill: Brand500,
      x: 0,
      y: 18,
      cornerRadius: 2,
    })
    container.add(barContainer)
    container.add(progressContainer)

    if (width > 18) {
      const getTriangleColor = (type: string) => {
        if (type === 'left' && progressContainer.attribute.width! >= 8) {
          return Brand500
        }

        if (type === 'right' && progressContainer.attribute.width! >= width - 8) {
          return Brand500
        }

        return TaskbarIng
      }

      // 分组两侧的收口，用于凸显层级
      const leftTriangle = new VTableGantt.VRender.Path({
        path: 'M2 0C0.895431 0 0 0.89543 0 2V12H1.16667L7 8V0H2Z',
        fill: getTriangleColor('left'),
        x: 0,
        y: curGroup.groupLevel === 0 ? 22 : 18,
      })

      const rightTriangle = new VTableGantt.VRender.Path({
        path: 'M5 0C6.10457 0 7 0.89543 7 2V12H5.83333L0 8V0H5Z',
        fill: getTriangleColor('right'),
        x: width - 7,
        y: curGroup.groupLevel === 0 ? 22 : 18,
      })

      container.add(leftTriangle)
      container.add(rightTriangle)
    }

    if (curRecord.totalCount) {
      const countEl = new VTableGantt.VRender.Text({
        text: `  ${curRecord.finishedCount}/${curRecord.totalCount}`,
        fontSize: 12,
        fontWeight: 500,
        fill: TextPrimary,
        boundsPadding: [1, 0, 0, 0],
      })
      textContainer.add(countEl)
    }

    return container
  }

  const option: VTableGantt.GanttConstructorOptions = {
    rowHeight: 48,
    overscrollBehavior: 'none',
    records,
    taskKeyField: taskKeyField,
    minDate: dayjs().startOf('year').subtract(2, 'year').format('YYYY-MM-DD'),
    maxDate: dayjs().endOf('year').add(2, 'year').format('YYYY-MM-DD'),
    taskListTable: {
      widthMode: 'standard',
      // hierarchyExpandLevel: 4,
      editCellTrigger: 'api',
      // groupTitleCustomLayout: (args) => {
      //   console.log('>>> groupTitleFieldFormat >>>', args);

      //   const { record } = args;
      //   return record.title;
      // },
      select: {
        disableSelect: true,
        disableHeaderSelect: true,
      },
      hover: {
        disableHeaderHover: true,
        highlightMode: 'row',
      },
      resize: {
        columnResizeMode: 'none',
      },
      columns,
      // heightMode: 'adaptive',
      // heightAdaptiveMode: 'only-body',
      // autoHeightInAdaptiveMode: false,
      // autoFillHeight: true,
      // canvasHeight: 'auto',
      // maxCanvasHeight: 853,
      // tableWidth: 250,
      minTableWidth: 240,
      maxTableWidth: 600,
      customComputeRowHeight(args) {
        /**
         * 必须用 getRecordByRowCol 来获取真实的 record
         * args.row 表示的是内置的拍平树结构的下标，不是原始数据的下标
         */
        // const record = ganttRef.current?.taskListTableInstance?.getRecordByRowCol(0, args.row) as
        //   | IGanttTaskGroupInfo
        //   | undefined;

        // 0 表示表头
        if (args.row === 0) {
          return 64
        }

        // if (record?.isGroup) {
        //   return 48;
        // }

        return 48
      },
      theme: themeConfig,
      tooltip: {
        isShowOverflowTextTooltip: true,
      },
    },
    frame: {
      outerFrameStyle: {
        borderLineWidth: 0,
        borderColor: 'transparent',
        cornerRadius: 0,
      },
      // listTable 和 gantt 的分割线, 如果使用该分割线, 会在dark模式下显示异常, 采用css方案
      verticalSplitLine: {
        lineWidth: 0,
      },
      horizontalSplitLine: {
        lineColor: themeTokensRef.current?.['aBlack12'],
        lineWidth: 1,
      },
      verticalSplitLineMoveable: true,
      verticalSplitLineHighlight: {
        lineColor: themeTokensRef.current?.Brand600,
        lineWidth: 2,
      },
    },
    grid: {
      backgroundColor: themeTokensRef.current?.bgBottom,
      weekendBackgroundColor: themeTokensRef.current?.N50,
      verticalBackgroundColor: [themeTokensRef.current?.bgBottom],
      verticalLine: {
        lineWidth: 1,
        lineColor: themeTokensRef.current?.['aBlack8'],
      },
      horizontalLine: undefined,
    },
    // headerRowHeight: 40,
    // rowHeight: 36,
    taskBar: {
      customLayout: args => {
        const isGroupRecord = isGroupRecordFn(args.taskRecord)
        let container: VTableGantt.VRender.Group

        if (!isGroupRecord) {
          container = renderDetailBar(args)
        } else {
          container = renderGroupBar(args)
        }

        return {
          // 自定义属性，允许内容溢出
          allowOverflow: true,
          rootContainer: container,
          renderDefault: false,
        }
      },
      scheduleCreatable: false,
      startDateField,
      endDateField,
      resizable: args => {
        return !isGroupRecordFn(args.taskRecord)
      },
      moveable: args => {
        return !isGroupRecordFn(args.taskRecord)
      },
      selectable: true,
      selectedBarStyle: {
        shadowBlur: 0,
        shadowColor: 'transparent',
        borderColor: 'transparent',
      },
      hoverBarStyle: {
        barOverlayColor: 'transparent',
      },
    },
    timelineHeader: getTimelineHeader(scaleType),
    markLine: {
      date: dayjs().format('YYYY-MM-DD'),
      position: 'middle',
      style: {
        lineColor: themeTokensRef.current?.R200,
        lineWidth: 1,
      },
    },
    scrollStyle: {
      visible: 'always',
      width: 6,
      scrollSliderCornerRadius: 4,
      scrollSliderColor: themeTokensRef.current?.['aBlack12'],
      scrollRailColor: 'transparent',
    },
    rowSeriesNumber: {
      width: 40,
      // title: '',
      // dragOrder: true,
      // 不展示行号
      format: () => {
        return ''
      },
      icon: args => {
        const { col, row } = args
        const record = ganttRef.current?.taskListTableInstance?.getRecordByRowCol(col, row)
        if (!record) return
        const isAddRow = record.taskId === AddMenuId || record.taskId === TaskTableRowTypeAdd
        const hasParentId = !!record.parentId
        const canDrag = (_.isEmpty(queryGroupBys) && !isAddRow && querySort?.fieldName === 'customize') || hasParentId
        return canDrag ? 'dragReorder' : undefined
      },
    },
  }

  const optionRef = useLatest(option)
  const onReadyRef = useLatest(onReady)

  const onTaskBarClickProxy = useMemoizedFn(args => {
    const record = args.record
    if (isGroupRecordFn(record)) {
      return
    }

    onTaskBarClick?.(args)
  })

  const handleScroll = useMemoizedFn(() => {
    // 获取可见行范围
    const visibleRowRange = ganttRef.current?.taskListTableInstance?.getBodyVisibleRowRange?.()
    if (!visibleRowRange) return

    // 获取可见行的数据
    if (visibleRowRange) {
      for (let rowIndex = visibleRowRange.rowStart; rowIndex <= visibleRowRange.rowEnd; rowIndex++) {
        // 使用 getRecordByRowCol 获取行数据
        // 第一个参数是列索引，通常使用0获取整行数据
        const rowData = ganttRef.current?.taskListTableInstance?.getRecordByRowCol(0, rowIndex)
        if (rowData) {
          getPermissionsByTaskId(rowData.taskId)
        }
      }
    }
  })

  const showListTable = useMemoizedFn(() => {
    setSidebarExpand(true)
    ganttRef.current!.taskTableWidth = 340
    ganttRef.current?._resize()
    ganttRef.current?._updateListTableSize(ganttRef.current.taskListTableInstance!)
    ganttRef.current?._syncPropsFromTable()
    ganttRef.current!.element.style.left = '340px'
    ganttRef.current!.verticalSplitResizeLine.classList.remove(s.list__hide)
  })

  const onToggleSidebar = useMemoizedFn(() => {
    if (ganttRef.current?.taskTableWidth) {
      setSidebarExpand(false)
      ganttRef.current!.taskTableWidth = 0
      ganttRef.current?._resize()
      ganttRef.current?._updateListTableSize(ganttRef.current.taskListTableInstance!)
      ganttRef.current?._syncPropsFromTable()
      ganttRef.current!.element.style.left = '0px'
      ganttRef.current!.verticalSplitResizeLine.classList.add(s.list__hide)
    } else {
      showListTable()
    }
  })

  const onChangeDateRange = useMemoizedFn(args => {
    // 获取开始时间和截止时间
    const { startDate, endDate, record } = args
    const startDateTimeStamp = new Date(startDate).getTime()
    const endDateTimeStamp = new Date(endDate).getTime()
    const params = {
      taskId: record?.taskId,
      startTime: startDateTimeStamp,
      deadline: endDateTimeStamp,
      // 因为拖拽的step是以天为单位,所有默认用1
      deadlineFormat: PPTimeFormat.olayDay,
      // 没有rule,默认传空
      rrule: '',
    }
    // 调用接口
    taskUpdateDeadline(params).then(() => {
      dispatch.gantt.getTodoList({
        page: 1,
      })
    })
  })

  useEffect(() => {
    ganttRef.current = new VTableGantt.Gantt(domRef.current!, optionRef.current)

    // TODO：产品要求外拆右上角按钮，临时方案先挂在到全局 gantt 实例上最简单，不需要额外的通信成本
    // @ts-ignore
    ganttRef.current.toggleSidebar = onToggleSidebar
    // @ts-ignore
    ganttRef.current.onSwipe = onSwipe
    // @ts-ignore
    ganttRef.current.onToggleView = onToggleView
    // @ts-ignore
    ganttRef.current.onBackToToday = onBackToToday

    onReadyRef.current?.(ganttRef.current)

    window.gantt = ganttRef.current

    ganttRef.current.on('click_task_bar', onTaskBarClickProxy)
    ganttRef.current?.taskListTableInstance?.on('scroll', handleScroll)
    ganttRef.current?.taskListTableInstance?.on('tree_hierarchy_state_change', handleScroll)
    ganttRef.current?.verticalSplitResizeLine.classList.add(s.resize__line)
    ganttRef.current?.on('change_date_range', onChangeDateRange)

    return () => {
      ganttRef.current?.off('click_task_bar', onTaskBarClickProxy)
      ganttRef.current?.taskListTableInstance?.off('scroll', handleScroll)
      ganttRef.current?.taskListTableInstance?.off('tree_hierarchy_state_change', handleScroll)
      ganttRef.current?.off('change_date_range', onChangeDateRange)
      ganttRef.current?.release()
      window.gantt = null
    }
  }, [])

  useEffect(() => {
    const resizeLine = ganttRef.current?.verticalSplitResizeLine
    const div = document.createElement('div')
    div.classList.add(s.toggle__container)

    div.style.left = sidebarExpand ? '-26px' : '0'

    const root = ReactDOM.createRoot(div)
    if (resizeLine) {
      root.render(
        <Tooltip arrowPointAtCenter title={sidebarExpand ? I18N.auto.collapseTaskList : I18N.auto.expandTaskList}>
          <div
            className={s.toggle}
            style={{
              cursor: 'pointer',
              pointerEvents: 'auto',
            }}
            onClick={onToggleSidebar}
            onMouseOver={e => {
              e.stopPropagation()
            }}
          >
            <img
              src={getVTableIcons('list-fold', themeTokensRef.current._name, 'expand')}
              className={s.toggleIcon}
              style={{
                transform: sidebarExpand ? 'rotate(180deg)' : 'rotate(0deg)',
              }}
            />
          </div>
        </Tooltip>,
      )
      resizeLine.appendChild(div)
    }
    return () => {
      root.unmount()
      resizeLine?.removeChild(div)
    }
  }, [sidebarExpand, theme])

  useEffect(() => {
    ganttRef.current?.setRecords(Array.isArray(records) ? records : [])
    handleScroll()
  }, [records, permissionsMap])

  useEffect(() => {
    showListTable()
    ganttRef.current?.updateOption(optionRef.current)
    // ganttRef.current?.scenegraph.updateSceneGraph();
    ganttRef.current?.scenegraph.refreshTaskBarsAndGrid()
    // ganttRef.current?._updateSize();
  }, [theme])

  const onBackToToday = () => {
    ganttRef.current?._scrollToMarkLine()
  }

  const onToggleView = useMemoizedFn((type: ScaleType) => {
    if (type === scaleType) {
      return
    }

    if (sidebarExpand && optionRef.current?.taskListTable) {
      optionRef.current.taskListTable.tableWidth = 340
    } else if (optionRef.current?.taskListTable) {
      optionRef.current.taskListTable.tableWidth = 0
    }

    ganttRef.current?.updateOption({
      ...optionRef.current,
      records: ganttRef.current.records,
      timelineHeader: getTimelineHeader(type),
    })
    ganttRef.current?.updateMarkLine([
      {
        date: dayjs().format('YYYY-MM-DD'),
        position: 'middle',
        style: {
          lineColor: 'rgb(255, 149, 155)',
          lineWidth: 1,
        },
      },
    ])

    /**
     * 注意：这段逻辑必须配合顶部 optionRef.current.taskListTable.tableWidth 的逻辑
     */
    if (!ganttRef.current?.taskTableWidth) {
      ganttRef.current!.taskTableWidth = 0
      // ganttRef.current?._resize();
      // ganttRef.current?._updateListTableSize(ganttRef.current.taskListTableInstance!);
      ganttRef.current!.element.style.left = '0px'
      // ganttRef.current!.verticalSplitResizeLine.style.display = 'none';
      ganttRef.current!.verticalSplitResizeLine.classList.add(s.list__hide)
    }

    ganttRef.current?._resize()
    ganttRef.current?._updateListTableSize(ganttRef.current.taskListTableInstance!)

    onScaleChange?.(type)
  })

  const onSwipe = (dir: 'prev' | 'next') => {
    const ganttWrapEl = domRef.current!.querySelector('.vtable-gantt') as HTMLDivElement

    if (ganttWrapEl) {
      const width = ganttWrapEl.offsetWidth
      ganttRef.current!.scrollLeft += (dir === 'prev' ? -1 : 1) * width
    }
  }

  // 排序方式、分组变更的时候,需重新更新一下
  useEffect(() => {
    ganttRef.current?.updateOption(optionRef.current)
  }, [querySort, queryGroupBys])

  return (
    <div className={s.ganttView}>
      <div id="gantt-view" className={s.view} ref={domRef}></div>
    </div>
  )
}

export default Gantt
