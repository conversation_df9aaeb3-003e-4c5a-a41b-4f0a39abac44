import { OperateAdd, OperateCloseS, OperateDown } from '@babylon/popo-icons';
import { FormInstance } from '@bedrock/components/lib/Form';
import { useSortable } from '@dnd-kit/sortable';

import { randomString } from '@/utils';
import { getCustomColor } from '@/utils/customfields';
import I18N from '@/utils/I18N';

import { Button, Form, Icon, Input } from '../basic';
import DnDList from '../dndkit/dnd-list';
import ColorPanelPopover from './colorPanel';
import { FieldOptionColor, FieldOptionColorSequence, SelectFieldOption } from './const';
import s from './selectOption.less';

type Props = {
  form: FormInstance;
  disabledKeys?: string[];
  onChange?: (value: any) => any;
};

export default function SelectOptionList({ form, onChange, disabledKeys = [] }: Props) {
  return (
    // 为了支持form.validateFields，只能基于form.list改装
    <Form.List
      name={'options'}
      initialValue={
        [
          { name: I18N.auto.option_2, color: 'pibg_blue', value: randomString(8) },
          { name: I18N.auto.option_3, color: 'pibg_red', value: randomString(8) },
        ] as SelectFieldOption[]
      }
    >
      {(fields, { add, remove }, { errors }) => {
        return (
          <Form.Item label={I18N.auto.option} className={s.label}>
            <DnDList<SelectFieldOption>
              wholeRowHandle={false}
              list={fields.map((field: any) => {
                return {
                  id: form.getFieldValue(['options', field.name, 'value']),
                  name: field.name,
                  irremovable: form.getFieldValue(['options', field.name, 'irremovable']),
                };
              })}
              renderItem={({ id, name, irremovable }) => {
                return (
                  <DndItem
                    id={id}
                    name={name}
                    onDelete={remove}
                    form={form}
                    onItemChange={() => {
                      const options = form.getFieldValue('options') as SelectFieldOption[];
                      onChange?.(options);
                    }}
                  ></DndItem>
                );
              }}
              onDragEnd={(e, list) => {
                let preList = form.getFieldValue('options') as SelectFieldOption[];

                let newList = list.map(({ id }) => {
                  return preList.find((opt) => opt.value == id);
                });
                onChange?.(newList);
              }}
            ></DnDList>
            <Button
              type="checked-neutral"
              icon={<OperateAdd></OperateAdd>}
              className={s.addBtn}
              disabled={fields.length >= 60}
              onClick={() => {
                console.log(
                  'FieldOptionColorSequence[fields.length % 8]',
                  FieldOptionColorSequence[fields.length % 8]
                );

                add({
                  name: I18N.template(I18N.auto.option_4, { val1: fields.length + 1 }),
                  // color: Object.keys(FieldOptionColor)[fields.length % 8],
                  color: FieldOptionColorSequence[fields.length % 9],
                  value: randomString(8),
                } as SelectFieldOption);
              }}
              size="small"
            >
              {I18N.auto.addConditions}
            </Button>
          </Form.Item>
        );
      }}
    </Form.List>
  );
}

function DndItem({
  id,
  name,
  form,
  onItemChange,
  onDelete,
}: {
  id: any;
  name: string;
  form: FormInstance;
  onItemChange: () => any;
  onDelete: (name: any) => any;
}) {
  const { attributes, listeners } = useSortable({
    id,
  });

  return (
    <>
      <Form.Item
        noStyle
        shouldUpdate={(preValues, nextValues) => {
          return preValues?.options !== nextValues?.options;
        }}
      >
        {() => {
          let option = form.getFieldValue(['options', name]) as SelectFieldOption;
          return (
            <Form.Item
              name={[name, 'name']}
              rules={[{ required: true, message: I18N.auto.thisItemCannotBe }]}
              className={s.formItem}
              showLabel={false}
            >
              <div className={s.option}>
                <div className={`${s.sortIconWrapper} flex-center`} {...attributes} {...listeners}>
                  <Icon name="icon-pc_plane_move" className={s.sortIcon}></Icon>
                </div>
                <ColorPanelPopover
                  value={option.color}
                  onChange={(color) => {
                    form.setFieldValue(['options', name, 'color'], color);
                    onItemChange?.();
                  }}
                >
                  <div
                    className={`${s.color__cube} flex-center`}
                    style={{
                      backgroundColor: getCustomColor(FieldOptionColor[option.color]),
                    }}
                  >
                    <OperateDown />
                  </div>
                </ColorPanelPopover>
                <Input
                  className={s.input}
                  placeholder=""
                  value={option.name}
                  size="xSmall"
                  onChange={(e) => {
                    form.setFieldValue(['options', name, 'name'], e.target.value);
                    onItemChange?.();
                  }}
                  block={false}
                  maxLength={100}
                />

                <OperateCloseS
                  className={s.icon}
                  onClick={() => {
                    onDelete(name);
                  }}
                />
              </div>
            </Form.Item>
          );
        }}
      </Form.Item>

      <Form.Item noStyle name="color"></Form.Item>
      <Form.Item noStyle name="value"></Form.Item>
    </>
  );
}
