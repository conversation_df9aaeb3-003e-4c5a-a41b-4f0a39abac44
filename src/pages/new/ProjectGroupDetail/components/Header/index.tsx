import { Button, CommonSearch, Icon, Modal, ToggleMenu, Tooltip } from '@/components/basic';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';
import { Data16Pojectline, OperateCollapse2, OperateComin } from '@babylon/popo-icons';
import { FC, lazy, Suspense, useEffect, useRef, useState } from 'react';
// import AddProjectModal from '../AddProjectModal';
const AddProjectModal = lazy(() => import('../AddProjectModal'));
import s from './index.less';
import { apiProjectGroupAddMembersPost } from '@/api';
import Head from '@/pages/new/project-list/head';
import { useDispatch } from 'react-redux';
import { Dispatch } from '@/models/store';
import { ProjectListMenuId } from '@/utils/const';

interface IHeaderProps {
  title: string;
  groupId: number;
  onSearchSuccess?: (v: ProjectInfo[]) => void;
  onAddProjectsSuccess?: () => Promise<any>;
  onCreateProject?: () => void;
}

const Header: FC<IHeaderProps> = ({
  title,
  groupId,
  onSearchSuccess,
  onAddProjectsSuccess,
  onCreateProject,
}) => {
  const dispatch = useDispatch<Dispatch>();
  const projectModalContentRef = useRef();
  const modalOperateRef = useRef<any>();

  const handleAddProjects = () => {
    modalOperateRef.current = Modal.open({
      className: s.add__modal,
      title: I18N.auto.ungroupedProject,
      content: (
        <Suspense>
          <AddProjectModal ref={projectModalContentRef} />
        </Suspense>
      ),
      width: 480,
      scrolled: false,
      onOk: () => {
        return apiProjectGroupAddMembersPost({
          groupId: groupId,
          projectIds: projectModalContentRef.current?.getSelectedIds(),
        }).then(() => {
          onAddProjectsSuccess?.();
          dispatch.project.getPingProjectList({});
          modalOperateRef.current?.destroy();
        });
      },
    });
  };

  const handleToAllProjects = () => {
    dispatch.viewSetting.openNavigator({
      navigatorId: ProjectListMenuId,
    });
  };

  const handleRenderTitle = () => {
    return (
      <div className={`${s.title} flex-y-center`}>
        <span onClick={handleToAllProjects} className={`${s.secondary} ${s.all__project}`}>
          {I18N.auto.allProjects}
        </span>
        <span className={s.secondary}>
          <OperateComin className="fs-16" />
        </span>
        <Tooltip title={title} onlyEllipsis>
          <span className={`${s.primary} ellipsis`}>{title}</span>
        </Tooltip>
      </div>
    );
  };

  const handleRenderAction = () => {
    return (
      <div className={`${s.action} flex-y-center`}>
        <Button
          onClick={handleAddProjects}
          type="checked-neutral"
          icon={<Icon name="icon-sys_add" className="mr-4" />}
        >
          {I18N.auto.addProject}
        </Button>
        <Button
          type="primary"
          className="origin__primary__btn mr-12"
          icon={<Data16Pojectline className="fs-16" />}
          onClick={onCreateProject}
        >
          {I18N.auto.newProject}
        </Button>
      </div>
    );
  };

  useEffect(() => {
    const destroy = () => {
      modalOperateRef.current?.destroy();
    };
    return destroy;
  }, []);

  return (
    <Head
      renderTitle={handleRenderTitle}
      renderSearch={() => null}
      renderAction={handleRenderAction}
    />
  );
};

export default Header;
