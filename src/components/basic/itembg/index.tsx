import classNames from 'classnames';
import { PropsWithChildren } from 'react';

import s from './index.less';

interface PlaceholderProps {
  className?: string;
  showbg?: boolean;
  style?: React.CSSProperties;
  disabled?: boolean;
}

const Itembg: React.FC<PropsWithChildren<PlaceholderProps>> = (props) => {
  const { showbg, children, className, disabled, ...resetProps } = props;
  return (
    <div
      {...resetProps}
      className={classNames(
        'todo-item-bg',
        s.itemBg,
        { [s.disabled]: disabled },
        { [s.showbg]: showbg },
        className
      )}
    >
      {children}
    </div>
  );
};

export default Itembg;
