// import * as Sentry from '@sentry/browser';
import React, { PropsWithChildren, useEffect } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
interface Props {}

const BoundaryFallback: React.FC = (props) => {
  useEffect(() => {
    //Sentry.captureMessage('ListErrorBoundary');
    // Sentry.captureException(new Error('ListBoundaryError'));
  }, []);
  return <div></div>;
};

const ListErrorBoundary: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children } = props;
  return <ErrorBoundary fallback={<BoundaryFallback></BoundaryFallback>}>{children}</ErrorBoundary>;
};

export default ListErrorBoundary;
