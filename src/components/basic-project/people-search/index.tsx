import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { PropsWithChildren, useContext, useEffect, useState } from 'react';

import { apiUserSearchMemberPost } from '@/api';
import { CommonSearch, Divider, Dropdown, Icon, IconBtn } from '@/components/basic';
import SearchList from '@/components/basic/people-picker/components/search-list';
import { UserInfo } from '@/types';
import { bridgeGetRecentContactsList } from '@/utils/bridge';
import { NewBridgeUserType } from '@/utils/const';
import I18N from '@/utils/I18N';

import { PeopleSearchContext } from './context';
import s from './index.less';
export type Props = {
  className?: string;
  overlayClassName?: string;
  value: UserInfo[];
  onChange: (users: UserInfo[]) => void;
  /**
   * 默认值 不能被选的
   */
  uncheckableDefaultItems?: UserInfo[];
  /**
   * 禁用的用户
   */
  disabledUsers?: UserInfo[]; // 目前仅对端上的选人组件生效
  chooseIMContactsPlaceholder?: string;
  onOpenChange?: (visible: boolean) => void;
};

const PeoplePicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    value,
    onChange,
    className,
    overlayClassName = '',
    uncheckableDefaultItems,
    disabledUsers,
    chooseIMContactsPlaceholder,
    onOpenChange,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>();
  const [searchList, setSearchList] = useState<UserInfo[]>([]);
  const [recentContactsList, setRecentContactsList] = useState<UserInfo[]>([]);
  // 最近联系人
  const [loading, setLoading] = useState<boolean>(false);

  const { extraItemTag } = useContext(PeopleSearchContext);

  // 调用im组件批量选择
  const openIMContacts = () => {
    const checkableDefaultItems = (uncheckableDefaultItems || value).map((item) => ({
      id: item.uid!,
      type: 1,
    }));

    pp.chooseIMContacts({
      groupCollapse: false,
      uncheckableDefaultItems: checkableDefaultItems,
      title: chooseIMContactsPlaceholder || I18N.auto.chooseGroupChatOr,
      //@ts-ignore
      disabledItems: disabledUsers?.map((item) => ({
        id: item.uid!,
        type: item.sessionType!,
        reason: '',
      })),
      maxCount: 1200,
    })
      .then((res) => {
        if (Array.isArray(res.data)) {
          const obj: Record<string, UserInfo> = {};

          console.log('res.data', res.data, value);

          res.data.forEach((item) => {
            obj[item.id] = { ...item, uid: item.id, sessionType: item.type };
          });

          // 过滤掉已选的用户
          value.forEach((item) => {
            if (obj[item.uid!]) {
              delete obj[item.uid!];
            }
          });

          const _list = Object.values(obj);
          onChange([..._list, ...value]);
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        });
      });
  };

  /**
   * 搜索接口
   * @param v
   */
  const searchPeople = (v: string) => {
    setLoading(true);
    setVisible(true);
    apiUserSearchMemberPost({ keyword: v })
      .then((res) => {
        // const _res = res.map((item) => ({ ...item, sessionType: EnumSessionType.p2p }));
        const _res: UserInfo[] = res
          //.filter((item) => !excludeUsers?.find((v) => v.uid === item.memberId))
          .map((item) => ({
            uid: item.memberId,
            avatarUrl: item.pic,
            sessionType: item.memberType,
            name: item.name,
          }));
        setSearchList(_res);
        if (!v && !_res.length) {
          setSearchList(recentContactsList);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getPopupContaineretRecentContactsList = async () => {
    // 打开面板用户不存在时获取最近联系人
    const recentContactsList = await bridgeGetRecentContactsList({
      maxCount: 30,
      //最近联系人群sessionType是2
      sessionTypes: [NewBridgeUserType.p2p, NewBridgeUserType.team],
    });
    setSearchList(recentContactsList);
    setRecentContactsList(recentContactsList);
  };

  const onSearchChange = (userList: UserInfo[]) => {
    onChange([...userList]);
    setVisible?.(false);
    setSearchValue('');
    setSearchList(recentContactsList);
    setRecentContactsList(recentContactsList);
    handleOpenChange(false);
  };

  const handleInputClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    setVisible(true);
    handleOpenChange(true);
  };

  const handleOpenChange = (open: boolean) => {
    setTimeout(() => {
      onOpenChange?.(open);
    }, 160);
  };

  useEffect(() => {
    if (visible) {
      if (!searchValue) {
        getPopupContaineretRecentContactsList();
      } else {
        searchPeople(searchValue);
      }
    }
  }, [visible]);

  return (
    <Dropdown
      className={classNames(s.peoplePickerDropdown, className)}
      title={
        <div className={s.addPeople}>
          <CommonSearch
            id="user-search"
            // ref={commonSearchRef}
            className={s.input}
            placeholder={I18N.auto.searchGroupChatOr}
            onSearch={(v) => {
              searchPeople(v);
            }}
            value={searchValue}
            onChange={(v) => {
              setSearchValue(v);
            }}
            fill={false}
            round={false}
            debounceTime={350}
            onClick={handleInputClick}
            suffix={
              <div
                className="flex-y-center"
                onClick={(e) => {
                  setVisible(false);
                  handleOpenChange(false);
                  e.stopPropagation();
                  openIMContacts();
                }}
              >
                <Divider className={s.line} />
                <Icon fontSize={16} name="icon-kit_user_add" />
              </div>
            }
          />
        </div>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      open={visible}
      onOpenChange={(open) => {
        setVisible?.(open);
        handleOpenChange(open);
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      //@ts-ignore
      getPopupContainer={(dom) => dom.parentNode}
      overlay={
        <div
          className={classNames('com-dropdown-select', s.panel, s.searchingPanel, overlayClassName)}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <div className={s.container}>
            <SearchList
              loading={loading}
              list={searchList}
              onChange={onSearchChange}
              className={s.list}
              multiple={true}
              extraItemTag={extraItemTag}
              searchNoDataTip={
                !loading && !searchList.length && searchValue ? I18N.auto.noSearchResultsFound : ''
              }
            />
          </div>
        </div>
      }
    />
  );
};

export default PeoplePicker;
