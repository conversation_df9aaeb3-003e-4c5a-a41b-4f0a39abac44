import React from 'react';

import SortBtn from '@/components/basic-task/sort-btn';
import { OrderOption } from '@/types';
import { Order, OrderCustom } from '@/utils/const';
import { SortItem } from '@/utils/fields';
import I18N from '@/utils/I18N';

import Container from '../../overlay-container';
import s from './index.less';

export type Props = {
  className?: string;
  order: Order;
  orderBy?: string;
  onChange?: (v: OrderOption) => void;
  list: SortItem[];
};

const Sort: React.FC<Props> = (props) => {
  const { order, orderBy, onChange, list } = props;
  return (
    <Container title={I18N.auto.sort}>
      {list.map((item, index) => {
        return (
          <SortBtn
            key={index}
            icon={item.icon}
            title={item.name}
            sortIndex={item.value}
            order={order}
            onChange={(v) => {
              if (v.orderby === orderBy && v.orderby === OrderCustom) {
                return;
              }
              onChange?.(v);
            }}
            active={orderBy === item.value}
          ></SortBtn>
        );
      })}
    </Container>
  );
};

export default Sort;
