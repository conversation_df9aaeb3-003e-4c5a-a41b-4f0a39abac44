import { apiV3ProjectFetchGroupsPost } from '@/api';
import { Select } from '@/components/basic';
import I18N from '@/utils/I18N';
import { useRequest } from 'ahooks';
import { FC } from 'react';
import s from './index.less';
import { OperateExpand } from '@babylon/popo-icons';

interface IAddToGroupProps {
  groupId?: number;
  onChange?: (v: number) => void;
}

const AddToGroup: FC<IAddToGroupProps> = ({ groupId, onChange }) => {
  const { data: groupData } = useRequest(() => {
    return apiV3ProjectFetchGroupsPost({});
  });

  const renderLabel = (item: any) => {
    return (
      <div className="flex-y-center">
        <OperateExpand className="fs-16 mr-8" /> {item.groupName}
      </div>
    );
  };

  return (
    <div className={s.container}>
      <span className={s.label}>{I18N.auto.addProjectGroup}</span>
      <Select
        size="large"
        className={s.select}
        value={groupId}
        placeholder={I18N.auto.selectProjectGroup}
        options={groupData?.projectGroups || []}
        renderLabel={renderLabel}
        onChange={onChange}
        fieldNames={{ value: 'groupId', name: 'groupName' }}
      />
    </div>
  );
};
export default AddToGroup;
