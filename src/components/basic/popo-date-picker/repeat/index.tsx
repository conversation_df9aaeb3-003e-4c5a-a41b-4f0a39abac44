import { useEffect } from 'react';

import { Select } from '@/components/basic';

import { repeatOptions, RepeatType } from '../utils';
import s from './index.less';
export type Props = {
  value?: number;
  onChange?: (v: number) => void;
  className?: string;
};

export default function RepeatSelect(props: Props) {
  const { value = RepeatType.WorkDay, onChange: propsOnChange, className } = props;
  const onChange = (value: unknown) => {
    propsOnChange?.(value as number);
  };
  useEffect(() => {
    //打开重复选择 给父级一个回调默认值
    if (value) {
      propsOnChange?.(value as number);
    }
  }, []);
  return (
    <div className={s.repeat}>
      <Select
        className={className}
        value={value}
        onChange={onChange}
        // onClear={() => {}}
        fill
        options={repeatOptions}
        triggerProps={{ popupPlacement: 'topLeft' }}
        //@ts-ignore
        getPopupContainer={(dom) => dom.parentNode}
      />
    </div>
  );
}
