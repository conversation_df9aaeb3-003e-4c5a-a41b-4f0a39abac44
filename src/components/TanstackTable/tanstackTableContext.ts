import { createContext, useContext } from 'react';
import { Order } from '@/utils/const';
import { UniqueIdentifier } from '@dnd-kit/core';

// 定义表格上下文类型
interface TanstackTableContextType {
  onSortChange?: (dataIndex: string, sortOrder: Order) => void;
  sortOrder?: { dataIndex?: string; order?: Order; orderBy?: string };
  dataIds?: UniqueIdentifier[];
  columnOrder?: string[];
  rowsDraggable?: boolean;
  total?: number;
  setDataIds?: (dataIds: number[]) => void;
  containerWidth?: number;
  onRowClick?: (row: any) => void;
  onRowMouseDown?: (row: any) => void;
  horizonScrolled?: boolean;
}

// 创建表格上下文
export const TanstackTableContext = createContext<TanstackTableContextType>({});

// 创建表格上下文Hook
export const useTanstackTableContext = () => {
  const context = useContext(TanstackTableContext);
  if (!context) {
    throw new Error('useTanstackTable must be used within a TanstackTableProvider');
  }
  return context;
};
