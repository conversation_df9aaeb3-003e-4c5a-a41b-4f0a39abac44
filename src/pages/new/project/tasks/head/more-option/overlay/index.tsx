import { OperateNoti, OperateRemovepj } from '@babylon/popo-icons';
import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { lazy, Suspense, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiProjectCreateTeamPost, apiProjectRelateTeamPost } from '@/api';
import { Icon, Menu, Message, Modal } from '@/components/basic';
import { DeleteProject, ExitProject, InvitedMembers } from '@/components/basic-project/more-option';
import { Dispatch, RootState } from '@/models/store';
import { Permission, ProjectInfo } from '@/types';
import { TaskNavigatorType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import { useOpenModal } from '@/hooks/useOpenModal';

const TeamNotifitycation = lazy(() => import('./components/TeamNotification'));
// import TeamNotifitycation from './components/TeamNotification';

export type Props = {
  className?: string;
  projectInfo?: ProjectInfo;
  permissions?: Permission[];
};

const Overlay: React.FC<Props> = (props) => {
  const { className, permissions } = props;
  const dispatch = useDispatch<Dispatch>();
  const modalRef = useRef<any>(null);

  const { language, projectInfo, corlorMode } = useSelector((state: RootState) => ({
    language: state.user.language,
    projectInfo: state.project.projectInfo,
    corlorMode: state.user?.corlorMode,
  }));

  const createTeam = () => {
    createTeamByProject({ projectId: projectInfo?.projectId!, dispatch });
  };

  const linkedTeam = () => {
    linkTeam({ projectId: projectInfo?.projectId!, dispatch });
  };
  const handleOpenSession = () => {
    openSession({ teamId: projectInfo?.tid });
  };

  const createSchedule = () => {
    console.log('新建日程');
    pp.openCalendarCreatePage({
      title: projectInfo?.name,
      participants: projectInfo?.members?.map((item) => item.uid!) || [],
    });
  };

  const { openModal: openGroupSetting } = useOpenModal();

  const handleSettingGroup = () => {
    modalRef.current = openGroupSetting({
      content: (
        <Suspense>
          <TeamNotifitycation
            dispatch={dispatch}
            projectInfo={projectInfo}
            language={language}
            colorMode={corlorMode}
          />
        </Suspense>
      ),
      width: 640,
      title: I18N.auto.groupNotification,
      footer: null,
      className: s.team__config,
      params: { createTeam, linkedTeam, tid: projectInfo?.tid },
      zIndex: 1000,
      maskClosable: false,
      onClose: () => {
        modalRef.current = null;
      },
      onCancel: () => {
        modalRef.current = null;
      },
    });
  };

  const memoPermissions = useMemo(() => {
    const [
      CAN_CREATE_IM_TEAM,
      CAN_ENTER_IM_TEAM,
      CAN_CREATE_SCHEDULE,
      CAN_ADD_MANAGER,
      CAN_ADD_EDITOR,
      CAN_ADD_VIEWER,
      CAN_EXIT,
      CAN_DELETE,
    ] = validatesPermission({
      permissions: permissions,
      key: [
        ProjectPermissionEnum.CAN_CREATE_IM_TEAM,
        ProjectPermissionEnum.CAN_ENTER_IM_TEAM,
        ProjectPermissionEnum.CAN_CREATE_SCHEDULE,
        ProjectPermissionEnum.CAN_ADD_MANAGER,
        ProjectPermissionEnum.CAN_ADD_EDITOR,
        ProjectPermissionEnum.CAN_ADD_VIEWER,
        ProjectPermissionEnum.CAN_EXIT,
        ProjectPermissionEnum.CAN_DELETE,
      ],
    }) as boolean[];

    const CAN_EDIT_MEMBER = CAN_ADD_MANAGER || CAN_ADD_EDITOR || CAN_ADD_VIEWER;
    const _CAN_ENTER_IM_TEAM = CAN_ENTER_IM_TEAM && projectInfo?.tid;
    return {
      CAN_CREATE_IM_TEAM: CAN_CREATE_IM_TEAM,
      CAN_ENTER_IM_TEAM,
      CAN_CREATE_SCHEDULE,
      CAN_ADD_MANAGER,
      CAN_ADD_EDITOR,
      CAN_ADD_VIEWER,
      CAN_EDIT_MEMBER: CAN_EDIT_MEMBER,
      CAN_EXIT,
      CAN_DELETE,
      hiden_CAN_EDIT_MEMBER_divider: !(
        CAN_CREATE_IM_TEAM ||
        _CAN_ENTER_IM_TEAM ||
        CAN_CREATE_SCHEDULE
      ),

      hiden_CAN_DELETE_divider:
        !CAN_DELETE ||
        (CAN_DELETE &&
          !(
            CAN_CREATE_IM_TEAM ||
            _CAN_ENTER_IM_TEAM ||
            CAN_CREATE_SCHEDULE ||
            CAN_EDIT_MEMBER ||
            CAN_EXIT
          )),
    };
  }, [permissions, projectInfo]);

  return (
    <Menu className={s.overlay}>
      {/* {memoPermissions.CAN_CREATE_IM_TEAM && !projectInfo?.tid ? (
        <>
          <Menu.Item key="A" className={s.item} onClick={createTeam}>
            <Icon name="icon-details_nav_addcart" className="mr-7"></Icon>
            {I18N.auto.createANewGroupChat}
          </Menu.Item>
          <Menu.Item key="A1" className={s.item} onClick={linkedTeam}>
            <Icon name="icon-details_nav_addcart" className="mr-7"></Icon>
            {I18N.auto.relatedGroupChat}
          </Menu.Item>
        </>
      ) : null}
      {memoPermissions.CAN_ENTER_IM_TEAM && projectInfo?.tid ? (
        <Menu.Item key="F" className={s.item} onClick={handleOpenSession}>
          <Icon name="icon-details_nav_incart" className="mr-7"></Icon>
          {I18N.auto.enterGroupChat}
        </Menu.Item>
      ) : null}
      {memoPermissions.CAN_CREATE_IM_TEAM && (
        <Menu.Item key="G" className={s.item} onClick={handleSettingGroup}>
          <OperateNoti className="fs-16 mr-7" />
          {I18N.auto.groupNotification}
        </Menu.Item>
      )} */}

      {/* NOTE: 暂无该功能 */}
      {/* {memoPermissions.CAN_CREATE_SCHEDULE ? (
        <Menu.Item key="B" className={s.item} onClick={createSchedule}>
          <Icon name="icon-details_nav_newcalendar" className="mr-7"></Icon>
          {I18N.auto.newSchedule}
        </Menu.Item>
      ) : null} */}

      {/* {memoPermissions.hiden_CAN_EDIT_MEMBER_divider ? null : (
        <Menu.Divider className={s.divider} />
      )}

      {memoPermissions.CAN_EDIT_MEMBER ? (
        <Menu.Item key="C" className={s.item}>
          <InvitedMembers projectId={projectInfo?.projectId}>
            <div>
              <Icon name="icon-kit_user_add" className="mr-7"></Icon>
              {I18N.auto.inviteMembers}
            </div>
          </InvitedMembers>
        </Menu.Item>
      ) : null} */}

      {memoPermissions.CAN_EXIT ? (
        <Menu.Item key="D" className={classNames(s.item, s.all)}>
          {/* 多个组件放在Menu.Item外会有警告*/}
          <ExitProject
            projectInfo={projectInfo}
            onExitSuccess={() => {
              dispatch.project.getPingProjectList();
              dispatch.viewSetting.openNavigator({
                navigatorId: TaskNavigatorType.assignToMe,
              });
            }}
          >
            <div className={s.allBtn}>
              <Icon name="icon-details_nav_out" className="mr-7"></Icon>
              {I18N.auto.exitTheProject}
            </div>
          </ExitProject>
        </Menu.Item>
      ) : null}
      {/* {memoPermissions.hiden_CAN_DELETE_divider ? null : <Menu.Divider className={s.divider} />} */}
      {memoPermissions.CAN_DELETE ? (
        <Menu.Item key="E" className={classNames(s.item, s.all)}>
          {/* 多个组件放在Menu.Item外会有警告*/}
          <DeleteProject
            projectInfo={projectInfo}
            onDeleteSuccess={() => {
              dispatch.project.getPingProjectList();
              dispatch.viewSetting.openNavigator({
                navigatorId: TaskNavigatorType.assignToMe,
              });
            }}
          >
            <div className={classNames(s.allBtn, s.delete)}>
              <OperateRemovepj className="mr-7 fs-16" />
              {I18N.auto.dissolveTheProject}
            </div>
          </DeleteProject>
        </Menu.Item>
      ) : null}
    </Menu>
  );
};

interface ITeamProps {
  projectId: number;
  dispatch: Dispatch;
}

/**
 * 创建群聊
 * @param param0
 * @returns
 */
export const createTeamByProject = ({ projectId, dispatch }: ITeamProps) => {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      width: 400,
      title: I18N.auto.createAGroupChat,
      content: I18N.auto.allWithinTheProject,
      centered: true,
      cancelText: I18N.auto.cancel,
      okText: I18N.auto.establish,
      zIndex: 1100,
      className: s.confirm,
      onOk: () => {
        apiProjectCreateTeamPost({
          projectId: projectId,
        })
          .then((res) => {
            if (res.isSuccess) {
              dispatch.project.getProject({
                id: String(String(projectId)),
                hideLoading: true,
              });
              if (res.tid) {
                resolve(res.tid);
                pp.openMessageSession({
                  id: res.tid,
                  type: 2, //此桥 群的type为2
                });
              }
            } else {
              if (res.errorMsg) {
                Message.error(res.errorMsg);
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
      },
    });
  });
};

/**
 * 关联群
 * @param param0
 * @returns
 */
export const linkTeam = ({ projectId, dispatch }: ITeamProps) => {
  return new Promise((resolve, reject) => {
    pp.chooseIMContacts({
      groupCollapse: false,
      uncheckableDefaultItems: [],
      organizationSupportSelected: true,
      title: I18N.auto.relatedGroupChat,
      //maxCount: 1,
      multiSelectStatus: 1,
    })
      .then((res) => {
        if (Array.isArray(res.data) && res.data.length === 1) {
          const [team] = res.data;
          if (team.type === 3) {
            apiProjectRelateTeamPost(
              {
                projectId: projectId,
                teamId: team.id,
              },
              { errorSilent: true }
            )
              .then(() => {
                Message.text(I18N.auto.successfullyAssociated);
                //更新项目详情
                dispatch.project.getProject({
                  id: String(projectId),
                  hideLoading: true,
                });
                resolve(team.id);
              })
              .catch((error) => {
                console.log('apiProjectRelateTeamPost error', error);
              });
          } else {
            Message.text(I18N.auto.onlySupportsAssociation);
          }
        }
      })
      .catch(() => {
        pp.showToast({
          title: I18N.auto.theCurrentVersionDoesNot,
          iconType: 0,
        });
      });
  });
};

/**
 * 打开群聊
 * @param teamId 群id
 * @returns
 */
export const openSession = ({ teamId }: { teamId?: string }) => {
  console.log('进入群聊', teamId);
  if (teamId) {
    pp.openMessageSession({
      id: teamId,
      type: 2, //此桥 群的type为2
    });
  }
};

export default Overlay;
