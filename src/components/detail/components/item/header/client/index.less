.head {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  border-bottom: 1px solid var(--aBlack6);
}
.clientHead {
  display: flex;
  padding: 16px 24px 0 24px;
  .back {
    cursor: pointer;
    .backIcon {
      transform: rotate(180deg);
    }
  }

  &.hidden {
    display: none;
  }
}
.todoHead {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 24px;
  .headLeft {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }
}
.back {
  color: var(--TextPrimary);
  .backIcon {
    transform: rotate(90deg);
    margin-right: 4px;
  }
}
.deleteTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--TextPrimary);
}
.confirm {
  :global {
    .rock-btn-primary {
      background-color: var(--R600) !important;
      &:hover {
        background-color: var(--R500) !important;
      }
    }
  }
}
.icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  line-height: 24px;
  // margin-left: 10px;
  text-align: center;
  border-radius: 6px;
  size: 24px;
  color: var(--IconPrimary);
  cursor: pointer;
  &:hover {
    background-color: var(--aBlack6);
  }
}
