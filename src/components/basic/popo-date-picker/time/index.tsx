import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';

import { Input, Select } from '@/components/basic';
import {
  getHourMilliseconds,
  getHourStr,
  getTimePeriodOptions,
} from '@/components/basic/popo-date-picker/utils';

import s from './index.less';

export type Props = {
  value?: number;
  onChange?: (v: number) => void;
  className?: string;
  placeholder?: string;
};

export default function RepeatSelect(props: Props) {
  const { value: propValue, onChange: propOnChange, className, placeholder } = props;
  const [value, setValue] = useState<string>();
  const [visible, setVisible] = useState<boolean>(false);
  const [timePeriod] = useState(getTimePeriodOptions());
  const ref = useRef<any>();
  const changeValue = (v: string) => {
    const milliseconds = getHourMilliseconds(v);
    propOnChange?.(milliseconds || 0);
  };
  const resetValue = () => {
    if (propValue && propValue >= 0) {
      const str = getHourStr(propValue);
      setValue(str);
    } else {
      setValue(undefined);
    }
  };

  useEffect(() => {
    resetValue();
  }, [propValue]);

  const onInputSelectValue: React.FocusEventHandler<HTMLInputElement> = (e) => {
    e.target.blur();
    const _v = e.target.value;
    if (_v) {
      const str = e.target.value.replace(/[^0-9]/gi, '');
      if (str) {
        const numV = e.target.value.replace(/[^0-9]/gi, '').padStart(4, '0');
        const hour = numV.slice(0, 2);
        const min = numV.slice(2, 4);
        if (Number(hour) < 24 && Number(min) < 60) {
          const v = `${hour}:${min}`;
          setValue(v);
          changeValue(v);
          setVisible(false);
          return;
        }
      }
      // 其他无效的情况都重置到之前的值
      resetValue();
    } else {
      const v = '00:00';
      setValue(v);
      changeValue(v);
    }
    setVisible(false);
  };

  return (
    <div>
      <Input
        ref={ref}
        id="todoTimeInput"
        className={classNames(s.timeInput, className)}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
        }}
        placeholder={placeholder}
        onBlur={onInputSelectValue}
        onPressEnter={onInputSelectValue}
        onFocus={() => {
          setVisible(true);
        }}
        onClick={() => {
          ref?.current?.input?.setSelectionRange(0, value?.length);
        }}
      ></Input>
      <Select
        //selectedType="check-icon"
        multipleCheckedType="check-icon"
        value={value}
        onChange={(v: string) => {
          setValue(v);
          //会触发Input组件的onBlur时间, 不重复执行changeValue
          //changeValue(v);
        }}
        arrow={false}
        className={s.timeSelect}
        options={timePeriod}
        visible={visible}
        dropdownClassName={s.timeSelectDropDown}
        //@ts-ignore
        getPopupContainer={(dom) => dom.parentNode.parentNode}
      ></Select>
    </div>
  );
}
