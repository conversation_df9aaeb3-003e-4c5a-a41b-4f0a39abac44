import classNames from 'classnames';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import { apiProjectUpdateTaskPost } from '@/api';
import { IconBtn, Itembg, Message } from '@/components/basic';
import { ProjectPicker } from '@/components/basic-project';
import { Dispatch, RootState } from '@/models/store';
import { ProjectInfo } from '@/types';
import { ViewType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { ToolInfo } from '@babylon/popo-icons';

export type Props = {
  className?: string;
  value?: ProjectInfo;
  onChange?: (v: ProjectInfo) => void;
  disabled?: boolean;
  taskId: number;
};

const ProjectItem: React.FC<Props> = (props) => {
  const { taskId, value, disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { currentViewTab } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const dispatch = useDispatch<Dispatch>();
  const change = (v: ProjectInfo) => {
    apiProjectUpdateTaskPost({
      projectId: v.projectId ? v.projectId : undefined,
      taskId: taskId,
    }).finally(async () => {
      // 重新请求详情数据
      // dispatch.detail
      //   .getTodoDetail(taskId)
      //   .then((res) => {
      //     if (v.projectId) {
      //       dispatch.detail.getProjectFieldList(v.projectId);
      //     } else {
      //       dispatch.detail.setData({
      //         customFields: [],
      //       });
      //     }
      //     dispatch.viewSetting.refreshDataByDataChange({
      //       refreshList: true,
      //       refreshCount: false,
      //       detail: res,
      //     });
      //   })
      //   .catch(() => {
      //     //判断自己是否还有权限
      //     dispatch.detail.setVisibleDetail(false);
      //   });
      // if (currentViewTab.type === ViewType.list) {
      dispatch.viewSetting.updateItemByDetail({ taskId: taskId });
      // }
      // dispatch.viewSetting.refreshDataByDataChange({
      //   refreshList: true,
      //   refreshCount: false,
      // });
    });
  };

  const handleItemClick = () => {
    if (disabled) {
      Message.warn({
        content: I18N.auto.editingNotAllowed,
        icon: <ToolInfo style={{ color: 'var(--absWhite)' }} className="fs-18" />,
      });
    }
  };

  return (
    <Itembg
      showbg={true}
      className={classNames(s.projectWrapper, {})}
      disabled={disabled}
      onClick={handleItemClick}
    >
      <div className={s.itemProject}>
        <ProjectPicker
          onChange={change}
          value={value}
          placeholder={disabled ? I18N.auto.nothing : I18N.auto.thereIsCurrentlyNoBelongingItem}
          visible={visible}
          onVisible={setVisible}
          disabled={disabled}
          showArrow
        ></ProjectPicker>
      </div>
      {value?.projectId && !disabled ? (
        <IconBtn
          iconClassName={s.clear}
          fontSize={16}
          title={I18N.auto.delete}
          iconName="icon-sys_close"
          onClick={() => {
            change({});
          }}
        ></IconBtn>
      ) : null}
    </Itembg>
  );
};

export default ProjectItem;
