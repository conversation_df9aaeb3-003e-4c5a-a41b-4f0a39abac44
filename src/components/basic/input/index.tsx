import './index.less';

import { Input as BaseInput } from '@bedrock/components';
import { InputProps as BaseInputProps } from '@bedrock/components/lib/Input';
import { TextAreaProps as BaseTextAreaProps } from '@bedrock/components/lib/Input/TextArea';
import classNames from 'classnames';
import React, { useRef } from 'react';
import { forwardRef } from 'react';

export const enum Variant {
  filled = 'filled',
  borderless = 'borderless',
}
export interface InputProps extends BaseInputProps {
  variant?: Variant;
}

export interface TextAreaProps extends BaseTextAreaProps {
  variant?: Variant;
}

export const Input = React.forwardRef<
  {
    focus: () => void;
  },
  InputProps
>((props, refs) => {
  const { variant, className } = props;
  return (
    <BaseInput
      {...props}
      ref={refs}
      className={classNames(
        { ['task-rock-input-borderless']: variant === Variant.borderless },
        className
      )}
    ></BaseInput>
  );
});

export const CompositionInput = ({ onPressEnter, ...props }: InputProps) => {
  const compositionState = useRef(false);

  const handlePressEnter = (e) => {
    e.stopPropagation();
    if (!compositionState.current) {
      onPressEnter?.(e);
    }
  };

  return (
    <Input
      onCompositionStart={(e) => {
        compositionState.current = true;
      }}
      onCompositionEnd={(e) => {
        setTimeout(() => {
          compositionState.current = false;
        }, 32);
      }}
      onPressEnter={handlePressEnter}
      {...props}
    />
  );
};

export const TextArea = React.forwardRef<
  {
    focus: () => void;
  },
  TextAreaProps
>((props, refs) => {
  const { variant, className } = props;
  return (
    <BaseInput.TextArea
      {...props}
      ref={refs}
      className={classNames(
        { ['task-rock-input-borderless']: variant === Variant.borderless },
        className
      )}
    ></BaseInput.TextArea>
  );
});

export const CompositionTextArea = forwardRef(({ onKeyDown, ...props }: TextAreaProps, refs) => {
  const compositionState = useRef(false);

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.stopPropagation();
      if (!compositionState.current) {
        onKeyDown?.(e);
      }
    } else {
      onKeyDown?.(e);
    }
  };

  return (
    <TextArea
      onCompositionStart={(e) => {
        compositionState.current = true;
      }}
      onCompositionEnd={(e) => {
        setTimeout(() => {
          compositionState.current = false;
        }, 32);
      }}
      onKeyDown={handleKeyDown}
      ref={refs}
      {...props}
    />
  );
});
