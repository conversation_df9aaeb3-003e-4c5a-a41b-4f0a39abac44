.chatRecordItem {
  max-height: 104px;
  border-radius: 4px;
  padding: 8px 6px 8px 10px;
  margin-bottom: 8px;
  position: relative;
  background-color: var(--aBlack4);
  .title {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    padding-right: 20px;
    color: var(--TextPrimary-strong);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .close {
    position: absolute;
    right: 15px;
    cursor: pointer;
    color: var(--IconBlack);
    font-size: 16px;
    top: 4px;
    .delete {
      margin-top: -4px;
      margin-right: -8px;
      width: 20px;
      height: 20px;
      &:hover {
        background-color: var(--aBlack10);
      }
    }
  }
  .chat {
    display: flex;
    flex-direction: column;
    .message {
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      color: var(--TextTertiary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
