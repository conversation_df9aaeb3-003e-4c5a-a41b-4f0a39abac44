import React, { forwardRef, useImperativeHandle, useState, useContext } from 'react';

import { PeoplePicker, Placeholder, RenderPeoples, Tooltip } from '@/components/basic';
import { UserInfo } from '@/types';
import { OneAndMoreType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { PeoplePickerContext } from '../context';
import { apiTodoParticipantListGet } from '@/api';

export type Value = { list: UserInfo[]; count?: number };

export type Props = {
  className?: string;
  placeholder?: string;
  value: UserInfo[];
  onChange: (users: UserInfo[]) => void;
  onOneAndMoreChange?: (v: OneAndMoreType) => void;
  showPercent?: boolean;
  maxShowCount?: number;
  oneAndMoreType?: OneAndMoreType;
  taskId?: number;
  // 指派者信息
  assigner?: UserInfo;
  // 只显示指派者 为 true 时，assigner 必填
  onlyAssigner?: boolean;
  // 指派者 id
  assignerUid: string;
  disabled?: boolean;
  count?: number; //分配人数
  hasArrow?: boolean;
  setPickerVis?: React.Dispatch<React.SetStateAction<boolean>>;
  resultGroup?: boolean;
  showCountIcon?: boolean;
  showAddIcon?: boolean;
  finishPercentTip?: React.ReactNode;
  toolTipTitle?: string;
  onVisibleChange?: (v: boolean) => void;
  getPopupContainer?: () => HTMLElement;
};

const ListExecutorItem = forwardRef<any, Props>((props, ref) => {
  const {
    value,
    onChange,
    onOneAndMoreChange,
    assigner,
    assignerUid,
    setPickerVis,
    finishPercentTip,
    disabled,
    hasArrow,
    taskId,
    showCountIcon,
    showAddIcon,
    oneAndMoreType,
    onVisibleChange,
    ...rest
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const {
    isAssigneeUpdated,
  } = useContext(PeoplePickerContext);

  useImperativeHandle(
    ref,
    () => ({
      toggleVisible: (visible: boolean) => {
        setVisible(visible);
      },
    }),
    [visible]
  );

  const handleVisibleChange = (v: boolean) => {
    if (isAssigneeUpdated) {
      const btn = document.body.querySelector(`.confirm__btn`);
      if (btn) {
        btn?.classList.add('blink__animation');
        setTimeout(() => {
          btn?.classList.remove('blink__animation');
        }, 200);
      }
    } else {
      setVisible(v);
      onVisibleChange?.(v);
      setPickerVis?.(v);
    }
  };

  return (
    <PeoplePicker
      assignerUid={assignerUid}
      search
      value={[...value]}
      onChange={onChange}
      hasMinimum
      assigner={assigner}
      visible={visible && !(disabled && !value.length)}
      onVisible={v => handleVisibleChange(v)}
      disabled={disabled}
      hasArrow={hasArrow}
      taskId={taskId!}
      oneAndMoreType={oneAndMoreType}
      onOneAndMoreChange={onOneAndMoreChange}
      {...rest}
    >
      <div className={`${s.add} ${disabled && s.addDisabled}`}>
        {value.length ? (
          <RenderPeoples
            showFinishedIcon={oneAndMoreType === OneAndMoreType.all}
            list={value || []}
            count={value?.length}
            maxShowCount={6}
            showCountIcon={showCountIcon}
            showAddIcon={showAddIcon}
            avatarClassName="mr-4"
            finishPercentTip={finishPercentTip}
          ></RenderPeoples>
        ) : (
          <Placeholder text={disabled ? I18N.auto.nothing : I18N.auto.addTo}></Placeholder>
        )}
      </div>
    </PeoplePicker>
  );
});

export default ListExecutorItem;
