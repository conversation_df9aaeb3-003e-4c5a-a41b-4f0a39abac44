import { Scrollbar } from '@bedrock/components';
import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import Quill from 'quill';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { AvatarPeople } from '@/components/basic';
import { useOutsideClick } from '@/hooks';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import { MentionExtraData } from '../../../utils';
import s from './index.less';

interface Props {
  quill: Quill;
  _this: any;
  remove: () => void;
  onSlected: (v: UserInfo) => void;
  list: UserInfo[];
  search: boolean;
  participants: UserInfo[];
}

const Keys = {
  ESC: 27,
  ENTER: 'Enter',
  UP: 38,
  DOWN: 40,
  LEFT: 37,
  RIGHT: 39,
};

const MentionList = (props: Props) => {
  const { quill, _this, remove, onSlected, list, search, participants } = props;
  const listRef = useRef<UserInfo[]>(list);
  const scrollRef = useRef<any>(null);
  listRef.current = list;
  const currentIndexRef = useRef(0);
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const destroy = () => {
    remove();
    _this.show = false;
  };
  const outsideRef = useOutsideClick<HTMLDivElement>(() => {
    destroy();
  });

  const selectIndex = (d: number) => {
    //d -1 1
    if (d === -1 && currentIndexRef.current === 0) {
      currentIndexRef.current = listRef.current.length + d;
    } else {
      const newIndex = (currentIndexRef.current + d) % listRef.current.length;
      currentIndexRef.current = Math.abs(newIndex);
    }
    setCurrentIndex(currentIndexRef.current);

    // 计算滚动条滚动距离
    //向下正向
    if (d === 1) {
      //超出一屏幕开始滚动
      if (currentIndexRef.current >= 5) {
        //扣除最近联系人的高度34
        const top = 34 + 36 * (currentIndexRef.current - 5);
        scrollRef.current.scrollTop(top);
      }
      //循环到头的时候置顶
      if (currentIndexRef.current === 0) {
        scrollRef.current.scrollTop(0);
      }
    } else {
      // 超出一屏滚动
      if (currentIndexRef.current >= 5) {
        const top = 34 + 36 * currentIndexRef.current;
        scrollRef.current.scrollTop(top);
      } else {
        //一屏幕数据内 置顶
        scrollRef.current.scrollTop(0);
      }
    }
  };
  useEffect(() => {
    quill.keyboard.addBinding(
      {
        key: Keys.UP,
      },
      () => {
        selectIndex(-1);
        return false;
      }
    );

    quill.keyboard.addBinding(
      {
        key: Keys.DOWN,
      },
      () => {
        selectIndex(1);
        return false;
      }
    );

    quill.keyboard.addBinding(
      {
        key: Keys.LEFT,
      },
      () => {
        destroy();
        return true;
      }
    );

    quill.keyboard.addBinding(
      {
        key: Keys.RIGHT,
      },
      () => {
        destroy();
        return true;
      }
    );

    quill.keyboard.addBinding(
      {
        key: Keys.ENTER,
      },
      () => {
        onSlected(listRef.current[currentIndexRef.current]);
        destroy();
      }
    );

    // 把回车键置顶
    //@ts-ignore
    quill.keyboard.bindings[Keys.ENTER].unshift(quill.keyboard.bindings[Keys.ENTER].pop());
    return () => {
      //@ts-ignore
      quill.keyboard.bindings[Keys.ENTER].shift();
      //@ts-ignore
      quill.keyboard.bindings[Keys.UP].pop();
      //@ts-ignore
      quill.keyboard.bindings[Keys.DOWN].pop();
      //@ts-ignore
      quill.keyboard.bindings[Keys.LEFT].pop();
      //@ts-ignore
      quill.keyboard.bindings[Keys.RIGHT].pop();
    };
  }, []);
  useEffect(() => {
    pp.setHotkeyFilterList({
      filters: [
        {
          modifiers: '',
          key: 'Key_Escape',
        },
      ],
    });

    const listener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        destroy();
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
      pp.setHotkeyFilterList({
        filters: [],
      });
    };
  }, []);
  const title = useMemo(() => {
    if (search) {
      return I18N.auto.contacts;
    }
    if (!participants.filter((item) => item.uid !== MentionExtraData.userInfo?.uid).length) {
      return I18N.auto.recentContacts;
    } else {
      return I18N.auto.participants;
    }
  }, [search, participants]);
  return (
    <div
      className={s.mentionPanel}
      ref={outsideRef}
      onClick={(e) => {
        quill?.focus?.(); //失去焦点quill.keyboard.addBinding会失效
        e.stopPropagation();
      }}
    >
      <div className={s.box}>
        <Scrollbar ref={scrollRef}>
          {/* <div className={s.title}>{title}</div> */}
          <div className={s.list}>
            {list.map((item, index) => {
              let isParticipant = true;
              if (search && !participants.find((v) => v.uid === item.uid)) {
                isParticipant = false;
              }
              return (
                <div
                  className={classNames(s.item, { [s.active]: currentIndex === index })}
                  key={index}
                  onClick={(e) => {
                    setCurrentIndex(index);
                    currentIndexRef.current === index;
                    onSlected(item);
                    destroy();
                    e.stopPropagation();
                  }}
                >
                  <div className={s.left}>
                    <AvatarPeople
                      className={classNames(s.avatar)}
                      avatarUrl={item.avatarUrl}
                    ></AvatarPeople>
                    <div className={s.name}>{item.name}</div>
                    {item.desc ? <div className={s.desc}>{item.desc}</div> : null}
                  </div>
                  {search && !isParticipant ? (
                    <div className={s.tag}>{I18N.auto.notJoined}</div>
                  ) : null}
                </div>
              );
            })}
          </div>
        </Scrollbar>
      </div>
      <div className={s.tip}>
        <div>{I18N.auto.switch}</div>
        <div className={s.line}></div>
        <div>{I18N.auto.enter}</div>
        <div className={s.line}></div>
        <div>{I18N.auto.escCancel}</div>
      </div>
    </div>
  );
};

export default MentionList;
