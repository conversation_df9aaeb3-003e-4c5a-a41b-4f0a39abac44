import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { Icon } from '@/components/basic';
import { Dispatch } from '@/models/store';
import { TaskGroupItem, TodoInfo } from '@/types';
import { EnumKanbanAddSource, TaskTableRowType, ViewAddSource } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

export interface AddBtnProps {
  context: {
    groupInfo: TaskGroupItem;
    list: TodoInfo[];
  };
}

const AddBtn: React.FC<AddBtnProps> = (props) => {
  const { context } = props;
  const { groupInfo, list } = context;
  const dispatch = useDispatch<Dispatch>();
  const onAdd = () => {
    dispatch.kanban.tableAddTaskItem({
      groupId: groupInfo.groupId!,
      isHeader: false,
      defaultParams: {
        ...groupInfo.defaultParams,
        assigneeUids: groupInfo.assigneeUids || [],
        //@ts-ignore
        assigneeCount: groupInfo.assigneeCount || 0,
      },
    });
  };
  const hasAdd = useMemo(() => {
    return list.findIndex((item) => item?._rowType === TaskTableRowType.add) > -1;
  }, [list]);
  return hasAdd ? null : (
    <div className="pb-14">
      <div
        className={s.addBtn}
        onClick={(e) => {
          e.stopPropagation();
          // 标注新建来源
          ViewAddSource.kanban = EnumKanbanAddSource.laneBottomCreate;
          onAdd();
        }}
      >
        <Icon name="icon-sys_add" className={s.icon} fontSize={16}></Icon>
        {I18N.auto.newTask}
      </div>
    </div>
  );
};

export default AddBtn;
