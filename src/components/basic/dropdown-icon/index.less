.label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  .content {
    display: flex;
    flex: 1;
    align-items: center;
    // justify-content: center;
    // overflow: hidden;
  }

  .arrow {
    width: 14px;
    font-size: 14px;
    flex-shrink: 0;
    transform: rotate(90deg);
    color: var(--IconSecondary);
    display: none;
    transition: 0.3s;
  }

  .down {
    transform: rotate(-90deg);
  }

  &:hover {
    .arrow {
      display: block;
    }
  }
}
