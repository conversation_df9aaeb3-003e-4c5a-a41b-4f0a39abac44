import classNames from 'classnames'
import React, { useEffect, useMemo, useRef, useState } from 'react'

import { CustomFieldSelect } from '@/components/basic-project'
import { TodoInfo } from '@/types'
import { CustomField, FieldTypeEnum, SelectFieldOption } from '@/types/custom-field'

import AddRowBox from '../row-box'
import s from './index.less'

interface Props {
  taskInfo: TodoInfo
  filedInfo: CustomField
  disabled?: boolean
  value?: string[]
  onChange?: (v: string[]) => void
}

const RenderCustomFieldSelect: React.FC<Props> = props => {
  const { taskInfo, filedInfo, disabled, value, onChange } = props
  const [visible, setVisible] = useState<boolean>(false)

  const selectContainerRef = useRef<HTMLDivElement>(null)
  const scrollPositionRef = useRef({ x: 0, y: 0 })

  const changeVisible = (v: boolean) => {
    setVisible(v)
  }

  const change = v => {
    onChange?.(v)
  }

  const options = filedInfo.options

  const optionsMap = useMemo(() => {
    return options?.reduce((acc, cur) => {
      acc[cur.value] = cur
      return acc
    }, {} as Record<string, SelectFieldOption>)
  }, [options])

  const newValue = useMemo(() => {
    // 非单选/多选，直接返回value
    if (!optionsMap) {
      return value
    }
    if (!value) {
      return
    }
    // 单选
    if (typeof value === 'string' && optionsMap[value]) {
      return value
    }
    // 多选
    if (Array.isArray(value)) {
      return value.filter(val => optionsMap[val])
    }
  }, [optionsMap, value])

  const isMulti = filedInfo.type === FieldTypeEnum.multiOption

  const handleOpenChange = v => {
    if (!v && isMulti) {
      return
    }
    changeVisible(v)
  }

  useEffect(() => {
    function handleDropdown(e) {
      try {
        const selector = `.select-dropdown-${taskInfo.taskId}-${filedInfo?.fieldId}`
        const dropdownContainer = document.querySelector(selector)
        if (dropdownContainer && dropdownContainer.contains(e.target as Node)) {
          setVisible(true)
        } else {
          setVisible(false)
        }
      } catch (error) {
        console.log(error)
      }
    }

    document.addEventListener('click', handleDropdown, {
      capture: true,
    })

    return () => {
      document.removeEventListener('click', handleDropdown, {
        capture: true,
      })
    }
  }, [isMulti, visible])

  return (
    <AddRowBox
      tipTitle=""
      placeholder={newValue || visible ? '' : '-'}
      focused={visible}
      disabled={disabled}
      taskId={taskInfo.taskId}
      hasDelete={!!newValue?.length}
      onClear={() => change(isMulti ? [] : '')}
    >
      <div
        className={s.fieldSelect}
        ref={selectContainerRef}
        onClick={() => {
          handleOpenChange(!visible)
        }}
      >
        <CustomFieldSelect
          open={visible}
          onOpenChange={handleOpenChange}
          value={newValue}
          options={options!}
          onChange={change}
          className={classNames(s.select)}
          multiple={isMulti}
          maxTagCount={'responsive'}
          tagCloseable={false}
          noRadius
          dropdownClassName={`select-dropdown-${taskInfo.taskId}-${filedInfo?.fieldId}`}
        ></CustomFieldSelect>
      </div>
    </AddRowBox>
  )
}

export default RenderCustomFieldSelect
