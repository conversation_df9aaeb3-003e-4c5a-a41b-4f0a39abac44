import { pp } from '@popo-bridge/web';
import { useMount } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { Resizable } from 're-resizable';
import { FC, useEffect, useMemo, useRef } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { Outlet } from 'umi';

import { Dispatch, RootState } from '@/models/store';
import { EnumTrackeKey } from '@/utils/skyline';

import Menu from './components/menu';
import s from './index.less';
import { Platform } from '@/utils/platform';

const NewPage: React.FC = () => {
  const resizableRef = useRef<Resizable>(null);
  const defaultWidthRef = useRef<number>(0);

  const { showMenu = true, showSeparate } = useSelector(
    (state: RootState) => ({
      showMenu: state.user.showMenu,
      showSeparate: state.user.showSeparate,
    }),
    shallowEqual
  );

  const dispatch = useDispatch<Dispatch>();

  const scale = useMemo(() => {
    const rootFontSize = parseFloat(
      window.getComputedStyle(document.documentElement).fontSize.replace('px', '')
    );

    return rootFontSize / 100;
  }, []);

  const getDefaultWidth = () => {
    const cacheWidth = localStorage.getItem('navigation-left-width');
    if (cacheWidth) {
      return parseFloat(cacheWidth);
    }

    return 220 * scale;
  };

  const handleResizeStart = () => {
    // 禁止动画
    resizableRef.current?.resizable?.classList.add(s.resizing);
  };

  const handleResizeStop = (
    event: MouseEvent | TouchEvent,
    direction: any,
    elementRef: HTMLElement
  ) => {
    requestAnimationFrame(() => {
      // 在这里进行元素尺寸的修改操作
      const { width = defaultWidthRef.current } = elementRef.getBoundingClientRect();

      localStorage.setItem('navigation-left-width', width.toString());
      defaultWidthRef.current = width;
      resizableRef.current?.updateSize({ width });
      resizableRef.current?.resizable?.classList.remove(s.resizing);
    });
  };

  useMount(() => {
    window.POPOTaskTrackTimeMap = {
      ...(window.POPOTaskTrackTimeMap || {}),
      pagesNew: Date.now(), //进入pages-new时间
    };
    let timeArr: { point: string; time: number }[] = [];
    Object.keys(window.POPOTaskTrackTimeMap).forEach((key) => {
      timeArr.push({
        point: key,
        //@ts-ignore
        time: window.POPOTaskTrackTimeMap[key],
      });
    });
    //@ts-ignore
    timeArr = timeArr
      .sort((a, b) => a.time - b.time)
      .map((item) => ({
        point: item.point,
        time: item.time ? dayjs(item.time).format('YYYY-MM-DD HH:mm:ss.SSS') : '',
      }));
    const _data = {
      key: EnumTrackeKey.LoadingPointsTime,
      times: timeArr,
    };
    dispatch.user.tracking(_data);
    try {
      pp.log({
        key: EnumTrackeKey.LoadingPointsTime,
        times: JSON.stringify(timeArr),
      });
    } catch (error) {
      console.log(error);
    }

    defaultWidthRef.current = getDefaultWidth();
  });

  useEffect(() => {
    resizableRef.current?.updateSize({ width: showMenu ? defaultWidthRef.current : 0 });
  }, [showMenu]);

  return (
    <div className={classNames(s.page, { [s.pageBorderTop]: showSeparate })}>
      <Resizable
        ref={resizableRef}
        minWidth={showMenu ? 220 * scale : 0}
        maxWidth={330 * scale}
        onResizeStart={handleResizeStart}
        onResizeStop={handleResizeStop}
        handleComponent={{
          right: <CustomHandle visible={showMenu} />,
        }}
        className={s.page__resizer}
      >
        <div className={s.pageLeft}>
          <Menu />
        </div>
      </Resizable>

      <div className={classNames(s.pageRight, { ['hidden-menu']: !showMenu })}>
        <Outlet />
      </div>
    </div>
  );
};

interface ICustomHandleProps {
  visible: boolean;
}
const CustomHandle: FC<ICustomHandleProps> = ({ visible }) => {
  return (
    <div
      className={s.resizer__handle}
      tabIndex={-1}
      style={{
        pointerEvents: visible ? 'all' : 'none',
        cursor: Platform.OS === 'windows' ? 'e-resize' : 'col-resize',
      }}
    />
  );
};

export default NewPage;
