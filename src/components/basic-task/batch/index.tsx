import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { IconBtn } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { SELECT_MAX_LENGTH } from '@/utils/const';
import I18N from '@/utils/I18N';

import BatchDelete from './batch-delete';
// import BatchEsc from './batch-esc';
import BatchLevel from './batch-level';
// import BatchRemindTime from './batch-remind-time';
import BatchStatus from './batch-status';
import BatchTime from './batch-time';
import s from './index.less';
export type Props = {
  className?: string;
};

const BatchList: React.FC<Props> = () => {
  const dispatch = useDispatch<Dispatch>();
  const { batchList, isBatchMode } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
    isBatchMode: state.batch.isBatchMode,
  }));
  const listener = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      dispatch.batch.setBatchMode(false);
    }
    if (e.key === 'a' && (e.ctrlKey || e.metaKey)) {
      //全选
      dispatch.batch.allSelectList({});
    }
  };
  useEffect(() => {
    if (isBatchMode) {
      pp.setHotkeyFilterList({
        filters: [
          {
            modifiers: '',
            key: 'Key_Escape',
          },
        ],
      });
      document.addEventListener('keydown', listener);
      return () => {
        document.removeEventListener('keydown', listener);
        pp.setHotkeyFilterList({
          filters: [],
        });
      };
    }
  }, [isBatchMode]);
  const disabled = useMemo(() => {
    return batchList.length === 0 || batchList.length > SELECT_MAX_LENGTH;
  }, [batchList]);
  return (
    <>
      <BatchStatus disabled={disabled}></BatchStatus>
      <BatchLevel disabled={disabled}></BatchLevel>
      <BatchTime disabled={disabled}></BatchTime>
      {/* <BatchRemindTime disabled={disabled}></BatchRemindTime> */}
      <BatchDelete disabled={disabled}></BatchDelete>
      {/* <BatchEsc></BatchEsc> */}
    </>
  );
};

const Batch: React.FC<Props> = (props) => {
  const { className } = props;
  const { isBatchMode, batchList } = useSelector((state: RootState) => ({
    isBatchMode: state.batch.isBatchMode,
    batchList: state.batch.batchList,
  }));
  const dispatch = useDispatch<Dispatch>();

  useEffect(() => {
    if (isBatchMode) {
      dispatch.task.setEditing(false);
    }
  }, [isBatchMode]);
  const showMaxTip = useMemo(() => {
    return batchList.length > SELECT_MAX_LENGTH;
  }, [batchList.length]);
  return isBatchMode ? (
    <>
      {showMaxTip ? (
        <div className={s.tip}>
          {I18N.template(I18N.auto.selectedWaitingItems, {
            val1: batchList.length,
            val2: SELECT_MAX_LENGTH,
          })}
        </div>
      ) : null}
      <div className={classNames(s.batchBox, { [s.hideLine]: showMaxTip })}>
        <IconBtn
          title={I18N.auto.cancelSelection}
          iconName="icon-close"
          className={s.deleteIcon}
          onClick={() => {
            dispatch.batch.setBatchMode(false);
          }}
        ></IconBtn>
        {batchList.length ? (
          <div className={s.selectCount}>
            {I18N.template(I18N.auto.selectItem, { val1: batchList.length })}
          </div>
        ) : null}
        <div className={s.line}></div>
        <BatchList></BatchList>
      </div>
    </>
  ) : null;
};

export default Batch;
