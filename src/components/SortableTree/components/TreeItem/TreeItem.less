.TreeItemWrapper {
  position: relative;
  cursor: pointer;

  &.disabled {
    pointer-events: none;
  }

  &.ghost {
    &.indicator {
      opacity: 1;
      position: relative;
      z-index: 1;
      margin-bottom: -1px;

      .TreeItem {
        position: relative;
        padding: 0;
        height: 8px;
        border-color: #2389ff;

        &:before {
          position: absolute;
          left: -8px;
          top: -4px;
          display: block;
          content: '';
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 1px solid #2389ff;
          background-color: #ffffff;
        }

        > * {
          /* Items are hidden using height and opacity to retain focus */
          opacity: 0;
          height: 0;
        }
      }
    }

    &:not(.indicator) {
      opacity: 0.5;
    }

    .TreeItem > * {
      box-shadow: none;
      background-color: transparent;
    }
  }

  &.before {
    &::before {
      content: '';
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--Brand500);
    }
  }

  &.after {
    &::after {
      content: '';
      position: absolute;
      z-index: 10;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--Brand500);
    }
  }

  &.inside {
    background-color: var(--aBlack6);
  }

  &.isAbover {
    position: relative;

    background-color: var(--aBrand12) !important;

    &::after {
      content: '';
      position: absolute;
      z-index: 10;
      inset: 0;
      border: 1px solid var(--Brand400) !important;
    }
  }

  &.clone {
    pointer-events: none;
    padding: 0;
    padding-left: 10px;
    padding-top: 5px;

    .TreeItem {
      background-color: var(--bgApplication);
      --vertical-padding: 5px;
      padding-right: 24px;
      border-radius: 4px;
      box-shadow: 0px 4px 8px 0px var(--aBlack12), 0px 2px 4px 0px var(--aBlack6);
    }
  }
}

.Wrapper {
  position: relative;
  list-style: none;
  box-sizing: border-box;
  padding-left: var(--spacing);
  flex: 1;
  min-width: 0;
}

.Wrapper__drag {
  .TreeItem {
    padding-left: 20px;
  }
}

.TreeItem {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--TextPrimary-strong);
  flex: 1;
  min-width: 0;
}

.Text {
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 22px;
}

.Count {
  position: absolute;
  top: -10px;
  right: -10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #2389ff;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
}

.disableInteraction {
  pointer-events: none;
}

.disableSelection,
.clone {
  .Text,
  .Count {
    user-select: none;
    -webkit-user-select: none;
  }
}

.Collapse {
  cursor: pointer;
  margin-right: 8px;
  opacity: 0;
  pointer-events: none;
  height: 16px;

  .collapse__icon {
    color: var(--IconPrimary);
    transition: transform 250ms ease;
  }

  &.collapsed .collapse__icon {
    transform: rotate(-90deg);
  }

  &.collapsible {
    pointer-events: all;
    opacity: 1;
  }
}
