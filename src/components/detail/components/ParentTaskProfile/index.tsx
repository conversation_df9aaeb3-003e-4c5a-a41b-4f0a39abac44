import { Dispatch } from '@/models/store';
import { DetailTodoInfo } from '@/types';
import { ChevronLeft } from '@bedrock/icons-react';
import { FC } from 'react';
import { useDispatch } from 'react-redux';
import s from './index.less';
import I18N from '@/utils/I18N';

interface IParentTaskProfileProps {
  taskInfo: DetailTodoInfo;
}
const ParentTaskProfile: FC<IParentTaskProfileProps> = ({ taskInfo }) => {
  const dispatch = useDispatch<Dispatch>();
  if (!taskInfo?.parentId) {
    return null;
  }

  const handleOpenDetail = () => {
    // 打开详情
    dispatch.detail.openDetail(Number(taskInfo.parentId));
  };

  return (
    <div className={s.container}>
      <div className={`${s['parent-task-profile']} flex-y-center`} onClick={handleOpenDetail}>
        <ChevronLeft className={`${s['parent-task-icon']} fs-14`} />
        <span className="ellipsis">
          {I18N.auto.backToParentTask}: {taskInfo.parentTaskTitle}
        </span>
      </div>
    </div>
  );
};

export default ParentTaskProfile;
