.confirm {
  :global {
    .rock-btn-primary {
      background-color: var(--R600) !important;
      &:hover {
        background-color: var(--R500) !important;
      }
    }
  }

  .content {
    font-size: 14px;
    line-height: 22px;
    color: var(--TextPrimary-strong, rgba(12, 12, 12, 1));
    margin-top: 8px;
    margin-bottom: 8px;
  }
}
.checkbox {
  &:global(.rock-checkbox-wrapper:hover .rock-checkbox-inner) {
    border-color: var(--Brand400) !important;
  }

  :global {
    .rock-checkbox-checked {
      .rock-checkbox-check-icon {
        display: inline-block !important;
      }
    }
  }
}
