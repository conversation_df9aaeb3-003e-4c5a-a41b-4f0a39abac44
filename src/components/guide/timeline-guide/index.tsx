import { FC, useMemo, useState } from 'react';

import { Button } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

interface TimelineGuideProps {
  user: {
    corlorMode: string;
    language: string;
  };
}

const TimelineGuide: FC<TimelineGuideProps> = ({ user }) => {
  const [currStep, setCurrStep] = useState(1);

  const { corlorMode, language } = user;

  const isShowGuide = useMemo(() => {
    const flag = window.localStorage.getItem('TIMELINE-GUIDE-DONE');
    return !flag;
  }, [corlorMode]);

  if (!isShowGuide) {
    return null;
  }

  const handleHideGuide = () => {
    window.localStorage.setItem('TIMELINE-GUIDE-DONE', '1');
  };

  const handleNext = () => {
    if (currStep === 1) {
      setCurrStep(2);
    }
    handleHideGuide();
  };

  const handlePrev = () => {
    if (currStep === 2) {
      setCurrStep(1);
    }
    handleHideGuide();
  };

  const handleDone = () => {
    document.querySelector('#timeline-guide')?.remove();
    handleHideGuide();
  };

  const stepData = {
    1: {
      title: I18N.auto.newTimelineView,
      des: I18N.auto.trackProgress,
      img: `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/timeline-guide/timeline-${corlorMode}-${language}.png?t=%${Date.now()}`,
      btnText: I18N.auto.next,
    },
    2: {
      title: I18N.auto.setTaskTimeframes,
      des: I18N.auto.planSmartWith,
      img: `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/timeline-guide/date-${corlorMode}-${language}.png?t=%${Date.now()}`,
      btnText: [I18N.auto.thePrevious, I18N.auto.iGotIt],
    },
  };

  const currStepData = stepData[currStep as keyof typeof stepData];

  return (
    <>
      <div className="rock-popover rock-popover-dark rock-popover rock-popover-placement-bottomLeft">
        <div
          className="rock-popover-arrow"
          style={{
            left: '0.32rem',
            top: '0.06rem',
            backgroundColor: 'var(--TaskGuideBg)',
            borderColor: 'var(--aBlack8)',
            borderTopLeftRadius: '0.02rem',
            borderTop: '1px solid var(--popover-border-color, var(--border-color-2))',
            borderLeft: '1px solid var(--popover-border-color, var(--border-color-2))',
          }}
        ></div>
        <div
          className="rock-popover-inner"
          style={{
            width: '3.8rem',
            background: 'linear-gradient(180deg, var(--TaskGuideBg) 0%, var(--bgTop) 100%)',
            borderColor: 'var(--aBlack8)',
            borderRadius: '0.12rem',
            padding: '0.2rem',
          }}
        >
          <div className={s.wrapper}>
            <div className={s.title}>{currStepData.title}</div>
            <div className={s.des}>{currStepData.des}</div>
            <div className={s.img__container}>
              <img src={currStepData.img} alt="img" />
            </div>
            <div className={s.btnWrapper}>
              <div className={s.stepIndicator}>{currStep}/2</div>
              {typeof currStepData.btnText === 'string' ? (
                <Button className={s.btn} type="primary" onClick={handleNext}>
                  {currStepData.btnText}
                </Button>
              ) : (
                <div className={s.btnGroup}>
                  <Button type="checked-neutral" onClick={handlePrev}>
                    {currStepData.btnText[0]}
                  </Button>
                  <Button className={s.btn} type="primary" onClick={handleDone}>
                    {currStepData.btnText[1]}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className={s.mask}></div>
    </>
  );
};

export default TimelineGuide;
