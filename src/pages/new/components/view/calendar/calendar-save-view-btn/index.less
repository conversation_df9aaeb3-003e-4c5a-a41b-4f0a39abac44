.options {
  display: flex;
  padding: 14px 20px;
  flex-shrink: 0;
  :global {
    .rock-btn {
      font-size: 13px;
      padding: 0 10px 0 8px;
    }
    .option-active {
      background-color: var(--aBrand12) !important;
      color: var(--Brand600) !important;
    }
    .option-opened {
      background-color: var(--aBlack4);
    }
  }
}
.optionsWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 20px;
}
.saveBtnWrapper {
  display: flex;
  align-items: center;
}

.add {
  margin-right: 16px;
}
