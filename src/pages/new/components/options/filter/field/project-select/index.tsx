import { ChangeParams } from '@bedrock/components/lib/Select';
import classNames from 'classnames';
import _ from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';

import { apiProjectBatchGetNamePost, apiProjectSearchProjectPost } from '@/api';
import { IconProject, Select, Spin, Tooltip } from '@/components/basic';
import SelectedItem from '@/components/basic/select/selected-item';
import ProjectTag from '@/components/basic-project/project-picker/tag-item';
import { tfProject } from '@/models/utils';
import { ProjectInfo } from '@/types';
import { SelectType } from '@/utils/fields';

import s from './index.less';

interface TabsProps {
  value: string[];
  onChange?: (value: any, params?: Partial<ChangeParams>) => void;
  type: SelectType;
}

const renderLabel = (option: ProjectInfo) => {
  return (
    <Tooltip
      title={option.name}
      getPopupContainer={(dom) => dom.parentNode}
      destroyTooltipOnHide
      overlayClassName={s.tip}
      onlyEllipsis
    >
      <div>
        <ProjectTag className={s.item} item={option}></ProjectTag>
      </div>
    </Tooltip>
  );
};

const FieldSelect: React.FC<TabsProps> = (props) => {
  const { value, onChange } = props;
  const searchRef = useRef();
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<ProjectInfo[]>([]);
  const fetchData = useMemo(() => {
    return _.debounce((v) => {
      //TODO Select组件的onSearch在拼音输入的时候 单个字母也会发起请求,需要升级bedrock至少要升级到2.2.161 + 以后,暂时还未发布
      if (searchRef.current === v) {
        return;
      }
      searchRef.current = v;
      setFetching(true);
      apiProjectSearchProjectPost({
        size: 30,
        keyword: v,
        //阶段1服务端说传这个三个就ok, 感觉处理的不靠谱. 阶段2修改全部都不传,都查出来,服务端和产品都同意,服务端改造成本高先这样做
        //roles: [],
      })
        .then((ret) => {
          const projectList = (tfProject(ret.list || []) as ProjectInfo[]).map((item) => ({
            ...item,
            name: item.name,
            value: item.projectId + '', //服务端要求conditions中values为字符串数组
          }));
          setOptions(projectList);
        })
        .finally(() => {
          setFetching(false);
        });
    }, 300);
  }, []);
  const getInitData = async () => {
    //确保组件在渲染前value是有数据的
    if (value.length) {
      const projectList = await apiProjectBatchGetNamePost({
        projectIds: value,
      }).then((res) => {
        return res.map((item) => ({
          ...item,
          value: item.projectId,
        }));
      });
      setOptions(projectList);
    }
    fetchData('');
  };
  useEffect(() => {
    getInitData();
  }, []);
  return (
    <Select
      dropdownClassName={s.fieldSelect}
      multipleCheckedType={'check-icon'}
      multiple
      value={value}
      onChange={onChange}
      options={options}
      showSearch="inner"
      onSearch={fetchData}
      notFoundContent={fetching ? <Spin /> : ''}
      renderLabel={(option) => {
        return renderLabel(option);
      }}
      renderSelectedItem={(option, { onClose }) => {
        return {
          content: (
            <div className={classNames(option.className, s.tag)}>
              <SelectedItem
                onClose={(e) => {
                  onClose(e, option);
                }}
              >
                <div className={s.selectedItem}>
                  <IconProject
                    className={s.icon}
                    fontSize={16}
                    name={option.icon}
                    bgColor={option.iconColor}
                    active
                  ></IconProject>
                  {option.name}
                </div>
              </SelectedItem>
            </div>
          ),
          isRenderInTag: false,
        };
      }}
      zIndex={999999}
    />
  );
};
export default FieldSelect;
