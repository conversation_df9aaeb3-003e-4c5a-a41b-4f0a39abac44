import React, { FC } from 'react';
import s from './index.less';
import classNames from 'classnames';
import { CalendarModeType, EnumEmitter } from '@/utils/const';
import I18N from '@/utils/I18N';
import { useSelector } from 'react-redux';
import { RootState } from '@/models/store';
import { POPOBridgeEmitter } from '@popo-bridge/web';

const CalendarMode = () => {
  const { planViewMode = CalendarModeType.week } = useSelector((state: RootState) => ({
    planViewMode: state.viewSetting.currentViewTab.planViewMode || CalendarModeType.week,
  }));

  const handleChangeMode = (mode: CalendarModeType) => {
    POPOBridgeEmitter.emit(EnumEmitter.CalendarModeTypeChange, mode);
  };

  return (
    <div className={s.mode}>
      <div
        className={classNames(s.modeBtn, {
          [s.modeBtnActive]: planViewMode === CalendarModeType.week,
        })}
        onClick={() => {
          handleChangeMode(CalendarModeType.week);
        }}
      >
        {I18N.auto.week}
      </div>
      <div
        className={classNames(s.modeBtn, {
          [s.modeBtnActive]: planViewMode === CalendarModeType.month,
        })}
        onClick={() => {
          handleChangeMode(CalendarModeType.month);
        }}
      >
        {I18N.auto.month}
      </div>
    </div>
  );
};

export default CalendarMode;
