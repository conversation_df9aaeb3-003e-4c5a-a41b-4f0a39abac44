import React, { useState } from 'react';

import { POPODatePicker } from '@/components/basic';
import RemindStr from '@/components/basic/popo-convergence-date-picker/remind-item/remind-label';
import { TimeValue } from '@/components/basic/popo-date-picker';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  hasArrow?: boolean;
  value?: TimeValue;
  onChange: (v: TimeValue) => void;
  onOpen?: (v: boolean, itemName: string) => void;
};

const AddRow: React.FC<Props> = (props) => {
  const { hasArrow, value, onChange, onOpen } = props;

  const [visible, setVisible] = useState<boolean>(false);
  return (
    <POPODatePicker
      visible={visible}
      onVisible={(v) => {
        setVisible(v);
        onOpen?.(v, 'deadline');
      }}
      hasArrow={hasArrow}
      placeholder={I18N.auto.whenSettingReminders}
      value={value}
      onChange={onChange}
      hasRRule={true}
      showLabelRRuleIcon
      renderLabel={(time, rrule) => {
        return <RemindStr time={time} timeFormat={value?.timeFormat} rrule={rrule}></RemindStr>;
      }}
    ></POPODatePicker>
  );
};

export default AddRow;
