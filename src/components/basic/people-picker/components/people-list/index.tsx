import React, { useEffect, useMemo, useState } from 'react';

import PeopleItem from '@/components/basic/people-picker/components/people-item';
import { UserInfo } from '@/types';
import classNames from 'classnames';
import s from './index.less';

export type TagData = {
  tagName: string;
  tooltipTitle: string;
  disabled: boolean;
};

export type Props = {
  className?: string;
  /**
   * 选中的用户数据  list
   */
  value?: UserInfo[];
  list: UserInfo[];
  multiple?: boolean;
  finished?: boolean;
  onChange?: (list: UserInfo[], isSelfDelete?: boolean) => void;
  selectedList?: UserInfo[];
  emptyTip?: React.ReactNode;
  canSelect?: boolean; //全部 通过select 选择和取消
  canDelete?: boolean; //单独的删除
  canDeleteSelf?: boolean; //能否单独删除自己
  extraItemTag?: (item: UserInfo) => TagData | undefined;
  currentUser?: UserInfo;
};
const PeopleList: React.FC<Props> = (props) => {
  const {
    list,
    value,
    multiple = false,
    finished,
    onChange,
    emptyTip,
    canSelect,
    canDelete,
    canDeleteSelf,
    extraItemTag,
    currentUser,
  } = props;

  const [focusedIndex, setFocusedIndex] = useState(-1);

  const onClick = (info: UserInfo) => {
    if (!multiple) {
      // 如果不是多选直接返回
      onChange?.([info]);
    } else {
      let _value = value || [];
      const selected = value?.some((v) => v.uid === info.uid);
      if (selected) {
        _value = value?.filter((item) => item.uid !== info.uid) || [];
      } else {
        _value?.push(info);
      }
      onChange?.(_value);
    }
  };
  const memoList = useMemo(() => {
    let selfIdx = -1;
    const tempList = list.map((item, idx) => {
      if (item.uid === currentUser?.uid) {
        selfIdx = idx;
      }
      return {
        ...item,
        selected: value?.some((v) => v.uid === item.uid),
      };
    });
    if (canDeleteSelf && selfIdx !== -1) {
      const tempItem = { ...tempList[0] };
      tempList[0] = tempList[selfIdx];
      tempList[selfIdx] = tempItem;
    }
    return tempList;
  }, [list, canDeleteSelf, currentUser?.uid, value]);

  const onRemove = (user: UserInfo) => {
    //value
    const _value = value?.filter((item) => item.uid !== user.uid) || [];
    onChange?.(_value, canDeleteSelf);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        e.stopPropagation();
        e.preventDefault();
        setFocusedIndex((prev) => Math.min(prev + 1, memoList.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.stopPropagation();
        e.preventDefault();
        setFocusedIndex((prev) => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter' && focusedIndex !== -1) {
        e.stopPropagation();
        e.preventDefault();
        const item = memoList[focusedIndex];
        const data = extraItemTag?.(item);
        if (!data?.disabled) {
          onClick(item);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [memoList, focusedIndex]);

  useEffect(() => {
    if (focusedIndex !== -1) {
      const focusedItem = document.querySelector(`.${s.focused}`);
      if (focusedItem) {
        focusedItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [focusedIndex]);

  return (
    <>
      {memoList.length
        ? memoList.map((item, index) => {
            const { avatarUrl, name, selected, uid } = item;

            let tagName = '';
            let tooltipTitle = '';
            let disabled = false;
            if (extraItemTag) {
              const data = extraItemTag(item);
              tagName = data?.tagName || '';
              tooltipTitle = data?.tooltipTitle || '';
              disabled = data?.disabled || false;
            }
            return (
              <PeopleItem
                avatarUrl={avatarUrl}
                name={name}
                tagName={tagName}
                tooltipTitle={tooltipTitle}
                disabled={disabled}
                key={index}
                className={classNames({
                  [s.focused]: index === focusedIndex,
                })}
                selected={selected}
                finished={finished}
                onClick={() => {
                  if (!disabled) {
                    if (item.selected) {
                      onRemove(item);
                    } else {
                      onClick({
                        ...item,
                        selected: true,
                      });
                    }
                  }
                }}
                onRemove={() => {
                  onRemove(item);
                }}
                canSelect={canSelect}
                showDelete={canDelete || (uid === currentUser?.uid && canDeleteSelf)}
              />
            );
          })
        : emptyTip}
    </>
  );
};

export default PeopleList;
