.overlay {
  min-width: 150px;
}
.stateSubMenu {
  min-width: 200px !important;
}
.menu {
  padding: 12px 20px 20px;
  background-color: var(--bgTop);
  border-radius: 8px;
  box-shadow: var(--ComBoxShadow);
}

.item {
  display: flex;
  align-items: center;
  color: var(--TextPrimary);
  font-weight: 400;
  border-radius: 4px;
  :global {
    .rock-dropdown-menu-submenu-title {
      width: 100%;
      margin: 0 4px;
      color: var(--TextPrimary);
    }
  }
}
.divider {
  margin-top: 4px !important;
  margin-bottom: 4px;
}
.delete {
  color: var(--R600);
}
