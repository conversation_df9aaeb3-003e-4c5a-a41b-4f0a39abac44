.iconBox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
}
.icon {
  display: flex;
  color: var(--IconSecondary);
}
.active {
  color: var(--absWhite);
}

@icon-size: 12, 13, 14, 16, 18, 20, 22, 24, 28, 36;

each(@icon-size, {
  .icon-@{value} {
    height: unit(@value, px);
    line-height: unit(@value, px); 
    width: unit(@value, px);
    font-size: unit(@value, px);
    text-align: center
  }
});
