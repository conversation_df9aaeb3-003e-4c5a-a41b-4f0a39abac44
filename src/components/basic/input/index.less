body {
  .rock-input::placeholder {
    color: var(--TextTertiary) !important;
    font-size: 14px;
  }
  .rock-input-wrapper {
    --border-color-1: var(--aBlack12);
    --component-bg: var(--bgTop);
    color: var(--TextPrimary);
    padding-left: 8px;
    padding-right: 8px;
    .rock-input-disabled,
    .rock-input[disabled] {
      color: var(--TextPrimary);
      cursor: default;
      //
    }

    // .rock-input {
    //   font-size: 13px;

    //   &::placeholder {
    //     font-size: 13px;
    //   }
    // }

    // &::before {
    //   // 不能在这里统一去除, 影响面太多
    //   opacity: 0;
    // }
    // border-color: var(--aBlack12);
    &.rock-input-wrapper-inline {
      // background-color: var(--bgTop);
      // border-color: var(--aBlack12);
      // &.rock-input-wrapper-focused {
      //   border-color: var(--Brand500);
      // }
      // &:not(.rock-input-wrapper-disabled):active {
      //   border-color: var(--Brand500);
      // }
      // .rock-input {
      //   color: var(--TextPrimary);
      // }
      // &.rock-input-wrapper-no-border {
      //   border-color: transparent !important;
      // }
    }
    // &.rock-input-wrapper:focus,
    // &.rock-input-wrapper-focused {
    //   border-color: var(--Brand500) !important;
    // }
  }
  .rock-input-number-disabled.rock-input-wrapper-no-border {
    background: transparent;
  }
  .task-rock-input-borderless {
    &::before {
      border-color: transparent;
    }
  }

  //填充模式
  // .rock-input-wrapper.rock-input-wrapper-fill {
  //   border-color: transparent;
  //   background-color: var(--aBlack4);
  // }
}
