.list {
  background: var(--bgBottom);
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
  max-width: 200px;
  height: 26px;
  margin-right: 24px;
  position: relative;

  &:hover {
    background-color: var(--aBlack6);
  }

  .title {
    color: var(--TextPrimary);
    font-size: 13px;
    margin-left: 4px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 22px;
    cursor: default;
    .input {
      height: 22px;
      border-radius: 6px;
      padding-left: 6px;
      padding-right: 6px;
      width: 100%;
    }
  }
  .itemRight {
    display: flex;
    align-items: center;
    margin-left: 8px;
    .icon {
      &:last-child {
        color: var(--IconSecondary);
      }
    }
    .sysIcon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:hover {
        background-color: var(--aBlack6);
        border-radius: 4px;
      }
    }
  }
  .itemRight > .icon {
    &:hover {
      background-color: var(--aBlack6);
      border-radius: 4px;
    }
  }
}

.lastItem {
  border-bottom: none;
}

.moreBtn {
  white-space: nowrap;
  margin-right: 24px;
}

.hiddenTabs {
  position: fixed;
  top: -200px;
  display: flex;
  left: 0;
}

.moreWrapperBtn {
  height: 26px;
  display: flex;
  align-items: center;
  .moreBtn {
    line-height: 22px;
  }
  .activeMoreBtn {
    color: var(--Brand600);
    font-size: 13px;
  }
  .inkbar {
    bottom: 0px;
    max-width: 62px;
  }
}

.tabMoreDropdown {
  min-width: 178px;
  max-height: 400px;
  overflow-y: auto;
  .item {
    cursor: pointer;
    i {
      margin-right: 4px;
    }
    height: 36px;
    margin-right: 0px;
    border-radius: 4px;
    padding-left: 8px;
    padding-right: 8px;
  }
}

.inkbar {
  position: absolute;
  bottom: -1px;
  height: 2px;
  width: 100%;
  background-color: var(--Brand600);
  transition: width 0.3s, transform 0.3s, visibility 0s 0.3s;
  //transform: translateX(-100px);
}

.activeTabItem {
  .icon {
    color: var(--Brand600);
  }
  .title {
    color: var(--Brand600);
  }
}

.new__label {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  height: 14px;
  font-size: 10px;
  padding: 0 2px;
  transform: translate(22px, -10px);
  border-radius: 7px 7px 7px 0;
  background-color: var(--R500);
  color: var(--absWhite);
}
