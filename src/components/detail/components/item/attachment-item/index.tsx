import classNames from 'classnames';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';

import { AtachmentPicker } from '@/components/basic';
import { ApiAttachment } from '@/components/basic/attachment-picker/file-item';
import { Dispatch } from '@/models/store';

import s from './index.less';

export type Props = {
  className?: string;
  value?: ApiAttachment[];
  placeholder?: string;
  disabled?: boolean;
  taskId: number;
};

const AttachmentItem: React.FC<Props> = (props) => {
  const { value, taskId, disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();
  const onChange = (v: ApiAttachment[]) => {
    dispatch.detail.getTodoDetail(taskId);
  };
  return (
    <div className={s.attachment}>
      <AtachmentPicker
        visible={visible}
        value={value}
        onChange={onChange}
        onVisible={setVisible}
        taskId={taskId}
        disabled={disabled}
      ></AtachmentPicker>
    </div>
  );
};

export default AttachmentItem;
