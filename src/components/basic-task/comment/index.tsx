import { Tabs } from '@bedrock/components';
import classNames from 'classnames';
import Quill from 'quill';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { LoadMore, Timeline } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { EventCategory } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { Record, RecordEvent } from './record';
export type Props = {
  className?: string;
  taskId?: number;
  canComment?: boolean;
};

const Comment: React.FC<Props> = (props: Props) => {
  const { className, taskId, canComment } = props;
  const dispatch = useDispatch<Dispatch>();
  const {
    recordList,
    hasMore,
    eventCategory,
    readComment: { readCommentTimestamp },
    userInfo,
  } = useSelector((state: RootState) => ({
    recordList: state.record.recordList,
    hasMore: state.record.hasMore,
    eventCategory: state.record.eventCategory,
    readComment: state.record.readComment,
    userInfo: state.user.userInfo,
  }));

  const changeType = (type: number) => {
    if (type !== eventCategory) {
      dispatch.record.setReplyCommentId({});
      dispatch.record.setEventCategory(type);
      dispatch.record.getRecordList({ page: 1 });
    }
  };
  const loadMore = async () => {
    return dispatch.record.getRecordList({ taskId: String(taskId) });
  };

  useEffect(() => {
    if (recordList.length) {
      //时间倒序 取到第一个评论即可判断
      const commentRecord = recordList.find((item) => item.eventType === RecordEvent.addComment);
      if (commentRecord && Number(commentRecord.timestamp) > Number(readCommentTimestamp)) {
        dispatch.record.commentRead({
          commentId: commentRecord.eventDetail?.commentId,
          taskId: commentRecord.todoId,
        });
      }
    }
  }, [recordList, readCommentTimestamp]);

  // useEffect(() => {
  //   let quill = new Quill('#quillEditorDemo', {
  //     debug: 'error',
  //     theme: 'snow',
  //     modules: {
  //       toolbar: false,
  //       popoDoc: {},
  //       popoEmoji: {},
  //       popoAt: {},
  //     },
  //   });
  //   dispatch.record.setQuill(quill);
  // }, []);

  const { TabPane } = Tabs;
  // @ts-ignore
  const Content = ({ children }) => <div className="p-5 text-color-2">{children}</div>;
  const TabContent = (
    <div className={s.commentList}>
      <Timeline>
        {recordList.map((item, index) => {
          return (
            <Record
              readCommentTimestamp={readCommentTimestamp || 0}
              key={index}
              item={item}
              userId={userInfo?.uid}
              eventCategory={eventCategory}
              canComment={canComment}
            ></Record>
          );
        })}
      </Timeline>
      <LoadMore
        canLoadMore={hasMore}
        moreFetcher={loadMore}
        scrollContent=".detail-scroll-content .rock-scrollbar-view"
      />
    </div>
  );
  return (
    <div id="commentsContainer" className={s.commentModule}>
      <div className={s.quillDemo}>
        <div id="quillEditorDemo" className={classNames('popo-qiull-editor')}></div>
      </div>
      <Tabs
        activeKey={String(eventCategory)}
        onChange={(val) => {
          changeType(Number(val));
        }}
        className={s.commentTabs}
      >
        <TabPane tab={I18N.auto.comment} key={String(EventCategory.comment)}>
          <Content>{TabContent}</Content>
        </TabPane>
        <TabPane tab={I18N.auto.editRecord} key={String(EventCategory.records)}>
          <Content>{TabContent}</Content>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Comment;
