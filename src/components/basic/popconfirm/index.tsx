// eslint-disable-next-line simple-import-sort/imports
import { Popconfirm as BedrockPopconfirm } from '@bedrock/components';
import type { PopoverConfirmProps } from '@bedrock/components/es/Popover/Confirm';
import classNames from 'classnames';

import './index.less';

interface Props extends PopoverConfirmProps {
  hasIcon?: boolean;
  danger?: boolean;
}

export default function Popconfirm(props: Props) {
  const { hasIcon, children, overlayClassName, danger, ...rest } = props;
  return (
    <BedrockPopconfirm
      {...rest}
      okButtonProps={{ danger }}
      overlayClassName={classNames(overlayClassName, { noIcon: !hasIcon })}
    >
      {children}
    </BedrockPopconfirm>
  );
}
