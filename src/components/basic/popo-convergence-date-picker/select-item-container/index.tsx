import classNames from 'classnames';
import React, { FC, PropsWithChildren } from 'react';

import s from './index.less';

interface ISelectItemContainerProps {
  icon: React.ReactNode;
  title: string;
}

const SelectItemContainer: FC<PropsWithChildren<ISelectItemContainerProps>> = ({
  icon,
  title,
  children,
}) => {
  return (
    <div className={classNames(s.item__container)}>
      <div className={`${s.left} flex-y-center`}>
        <span className={`${s.suffixIcon} flex-y-center`}>{icon}</span>
        <span>{title}</span>
      </div>
      {children}
    </div>
  );
};

export default SelectItemContainer;
