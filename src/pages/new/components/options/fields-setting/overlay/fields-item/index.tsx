import { useSortable } from '@dnd-kit/sortable';
import classNames from 'classnames';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Icon, IconBtn, Switch } from '@/components/basic';
import CustomFieldDropdown from '@/components/custom-field-dropdown';
import { FiledTypeList } from '@/components/field-edit-popover/const';
import { RootState } from '@/models/store';
import { CustomField } from '@/types/custom-field';
import { attrCreatTime, defaultFieldOrder, DisplayField } from '@/utils/fields';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';

export type Props = {
  item: DisplayField;
  className?: string;
  hasHandle?: boolean;
  onChange?: (v: boolean) => void;
  onOpenModal?: (fieldId: any) => void;
  customFields?: CustomField[];
};

const FieldItem: React.FC<Props> = (props) => {
  const { item, hasHandle = false, onChange, onOpenModal, customFields } = props;
  const { attributes, listeners } = useSortable({
    id: item.id,
  });

  const { perms } = useSelector((state: RootState) => ({
    perms: state.viewSetting.permissions,
  }));

  const handleProps = useMemo(() => {
    return hasHandle ? { ...attributes, ...listeners } : {};
  }, [hasHandle, attributes, listeners]);

  const memoIcon = useMemo(() => {
    if (item.customFieldId) {
      const field = customFields?.find((v) => v.fieldId === item.customFieldId);
      const filedType = FiledTypeList.find((v) => v.value === field?.type);
      if (filedType?.icon) {
        return React.cloneElement(filedType?.icon, {
          className: `${filedType.icon.props.className || ''} ${s.attrIcon}`,
        });
      }
    } else {
      const filedType = [...defaultFieldOrder, attrCreatTime].find(
        (v) => v.value === item.fieldName
      );
      if (filedType?.icon) {
        return React.cloneElement(filedType?.icon, {
          className: `${filedType.icon.props.className || ''} ${s.attrIcon}`,
        });
      }
    }
  }, [customFields, item]);

  return (
    <div className={classNames(s.item)}>
      <Icon className={s.icon} name="icon-pc_plane_move" {...handleProps}></Icon>
      <span className={s.fieldName}>
        {memoIcon}
        <span className={s.name}>{item.name}</span>
      </span>
      <div className={s.right}>
        {item.customFieldId &&
        validatesPermission({ key: ProjectPermissionEnum.CAN_MANAGE_FIELD, permissions: perms }) ? (
          <CustomFieldDropdown onOpenModal={onOpenModal} fieldId={item.id} name={item.name || ''}>
            <IconBtn
              iconName="icon-sys_more"
              className={s.icon}
              // iconClassName="mr-4"
            />
          </CustomFieldDropdown>
        ) : null}
        <Switch
          value={!!item.visible}
          className={s.switch}
          onChange={(v) => {
            onChange?.(v);
          }}
          key={item.fieldName}
        />
      </div>
    </div>
  );
};

export default FieldItem;
