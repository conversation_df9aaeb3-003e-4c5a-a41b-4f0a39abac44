.leveLabel {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  font-size: 12px;
  .noValueItemText {
    font-size: 14px;
    color: var(--TextTertiary);
  }
  .remove {
    // display: none;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-left: 4px;
    font-size: 12px;
    text-align: center;
    color: var(--IconTertiary);
    border-radius: 4px;
  }
  &:hover {
    .remove {
      display: flex;
    }
  }
  .remove {
    &:hover {
      background-color: var(--aBlack6);
    }
  }
}
