import { SoftZd } from '@babylon/popo-icons';
import classNames from 'classnames';
import { cloneElement, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as _ from 'lodash';

import { Button, Icon, Popover } from '@/components/basic';
import { FilterGuide } from '@/components/guide/filter-guide';
import useGuide from '@/components/guide/useGuide';
import { Dispatch, RootState } from '@/models/store';
import { isProjectId, isTaskMenuId } from '@/models/utils';
import {
  EnumKanbanAddSource,
  EnumListAddSource,
  GuideType,
  ViewAddSource,
  ViewType,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import {
  ProjectPermissionEnum,
  validatesPermission,
  ViewPermissionEnum,
  ViewTypeEnum,
} from '@/utils/permission';

import CardConfig from './card-config';
import FieldsSetting from './fields-setting';
import Filter from './filter';
import Group from './group';
import s from './index.less';
import Sort from './sort';
import SaveViewBtn from './save-view-btn';
import { useParams } from 'umi';
import MoreOptions from './MoreOptions';

export interface IOptionProps {
  className?: string;
  prefixOption?: React.ReactNode;
}

const Options: React.FC<IOptionProps> = (props) => {
  const { className, prefixOption = null } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const optionsRef = useRef({
    visibleOptions: [],
    hiddenOptions: [],
  });

  const showContainerRef = useRef<HTMLDivElement>(null);
  const optionsContainerRef = useRef<HTMLDivElement>(null);

  const dispatch = useDispatch<Dispatch>();

  const { currentViewTab, permissions, viewTabList, projectInfo, navigatorId } = useSelector(
    (state: RootState) => ({
      currentViewTab: state.viewSetting.currentViewTab,
      permissions: state.viewSetting.permissions,
      viewTabList: state.viewSetting.viewTabList,
      projectInfo: state.project.projectInfo,
      navigatorId: state.viewSetting.navigatorId,
    })
  );

  const { changeSearchParams, hasViewChanged } = currentViewTab;

  const { id: projectId } = useParams();

  const isProjectTab = useMemo(() => {
    return isProjectId(navigatorId as number);
  }, [navigatorId]);

  const isTaskMenuTab = useMemo(() => {
    return isTaskMenuId(navigatorId as number);
  }, [navigatorId]);

  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.filter,
  });

  const ref = useRef<{ changeVisible: (v: boolean) => void }>({
    changeVisible: () => {},
  });

  const [canEditView] = useMemo(() => {
    const isCommonView = currentViewTab.viewDistribution !== ViewTypeEnum.Personal;

    const canEditView = validatesPermission({
      permissions: currentViewTab.permissions,
      key: isCommonView
        ? ViewPermissionEnum.CAN_EDIT_COMMON_VIEW
        : ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
    });
    return [canEditView];
  }, [currentViewTab]);

  const [canCreateCommonView, canCreatePersonView] = validatesPermission({
    permissions,
    key: [
      ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
      ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW,
    ],
  }) as boolean[];

  const showSaveNewBtn = useMemo(() => {
    return changeSearchParams && hasViewChanged && (canCreateCommonView || canCreatePersonView);
  }, [changeSearchParams, hasViewChanged, canCreateCommonView, canCreatePersonView]);

  const onAdd = () => {
    //e.stopPropagation();
    setTimeout(() => {
      if (currentViewTab?.viewType === ViewType.list) {
        // 标注最新新建来源
        ViewAddSource.list = EnumListAddSource.globalCreate;
        dispatch.task.tableAddTaskItem({
          groupId: undefined,
          isHeader: true,
          defaultParams: {},
          isSubtask: true,
        });
      } else if (currentViewTab?.viewType === ViewType.kanban) {
        // 标注最新新建来源
        ViewAddSource.kanban = EnumKanbanAddSource.globalCreate;
        dispatch.kanban.tableAddTaskItem({ groupId: undefined, isHeader: true, defaultParams: {} });
      } else if (currentViewTab?.viewType === ViewType.timeline) {
        // dispatch.timeline.tableAddTaskItem({
        //   groupId: undefined,
        //   isHeader: true,
        //   defaultParams: {},
        // });
        window.postMessage({ type: 'timeline-add-task' }, '*');
      }
    }, 0);
  };

  const showAddBtn = useMemo(() => {
    return validatesPermission({
      permissions: permissions,
      key: ProjectPermissionEnum.CAN_CREATE_TASK,
    });
  }, [permissions]);

  useEffect(() => {
    //筛选引导目前只放在任务快捷视图下
    if (isTaskMenuTab) {
      getGuideData();
    }
  }, [isTaskMenuTab]);

  const filter = (
    <Popover
      overlayClassName={s.guidePopover}
      arrowPointAtCenter
      placement={'bottomLeft'}
      trigger={'click'}
      visible={showGuide}
      content={
        <FilterGuide
          onClick={() => {
            handleHideGuide();
          }}
          onExpClick={() => {
            handleHideGuide().then(() => {
              ref.current.changeVisible(true);
            });
          }}
        />
      }
    >
      <Filter className="option__item" ref={ref}></Filter>
    </Popover>
  );

  const sort = <Sort className="option__item" />;

  const group = <Group className="option__item" />;

  const fieldSetting = currentViewTab.viewType !== ViewType.timeline && (
    <FieldsSetting className="option__item">
      <Button
        className={classNames('mr-8', 'task-fields-setting')}
        type="text-subtle"
        icon={<SoftZd className="fs-16" />}
      >
        {I18N.auto.fieldConfiguration}
      </Button>
    </FieldsSetting>
  );

  // 看板-> 卡片配置
  const cardConfig = currentViewTab.type === ViewType.kanban && (
    <CardConfig className="option__item" />
  );

  const allOptions = [filter, sort, group, fieldSetting, cardConfig].filter(Boolean);

  const handleOptions = _.throttle(
    () => {
      if (!containerRef.current || !allOptions.length) return;

      const container = containerRef.current;
      let visibleOptions: JSX.Element[] = [];
      let hidden: JSX.Element[] = [];

      const optionItems = container.querySelectorAll('.option__item');
      const containerRight = container.getBoundingClientRect().right;

      optionItems.forEach((optionItem, index) => {
        const optionRight = optionItem.getBoundingClientRect().right;
        const currOptionNode = allOptions[index];
        if (optionRight > containerRight) {
          if (currOptionNode) {
            hidden.push(
              cloneElement(currOptionNode, {
                placement: 'leftTop',
              })
            );
            // optionItem.classList.add(s.hidden);
          }
        } else {
          if (currOptionNode) {
            visibleOptions.push(currOptionNode);
            // hidden = hidden.filter((item) => item !== currOptionNode);
            // optionItem.classList.remove(s.hidden);
          }
        }
      });
      optionsRef.current.visibleOptions = visibleOptions;
      optionsRef.current.hiddenOptions = hidden;
    },
    32,
    {
      leading: true,
      trailing: false,
    }
  );

  // 监听容器宽度变化
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerLeft = optionsContainerRef.current?.getBoundingClientRect().left || 0;
        const left = showContainerRef.current?.getBoundingClientRect().left || 0;

        const optionsLeft = left - containerLeft;

        containerRef.current.style.left = `${optionsLeft}px`;
        containerRef.current.style.width = `calc(100% - ${optionsLeft}px - ${
          46 * window.fontSizeScaleRatio
        }px)`;

        handleOptions();
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    handleResize();
    // 此处要二次调用resize，因为第一次调用可能会出现更多按钮，影响容器宽度
    setTimeout(() => {
      handleResize();
    }, 50);

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const { hiddenOptions, visibleOptions } = optionsRef.current;

  return currentViewTab?.type != ViewType.calendar ? (
    <div className={classNames(s.optionsWrapper, className)}>
      <div className={s.options} ref={optionsContainerRef}>
        {showAddBtn ? (
          <Button
            className={classNames(s.add, 'option__add--task')}
            icon={<Icon name="icon-sys_add" fontSize={16}></Icon>}
            type="checked-neutral"
            onClick={onAdd}
          >
            {I18N.auto.newTask}
          </Button>
        ) : null}
        {prefixOption}
        <div
          style={{
            flexWrap: 'nowrap',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
          className="task-fields-select"
          ref={showContainerRef}
        >
          <div style={{ display: 'inline-flex', alignItems: 'center' }}>
            {!!hiddenOptions?.length ? visibleOptions : allOptions}
          </div>
        </div>
        <MoreOptions className="mr-8" options={hiddenOptions} />

        <div
          style={{
            flexWrap: 'nowrap',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            opacity: 0,
            pointerEvents: 'none',
            position: 'absolute',
            left: '250px',
            width: 'calc(100% - 250px)',
          }}
          className="task-fields-select"
          ref={containerRef}
        >
          <div style={{ display: 'inline-flex', alignItems: 'center' }}>{allOptions}</div>
        </div>
        <SaveViewBtn
          showSaveNewBtn={showSaveNewBtn}
          isProjectTab={isProjectTab}
          canEditView={canEditView}
          currentViewTab={currentViewTab}
          projectId={projectInfo.projectId || projectId}
        />
      </div>
    </div>
  ) : null;
};
export default Options;
