.panel {
  width: 240px;
  //padding: 12px;
  cursor: auto;

  .panelSearchPd {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 0 8px;
  }
  .input {
    flex: 1;
    height: 40px;
    border: none;
    padding-left: 4px !important;
    padding-right: 0 !important;
  }
  .verticalLine {
    margin: 0 8px;
  }
  .iconAdd {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    text-align: center;
    font-size: 14px;
    color: var(--IconPrimary);
    cursor: pointer;
    i {
      color: var(--IconPrimary);
    }
  }
}
.searchingPanel {
  max-height: 320px;
  min-height: 320px;
  :global {
    .rock-divider-horizontal-solid {
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}
.divider {
  margin: 0;
}

.container {
  display: flex;
  flex: 1;
  min-height: 0px;
}

.followerTip {
  height: 30px;
  color: var(--TextSecondary);
  font-size: 12px;
  line-height: 30px;
  margin-top: 4px;
  padding-left: 2px;
}
