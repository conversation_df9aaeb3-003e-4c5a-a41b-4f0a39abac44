// eslint-disable-next-line simple-import-sort/imports
import { Popover as BasePopover } from '@bedrock/components';
import type { PopoverProps as BasePopoverProps } from '@bedrock/components/es/Popover';
import classNames from 'classnames';
import React from 'react';

import './index.less';

export interface PopoverProps extends BasePopoverProps {}

export const Popover: React.FC<PopoverProps> = ({ children, ...props }) => {
  return (
    <BasePopover
      {...props}
      overlayClassName={classNames('tobedone-popover', props.overlayClassName)}
    >
      {children}
    </BasePopover>
  );
};
