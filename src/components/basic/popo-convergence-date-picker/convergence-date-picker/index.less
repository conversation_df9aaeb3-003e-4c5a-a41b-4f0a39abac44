.clear {
  width: 22px;
  height: 22px;
  color: var(--IconPrimary);
  &:hover {
    background-color: var(--aBlack6);
  }
}

.input__container {
  padding: 12px 16px;
}
.newDatePickerWrapper {
  background-color: var(--bgTop);
  :global {
    .rock-tooltip-inner {
      background-color: var(--bgTop);
      border-radius: 8px;
      border: 1px solid var(--aBlack12);
      padding: 0 0;
    }

    .rock-tooltip-arrow {
      display: none;
    }

    .rock-picker-header {
      height: 26px !important;
      padding: 0 16px;
      margin-top: 8px;
    }

    .rock-picker-body {
      padding-right: 16px !important;
      padding-left: 16px !important;
    }
    .rock-select-dropdown-placement-bottomLeft {
      top: 60px !important;
      .rock-select-content {
        width: 100%;
        .rock-select-item {
          //padding: 6px 8px;
        }
      }
    }
  }

  .divider {
    height: 1px;
    width: 100%;
    background-color: var(--aBlack6);
  }

  .setRepeatRemind {
    display: flex;
    justify-content: space-between;
    i {
      margin-right: 8px;
    }
  }

  .setRemindRepeatSection {
    padding: 8px 16px;
  }
}

.footer {
  padding: 12px 16px;
  display: flex;
  column-gap: 12px;

  .confirm__btn {
    background-color: var(--Brand500);
    color: var(--absWhite);

    &[disabled]:hover {
      color: var(--absWhite);
    }
  }

  :global {
    .rock-btn {
      flex: 1;
    }
  }
}
