import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiProjectFieldListPost, apiTaskUpdateFieldPost } from '@/api';
import { Icon, Tooltip } from '@/components/basic';
import { Variant } from '@/components/basic/input';
import { CustomFieldSelect, CustomFieldTime } from '@/components/basic-project';
import ComFieldText from '@/components/basic-project/com-field-text';
import { FieldTypeEnum, FiledTypeList } from '@/components/field-edit-popover/const';
import { Dispatch, RootState } from '@/models/store';
import { DetailTodoInfo } from '@/types';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import RenderItem from '../render-item';
import { FieldNumber } from './custom-field-number';
import { FieldPeople } from './custom-field-people';
import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

const CustomRenderItems = ({ taskInfo }: { taskInfo: DetailTodoInfo }) => {
  const { currentViewTab, customFields } = useSelector((state: RootState) => {
    return {
      customFields: state.detail.customFields,
      currentViewTab: state.viewSetting.currentViewTab,
      //projectId: state.detail.taskInfo.project?.projectId,
    };
  });
  const { project = {} } = taskInfo;
  const { projectId } = project;

  const permissions = useGetPermissions({ taskId: taskInfo.taskId });

  const dispatch = useDispatch<Dispatch>();

  const customFieldList =
    customFields
      .map((item) => {
        const fieldItem = FiledTypeList.find((v) => v.value === item.type);
        return { ...fieldItem, ...item };
      })
      .filter((item) => !!item) || [];

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    });
    return !CAN_EDIT;
  }, [permissions]);

  const ItemContent = useMemoizedFn((field) => {
    let defaultValue = undefined;
    if (field.type === FieldTypeEnum.user) {
      defaultValue = [];
    }

    // @ts-ignore
    defaultValue = taskInfo.customFieldValues?.values?.[field.fieldId]?.value ?? defaultValue;

    // @ts-ignore
    const onChange = (v) => {
      apiTaskUpdateFieldPost({
        fieldValue: {
          fieldId: field.fieldId,
          fieldVersion: field.fieldVersion,
          value: v,
        },
        taskId: taskInfo.taskId,
      }).finally(() => {
        dispatch.detail.getTodoDetail(Number(taskInfo.id));
        if (currentViewTab.type) {
          dispatch.viewSetting.refreshDataByDataChange({
            refreshList: true,
            refreshCount: false,
          });
        }
      });
    };
    switch (field.type) {
      case FieldTypeEnum.datetime:
        return {
          content: (
            <div className={s.customTimeWrapper}>
              <CustomFieldTime
                value={defaultValue}
                onChange={(v) => {
                  onChange?.(dayjs(v).valueOf());
                }}
                placeholder="-"
                className={classNames(s.customTime, {
                  [s.noTimeValue]: !defaultValue,
                  [s.disabled]: disabled,
                })}
                disabled={disabled}
                timeFormat={field.format as PPTimeFormat}
              />

              {defaultValue && !disabled && (
                <div
                  className={s.closeIcon}
                  onClick={() => {
                    onChange(undefined);
                  }}
                >
                  <Icon fontSize={16} name="icon-sys_close" className={s.timeDeleteIcon}></Icon>
                </div>
              )}
            </div>
          ),
        };

      case FieldTypeEnum.multiOption:
      case FieldTypeEnum.option:
        return {
          content: (
            <CustomFieldSelect
              multiple={field.type === FieldTypeEnum.multiOption ? true : false}
              value={defaultValue}
              options={field.options}
              placeholder={'-'}
              onChange={onChange}
              dropdownClassName={s.customSelectDropdown}
              className={
                field.type === FieldTypeEnum.multiOption ? s.multipleCustomSelect : s.customSelect
              }
              disabled={disabled}
            />
          ),
          allowClear:
            !disabled &&
            (field.type == FieldTypeEnum.option ? !!defaultValue : defaultValue?.length),
          onClear() {
            onChange(field.type == FieldTypeEnum.option ? '' : []);
          },
        };

      case FieldTypeEnum.number:
        return {
          content: (
            <FieldNumber
              filedInfo={field}
              value={defaultValue}
              onChange={onChange}
              disabled={disabled}
              key={field.fieldId}
              placeholder="-"
            />
          ),
        };

      case FieldTypeEnum.text:
        return {
          content: (
            <ComFieldText
              value={defaultValue}
              disabled={disabled}
              placeholder="-"
              className={classNames(s.customText, { [s.disabled]: disabled })}
              onChange={onChange}
              blurChangeValue={true}
              variant={Variant.borderless}
            />
          ),
        };

      case FieldTypeEnum.user:
        return {
          content: (
            <FieldPeople
              filedInfo={field}
              taskInfo={taskInfo}
              disabled={disabled}
              onChange={onChange}
              placeholder="-"
            />
          ),
        };
      default:
        return {
          content: '',
        };
    }
  });
  useEffect(() => {
    if (projectId) {
      dispatch.detail.getProjectFieldList(projectId);
    } else {
      dispatch.detail.setData({
        customFields: [],
      });
    }
  }, [projectId]);

  return (
    !!customFieldList.length && (
      <div className={classNames(s.customFieldsWrapper)}>
        {customFieldList.map((field) => {
          let config = ItemContent(field);

          return (
            <div className={s.customField} key={field.name}>
              <RenderItem
                leftRender={
                  <span className={s.customFieldLeft}>
                    {field.icon}
                    <Tooltip title={field.name} onlyEllipsis>
                      <span className={s.name}>{field.name}</span>
                    </Tooltip>
                  </span>
                }
                className={s.wrapper}
                allowClear={config.allowClear}
                onClear={config.onClear}
              >
                <div className={s.customFieldRight}>{config.content}</div>
              </RenderItem>
            </div>
          );
        })}
      </div>
    )
  );
};

export default CustomRenderItems;
