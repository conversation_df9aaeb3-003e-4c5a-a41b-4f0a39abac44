import classNames from 'classnames';
import s from '../index.less';
import { Icon } from '@/components/basic';
import React from 'react';

interface IPersistMenuProps {
  name: string;
  active?: boolean;
  onClick?: () => void;
  icon?: React.ReactNode;
}
/**
 * 常驻菜单样式
 * @param param0
 * @returns
 */
const PersistMenu: React.FC<IPersistMenuProps> = ({ icon, name, active, onClick }) => {
  return (
    <div
      className={classNames(s.menuItem, 'mb-2', {
        [s.active]: active,
      })}
      onClick={onClick}
      draggable={false}
    >
      {icon && <Icon name={icon} />}
      <div className={s.menuName}>
        <div>{name}</div>
      </div>
    </div>
  );
};
export default PersistMenu;
