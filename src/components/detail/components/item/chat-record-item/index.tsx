import { pp } from '@popo-bridge/web';
import React from 'react';
import { useDispatch } from 'react-redux';

import { apiTodoMultMsgRemovePost } from '@/api';
import { IconBtn } from '@/components/basic';
import { Dispatch } from '@/models/store';

import s from './index.less';

export type Props = {
  title: string;
  content: string;
  digest: string[];
  taskId: number;
  disabled?: boolean;
};

const ChatRecordItem: React.FC<Props> = (props) => {
  const { digest, title, content, taskId, disabled } = props;
  // TODO: 点击打开原生面板 把 content 传递给 bridge
  const dispatch = useDispatch<Dispatch>();
  const remove = (e: any) => {
    e.stopPropagation();
    apiTodoMultMsgRemovePost({ taskId })
      .then(() => {
        dispatch.detail.clearMultMsg();
      })
      .catch((err) => {
        console.log('remove mult messag error', err);
      });
  };
  const toPopo = () => {
    pp.openMergeMsgForm({
      content: content,
      type: 0,
      title: title,
    });
  };

  if (!title) {
    return null;
  }
  return (
    <div className={s['chatRecordItem']} onClick={toPopo}>
      {!disabled && (
        <div className={s.close} onClick={remove}>
          <IconBtn className={s.delete} iconName="icon-close" fontSize={12}></IconBtn>
        </div>
      )}
      <div className={s.title}>{title}</div>
      <div className={s.chat}>
        {digest.slice(0, 3).map((item, index) => {
          return (
            <span className={s.message} key={index}>
              {item}
            </span>
          );
        })}
      </div>
    </div>
  );
};

export default ChatRecordItem;
