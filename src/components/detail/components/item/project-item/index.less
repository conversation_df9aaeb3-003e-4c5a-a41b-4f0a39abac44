.projectWrapper {
  height: 32px;
  width: 100%;
  &:hover {
    // background-color: transparent !important;
  }
  :global {
    .picker-label-content {
      .com-placeholder {
        min-width: 0 !important;
        font-size: 14px !important;
      }
    }
  }
  .itemProject {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .clear {
    color: var(--IconTertiary);
    flex-shrink: 0;
  }
}
