import React, { PropsWithChildren, useMemo } from 'react';

import { Table } from '@/components/basic';

import s from './index.less';

interface DragOverLatProps {
  components: any;
  row: any;
  columns: any;
  id: number;
  data: any[];
}

const TableDragOverlay: React.FC<PropsWithChildren<DragOverLatProps>> = (props) => {
  const { components, columns, id, data } = props;
  const item = useMemo(() => {
    return data.filter((item) => item.id === id) || [];
  }, [data, id]);
  return (
    <Table
      wrapperClassName={s.tableDragOverlay}
      components={components}
      rowKey="id"
      columns={columns}
      data={item}
      rowSelection={{
        type: 'checkbox',
        props: {
          width: '22px',
          align: 'right',
        },
        onSelect: (...rest) => {
          console.log(...rest);
        },
        onSelectAll: (...rest) => {
          console.log(...rest);
        },
        onSelectAllPage: (...rest) => {
          console.log(...rest);
        },
      }}
    />
  );
};

export default TableDragOverlay;
