.groupRow {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  color: var(--TextPrimary);
  font-weight: 600;
  font-size: 16px;
}
.groupBox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.groupTitle {
  font-size: 15px;
  display: flex;
  align-items: center;
  text-align: left;

  .groupTaskCount {
    font-size: 0.14rem;
    display: flex;
    align-items: center;
    height: 0.16rem;
    padding: 0 0.06rem;
    font-weight: 400;
    color: var(--TextTertiary);
  }

  .ellipsis_2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.arrow {
  width: 20px;
  height: 20px;
  margin-right: 6px;

  :global {
    .iconfont {
      transform: rotate(0deg);
      transition: transform ease 0.25s;
    }
  }
}
.unfold {
  :global {
    .iconfont {
      transform: rotate(90deg);
    }
  }
}
