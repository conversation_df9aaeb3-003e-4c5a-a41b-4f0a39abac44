import classNames from 'classnames';
import { useMemo } from 'react';

import s from './index.less';
export type Props = {
  className?: string;
  totalCount: number;
  completedCount: number;
  width?: number;
  height?: number;
  strokeWidth?: number;
};
export default function PercentIcon(props: Props) {
  const { className, totalCount, completedCount, width = 20, height = 20, strokeWidth = 2 } = props;

  const strokeDashoffset = useMemo(() => {
    const percent = completedCount / totalCount;
    const r = width / 2 - strokeWidth / 2;
    return 2 * Math.PI * r * (1 - percent);
  }, [totalCount, completedCount, strokeWidth, width]);

  const strokeDasharray = useMemo(() => {
    const r = width / 2 - strokeWidth / 2;
    return Math.PI * r * 2;
  }, [strokeWidth, width]);
  return (
    <div className={s.percent}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
      >
        <circle
          fill="transparent"
          className={s.piebg}
          strokeWidth={strokeWidth}
          cx={width / 2}
          cy={height / 2}
          r={(width - strokeWidth) / 2}
        ></circle>
        <circle
          fill="transparent"
          className={s.piebar}
          strokeWidth={strokeWidth}
          cx={width / 2}
          cy={height / 2}
          r={(width - strokeWidth) / 2}
          strokeDashoffset={strokeDashoffset}
          strokeDasharray={strokeDasharray}
        ></circle>
      </svg>
    </div>
  );
}
