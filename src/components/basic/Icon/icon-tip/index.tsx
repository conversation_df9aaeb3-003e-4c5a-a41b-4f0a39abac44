import classNames from 'classnames';
import { ReactNode, SVGProps } from 'react';

import { Icon, Tooltip } from '@/components/basic';

import s from './index.less';
export type Props = {
  title?: ReactNode;
  className?: string;
  name?: string; //iconfont
  fontSize?: number;
  icon?: React.ReactNode; //图标组件
};

export default function IconTip(props: Props) {
  const { title, name, icon, ...resetProps } = props;
  return (
    <Tooltip title={title} overlayClassName="detail-item-tip">
      {name ? <Icon className={classNames(s.icon)} name={name} {...resetProps}></Icon> : icon}
    </Tooltip>
  );
}
