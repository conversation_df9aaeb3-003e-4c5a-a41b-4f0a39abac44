import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTaskUpdateCompleteConditionPost, apiTodoParticipantUpdatePost } from '@/api';
import { Modal } from '@/components/basic';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { Dispatch, RootState } from '@/models/store';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

export type Value = { list: UserInfo[]; count?: number };

export type Props = {
  taskId: number;
  className?: string;
  value: UserInfo[];
  showPercent?: boolean;
  oneAndMoreType?: OneAndMoreType;
  showFinishPercentTip?: boolean;
  maxShowCount?: number;
  // 指派者信息
  assigner?: UserInfo;
  // 只显示指派者 为 true 时，assigner 必填
  onlyAssigner?: boolean;
  // 指派者 id
  assignerUid: string;
  disabled?: boolean;
  count?: number; //分配人数
  hasArrow?: boolean;
  assigneeUids?: string[];
  followerUids?: string[];
  showAddIcon?: boolean;
  containerElement: HTMLElement;
  canEditCompleteCondition?: boolean;
};
import { useMemoizedFn } from 'ahooks';

import Executor from '@/components/basic/people-picker/executor-new';
import { useExecutorRemoveSelf } from '@/components/basic-task/use-executor-remove-self';
import {
  OneAndMoreServerParamsType,
  OneAndMoreType,
  PeoplePickerType,
  ViewType,
} from '@/utils/const';

import s from './index.less';

interface ILatestIds {
  /** 执行人是否已经更新 */
  updated: boolean;
  /** 执行人的原始数据生成的map，key是uid */
  srcAssigneeMap: Record<string, string> | null;
  /** 执行人原始数据，下拉打开时获取 */
  srcAssignees: UserInfo[];
  /** 执行人操作完成后要提交的uid */
  uids: string[];
}

const ListExecutorItem: React.FC<Props> = (props) => {
  const {
    value,
    taskId,
    assignerUid,
    showPercent,
    assigner,
    disabled,
    showAddIcon,
    hasArrow,
    oneAndMoreType,
    assigneeUids,
    followerUids,
    containerElement,
    canEditCompleteCondition,
  } = props;
  const [list, setList] = useState<UserInfo[]>([]);
  const { userInfo, currentViewTab } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    currentViewTab: state.viewSetting.currentViewTab,
  }));
  const [pickerVis, setPickerVis] = useState(false);
  const { removeSelf } = useExecutorRemoveSelf({
    className: s.confirm,
    taskId: taskId,
  });
  const ref = useRef<any>();
  const latestUidsRef = useRef<ILatestIds>({
    updated: false,
    srcAssigneeMap: null as Record<string, string> | null,
    srcAssignees: [] as UserInfo[],
    uids: [] as string[],
  });
  const executorRef = useRef<any>(null);

  const dispatch = useDispatch<Dispatch>();

  const uidsSubmit = () => {
    if (latestUidsRef.current.updated) {
      const uids = latestUidsRef.current.uids;
      if (typeof taskId === 'number') {
        return apiTodoParticipantUpdatePost({
          taskId: taskId,
          uids: uids,
        }).finally(async () => {
          latestUidsRef.current.updated = false;
          dispatch.detail.getTodoDetail(taskId).then((res) => {
            dispatch.task.updateTodoList(res);
          });
          if (currentViewTab.type) {
            if (currentViewTab.type === ViewType.list) {
              dispatch.task.updatePermissionsWithTask({ taskId });
            }
            dispatch.viewSetting.refreshDataByDataChange({
              refreshList: true,
              refreshCount: true,
            });
          }
        });
      }
    }
    return Promise.resolve();
  };

  const updateExecutor = (users: UserInfo[]) => {
    let newAddedUids: string[] = [];
    latestUidsRef.current.srcAssigneeMap =
      list?.reduce((pre, cur) => {
        if (cur.uid) {
          // @ts-ignore
          pre[cur.uid] = 1;
        }
        return pre;
      }, {}) || {};
    const newMap = { ...latestUidsRef.current.srcAssigneeMap };
    const uids: string[] = users.map((item) => {
      if (item.uid) {
        if (!newMap[item.uid]) {
          // 新增
          newAddedUids.push(item.uid);
        } else {
          delete newMap[item.uid];
        }
      }
      return item.uid!;
    });
    if (uids.length === 0 && list.length === 0) {
      return;
    }

    const deletedUids = Object.keys(newMap);
    latestUidsRef.current.uids = uids;
    if (!users.length) {
      if (latestUidsRef.current.srcAssignees.length) {
        latestUidsRef.current.updated = true;
      } else {
        latestUidsRef.current.updated = false;
      }
    } else {
      latestUidsRef.current.updated = newAddedUids.length > 0 || deletedUids.length > 0;
    }
  };
  const change = async (users: UserInfo[]) => {
    const oldSelf = list.findIndex((item) => item.uid === userInfo?.uid) > -1;
    const moveSelf = users.findIndex((item) => item.uid === userInfo?.uid) === -1;
    if (moveSelf && oldSelf) {
      removeSelf().then((persist) => {
        updateExecutor(users);
        if (!persist) {
          uidsSubmit().then(() => {
            executorRef.current?.toggleVisible(false);
            latestUidsRef.current.updated = false;
          });
          dispatch.detail.closeDetail({});
        }
      });
    } else {
      updateExecutor(users);
    }
    dispatch.detail.setTaskInfo({
      participants: users,
    });
  };

  const handleCancelUpdate = () => {
    change(latestUidsRef.current.srcAssignees);
    executorRef.current?.toggleVisible(false);
    latestUidsRef.current.updated = false;
  };

  const handleConfirmUpdate = () => {
    uidsSubmit();
    executorRef.current?.toggleVisible(false);
  };

  const onOneAndMoreChange = useMemoizedFn((val: number) => {
    // 调用更改任务完成方式接口逻辑
    taskId &&
      apiTaskUpdateCompleteConditionPost({
        taskId,
        completeCondition:
          val === OneAndMoreType.all
            ? OneAndMoreServerParamsType.all
            : OneAndMoreServerParamsType.one,
      }).then(() => {
        // 接口调用成功后，拉取更新日志
        dispatch.detail.getTodoDetail(taskId!);
        dispatch.viewSetting.refreshDataByDataChange({
          refreshList: true,
          refreshCount: false,
        });
      });
  });
  useEffect(() => {
    //TODO 判断被指派人是否有变化
    setList(value);
    if (!latestUidsRef.current.srcAssignees?.length) {
      latestUidsRef.current.srcAssignees = [...value];
    }
  }, [value]);

  const completedCount = useMemo(() => {
    return list.filter((item) => item.finished).length || 0;
  }, [list]);

  return (
    <div
      ref={ref}
      className={`people-picker-executor-item ${
        pickerVis && 'people-picker-executor-item-visible'
      }`}
    >
      <PeoplePickerContext.Provider
        value={{
          assignerUid: assignerUid,
          assigneeUids: assigneeUids,
          followerUids: followerUids,
          pickerType: PeoplePickerType.executor,
          currentUser: userInfo,
          canEditCompleteCondition: canEditCompleteCondition,
          isAssigneeUpdated: latestUidsRef.current.updated,
          onCancel: handleCancelUpdate,
          onConfirm: handleConfirmUpdate,
        }}
      >
        <Executor
          ref={executorRef}
          value={list}
          onChange={change}
          showPercent={showPercent}
          taskId={taskId}
          assigner={assigner}
          disabled={disabled}
          showAddIcon={showAddIcon}
          showCountIcon={false}
          hasArrow={hasArrow}
          oneAndMoreType={oneAndMoreType}
          assignerUid={assignerUid}
          setPickerVis={setPickerVis}
          finishPercentTip={
            <div className={s.finishPercentTip}>
              <div className={s.divider}></div>
              <div className={s.completeTip}>
                {oneAndMoreType === OneAndMoreType.all
                  ? I18N.template(I18N.auto.personCompleted_2, {
                      val1: `${completedCount}/${list.length}`,
                    })
                  : I18N.template(I18N.auto.humanExecution, { val1: list.length })}
              </div>
            </div>
          }
          onOneAndMoreChange={onOneAndMoreChange}
        ></Executor>
      </PeoplePickerContext.Provider>
    </div>
  );
};

export default ListExecutorItem;
