{"ppDir": "./.ppi18n", "sourceLang": "zh-CN", "languages": ["en-US", "ja-<PERSON>"], "mergeSameSource": true, "extension": ".ts", "baiduTranslateOption": {"baiduApiKey": {"appId": "20250327002316973", "appKey": "r6EJRzzxD9klYt3M5bhj"}, "QPS": 1, "baiduLangMap": {"zh-CN": "zh", "en-US": "en", "ja-JP": "jp"}}, "translateOrigin": "", "translateOptions": {"concurrentLimit": 10, "requestOptions": {}}, "defaultTranslateKeyApi": "Baidu", "importI18N": "import I18N from '@/utils/I18N';", "ignorePaths": [".umi", "api", "assets"]}