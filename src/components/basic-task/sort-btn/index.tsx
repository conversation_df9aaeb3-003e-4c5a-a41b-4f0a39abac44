import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Icon } from '@/components/basic';
import { OrderOption } from '@/types';
import { Order, OrderCustom } from '@/utils/const';

import s from './index.less';

export type Props = {
  className?: string;
  order: Order | undefined;
  active?: boolean;
  onChange?: (v: OrderOption) => void;
  title: string;
  icon: string;
  sortIndex: string | undefined;
};

const SortBtn: React.FC<PropsWithChildren<Props>> = (props) => {
  const { active, title, sortIndex, order, onChange } = props;
  const sort = () => {
    if (!onChange) {
      return;
    }
    // 默认
    if (sortIndex === undefined) {
      onChange({
        order: undefined,
        orderBy: undefined,
      });
      return;
    }
    //自定义
    if (sortIndex === OrderCustom) {
      onChange({
        order: Order.desc,
        orderBy: sortIndex,
      });
      return;
    }
    let _order = order;
    if (!active) {
      _order = undefined;
    }

    if (_order === Order.asc) {
      onChange({
        order: Order.desc,
        orderBy: sortIndex,
      });
    } else {
      onChange({
        order: Order.asc,
        orderBy: sortIndex,
      });
    }
  };
  return (
    <div className={s.item} onClick={sort}>
      <div className={classNames(s.label, { [s.active]: active })}>{title}</div>
      {sortIndex === OrderCustom && active && (
        <Icon name={'icon-sys_check'} className={classNames(s.activeCheck)} />
      )}
      <div
        className={classNames(
          s.orderBtn,
          'im-order-btn',
          { [s.hide]: sortIndex === OrderCustom || sortIndex === undefined },
          { [s.show]: active }
        )}
      >
        <div
          className={classNames(s.order, s.orderBtnDsce, {
            [s.active]: order === Order.desc && active,
          })}
          onClick={(e) => {
            e.stopPropagation();
            onChange?.({
              order: Order.desc,
              orderBy: sortIndex,
            });
          }}
        >
          <Icon name="icon-up"></Icon>
        </div>
        <div
          className={classNames(s.order, s.orderBtnAsce, {
            [s.active]: order === Order.asc && active,
          })}
          onClick={(e) => {
            e.stopPropagation();
            onChange?.({
              order: Order.asc,
              orderBy: sortIndex,
            });
          }}
        >
          <Icon name="icon-up"></Icon>
        </div>
      </div>
    </div>
  );
};

export default SortBtn;
