import classNames from 'classnames';
import { useState } from 'react';

import { IconBtn } from '@/components/basic';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';
import ProjectMoreOption from './more-option';
import { OperateAdd } from '@babylon/popo-icons';
import { Dispatch } from '@/models/store';
import { useDispatch } from 'react-redux';

interface Props {
  projectInfo: ProjectInfo;
}

const More: React.FC<Props> = (props) => {
  const { projectInfo } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const isGroup = projectInfo?.isGroup;

  const dispatch = useDispatch<Dispatch>();

  return (
    <div
      className={classNames('flex-y-center', 'operation__container', { more__visible: visible })}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <ProjectMoreOption projectInfo={projectInfo} onChangeVisible={setVisible}>
        <IconBtn
          // title={I18N.auto.more}
          iconName="icon-sys_more"
          className={classNames(s.more, 'todo-menu-more', { [s.open]: visible })}
          iconClassName={s.moreIcon}
        ></IconBtn>
      </ProjectMoreOption>
      {isGroup && (
        <IconBtn
          title={I18N.auto.newProject}
          icon={<OperateAdd className="fs-16" />}
          iconClassName={s.moreIcon}
          onClick={(e) => {
            e.stopPropagation();
            dispatch.project.gotoAddProject({ groupId: projectInfo.projectId });
          }}
        />
      )}
    </div>
  );
};
export default More;
