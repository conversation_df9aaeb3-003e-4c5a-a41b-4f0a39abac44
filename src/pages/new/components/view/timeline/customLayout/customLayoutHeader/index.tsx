import { VGroup, VImage, VText } from '@visactor/vtable';
import * as VTable from '@visactor/vtable';
import { BaseTableAPI } from '@visactor/vtable/es/ts-types/base-table';
import { FederatedPointerEvent } from '@visactor/vtable/es/vrender';

import { getVTableIcons } from '@/common/VTableIcons';

import { hideTooltip, mapValue, showTooltip } from '../../utils';
import I18N from '@/utils/I18N';

const showFoldTooltip = (table: any, col: number, row: number) => {
  const rect = table.getCellRect(col, row);

  const { top } = rect;

  const container = document.getElementById('gantt-view');
  const containerRect = container?.getBoundingClientRect() || { left: 0, top: 0 };
  const htmlFontSize = window.getComputedStyle(document.documentElement)?.fontSize;

  showTooltip(
    window.expandNodeSetMap.isAllExpand
      ? I18N.auto.collapseTasksOneClick
      : I18N.auto.expandTasksOneClick,
    16 + containerRect.left,
    top + containerRect.top - 6 - Math.round(mapValue(Number(htmlFontSize?.replace('px', ''))))
  );
};

export const createTaskHeader = (
  args: VTable.TYPES.CustomRenderFunctionArg<BaseTableAPI>,
  {
    dispatch,
    dataListRef,
    themeTokensRef,
  }: { dispatch?: any; dataListRef?: React.MutableRefObject<any[]>; themeTokensRef?: any } = {}
) => {
  const { table, row, col, rect, dataValue } = args;
  const { height, width } = rect || table.getCellRect(col, row);
  let hasGrouped = false;
  if (col === 0 && dispatch) {
    const { queryGroupBys } = dispatch.viewSetting.getComParams({});
    hasGrouped = queryGroupBys?.length > 0;
  }

  let isExpandAll = false;

  if (row === 0 && col === 0) {
    // 经测试只在展开收起等低频操作时才会触发，不会造成明显性能影响
    isExpandAll = window.expandNodeSetMap.isAllExpand;
  }

  const container = (
    <VGroup
      attribute={{
        width,
        height,
        display: 'flex',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'flex-end',
        alignContent: 'flex-end',
        id: 'container',
      }}
    >
      {col === 0 && hasGrouped && (
        <VGroup
          attribute={{
            width: 20,
            height: 20,
            boundsPadding: [0, 8, 0, 0],
            cornerRadius: 4,
          }}
          stateProxy={(stateName: string) => {
            if (stateName === 'hover') {
              return {
                background: themeTokensRef?.current?.['aBlack6'],
              };
            }
            return {};
          }}
          onMouseEnter={(event) => {
            event.currentTarget.addState('hover', true, false);
            event.currentTarget.stage.renderNextFrame();
          }}
          onMouseLeave={(event) => {
            event.currentTarget.removeState('hover', false);
            event.currentTarget.stage.renderNextFrame();
          }}
        >
          <VImage
            attribute={{
              id: 'status-icon',
              width: 20,
              height: 20,
              image: getVTableIcons(
                'fold-all',
                themeTokensRef.current._name,
                isExpandAll ? 'fold' : 'expand'
              ),
              boundsPadding: [0, 0, 4, 4],
              cursor: 'pointer',
            }}
            onMouseEnter={() => {
              showFoldTooltip(table, col, row);
            }}
            onMouseLeave={() => {
              hideTooltip();
            }}
            onClick={(e: FederatedPointerEvent, ...rest) => {
              // const isAllExpand = window.expandNodeSetMap.isAllExpand();

              window.expandNodeSetMap?.toggleAllHierarchyState();

              hideTooltip();
            }}
          ></VImage>
        </VGroup>
      )}

      <VText
        attribute={{
          id: 'title',
          text: dataValue,
          fontSize: 12,
          fontFamily: 'sans-serif',
          textAlign: 'left',
          textBaseline: 'top',
          fill: themeTokensRef?.current?.['TextSecondary-ongrey'],
          boundsPadding: [8, 8, 8, 4],
        }}
      ></VText>
    </VGroup>
  );
  return {
    rootContainer: container,
    renderDefault: false,
  };
};
