export const LinkRecognitionNodeSpec = {
  attrs: {
    href: { default: '' }, // 链接地址
    title: { default: null }, // 文档标题，也用作显示文本
    'data-docurl': { default: '' }, // 存储原始链接，与href可能相同
    'data-docicon': { default: '' }, // 文档图标路径
  },
  inline: true,
  group: 'inline',
  selectable: false,
  parseDOM: [
    {
      tag: 'a[href]', // 匹配具有 data-docurl 属性的 span 标签
      getAttrs: (domNode) => {
        const dom = domNode as HTMLElement; // 类型断言
        const docurl = dom.getAttribute('href');
        console.log('parseDOM', dom, docurl);
        return {
          href: docurl, // href 可以与 data-docurl 相同，或根据需要处理
          title: dom.textContent, // 使用span的文本内容作为title
          'data-docurl': docurl,
          'data-docicon':
            dom.querySelector('img')?.getAttribute('src') || dom.getAttribute('data-docicon'),
        };
      },
      priority: 92,
    },
    // 您可以添加更多的 parseDOM 规则来匹配其他可能的HTML结构
    // 例如，如果也想从普通的 <a> 标签解析（如果它有特定属性指示它是文档链接）
    // {
    //   tag: 'a[data-is-doclink]',
    //   getAttrs: (domNode) => { /* ... */ }
    // }
  ],
  // 定义如何将此标记渲染到DOM
  // 当编辑器内容转换为HTML时调用
  toDOM: (mark) => {
    const { href, title, 'data-docurl': docUrl, 'data-docicon': docIcon } = mark.attrs;
    // 返回一个数组，描述了要创建的DOM结构
    // ['tagname', {attributes}, ...children]
    return [
      'span',
      {
        'data-docurl': docUrl || href, // 确保 data-docurl 有值
        title: title, // hover时显示的title
        style: 'cursor: pointer; color: #007bff; text-decoration: underline;', // 添加一些基本样式
        // 您可能还想添加一个特定的类名，方便CSS选择和样式化
        // class: 'custom-doc-link'
      },
      // 子元素：图标 + 文本
      [
        'img',
        {
          src: docIcon,
          alt: 'doc',
          style: 'width: 16px; height: 16px; margin-right: 4px; vertical-align: middle;', // 示例图标样式
        },
      ],
      title || docUrl, // 显示的链接文本
    ];
  },
};
