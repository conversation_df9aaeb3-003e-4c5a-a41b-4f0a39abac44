.todoTable {
  height: 100%;
  width: 100%;
  .hidden {
    display: none;
  }
}

.addrow {
  display: flex;
  align-items: center;
  width: 100%;
  height: 36px;
  padding-left: 6px;
  color: var(--TextTertiary);
  cursor: pointer;
}
.hasCheckbox {
  padding-left: 28px;
}

:global {
  .todo-table {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .todo-tr {
      display: grid;
      &:hover {
        .todo-td {
          background-color: var(--N50);
        }
      }
      &.todo-tr-open {
        .todo-td {
          background-color: var(--Brand50) !important;
        }
      }

      .todo-th,
      .todo-td {
        border-bottom: 1px solid var(--aBlack6);
        border-right: 1px solid var(--aBlack6);
        padding: 7px 8px;
        text-align: center;
        display: flex;
        align-items: center;
        background-color: var(--bgBottom);
        &:nth-child(1) {
          // border-right: 0; //TODO 如果有多选的话 需要重新设置
          padding-top: 0;
          padding-bottom: 0;
          padding-left: 25px;
        }
        &:nth-child(2) {
          padding-top: 0;
          padding-bottom: 0;
        }
      }
      .todo-th {
        transition: background-color ease 0.25s;
        &:nth-child(1) {
          padding-left: 26px;
        }
      }
      .todo-td {
        padding: 0;
      }
    }
    .todo-drag-tr {
      .todo-drag {
        display: none;
      }
      &:hover {
        .todo-drag {
          display: block;
        }
      }
    }
    .todo-tr-disabled {
      //pointer-events: none;
      cursor: pointer;
    }

    .todo-td-right-no-border {
      border-right-width: 0 !important;
    }
    .todo-td-left-no-border {
      border-left-width: 0 !important;
    }
    .todo-td-fixed-left {
      position: sticky;
      left: 0;
      z-index: 2;
    }
    .todo-td-fixed-right {
      position: sticky;
      right: 0;
      z-index: 2;
      border-left: 1px solid var(--aBlack6);
      margin-left: -1px;
    }
  }
  .todo-table-has-select {
    .todo-tr {
      .todo-th,
      .todo-td {
        &:nth-child(1) {
          border-right: 0;
        }
      }
    }
  }
  .todo-thead {
    flex-shrink: 0;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE and Edge */
    &::-webkit-scrollbar {
      height: 0px;
    }
    /* WebKit (Safari/Chrome) */
    ::-webkit-scrollbar {
      display: none;
    }
    .todo-th {
      height: 36px;
      border-top: 1px solid var(--aBlack6);
      color: var(--TextSecondary);
      font-size: 12px;
      border-bottom: 1px solid var(--aBlack6);
      // margin-bottom: -1px;
      &:hover {
        .todo-sort {
          display: flex;
        }
      }
    }
  }
  .todo-tbody {
    position: relative;
    width: 100%;
    flex: 1;
    // :global {
    //   .scroll-content-group {
    //     overflow-x: auto;
    //   }
    // }
  }
}
