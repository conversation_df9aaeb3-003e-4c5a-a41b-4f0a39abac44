export const enum InsertPosition {
  before = 'before',
  after = 'after',
  inside = 'inside',
}

interface IGetInsertPositionParams {
  /** 被over的数据 */
  overItemData?: any;
  /** 当前数据Id，用来和被over的数据匹配，如果相等，则代表当前数据被over */
  currentId?: number;
  /** 当前数据索引，用来和被激活数据index（activeIndex）对比，如果currentIndex > activeIndex,代表被激活数据要插入到当前数据后面；反之，插入到前面 */
  currentIndex: number;
  /** 被激活数据索引 */
  activeIndex: number;
}
export const getInsertPosition = ({
  overItemData,
  currentId,
  currentIndex,
  activeIndex,
}: IGetInsertPositionParams) => {
  if (!overItemData) {
    return void 0;
  }
  if (overItemData?.id === currentId && currentIndex !== activeIndex) {
    return Number(currentIndex) > Number(activeIndex)
      ? InsertPosition.after
      : InsertPosition.before;
  }
  return void 0;
};
