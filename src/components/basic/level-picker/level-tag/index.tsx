import classNames from 'classnames';
import { PropsWithChildren, useMemo } from 'react';

import { Icon } from '@/components/basic';
import { LevelSelectOptionsMap } from '@/components/basic/level-picker/utils';

import s from './index.less';
export type Props = {
  className?: string;
  value: number;
};
const LevelTag: React.FC<PropsWithChildren<Props>> = (props) => {
  const { value, className } = props;
  const levelMap = LevelSelectOptionsMap;

  const item = useMemo(() => {
    return levelMap[value] || {};
  }, [levelMap, value]);

  if (!value) {
    return null;
  }
  return (
    <div className={classNames(item.className, 'com-level-bg', s.levelTag, className)}>
      <Icon className={classNames(s.levelIcon, 'com-level-icon')} name={item.IconName}></Icon>
      <span className={classNames(s.name, 'com-level-label')}>{item.name}</span>
    </div>
  );
};

export default LevelTag;
