.item {
  display: flex;
  padding: 8px 10px;
  margin-bottom: 4px;
  border: 1px solid var(--aBlack6);
  border-radius: 6px;
  &:hover {
    background-color: var(--aBlack6);
  }
  cursor: grabbing;
  .todoStr {
    margin-left: 8px;
    line-height: 18px;
    font-size: 13px;
    color: var(--TextPrimary);
    word-break: break-word;
  }
  .status {
    display: flex;
    align-items: center;
    height: 18px;
    // padding-top: 3px;
    color: var(--IconTertiary);
    cursor: pointer;
  }
}
