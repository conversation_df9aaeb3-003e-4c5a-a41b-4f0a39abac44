// eslint-disable-next-line simple-import-sort/imports
import { Tooltip as BedrockTooltip } from '@bedrock/components';
import { TooltipPropsWithTitle, TooltipPropsWithOverlay } from '@bedrock/components/es/Tooltip';
import './index.less';
import { useMemo, useState } from 'react';
import { useControllableValue } from 'ahooks';

interface TitleProps extends TooltipPropsWithTitle {
  onMouseEnter?: (e: any) => void;
  onlyEllipsis?: boolean;
}
interface OverlayProps extends TooltipPropsWithOverlay {
  onMouseEnter?: (e: any) => void;
  onlyEllipsis?: boolean;
}

type Props = TitleProps | OverlayProps;

export default function Tooltip(props: Props) {
  const { onMouseEnter, onlyEllipsis, title, disabled, ...setProps } = props;
  const [visible, setVisible] = useControllableValue<boolean>(props, {
    valuePropName: 'visible',
    trigger: 'onVisibleChange',
  });
  const [canShowTooltip, setCanShowTooltip] = useState<boolean>(false);

  function showToolTip(e) {
    if (onlyEllipsis) {
      if (
        e.target.clientWidth >= e.target.scrollWidth &&
        e.target.clientHeight >= e.target.scrollHeight
      ) {
        setCanShowTooltip(false);
      } else {
        setCanShowTooltip(true);
      }
    }
    onMouseEnter?.(e);
  }

  const memoShowTooltip = useMemo(() => {
    if (onlyEllipsis) {
      return visible && canShowTooltip;
    }
    return visible;
  }, [visible, canShowTooltip, onlyEllipsis]);
  //组件库部分版本有异常, title为空字符的时候会继续展示
  const memoDisabled = useMemo(() => {
    return disabled || !title;
  }, [disabled, title]);
  return (
    <BedrockTooltip
      {...setProps}
      title={title}
      disabled={memoDisabled}
      onMouseEnter={showToolTip}
      visible={memoShowTooltip}
      onVisibleChange={setVisible}
    ></BedrockTooltip>
  );
}
