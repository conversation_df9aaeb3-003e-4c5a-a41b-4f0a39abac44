import { ViewGantt } from '@babylon/popo-icons';
import classNames from 'classnames';

import Icon from '@/components/basic/Icon';
import I18N from '@/utils/I18N';

import s from './index.less'; // 引入样式文件

interface ViewModeProps {
  setActiveMode: React.Dispatch<React.SetStateAction<number>>;
  activeMode: number;
}

function ViewMode(props: ViewModeProps) {
  const { activeMode, setActiveMode } = props;

  const itemList = [
    {
      name: I18N.auto.list,
      iconName: 'icon-view_list_line',
      key: 1,
    },
    {
      name: I18N.auto.bulletinBoard,
      iconName: 'icon-view_kanban_line',
      key: 2,
    },
    {
      name: I18N.auto.calendar,
      iconName: 'icon-view_calendar_line',
      key: 3,
    },
    {
      name: I18N.auto.timeline,
      iconName: <ViewGantt />,
      key: 4,
    },
  ];

  return (
    <div className={s.viewModeWrapper}>
      {itemList.map((item) => (
        <div
          className={classNames(s.item, {
            [s.activeItem]: activeMode === item.key,
          })}
          key={item.key}
          onClick={() => {
            setActiveMode(item.key);
          }}
        >
          <Icon name={item.iconName} fontSize={16} />
          <div className={s.name}>{item.name}</div>
        </div>
      ))}
    </div>
  );
}

export default ViewMode;
