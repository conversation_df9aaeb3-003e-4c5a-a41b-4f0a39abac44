.timeline {
}

.desc {
  display: inline;
  font-size: 12px;
  color: var(--TextPrimary);
}

.lineThrough {
  text-decoration-line: line-through;
  color: var(--TextTertiary);
}

.message {
  color: var(--IconTertiary);
}
.newMessage {
  color: var(--Brand400);
}
.newMessageTips {
  font-weight: 400;
  font-size: 11px;
  line-height: 16px;
  color: var(--Brand600);
  margin-left: 4px;
}
.completed {
  :global {
    .rock-timeline-item__head__dot--pending {
      border-color: var(--TasksDone);
    }
  }
}

.completedLabel {
  color: var(--TasksDone);
}
.completedProgress {
  width: 12px;
  height: 12px;
  :global {
    .rock-progress-inner {
      width: 12px !important;
      height: 12px !important;
    }
    .rock-progress-circle-trail {
      stroke: var(--IconQuartus) !important;
      // stroke-width: 12.5;
    }
    .rock-progress-inner:not(.rock-progress-circle-gradient) .rock-progress-circle-path {
      stroke: var(--TasksDone);
      stroke-linecap: butt;
      // stroke-width: 12.5;
    }
  }
}
.operationIcon {
  width: 18px;
  height: 18px;
}
.completedIcon {
  color: var(--G500);
}
.defaultIcon {
  line-height: unset;
  color: var(--IconTertiary);
}

.avatar {
  width: 24px;
  height: 24px;
  user-select: none;
}
