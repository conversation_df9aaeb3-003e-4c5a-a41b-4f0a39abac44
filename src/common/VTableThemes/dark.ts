import { ITableThemeDefine } from '@visactor/vtable/es/ts-types';

import { darkTokens } from '../ColorTokens';

const {
  bgMiddle,
  aBlack8,
  aBlack6,
  'TextPrimary-strong': TextPrimaryStrong,
  'TextSecondary-ongrey': TextSecondaryOngrey,
  absWhite,
  admintoastBg,
} = darkTokens;

/**
 * dark theme
 * @name DARK
 * @memberof VTable.themes.DARK
 */
export default {
  name: 'DARK',
  underlayBackgroundColor: 'transparent',
  defaultStyle: {
    bgColor: bgMiddle,
    hover: {
      inlineRowBgColor: aBlack6,
    },
  },
  frameStyle: {
    borderLineWidth: 0,
    innerBorder: false,
  },
  headerStyle: {
    borderColor: aBlack8,
    borderLineWidth: [0, 0, 1, 0],
    fontWeight: 400,
    fontSize: 13,
    color: TextSecondaryOngrey,
    bgColor: 'transparent',
    hover: {
      cellBgColor: 'transparent',
    },
  },
  bodyStyle: {
    borderColor: aBlack8,
    borderLineWidth: (args) => {
      const { row } = args;
      if (row === 1) {
        return [0, 0, 1, 0];
      }
      return [1, 0, 0, 0];
    },
    fontSize: 16,
    color: TextPrimaryStrong,
    bgColor: 'transparent',
    hover: {
      cellBgColor: 'transparent',
      inlineRowBgColor: 'transparent',
    },
  },
  selectionStyle: {
    cellBorderLineWidth: 0,
    selectionFillMode: 'replace',
  },
  // 文本溢出tooltip style
  textPopTipStyle: {
    contentStyle: {
      fill: absWhite,
      fontSize: 12,
    },
    position: 'tl',
    padding: {
      top: 8,
      right: 12,
      bottom: 8,
      left: 12,
    },
    dx: 22,
    panel: {
      fill: admintoastBg,
      cornerRadius: 4,
      // background: 'rgba(0, 0, 0, 0.8)',
    },
  },
} as unknown as ITableThemeDefine;
