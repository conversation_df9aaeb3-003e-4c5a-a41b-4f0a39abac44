import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { debounce } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTaskCreatePost, ApiTaskCreatePostRequest } from '@/api';
import { Button, Divider, Icon, IconBtn, IconTip, Message } from '@/components/basic';
import { TimeValue } from '@/components/basic/popo-date-picker';
import { Dispatch, RootState } from '@/models/store';
import { isProjectId } from '@/models/utils';
import { UserInfo } from '@/types';
import { EnumCalendarAddSource, ViewAddSource } from '@/utils/const';
import I18N from '@/utils/I18N';
import { Platform } from '@/utils/platform';
import { EnumTrackeKey } from '@/utils/skyline';

import { AddDeadlineItem, AddExecutorItem, AddLevelItem, AddTitleItem } from './components';
import s from './index.less';
import RenderItem from './render-item';

export type Props = {
  className?: string;
  onClose?: () => void;
  onAddSuccess?: (opt: { hasDeadline: boolean }) => void;
  date?: Dayjs;
  assignees?: UserInfo[];
};

const AddDropdown: React.FC<Props> = (props) => {
  const { onClose, date, onAddSuccess, assignees } = props;
  const [title, setTitle] = useState<string>('');
  const [assignor, setAssignor] = useState<UserInfo>({});
  const [executorList, setExecutorList] = useState<UserInfo[]>(assignees || []);
  const [level, setLevel] = useState<number>(0); // 无优先级默认值是0
  const [dateTime, setDateTime] = useState<TimeValue>(); //
  const [remindTime, setRemindTime] = useState<TimeValue>(); //
  const { userInfo, navigatorId } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    navigatorId: state.viewSetting.navigatorId,
  }));

  const loadingRef = useRef<boolean>(false);
  const [createTime] = useState<Dayjs>(dayjs()); //
  const dispatch = useDispatch<Dispatch>();
  useEffect(() => {
    if (date) {
      setDateTime({
        time: date?.valueOf(),
        timeFormat: 1,
        rrule: '',
      });
    }
  }, [date]);
  useEffect(() => {
    if (userInfo?.uid) {
      setAssignor(userInfo);
    }
  }, [userInfo]);
  const onConfirm = () => {
    if (title.trim() === '') return;
    //@ts-ignore
    let params: ApiTaskCreatePostRequest = {};
    loadingRef.current = true;
    const { time, timeFormat, rrule } = dateTime || {};
    params = {
      deadline: time || 0,
      deadlineFormat: timeFormat,
      rrule,
      title: title.trim(), //标题
      assigner: assignor.uid,
      assignees: executorList.map((item) => item.uid!), //被指派人
      priority: level, //优先级
      source: 0,
      sourceSubType: 0,
      //@ts-ignore
      timezone: dayjs.tz.guess(),
    };
    if (remindTime?.time) {
      const { time, rrule, selectedOption } = remindTime;
      params.alarm = {
        alarmCreateTime: createTime.valueOf(),
        alarmRrule: rrule,
        alarmTimestamp: time,
        selectedOption,
      };
    } else {
      params.alarm = {
        alarmCreateTime: createTime.valueOf(),
        selectedOption: 'NONE',
      };
    }
    if (isProjectId(navigatorId)) {
      params.projectId = navigatorId as unknown as number;
    }
    apiTaskCreatePost(params)
      .then((res) => {
        let key = EnumTrackeKey.ListNewGlobalCreate;
        if (ViewAddSource.calendar === EnumCalendarAddSource.calendar) {
          key = EnumTrackeKey.CalendarNewCalendarCreate;
        } else if (ViewAddSource.calendar === EnumCalendarAddSource.unplanned) {
          key = EnumTrackeKey.CalendarNewUnplannedCreate;
        }
        // 日历新建埋点
        dispatch.user.trackingByView({
          key: key,
          taskId: res.taskId,
        });
        onAddSuccess?.({
          hasDeadline: !!params.deadline,
        });
        Message.success({
          content: (
            <div className={s.info}>
              <Icon name="icon-taskstate_nodownhover" fontSize={20} className={s.infoIcon}></Icon>
              {I18N.auto.newSuccessfully}
              <Divider type="vertical" className={s.divider}></Divider>
              <span
                className={s.view}
                onClick={(e) => {
                  e.stopPropagation(); // 不要触发详情的outclick事件
                  dispatch.detail.openDetail(res.taskId!);
                }}
              >
                {I18N.auto.viewDetails}
              </span>
            </div>
          ),

          duration: 2,
          className: s.successMessage,
        });
        onClose?.();
      })
      .finally(() => {
        loadingRef.current = false;
        dispatch.viewSetting.refreshDataByDataChange({
          refreshCount: true,
          refreshList: false,
        });
      });
  };
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const modifierKey = Platform.MAC ? event.metaKey : event.ctrlKey;

      if (event.key === 'Enter' && modifierKey && !event.shiftKey && !event.altKey) {
        event.preventDefault();
        debounce(onConfirm, 300)();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  return (
    <div className={s.add}>
      <div className={s.head}>
        <IconBtn iconName="icon-close" className={s.cloase} onClick={onClose}></IconBtn>
      </div>
      <div className={s.body}>
        <div className={s.addTitle}>
          <AddTitleItem value={title} onChange={setTitle}></AddTitleItem>
        </div>
        <RenderItem
          leftRender={
            <div className={s.leftTip}>
              <IconTip
                className={s.icon}
                name="icon-tage_implement_line"
                title={I18N.auto.assignee}
              ></IconTip>
              <div className={s.text}>{I18N.auto.assignedTo}</div>
            </div>
          }
        >
          <AddExecutorItem
            assignerUid={assignor.uid}
            value={executorList}
            onChange={(v) => {
              setExecutorList(v);
            }}
          ></AddExecutorItem>
        </RenderItem>
        <RenderItem
          leftRender={
            <div className={s.leftTip}>
              <IconTip
                className={s.icon}
                name="icon-details_data_calendar"
                title={I18N.auto.deadline}
              ></IconTip>
              <div className={s.text}>{I18N.auto.time_2}</div>
            </div>
          }
          showDeleteIcon={!!dateTime}
          onDelete={() => {
            setDateTime(undefined);
          }}
        >
          <AddDeadlineItem value={dateTime} onChange={setDateTime}></AddDeadlineItem>
        </RenderItem>
        <RenderItem
          leftRender={
            <div className={s.leftTip}>
              <IconTip
                className={s.icon}
                name="icon-details_data_redio"
                title={I18N.auto.priority}
              ></IconTip>
              <div className={s.text}>{I18N.auto.priority}</div>
            </div>
          }
          showDeleteIcon={!!level}
          onDelete={() => {
            setLevel(0);
          }}
        >
          <AddLevelItem value={level} onChange={setLevel}></AddLevelItem>
        </RenderItem>
        {/* <RenderItem
            leftRender={
              <IconTip
                className={s.icon}
                name="icon-pc_details_remind"
                title={I18N.auto.remind}
              ></IconTip>
            }
            showDeleteIcon={!!remindTime}
            onDelete={() => {
              setRemindTime(undefined);
            }}
           >
            <AddRemindItem value={remindTime} onChange={setRemindTime} />
           </RenderItem> */}
      </div>
      <div className={s.footer}>
        <Button className={classNames(s.btn, s.cancel, 'mr-20')} onClick={onClose}>
          {I18N.auto.cancel}
        </Button>
        <Button
          className={classNames(s.btn, s.confirm)}
          type="primary"
          onClick={debounce(onConfirm, 500)}
          disabled={!title}
        >
          {I18N.auto.determine}
        </Button>
      </div>
    </div>
  );
};

export default AddDropdown;
