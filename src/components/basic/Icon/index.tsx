import classNames from 'classnames';
import React, { isValidElement, ReactElement } from 'react';

import s from './index.less';
export type Props = {
  className?: string;
  fontSize?: number;
  name: string | ReactElement;
  onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  color?: string;
  style?: React.CSSProperties;
};
export default function Icon(props: Props) {
  const { className, name, fontSize = 16, onClick, color, style = {} } = props;

  if (typeof name === 'string') {
    return (
      <i
        {...props}
        style={{ color: color, ...style }}
        className={classNames('icon iconfont', name, s.icon, s[`icon-${fontSize}`], className)}
        onClick={(e) => {
          onClick?.(e);
        }}
      ></i>
    );
  }
  if (isValidElement(name)) {
    return React.cloneElement(name, {
      ...props,
      onClick,
      style: { color, ...style },
      className: classNames('icon iconfont', s.icon, s[`icon-${fontSize}`], className),
    });
  }
  return null;
}
