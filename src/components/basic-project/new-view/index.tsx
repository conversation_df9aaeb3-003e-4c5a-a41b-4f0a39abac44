import { SystemInfo } from '@babylon/popo-icons';
import { Form, Input } from '@bedrock/components';
import classNames from 'classnames';
import React, {
  PropsWithChildren,
  ReactElement,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import {
  apiProjectUpdateMemberPost,
  apiProjectViewCreatePost,
  ApiProjectViewCreatePostRequest,
} from '@/api';
import { getBatchViewCheckParams } from '@/api-common';
import { Icon, Message } from '@/components/basic';
import Modal from '@/components/basic/modal';
import Select from '@/components/basic/select';
import Tooltip from '@/components/basic/tooltip';
import { Dispatch, RootState } from '@/models/store';
import { Member, ViewTab } from '@/types';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission, ViewTypeEnum } from '@/utils/permission';

import s from './index.less';
import ViewMode from './view-mode';
import { ViewType } from '@/utils/const';

interface Props {
  disabled?: boolean;
  className?: string;
  onChange?: (v: Member[]) => void;
}

const NewView: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { projectInfo, permissions, viewTabList } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo!,
    permissions: state.viewSetting.permissions,
    viewTabList: state.viewSetting.viewTabList,
  }));
  const inputRef = useRef<HTMLInputElement>();
  const CAN_CREATE_COMMON_VIEW = validatesPermission({
    permissions: permissions,
    key: ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
  });

  const dispatch = useDispatch<Dispatch>();
  const [inputValue, setInputValue] = useState('');

  const [form] = Form.useForm();
  const [activeMode, setActiveMode] = useState(1); // 默认选中列表
  const _param = dispatch.viewSetting.getComParams({});

  const [commonViewExceed, personalViewExceed] = useMemo(() => {
    const commonViewCount = viewTabList.filter(
      (view) => view.viewDistribution !== ViewTypeEnum.Personal
    ).length;
    const personalViewCount = viewTabList.length - commonViewCount;
    return [commonViewCount >= 20, personalViewCount >= 20];
  }, [viewTabList]);

  const options = [
    {
      name: I18N.auto.personalView,
      value: ViewTypeEnum.Personal,
      subText: I18N.auto.onlyYouCanDoItYourself,
      disabled: personalViewExceed,
    },
    {
      name: I18N.auto.publicView_2,
      value: ViewTypeEnum.Common,
      subText: I18N.auto.allMembersAre,
      disabled: commonViewExceed,
    },
  ];

  const activeSelectOption = personalViewExceed ? ViewTypeEnum.Common : ViewTypeEnum.Personal;

  const onSubmit = () => {
    const payload: ApiProjectViewCreatePostRequest = {
      projectId: projectInfo.projectId!,
      viewType: activeMode,
      viewDistribution: form.getFieldValue('viewDistribution'),
      name: inputValue,
      navigatorId: String(_param.navigatorId),
    };

    // 时间线视图默认按照截止时间正序
    if (activeMode === ViewType.timeline) {
      payload.querySort = { fieldName: 'deadline', order: 'asc' };
    }

    // 更新接口
    apiProjectViewCreatePost(payload)
      .then(async (res) => {
        // TODO 将item塞入viewTabList中，之后调用batchCheck接口获取当前用户对于这个item的编辑删除权限数据
        let currentViewList = [{ ...res, type: res.viewType, id: res.viewId }];
        const viewPermissions = await getBatchViewCheckParams(currentViewList);
        currentViewList.forEach((item) => {
          // @ts-ignore
          item.permissions = viewPermissions[item.viewId!].permissions;
        });
        dispatch.viewSetting.setViewTabList([
          ...(viewTabList as ViewTab[])!,
          ...(currentViewList as ViewTab[]),
        ]);

        dispatch.viewSetting.toggeleView(currentViewList[0] as ViewTab);
        Message.success(I18N.auto.saveViewAs);
      })
      .finally(() => {
        setVisible(false);
      });
  };

  useEffect(() => {
    return () => {
      form.resetFields();
      setInputValue('');
      setActiveMode(1);
      inputRef.current?.focus();
    };
  }, [form, personalViewExceed, visible]);

  return (
    <>
      {React.cloneElement(children as unknown as ReactElement, {
        onClick: () => {
          if (!disabled) {
            setVisible(!visible);
          }
        },
      })}
      <Modal
        visible={visible}
        width={400}
        title={I18N.auto.addView}
        okText={I18N.auto.determine}
        closable
        centered
        onCancel={() => {
          setVisible(false);
        }}
        okButtonProps={{ disabled: !inputValue }}
        onOk={onSubmit}
        scrolled={false}
        wrapClassName={s.modal}
      >
        <Form form={form}>
          <Form.Item label={I18N.auto.viewName} name="name">
            <Input
              ref={inputRef}
              placeholder={I18N.auto.enterViewName}
              autoFocus
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
              }}
            />
          </Form.Item>
          <Form.Item label={I18N.auto.viewMode} name="mode">
            <ViewMode activeMode={activeMode} setActiveMode={setActiveMode} />
          </Form.Item>
          <Form.Item
            label={I18N.auto.viewRange}
            name="viewDistribution"
            initialValue={activeSelectOption}
          >
            {CAN_CREATE_COMMON_VIEW ? (
              <Select
                options={options}
                dropdownClassName={s.viewModeSelect}
                renderLabel={(option) => {
                  const isCommon = option.value === ViewTypeEnum.Common;
                  const disabledTitle =
                    (isCommon && commonViewExceed) || (!isCommon && personalViewExceed)
                      ? I18N.auto.thisTypeHasReached
                      : '';
                  return (
                    <Tooltip title={disabledTitle}>
                      <div
                        className={classNames(s.option, {
                          [s.disabledOption]: disabledTitle,
                        })}
                      >
                        <div className={s.text}>{option.name}</div>
                        <div className={s.subText}>{option.subText}</div>
                      </div>
                    </Tooltip>
                  );
                }}
              />
            ) : (
              <div className={s.personalViewTip}>
                <span className={s.tip}>{I18N.auto.personalView}</span>
                <Tooltip title={I18N.auto.thisViewIsFor}>
                  <SystemInfo className="fs-16" />
                </Tooltip>
              </div>
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default NewView;
