import React from 'react';
import { useDispatch } from 'react-redux';

import { Dispatch } from '@/models/store';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
};
const BatchEsc: React.FC<Props> = (props) => {
  const { className } = props;
  const dispatch = useDispatch<Dispatch>();
  const onClick = () => {
    dispatch.batch.setBatchMode(false);
  };

  return (
    <BatchItem
      className={s.batchEsc}
      name={I18N.auto.exitEsc}
      iconName="icon-close"
      onClick={onClick}
    ></BatchItem>
  );
};

export default BatchEsc;
