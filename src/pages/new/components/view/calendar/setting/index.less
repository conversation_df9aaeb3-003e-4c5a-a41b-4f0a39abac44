.list {
  background: var(--bgBottom);
}
.descTitle {
  padding: 14px 12px 8px 12px;
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 22px;
  font-weight: bold;
}
.back {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 208px;
  height: 38px;
  padding: 0 12px;
  border-radius: 4px;
  &:hover {
    background-color: var(--aBlack10);
  }
  .itemLeft {
    display: flex;
    align-items: center;

    .fieldName {
      font-size: 13px;
      color: var(--TextPrimary);
    }
  }
  .icon {
    margin-right: 4px;
    color: var(--IconTertiary);
  }

  :global {
    .rock-switch {
      width: 28px;
      min-width: 28px;
      height: 16px;
    }
    .rock-switch-input {
      height: 14px;
    }
    .rock-switch-thumb {
      width: 12px;
      height: 12px;
      border-radius: 12px;
    }
    .rock-switch-checked .rock-switch-thumb {
      left: calc(100% - 14px);
    }
  }
}
