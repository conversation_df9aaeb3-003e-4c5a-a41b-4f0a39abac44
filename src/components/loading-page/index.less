.loading {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10000000;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding-bottom: 20px;
  overflow: hidden;
  .head {
    width: 100%;
    flex-shrink: 0;
    height: 82px;
    padding: 16px 20px 14px 20px;
    border-bottom: 1px solid var(--aBlack6);
    .name {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .icon {
        width: 28px;
        height: 28px;
        margin-right: 12px;
      }
    }
  }
  .options {
    width: 100%;
    flex-shrink: 0;
    height: 56px;
    padding: 14px 20px;
  }
  .list {
    flex: 1;
    padding: 0 20px;
    min-height: 0;
    overflow: hidden;
    .item {
      display: flex;
      align-items: center;
      height: 44px;
      border-bottom: 1px solid var(--aBlack6);
      .avatar {
        height: 16px;
        width: 16px;
        border-radius: 16px;
        margin-right: 16px;
      }
    }
  }
  .text1 {
    width: 101px;
    height: 12px;
  }
  .text2 {
    width: 60px;
    height: 12px;
    max-height: 12px;
    flex-shrink: 0;
  }
  .text3 {
    width: 92px;
    height: 28px;
  }
}
