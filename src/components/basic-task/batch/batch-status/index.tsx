import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTodoBatchFinishPost, apiTodoBatchRebuildPost } from '@/api';
import { Message } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { Finished } from '@/utils/const';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
  disabled?: boolean;
};
const BatchStatus: React.FC<Props> = (props) => {
  const { disabled } = props;
  const { batchList } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onClick = (finished: boolean) => {
    const taskIds = batchList.map((item) => item.taskId!);
    // 请求批量操作
    if (finished) {
      apiTodoBatchRebuildPost({ taskIds: taskIds }).then(() => {
        dispatch.viewSetting.refreshDataByDataChange({
          refreshList: true,
          refreshCount: false,
        });
        dispatch.batch.setBatchList([]);
      });
    } else {
      apiTodoBatchFinishPost({ taskIds: taskIds }).then((res) => {
        const { failed } = res;
        if (failed && failed.length) {
          Message.text(I18N.template(I18N.auto.toDoCannotBeDone, { val1: failed.length }));
        }
        dispatch.viewSetting.refreshDataByDataChange({
          refreshList: true,
          refreshCount: false,
        });
        dispatch.batch.setBatchList([]);
        dispatch.batch.setBatchMode(false);
      });
    }
    //刷新左侧标签列表、标签数量、当前列表数据
  };
  const isAllFinished = useMemo(() => {
    return batchList.every((item) => item.selfFinished === Finished.finished) && !disabled;
  }, [batchList, disabled]);

  return (
    <BatchItem
      disabled={disabled}
      name={isAllFinished ? I18N.auto.markingIncomplete : I18N.auto.markingCompleted}
      iconName={isAllFinished ? 'icon-taskstate_nodown' : 'icon-taskstate_nodownhover'}
      onClick={() => {
        if (!disabled) {
          onClick(isAllFinished);
        }
      }}
    ></BatchItem>
  );
};

export default BatchStatus;
