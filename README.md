# POPO PC 新版本待办

## 项目简介

umijs4.0 为基础

[交接文档](https://docs.popo.netease.com/lingxi/82e219b8a5de4e2fa3f140cbb98c09b7?xyz=1740477924304&appVersion=4.29.0&deviceType=0&popo_hidenativebar=1&popo_noindicator=1&disposable_login_token=1&xyz=1740539381378#edit)

### 启动命令

```bash
pnpm install

npm run start
```

### 同步最新的 api

```bash
npm run ytt
```

### 查看打包后的代码体积依赖关系

```bash
npm run analyz
```

### 提取国际化文案

```bash
ppi18n extract -d src -p auto
```

[国际化翻译资源平台](https://i18n.popo.netease.com/my-project)

### 手动创建 sentry 的 releases

### sentry 服务已下架

sentry-cli releases -o netease -p popo-todo-fe new production_2023101901

sentry-cli releases -o netease -p popo-todo-fe files production_2023101901 upload-sourcemaps dist --url-prefix "~/" --validate dist

### 图标使用

老方案图标在 iconfont 上 https://www.iconfont.cn/manage/index?spm=a313x.manage_type_myicons.i1.db775f1f3.793a3a81bLAec2&manage_type=myprojects&projectId=3915461

新方案 采用直接加载图标库@babylon/popo-icons 设计有更新的时候执行 pnpm i @babylon/popo-icons@latest 具体用那个图标请查看设计稿 以及图标展示地址https://open-platform.doc.nie.netease.com/popo-icons/

### 消息卡片

- [测试免登](https://test-ncc.popo.netease.com:9004/fe/v1/auth/t/login?email=<EMAIL>)
- [测试地址](https://test-ncc.popo.netease.com:9004/blitz/?status=ONLINE)
- [线上地址](https://open-dev.popo.netease.com/blitz)
