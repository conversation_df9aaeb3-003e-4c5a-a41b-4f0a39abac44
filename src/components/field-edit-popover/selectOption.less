.label {
  margin-bottom: 4px;
  :global {
    .rock-form-item-input-content {
      flex-direction: column;
    }
  }
}

.option {
  display: flex;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
  font-size: 16px;
  padding: 2px;
  color: var(--IconSecondary);
  flex: none;

  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background-color: var(--aBlack6);
  }
}

.sortIconWrapper {
  width: 20px;
  height: 20px;
}
.sortIcon {
  color: var(--IconSecondary);
  font-size: 16px;
  cursor: grab;
  flex-shrink: 0;
}

.input {
  flex: 1 1 auto;
  outline: none;
  padding: 3px 5px !important;
  font-size: 13px;
  margin-left: 8px;
  margin-right: 8px;

  :global {
    .rock-input {
      font-size: 13px;
    }
  }
}

.addBtn {
  align-self: flex-start;
  margin-top: 2px;
}

.formItem {
  margin-bottom: 10px;
}

.active {
  &::after {
    background-color: #00000033;
  }
}

.color__cube {
  position: relative;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  color: var(--IconPrimary);
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background-color: transparent;
    transition: background-color 0.2s ease-in-out;
  }

  &:hover {
    .active;
  }

  &:global {
    &.rock-popover-open {
      .active;
    }
  }
}
