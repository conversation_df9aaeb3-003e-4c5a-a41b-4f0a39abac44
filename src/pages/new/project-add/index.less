.addPage {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  .head,
  .footer {
    height: 56px;
    width: 100%;
    flex-shrink: 0;
    padding: 18px 20px 10px 20px;
  }
}
.container {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  .content {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: auto;
  }
  .left {
    display: flex;
    flex-direction: column;
    margin-right: 81px;
    padding-left: 40px;
    width: 381px;
    box-sizing: content-box;
  }
  .right {
    width: 490px;
    // height: 291px;
    .img {
      width: 100%;
    }
  }
}

.item {
  margin-top: 30px;
}
.title {
  margin-bottom: 4px;
  font-size: 22px;
  font-weight: 600;
  line-height: 30px;
  color: var(--TextPrimary);
}

.desc {
  line-height: 21px;
  color: var(--TextTertiary);
  font-size: 14px;
  font-weight: 400;
}

.other {
  display: flex;
  align-items: center;
  margin-top: 8px;
  line-height: 24px;
  font-size: 13px;
  color: var(--TextQuartus);
  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    height: 24px;
    :global {
      .rock-checkbox {
        top: 0;
      }
    }
  }
  .createGroup {
    cursor: pointer;
    .tip {
      display: inline-block;
      height: 24px;
      line-height: 24px;
      color: var(--TextSecondary);
    }
    .tipDesc {
      display: inline-block;
      height: 24px;
      line-height: 24px;
      color: var(--TextTertiary);
    }
  }
  &.disabled {
    .tip,
    .tipDesc {
      color: var(--TextQuartus);
    }
  }
}

.btnBox {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  .btn {
    height: 32px;
    padding: 0 16px;
    &:global(.rock-btn-primary) {
      background-color: var(--Brand500);
      color: var(--absWhite);
      &:hover {
        background-color: var(--Brand600);
      }
    }
  }
}
