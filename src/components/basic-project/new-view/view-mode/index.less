.viewModeWrapper {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 2px;
  background-color: var(--aBlack4);
  border-radius: 6px;
  .item {
    flex: 1;
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    cursor: pointer;
    align-items: center;
    .name {
      margin-top: 2px;
      font-size: 13px;
      color: var(--TextPrimary);
    }
    i {
      color: var(--IconBlack);
    }
  }
  .activeItem {
    background-color: var(--bgTop);
    border-radius: 4px;
  }
}
