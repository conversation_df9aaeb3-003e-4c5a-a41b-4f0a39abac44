.timepickerDropdown {
  .renderTime {
    width: 100%;
    white-space: nowrap;
    color: var(--TextTertiary) !important;
    font-size: 13px;
    line-height: 18px;
    min-height: 30px !important;
  }

  &:global(.rock-dropdown-trigger-default) {
    width: 100%;
    min-width: unset !important ;
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    background-color: transparent !important;
  }

  :global {
    .rock-dropdown-down-arrow-icon {
      width: auto !important;
      color: var(--TextTertiary);
      margin-left: 4px;
    }
  }
}

.picker {
  min-width: 200px !important;
  // margin-left: -12px;
}
.datePicker {
  width: 260px;
}
.showQuick {
  width: auto !important;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 12px;
  margin-top: 4px;
  border-top: 1px solid var(--aBlack8);
}

.placeholder {
  min-width: unset;

  color: var(--TextTertiary);
}
