import React, { useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'

import { apiTodoUpdatePriorityPost } from '@/api'
import { LevelSelect } from '@/components/basic'
import { Dispatch } from '@/models/store'
import { TodoInfo } from '@/types'
import { TaskTableRowType } from '@/utils/const'
import { Priority } from '@/utils/fields'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import AddRowBox from '../row-box'
import s from './index.less'
import useGetPermissions from '@/hooks/useGetPermissions'
import { useCurrentModel } from '@/hooks'

interface Props {
  taskInfo: TodoInfo
}

const RenderLevel: React.FC<Props> = props => {
  const { taskInfo } = props
  const { taskId, priority, _rowType: rowType } = taskInfo
  const [visible, setVisible] = useState<boolean>(false)
  const dispatch = useDispatch<Dispatch>()

  const currentTaskModel = useCurrentModel()

  const permissions = useGetPermissions({ taskId })

  const change = (v: Priority) => {
    const params = {
      taskId: taskId,
      priority: v,
    }
    if (rowType === TaskTableRowType.add) {
      currentTaskModel.updateTodoList(Object.assign(taskInfo, params))
      return null
    }
    const copyValue = priority
    if (v === copyValue) {
      return
    }
    apiTodoUpdatePriorityPost(params)
      .finally(() => {
        //currentTaskModel.updateTodoList(params);
        dispatch.viewSetting.updateItemByDetail({ taskId: taskId })
      })
      .catch(error => {
        //Message.error('操作异常');
      })
  }

  const changeVisible = (v: boolean) => {
    setVisible(v)
  }

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    })
    return !CAN_EDIT && rowType !== TaskTableRowType.add
  }, [permissions, rowType])

  return (
    <AddRowBox
      tipTitle=""
      placeholder={priority === undefined ? '-' : ''}
      taskId={taskInfo.taskId}
      disabled={disabled}
      hasDelete={!!priority}
      focused={visible}
      onClick={e => {
        changeVisible(!visible)
      }}
      onClear={() => {
        change(Priority.Unset)
      }}
    >
      <LevelSelect
        disabled={disabled}
        showLabelDeleteIcon={visible}
        className={s.listLvelItem}
        value={priority}
        placeholder={' '}
        onChange={change}
        visible={visible}
        onVisible={v => {
          changeVisible(v)
        }}
        hasArrow={false}
      ></LevelSelect>
    </AddRowBox>
  )
}

export default RenderLevel
