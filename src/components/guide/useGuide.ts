import { POPOBridgeEmitter } from '@popo-bridge/web';
import { useState } from 'react';

import { apiUserHashGuideGet, apiUserSaveGuideGet } from '@/api';
import { GuideType } from '@/utils/const';

type Props = {
  guideType: GuideType;
};

//定义引导的优先级顺序
export const GuideOrder: GuideType[] = [
  GuideType.canCreateTeam,
  GuideType.createTeamSuccess,
  GuideType.project,
];

export const TaskGuideEmitter = 'TaskGuideEmitter';

//
class TaskGuideQueue {
  list: GuideType[] = [];
  pending: GuideType;
  getList() {
    return this.list;
  }
  setList(v: GuideType[]) {
    this.list = v;
    //开始引导第一个
  }
  start(v: GuideType) {
    if (v === this.list[0]) {
      this.emit();
    }
  }
  removeGuide(v: GuideType, next?: boolean) {
    const index = this.list.findIndex((item) => item === v);
    this.list.splice(index, 1);
    this.setList([...this.list]);
    if (index === 0 && next) {
      //开启下一个
      this.emit();
    }
    return this.list;
  }
  //检测引导是否在引导队列
  check(v: GuideType) {
    return this.list.find((item) => item === v);
  }

  addListener(v: GuideType, callBack: () => void) {
    POPOBridgeEmitter.addListener(TaskGuideEmitter, () => {
      if (this.list[0] === v) {
        callBack?.();
      }
    });
  }
  emit() {
    POPOBridgeEmitter.emit(TaskGuideEmitter);
  }
  //挂起引导 目前仅支持按顺序挂起
  addPending(v: GuideType) {
    this.pending = v;
  }
  emitPending(v: GuideType) {
    if (this.pending === v) {
      this.emit();
    }
  }
}

const TaskGuide = new TaskGuideQueue();

export { TaskGuide };

export default function useGuide(props: Props) {
  const { guideType } = props;
  const [showGuide, setShowGuide] = useState<boolean>(false);

  const handleHideGuide = async () => {
    return apiUserSaveGuideGet({
      keyword: guideType,
    }).then((e) => {
      setShowGuide(false);
      //判断当前引导是否在引导队列中, 如果在 当前引导结束  开启下一个引导
      if (TaskGuide.check(guideType)) {
        TaskGuide.removeGuide(guideType, true);
      }
    });
  };

  const getGuideData = async () => {
    return apiUserHashGuideGet({
      keyword: guideType,
    }).then((e) => {
      setShowGuide(!e);
      return !e;
    });
  };

  return {
    showGuide,
    setShowGuide,
    getGuideData,
    handleHideGuide,
  };
}
