import { SystemInfo } from '@babylon/popo-icons';
import React, { useRef, useState } from 'react';

import { apiProjectSearchProjectPost } from '@/api';
import { LoadMore } from '@/components/basic';
import CommonSearch from '@/components/basic/common-search';
import { useInfiniteRefresh } from '@/hooks';
import { tfProject } from '@/models/utils';
import { ProjectInfo } from '@/types';
import I18N from '@/utils/I18N';

import ProjectTag from '../tag-item';
import s from './index.less';

export type Props = {
  className?: string;
  // projectInfo?: ProjectInfo;
  onChange?: (v: ProjectInfo) => void;
};

const Overlay: React.FC<Props> = (props) => {
  const { onChange } = props;
  const inputRef = useRef('');
  const [searchValue, setSearchValue] = useState<string>('');
  inputRef.current = searchValue;

  const {
    loadMore,
    refresh: refreshTable,
    list,
    hasMore,
  } = useInfiniteRefresh<ProjectInfo>({
    request: ({ scrollId, searchId, size }) => {
      return apiProjectSearchProjectPost({
        size: size,
        scrollId: scrollId,
        keyword: inputRef.current,
        searchId: searchId,
        roles: ['manager', 'member'],
      }).then((ret) => {
        return {
          ...ret,
          list: tfProject(ret.list || []) as ProjectInfo[],
        };
      });
    },
    size: 20,
  });
  return (
    <div
      className={s.overlay}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <CommonSearch
        // ref={commonSearchRef}
        className={s.input}
        placeholder={I18N.auto.searchForProjects}
        onSearch={(v) => {
          refreshTable();
        }}
        value={searchValue}
        onChange={(v) => {
          setSearchValue(v);
        }}
        fill={false}
        border={false}
        round={false}
        debounceTime={350}
        hasPrefix={false}
      ></CommonSearch>
      <div className={s.list}>
        {list.map((item, index) => {
          return (
            <ProjectTag
              key={index}
              className={s.item}
              item={item}
              onClick={() => {
                onChange?.(item);
              }}
            ></ProjectTag>
          );
        })}
        <LoadMore canLoadMore={hasMore} moreFetcher={loadMore} scrollContent={`.${s.list}`} />
      </div>
      {/* <div className={s.tip}>
        <SystemInfo className={s.icon}></SystemInfo>
        <div className={s.desc}>{I18N.auto.addToProject}</div>
      </div> */}
    </div>
  );
};

export default Overlay;
