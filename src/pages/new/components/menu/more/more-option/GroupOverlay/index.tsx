import { OperateCaogao, OperateComin, OperateDelete, OperatePin3 } from '@babylon/popo-icons';
import { ChevronRight } from '@bedrock/icons-react';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import {
  apiProjectGetGet,
  apiProjectPinPut,
  apiProjectStateRecordGet,
  apiProjectUpdateStatePost,
  apiProjectUpdateTitleIconPost,
} from '@/api';
import { Icon, Menu } from '@/components/basic';
import { ProjectStatusTag } from '@/components/basic-project';
import { EditProject, InvitedMembers } from '@/components/basic-project/more-option';
import { EditProjectValue } from '@/components/basic-project/more-option/edit-project';
import useProjectShare from '@/components/basic-project/more-option/project-share/overlay/use-share-project';
import ProjectStatusRecord from '@/components/basic-project/project-status/project-status-record';
import useProjectStatus, {
  ProjectRecord,
} from '@/components/basic-project/project-status/use-project-status';
import { Dispatch, RootState } from '@/models/store';
import { Member, Permission, ProjectInfo } from '@/types';
import { EnumProjectStatus } from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import { validatesVersion } from '@/utils/validate-version';

import s from './index.less';

export type Props = {
  className?: string;
  projectInfo?: ProjectInfo;
  permissions?: Permission[];
};

const GroupOverlay: React.FC<Props> = (props) => {
  const { projectInfo, permissions } = props;
  const dispatch = useDispatch<Dispatch>();

  const onUnPing = () => {
    apiProjectPinPut({
      projectId: projectInfo?.projectId,
      isPin: false,
    }).then(() => {
      dispatch.project.getPingProjectList();
      dispatch.project.updateProjectList({
        projectId: projectInfo?.projectId,
        isPin: false,
      });
    });
  };

  const handleMenuClick = ({ key }) => {
    switch (key) {
      case 'rename':
        dispatch.project.updatePingProjectList({
          ...projectInfo,
          status: 'editing',
        });
        break;
      case 'delGroup':
        // TODO: 删除分组接口请求
        dispatch.project.deletePingProject({
          projectId: projectInfo?.projectId,
        });
        break;
      case 'unPin':
        onUnPing();
        break;
    }
  };

  // TODO: 权限确认
  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_ADD_MANAGER, CAN_SET_STATE, CAN_ADD_EDITOR, CAN_ADD_VIEWER, CAN_SHARE] =
      validatesPermission({
        permissions: permissions,
        key: [
          ProjectPermissionEnum.CAN_EDIT,
          ProjectPermissionEnum.CAN_ADD_MANAGER,
          ProjectPermissionEnum.CAN_SET_STATE,
          ProjectPermissionEnum.CAN_ADD_EDITOR,
          ProjectPermissionEnum.CAN_ADD_VIEWER,
          ProjectPermissionEnum.CAN_SHARE,
          ProjectPermissionEnum.CAN_EXIT,
          ProjectPermissionEnum.CAN_DELETE,
        ],
      }) as boolean[];
    const CAN_EDIT_MEMBER = CAN_ADD_MANAGER || CAN_ADD_EDITOR || CAN_ADD_VIEWER;
    return {
      CAN_EDIT,
      CAN_SET_STATE,
      CAN_ADD_MANAGER,
      CAN_ADD_EDITOR,
      CAN_ADD_VIEWER,
      CAN_EDIT_MEMBER: CAN_EDIT_MEMBER,
      CAN_SHARE,
    };
  }, [permissions]);

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
      // onMouseDown={(event) => event.stopPropagation()}
      className={s.Group}
    >
      <Menu
        //triggerSubMenuAction="click"
        className={s.menu}
        onClick={handleMenuClick}
        // subMenuCloseDelay={100000000}
      >
        <Menu.Item icon={<OperateCaogao className="fs-16" />} key="rename">
          重命名
        </Menu.Item>
        <Menu.Item icon={<OperateDelete className={`${s.delete} fs-16`} />} key="delGroup">
          <span className={s.delete}>删除分组</span>
        </Menu.Item>
        {memoPermissions.CAN_EDIT && (
          <>
            <Menu.Item icon={<OperateCaogao className="fs-16" />} key="rename">
              重命名
            </Menu.Item>
            <Menu.Item icon={<OperateDelete className={`${s.delete} fs-16`} />} key="delGroup">
              <span className={s.delete}>删除分组</span>
            </Menu.Item>
          </>
        )}
        <Menu.Item className={s.item} key="unPin">
          <OperatePin3 className="mr-7 fs-16" />
          {I18N.auto.removeFromNavigation}
        </Menu.Item>
      </Menu>
    </div>
  );
};

export default GroupOverlay;
