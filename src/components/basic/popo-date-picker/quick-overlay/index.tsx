import classNames from 'classnames';
import dayjs from 'dayjs';
import { useMemo } from 'react';

import { Divider } from '@/components/basic';
import { dddd, PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import { getDayEnd, getDayStart, TimeValue } from '../utils';
import s from './index.less';

export type Props = {
  className?: string;
  onChange?: (time: TimeValue) => void;
  onShowDateTimePanel?: () => void;
  showDelete?: boolean;
  isGetEnd?: boolean;
};
type Item = {
  title: string;
  showDesc: boolean;
  days: number;
};

export default function DatePickerOverlay(props: Props) {
  const { onChange, onShowDateTimePanel, showDelete, isGetEnd = true } = props;
  const onTime = (days: number) => {
    let time = getDayEnd(dayjs().add(days, 'day')).valueOf();
    if (!isGetEnd) {
      time = getDayStart(dayjs().add(days, 'day')).valueOf();
    }
    onChange?.({
      time: time,
      timeFormat: PPTimeFormat.olayDay,
      rrule: '',
    });
  };

  const onDeleteTime = () => {
    onChange?.({
      time: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
    });
  };

  const list = useMemo(() => {
    //变更时间当天为周一至周三时：提供 今天、明天、本周 (显示周五)（新增）、 下周一
    //变更时间当天为周四时：提供今天、明天、 下周一
    //变更时间当天为周五周六或周日时，今天、下周一 下周五
    const weekSatrt = dayjs().startOf('week');
    const currentDay = dayjs();
    let items: Item[] = [
      {
        title: I18N.auto.today,
        showDesc: true,
        days: 0,
      },
    ];

    //周一至周三
    if (currentDay.isAfter(weekSatrt) && currentDay.isBefore(weekSatrt.add(3, 'days'))) {
      items.push(
        ...[
          {
            title: I18N.auto.tomorrow,
            showDesc: true,
            days: 1,
          },
          {
            title: I18N.auto.thisWeek,
            showDesc: true,
            days: dayjs().day(5).diff(dayjs(), 'day'),
          },
          {
            title: I18N.auto.nextWeek,
            showDesc: true,
            days: dayjs().day(8).diff(dayjs(), 'day'),
          },
        ]
      );
    }
    //周四
    else if (
      currentDay.isAfter(weekSatrt.add(3, 'days')) &&
      currentDay.isBefore(weekSatrt.add(4, 'days'))
    ) {
      items.push(
        ...[
          {
            title: I18N.auto.tomorrow,
            showDesc: true,
            days: 1,
          },
          {
            title: I18N.auto.nextWeek,
            showDesc: true,
            days: dayjs().day(8).diff(dayjs(), 'day'),
          },
        ]
      );
    } else {
      items.push(
        ...[
          {
            title: I18N.auto.nextWeek,
            showDesc: true,
            days: dayjs().day(8).diff(dayjs(), 'day'),
          },
        ]
      );
    }
    return items;
  }, []);

  return (
    <div className={classNames('com-dropdown-select', s.panel)}>
      {list.map((item, index) => (
        <div
          key={index}
          className={s.item}
          onClick={() => {
            onTime(item.days);
          }}
        >
          <div className={s.title}>{item.title}</div>
          {item.showDesc ? (
            <div className={s.desc}>{dayjs().add(item.days, 'day').format(dddd)}</div>
          ) : null}
        </div>
      ))}

      {/* <div
          className={s.item}
          onClick={() => {
            onTime(0);
          }}
         >
          <div className={s.title}>{I18N.auto.today}</div>
          <div className={s.desc}>{dayjs().format(dddd)}</div>
         </div>
         <div
          className={s.item}
          onClick={() => {
            onTime(1);
          }}
         >
          <div className={s.title}>{I18N.auto.tomorrow}</div>
          <div className={s.desc}>{dayjs().add(1, 'day').format(dddd)}</div>
         </div>
         <div
          className={s.item}
          onClick={() => {
            onTime(7);
          }}
         >
          <div className={s.title}>{I18N.auto.nextWeek}</div>
          <div className={s.desc}>{dayjs().add(7, 'day').format(dddd)}</div>
         </div> */}
      <Divider type="horizontal" className={s.divider}></Divider>
      <div className={s.item} onClick={onShowDateTimePanel}>
        <div className={s.title}>{I18N.auto.dateAndTime}</div>
      </div>
      {showDelete ? (
        <>
          <Divider type="horizontal" className={s.divider}></Divider>
          <div className={s.item} onClick={onDeleteTime}>
            <div className={s.title}>{I18N.auto.deleteDeadline}</div>
          </div>
        </>
      ) : null}
    </div>
  );
}
