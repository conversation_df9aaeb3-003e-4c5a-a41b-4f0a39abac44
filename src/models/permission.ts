import { createModel } from '@rematch/core';
import { RootModel } from '.';
import { PermissionsTasksMap, TaskInfo } from '@/types';
import { getNoPermissionList } from './utils';
import { getPermissionsByTaskIds } from '@/api-common';
import { RequestQueue } from '@/utils/requestQueue';

interface IPermissionState {
  permissionsMap: PermissionsTasksMap;
}

const permPageSize = 30;
// 控制并发请求数量
const maxConcurrentRequests = 1;

const requestQueue = new RequestQueue(maxConcurrentRequests);

const gottedMap: Record<string, boolean> = {};

export const permissions = createModel<RootModel>()({
  state: {
    permissionsMap: {} as PermissionsTasksMap,
    // gottedPermissionIdsMap: {} as Record<string, boolean>,
  },
  reducers: {
    setData(state, payload: Partial<IPermissionState>) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    async setPermissionsMap(
      {
        list,
        widthSubtasks = true,
        limit = 50,
        extraPermissionList = [],
      }: {
        list: TaskInfo[];
        widthSubtasks?: boolean;
        limit?: number;
        extraPermissionList?: string[];
      },
      state
    ) {
      const { permissionsMap } = state.permissions;
      const { userInfo } = state.user;

      const noPermissionsData = getNoPermissionList({
        list,
        permissionsMap,
        userInfo,
        widthSubtasks,
        limit,
      });
      const noPermissionsTaskIds = noPermissionsData.noPermissionsTaskIds.filter((id) => {
        const validateId = typeof id === 'number' && id > 0;
        if (validateId && !gottedMap[id] && !extraPermissionList?.length) {
          gottedMap[id] = true;
          return true;
        }
        return false;
      });

      // 如果没有需要获取权限的任务，直接返回
      if (noPermissionsTaskIds.length === 0) return;

      // 创建一个新的权限映射对象
      let newPermissionsMap = permissionsMap;

      // 计算批次数量
      const times = Math.ceil(noPermissionsTaskIds.length / permPageSize);

      // 处理第一批数据
      if (times > 0) {
        const firstBatchIds = noPermissionsTaskIds.slice(0, permPageSize);
        const firstBatchParams = {
          noPermissionsTaskIds: firstBatchIds,
          myFollowedTaskIds: noPermissionsData.myFollowedTaskIds,
          extraPermissionList,
        };

        // 获取第一批权限并立即更新
        const firstBatchPermissions = await requestQueue.addRequest(() =>
          getPermissionsByTaskIds(firstBatchParams)
        );
        newPermissionsMap = { ...newPermissionsMap, ...firstBatchPermissions };

        // 立即更新第一批权限
        dispatch.permissions.setData({
          permissionsMap: newPermissionsMap,
        });
      }

      // 如果有更多批次，使用请求队列控制并发
      if (times > 1) {
        // 创建请求队列，限制并发数
        const remainingResults = [];

        // 添加剩余批次到请求队列
        for (let i = 1; i < times; i++) {
          const idsSlice = noPermissionsTaskIds.slice(
            i * permPageSize,
            i * permPageSize + permPageSize
          );

          const params = {
            noPermissionsTaskIds: idsSlice,
            myFollowedTaskIds: [], // 只在第一批中包含关注的任务ID
          };

          // 将请求添加到队列
          const resultPromise = requestQueue.addRequest(() => getPermissionsByTaskIds(params));
          remainingResults.push(resultPromise);
        }

        // 等待所有请求完成
        const results = await Promise.all(remainingResults);

        // 合并所有剩余批次的结果
        if (results.length > 0) {
          const combinedPermissions = results.reduce((acc, curr) => ({ ...acc, ...curr }), {});
          newPermissionsMap = { ...newPermissionsMap, ...combinedPermissions };

          // 更新所有剩余批次的权限
          dispatch.permissions.setData({
            permissionsMap: newPermissionsMap,
          });
        }
      }
    },
    updatePermissionsMap({ permission }, state) {
      const { taskId, roles } = permission;
      dispatch.permissions.setData({
        permissionsMap: {
          ...state.permissions.permissionsMap,
          [taskId]: {
            permissions: roles,
          },
        },
      });
    },
    updatePermissionsMapByExtraPermission({ list, extraPermission }, state) {
      const { permissionsMap } = state.permissions;
      const permissions = permissionsMap[taskId]?.permissions || [];
      const hasBasePermissions = permissions.length > 0;

      const newPermissions = getPermissionsByTaskIds({
        noPermissionsTaskIds: list,
        myFollowedTaskIds: [],
        extraPermissionList: extraPermission,
        hasBasePermissions: hasBasePermissions,
      });

      console.log('newPermissions', newPermissions);

      // const mergedPermissions = {
      //   ...permissionsMap[taskId],
      //   permissions: newPermissions,
      // };

      dispatch.permissions.setData({
        permissionsMap: {
          ...permissionsMap,
          [taskId]: {
            permissions: newPermissions,
          },
        },
      });
    },
  }),
});
