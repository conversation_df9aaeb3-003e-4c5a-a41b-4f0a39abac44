import { useMemoizedFn } from 'ahooks';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import Gantt from '@/components/gantt';
import { Dispatch, RootState } from '@/models/store';
import { ViewType } from '@/utils/const';
import { EnumTrackeKey } from '@/utils/skyline';

import Calendar from './calendar';
import Kanban from './kanban';
import List from './list';
import Timeline from './timeline';

const View: React.FC = () => {
  const { currentViewTab, navigatorId } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
    navigatorId: state.viewSetting.navigatorId,
  }));
  const dispatch = useDispatch<Dispatch>();
  const trackingTab = useMemoizedFn(() => {
    dispatch.user.trackingByView({
      key: EnumTrackeKey.ViewTab,
    });
  });
  const trackingNavigator = useMemoizedFn(() => {
    dispatch.user.trackingByView({
      key: EnumTrackeKey.ViewNavigator,
    });
  });
  useEffect(() => {
    if (currentViewTab?.type) {
      //埋点: 渲染不同视图tab
      trackingTab();
    }
  }, [currentViewTab?.type]);

  useEffect(() => {
    if (navigatorId) {
      //埋点: 渲染不同快捷导航
      trackingNavigator();
    }
  }, [navigatorId]);

  switch (currentViewTab?.type) {
    case ViewType.list:
      return <List />;
    case ViewType.kanban:
      return <Kanban />;
    case ViewType.calendar:
      return <Calendar />;
    case ViewType.timeline:
      return <Timeline />;
    default:
      return null;
  }

  return null;
};
export default View;
