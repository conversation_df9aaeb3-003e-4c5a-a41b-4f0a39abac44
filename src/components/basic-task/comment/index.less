.commentModule {
  flex: 1;
  padding-top: 12px;
  padding-left: 24px;
  padding-right: 24px;
  // user-select: none;
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      height: 22px;
      margin: 12px 0;
      line-height: 22px;
      font-size: 16px;
      font-weight: 600;
      color: var(--TextPrimary);
    }
    .right {
      display: flex;
      align-items: center;
      padding: 0;
      border-radius: 6px;
      font-size: 12px;
      background-color: var(--TasksTagbg);
      color: var(--TextSecondary);
    }
    .btn {
      display: flex;
      align-items: center;
      padding: 2px 6px;
      border-radius: 5px;
      cursor: pointer;
      border: 1px solid transparent;
    }
    .active {
      color: var(--TextPrimary);
      background-color: var(--bgBottom);
      border: 1px solid var(--aBlack12);
    }
  }
  .quillDemo {
    height: 0;
    visibility: hidden;
  }
  .commentList {
    margin-bottom: 5px;
    padding-top: 4px;
    padding-bottom: 12px;
  }
}

.commentTabs {
  :global {
    .rock-tabs-nav {
      height: 28px;
      margin-bottom: 16px;
    }
    .rock-tabs-extra-content {
      display: none;
    }
    .rock-tabs-nav::before {
      display: none;
    }
    .rock-tabs-nav-wrap {
      width: 100%;
    }

    .rock-tabs-nav > .rock-tabs-nav-wrap > .rock-tabs-nav-list .rock-tabs-tab:not(:first-of-type) {
      margin-left: 12px !important;
    }

    .rock-tabs-tab {
      padding: 0 !important;
      margin-bottom: 6px !important;
      margin-top: 0 !important;
      justify-content: flex-start;
      min-width: 28px;
      font-size: 13px;
      .rock-tabs-tab-btn-inner {
        color: var(--TextSecondary-ongrey);
      }
      &:hover {
        background-color: transparent !important;
      }
      &:active {
        background-color: transparent !important;
      }
    }
    .rock-tabs-tab-active {
      .rock-tabs-tab-btn-inner {
        color: var(--TextPrimary);
      }
    }
    .rock-tabs-ink-bar {
      height: 2px !important;
      background-color: var(--aBlack40);
      width: 28px !important;
      margin-left: 0px !important;
    }
  }
}
