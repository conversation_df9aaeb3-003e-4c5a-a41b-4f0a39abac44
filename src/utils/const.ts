import I18N from '@/utils/I18N'
export default class Const {
  // 开发环境
  static DEV = 'dev'
  // 测试 环境
  static ENV_TEST = 'test'

  // 预发布 环境
  static ENV_STAGING = 'staging'

  // 线上 环境
  static ENV_PRODUCTION = 'production'

  static DEVELOPEMENT = 'developement'

  static PRODUCTION = 'production'

  static LoginToken = 'login-token'

  static CollapseSessionKey = 'filter-collapse'
  static RetryLoginTimes = 'retry-login-times'
  static RetryReloadPageTimes = 'retry-reload-page-times'
  static NavigationView = 'navigation-view'
  static NavigatorId = 'navigator-id'
  static NavigatorIdSetting = 'navigator-id-setting'
  static ProjectAddItem = 'project-add-item'
  static PreNavigatorId = 'pre-navigatorId'
  static ProjectNewAdd = 'project-new-add'
}

export enum ErrorCode {
  NO_LOGIN = 305,
  //项目成员上限
  PROJECT_USER_LIMIT = 150003,
  //最后一个管理员
  PROJECT_LAST_ADMINISTRATOR = 150004,
  //不是群管理员
  TEAM_ADMINISTRATOR_LIMIT = 150006,
  // 创建项目群最少两个用户
  TEAM_CREATE_USERS_LESS_LIMIT = 150005,
  // 群关联项目超过上限
  TEAM_ASSOCIATE_LIMIT = 180001,
}

export const SurveyUrl = 'https://docs.popo.netease.com/lingxi/d671e52790674bf0bd54c032e72a29e4'
//https://popo.res.netease.com/popo-assets/todo/project/addpro_pic_light.png
export const CNDPath = 'https://popo.res.netease.com/popo-assets/todo'

export const TaskNoDataLight = `${CNDPath}/no-data/task-empty-light.png`
export const TaskNoDataDark = `${CNDPath}/no-data/task-empty-dark.png`
export const TaskSearchNoDataLight = `${CNDPath}/no-data/task-search-empty-light.png`
export const TaskSearchNoDataDark = `${CNDPath}/no-data/task-search-empty-dark.png`

export const ProjectNoDataLight = `${CNDPath}/no-data/project-empty-light.png`
export const ProjectNoDataDark = `${CNDPath}/no-data/project-empty-dark.png`
export const ProjectSearchNoDataLight = `${CNDPath}/no-data/project-search-empty-light.png`
export const ProjectSearchNoDataDark = `${CNDPath}/no-data/project-search-empty-dark.png`

export const EnvironmentsMap = {
  [Const.ENV_TEST]: 'test',
  [Const.ENV_STAGING]: 'staging',
  [Const.ENV_PRODUCTION]: 'production',
}

export const FrequenceTimeTypeText: Record<number | string, string> = {
  1: I18N.auto.day,
  2: I18N.auto.hour,
  3: I18N.auto.minute,
}

export interface IItem {
  key: string
  desc: string
  hidden?: boolean | string[]
}

export interface IPreLabels {
  id: string
  hasHead: boolean
  desc: string
  options: IItem[]
  hidden?: boolean
}

export enum ServerLabel {
  all = 'all',
  today = 'today',
  important = 'important',
  myTodo = 'myTodo',
  myPost = 'myPost',
  deferred = 'deferred',
  finished = 'finished',
  planned = 'planned',
}

// 筛选的条件
export const PreLabels: IPreLabels[] = [
  {
    id: 'status',
    hasHead: false,
    desc: '',
    options: [
      {
        key: 'status#finished',
        desc: I18N.auto.showCompleted,
        hidden: [ServerLabel.all, ServerLabel.finished, ServerLabel.deferred, ServerLabel.planned, 'p2p', 'team'],
      },
      {
        key: 'status#unfinished',
        desc: I18N.auto.hangInTheAir,
        hidden: true,
      },
    ],
  },
  {
    id: 'createTime',
    hasHead: true,
    desc: I18N.auto.creationTime,
    hidden: true,
    options: [
      {
        key: 'createTime#today',
        desc: I18N.auto.createdToday,
      },
    ],
  },
  {
    id: 'assignTo',
    hasHead: true,
    desc: I18N.auto.distribution,
    hidden: true,
    options: [
      {
        key: 'assignTo#me',
        desc: I18N.auto.assignToMe,
      },
    ],
  },
  {
    id: 'deadline',
    desc: I18N.auto.deadline,
    hasHead: true,
    options: [
      {
        key: 'deadline#deferred',
        desc: I18N.auto.deferred,
        hidden: [ServerLabel.planned],
      },
      {
        key: 'deadline#today',
        desc: I18N.auto.today,
        hidden: [ServerLabel.planned],
      },
      {
        key: 'deadline#tomorrow',
        desc: I18N.auto.tomorrow,
        hidden: [ServerLabel.planned],
      },
      {
        key: 'deadline#future',
        desc: I18N.auto.inTheFutureAndToday,
        hidden: [ServerLabel.planned],
      },
      {
        key: 'deadline#any',
        desc: I18N.auto.thereIsADeadline,
        hidden: [ServerLabel.planned],
      },
      {
        key: 'deadline#none',
        desc: I18N.auto.noDeadline,
        hidden: [ServerLabel.planned],
      },
    ],
  },
  {
    id: 'priority',
    desc: I18N.auto.priority,
    hasHead: true,
    options: [
      {
        key: 'priority#urgent',
        desc: I18N.auto.urgent,
      },
      {
        key: 'priority#high',
        desc: I18N.auto.high,
      },
      {
        key: 'priority#medium',
        desc: I18N.auto.in,
      },
      {
        key: 'priority#low',
        desc: I18N.auto.low,
      },
      {
        key: 'priority#any',
        desc: I18N.auto.withPriority,
      },
      {
        key: 'priority#none',
        desc: I18N.auto.noPriority,
      },
    ],
  },
  {
    id: 'remark',
    desc: I18N.auto.remarks,
    hasHead: true,
    options: [
      {
        key: 'remark#exist',
        desc: I18N.auto.withRemarks,
      },
      {
        key: 'remark#none',
        desc: I18N.auto.noRemarks,
      },
    ],
  },
]

export const CalenDarPreLabels: IPreLabels[] = [
  {
    id: 'priority',
    desc: I18N.auto.priority,
    hasHead: true,
    options: [
      {
        key: 'priority#urgent',
        desc: I18N.auto.urgent,
      },
      {
        key: 'priority#high',
        desc: I18N.auto.high,
      },
      {
        key: 'priority#medium',
        desc: I18N.auto.in,
      },
      {
        key: 'priority#low',
        desc: I18N.auto.low,
      },
      {
        key: 'priority#any',
        desc: I18N.auto.withPriority,
      },
      {
        key: 'priority#none',
        desc: I18N.auto.noPriority,
      },
    ],
  },
  {
    id: 'remark',
    desc: I18N.auto.remarks,
    hasHead: true,
    options: [
      {
        key: 'remark#exist',
        desc: I18N.auto.withRemarks,
      },
      {
        key: 'remark#none',
        desc: I18N.auto.noRemarks,
      },
    ],
  },
]

export const PCPreLabels: IPreLabels[] = [
  {
    id: 'createTime',
    hasHead: true,
    desc: I18N.auto.creationTime,
    hidden: true,
    options: [
      {
        key: 'createTime#today',
        desc: I18N.auto.createdToday,
      },
    ],
  },
  {
    id: 'assignTo',
    hasHead: true,
    desc: I18N.auto.distribution,
    hidden: true,
    options: [
      {
        key: 'assignTo#me',
        desc: I18N.auto.assignToMe,
      },
    ],
  },
  {
    id: 'deadline',
    desc: I18N.auto.deadline,
    hasHead: true,
    options: [
      {
        key: 'deadline#deferred',
        desc: I18N.auto.deferred,
      },
      {
        key: 'deadline#today',
        desc: I18N.auto.today,
      },
      {
        key: 'deadline#tomorrow',
        desc: I18N.auto.tomorrow,
      },
      {
        key: 'deadline#future',
        desc: I18N.auto.inTheFutureAndToday,
      },
      {
        key: 'deadline#any',
        desc: I18N.auto.thereIsADeadline,
      },
      {
        key: 'deadline#none',
        desc: I18N.auto.noDeadline,
      },
    ],
  },
  {
    id: 'priority',
    desc: I18N.auto.priority,
    hasHead: true,
    options: [
      {
        key: 'priority#urgent',
        desc: I18N.auto.urgent,
      },
      {
        key: 'priority#high',
        desc: I18N.auto.high,
      },
      {
        key: 'priority#medium',
        desc: I18N.auto.in,
      },
      {
        key: 'priority#low',
        desc: I18N.auto.low,
      },
      {
        key: 'priority#any',
        desc: I18N.auto.withPriority,
      },
      {
        key: 'priority#none',
        desc: I18N.auto.noPriority,
      },
    ],
  },
  {
    id: 'remark',
    desc: I18N.auto.remarks,
    hasHead: true,
    options: [
      {
        key: 'remark#exist',
        desc: I18N.auto.withRemarks,
      },
      {
        key: 'remark#none',
        desc: I18N.auto.noRemarks,
      },
    ],
  },
]

export const ServerLabelsNoDataDesc: Record<string, { title?: string; text: string[]; src: string }> = {
  '': {
    title: undefined,
    text: [I18N.auto.noDataAvailable],
    src: require('@/assets/images/no-data/all.png'),
  },
  planned: {
    title: undefined,
    text: [I18N.auto.deadlineSet],
    src: require('@/assets/images/no-data/finished.png'),
  },
  all: {
    title: undefined,
    text: [I18N.auto.viewHere],
    src: require('@/assets/images/no-data/all.png'),
  },
  important: {
    title: undefined,
    text: [I18N.auto.markAsUrgent],
    src: require('@/assets/images/no-data/important.png'),
  },
  today: {
    title: I18N.auto.focusOnToday,
    text: [I18N.auto.createAnd, I18N.auto.youCurrentlyDoNotHave],
    src: require('@/assets/images/no-data/today.png'),
  },
  finished: {
    title: undefined,
    text: [I18N.auto.notYet],
    src: require('@/assets/images/no-data/finished.png'),
  },
  myTodo: {
    title: undefined,
    text: [I18N.auto.noPointsForTheTimeBeing],
    src: require('@/assets/images/no-data/myTodo.png'),
  },
  deferred: {
    title: undefined,
    text: [I18N.auto.expiredPending],
    src: require('@/assets/images/no-data/deferred.png'),
  },
  myPost: {
    title: undefined,
    text: [I18N.auto.theresNoMeAtTheMoment],
    src: require('@/assets/images/no-data/myPost.png'),
  },
}

export const NONE_GROUP_BY = 'NONE'
export const UNKNOW_GROUP_BY = 'unknown'

export enum TableIndex {
  operation = 'operation',
  title = 'operation',
  owner = 'assigner',
  assignee = 'assignee',
  priority = 'priority',
  deadline = 'deadline',
  remind = 'remind',
  startTime = 'startTime',
}

export const FieldServerMap: Record<string, { key: string; name: string }> = {
  assigner: {
    key: 'assigner',
    name: I18N.auto.assignedBy,
  },
  assignee: {
    key: 'assignee',
    name: I18N.auto.assignedTo,
  },
  priority: {
    key: 'priority',
    name: I18N.auto.priority,
  },
  createTime: {
    key: 'createTime',
    name: I18N.auto.creationTime,
  },
  startTime: {
    key: 'startTime',
    name: I18N.auto.startTime,
  },
  deadline: {
    key: 'deadline',
    name: I18N.auto.deadline,
  },
}

export const FORMAT_DD = 'YYYY/MM/DD'

export const FORMAT_DD_mm = 'YYYY/MM/DD HH:mm'

export const MMM_DD_dddd = 'MMM DD dddd'

export const MMM_Do_HH_mm = 'MMMDo HH:mm'
export const MMM_Do_HH_mm_US = 'MMM Do HH:mm'

export const LOG_FORMAT_MM_DD_HH_mm = 'MM/DD HH:mm'
export const LOG_FORMAT_YYYY_MM_DD_HH_mm = 'MM/DD HH:mm'

export const dddd_HH_mm = 'dddd HH:mm'
export const dddd = 'dddd'

export enum PPTimeFormat {
  /**
   * 无日期
   */
  noDate = 0,
  /**
   * 仅有日期
   */
  olayDay = 1,
  /**
   * 日期和时间都有
   */
  dateAndTime = 2,
}

export const ServerSourceTypeMap = {
  0: 0, //主动创建
  1: 1, //p2p
  2: 3, //多人会话
  3: 2, //群
  4: 5, //服务号
  5: 6, //文件传输助手
}

//老的暂时不管了,推进取消
export enum BridgeUserType {
  p2p = 1,
  team = 3,
}

//最新的,以后都这么用
export enum NewBridgeUserType {
  p2p = 1,
  team = 2,
}

export enum EventCategory {
  comment = 1,
  records = 2,
}

export enum Finished {
  finished = 1,
  unFinished = 0,
}

export enum Order {
  asc = 'asc',
  desc = 'desc',
}

export enum ServerOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export const OrderCustom = 'customize'

export const ServerOrderMap = {
  [ServerOrder.ASC]: Order.asc,
  [ServerOrder.DESC]: Order.desc,
}

export enum Category {
  separate = '0', //独立页
  p2p = '1',
  team = '2',
}

export enum Collapse {
  /**
   * 收起
   */
  fold = 'fold',
  /**
   * 展开
   */
  unfold = 'unfold',
  /**
   * 默认态 收起的
   */
  default = 'default',
}

export enum TransferSelectType {
  cancel = 0, //取消
  transferringAndExit = 1, //情况一 对方不是执行人 转让自己为指派人的待办,并且退出; 情况二对方是执行人:转让后取消其执行人身份，自己退出该待办
  transferringAndSelfToExecutor = 2, //自己是指派人  情况一:转让后将自己添加为执行人 对方不是执行人  情况二:转让后取消其执行人身份,将自己添加为执行人 对方是是执行人
  transferringAndTargetToExecutor = 3, //转让非自己的指派人 并且将非自己的指派人加入执行人中  //TODO 暂时没有这类
  transferringExecutorAndRemoveExecutor = 4,
} //转让指派人给一位执行人, 并将原先执行人从执行人中剔除
//转让后取消其执行人身份 4  //创建时需要  除了这种情况 其他创建的情况无弹框

//转让后将自己添加为执行人 2  //详情编辑
//转让后取消其执行人身份，将自己添加为执行人 2 //详情编辑

//转让后退出该待办-- 1 //详情编辑
//转让后取消其执行人身份，自己退出该待办 1 //详情编辑

export type ServerSourceTypeMapKey = keyof typeof ServerSourceTypeMap

export const filepickerFop = '?fop=imageView/0/w/100/h/100'

/**
 * 标题限制300字
 */
export const TITLE_MAX_LENGTH = 300

/**
 * 限制人数
 */
export const PEOPLE_MAX_LENGTH = 120

/**
 * 批量操作上限
 */
export const SELECT_MAX_LENGTH = 100

//抽屉点击事件源头相关
export const DrawerClickOrignKey = 'drawerKey'

export const DrawerOrigin = {
  list: 'tableList',
  drawerSide: 'drawerSide',
  listAdd: 'listAdd',
}

export enum ListType {
  table = 'list',
  calendar = 'calendar',
  kanban = 'kanban',
  pc = 'pc',
}

export enum CalendarModeType {
  week = 'WEEK',
  month = 'MONTH',
  year = 'YEAR',
}

export enum CalendarSyncStatus {
  sync = 1,
  unsync = 2,
}

export enum TodoNotificationCode {
  refresh = 301,
}

export enum PeoplePickerType {
  assignor = 1,
  executor = 2,
  watcher = 3,
}

export enum FontSizeFactorType {
  fs12 = 1,
  fs14 = 2,
  fs15 = 3,
  fs17 = 4,
  fs20 = 5,
  fs23 = 6,
}
export const FontSizeFactorMap = {
  [FontSizeFactorType.fs12]: 0.86,
  [FontSizeFactorType.fs14]: 1,
  [FontSizeFactorType.fs15]: 1.07,
  [FontSizeFactorType.fs17]: 1.2,
  [FontSizeFactorType.fs20]: 1.44,
  [FontSizeFactorType.fs23]: 1.65,
}

export enum AtType {
  user = 'user', // @联系人
  all = 'all', //@所有人
  unfinished = 'unfinished',
} //@未完成待办的执行人

//@i18n-ignore
// export const RichTextStr =
//   '[微笑]测试[popoEmoji]{"text":"耶","key":"0029"}[/popoEmoji]222[popoEmoji]{"text":"破涕为笑","key":"00125"}[/popoEmoji][popoAt]{"text":"寒木(周庆)","uid":"<EMAIL>"}[/popoAt] 测试老链接https://aigc-api-demo.hz.netease.com/#/哈哈哈哈在测试一个链接[popoDoc]{"text":"测试链接1","docUrl":"https://docs.popo.netease.com/lingxi/5a8bf8798c714622a1fb50fc94ae11c3","docIcon":"https://popo.res.netease.com/popo-assets/todo/all.svg"}[/popoDoc][popoDoc]{"text":"测试链接1","docUrl":"https://docs.popo.netease.com/lingxi/5a8bf8798c714622a1fb50fc94ae11c3","docIcon":"https://popo.res.netease.com/popo-assets/todo/all.svg"}[/popoDoc]链接2';

export enum EnumSessionType {
  p2p = 1,
  team = 3,
} //1是人 3是群

export enum ViewType {
  list = 1,
  kanban = 2,
  calendar = 3,
  timeline = 4,
}

export enum Distribution {
  /**
   * 系统视图
   */
  sys = 1,
  /**
   * 自定义视图
   */
  custom = 2,
}

export enum TaskNavigatorType {
  /**
   * 全部任务
   */
  allTask = 'all',
  /**
   * 分配给我
   */
  assignToMe = 'assignee',
  /**
   * 我关注的
   */
  myFollow = 'follower',
  /**
   * 我创建的
   */
  myCreate = 'creator',
}

export const TaskNavigatorMap: Record<TaskNavigatorType, { name: string }> = {
  /**
   * 全部任务
   */
  [TaskNavigatorType.allTask]: {
    name: I18N.auto.allTasks,
  },
  /**
   * 分配给我
   */
  [TaskNavigatorType.assignToMe]: {
    name: I18N.auto.assignToMe,
  },
  /**
   * 我关注的
   */
  [TaskNavigatorType.myFollow]: {
    name: I18N.auto.whatIAmConcernedAbout,
  },
  /**
   * 我创建的
   */
  [TaskNavigatorType.myCreate]: {
    name: I18N.auto.iCreatedIt,
  },
}

export enum TaskTableRowType {
  normal, //独立页
  addBtn,
  add,
  group,
  creating, // 创建中
}

export const enum RelationType {
  normal = 0,
  parentTask = 1,
  subTask = 2,
}

export enum EditType {
  add,
  update,
}

export enum OneAndMoreType {
  one = 0,
  all = 1,
}

export enum TaskStatusCompleteType {
  me = 1,
  all = 2,
}

export enum OneAndMoreServerParamsType {
  one = 'any_one',
  all = 'all',
}

/**
 * 列表字段隐藏枚举
 */
export enum EnumFieldVisible {
  show = 1,
  hidden = 0,
}

export enum EnumTimePickerType {
  start = 'start',
  deadline = 'deadline',
}

export const TaskTableRowTypeAdd = 'add-'
export const TaskTableRowTypeGroup = 'group-'

export enum EnumEmitter {
  AddKanbanLaneAndScrollTop = 'AddKanbanLaneAndScrollTop',
  AddKanbanAndScrollRight = 'AddKanbanAndScrollRight',
  VirtuoSoscrollToIndex = 'VirtuoSoscrollToIndex',
  ListTableSoscrollToLeft = 'ListTableSoscrollToLeft',
  TanstackSoscrollToIndex = 'TanstackSoscrollToIndex',
  TreeExpandById = 'TreeExpandById',
  ProjectGroupChange = 'ProjectGroupChange',
  CalendarModeTypeChange = 'CalendarModeTypeChange',
}

export enum BaseIconSizeEnum {
  // 正常尺寸24px热区
  normal = 24,
  // 大尺寸28px热区
  large = 28,
}

export enum EnumCalendarAddSource {
  calendar,
  unplanned,
}

export enum EnumListAddSource {
  globalCreate,
  bottomCreate,
  groupBottomCreate,
}

export enum EnumKanbanAddSource {
  globalCreate,
  laneTopCreate,
  laneBottomCreate,
}

export const ViewAddSource: {
  list: EnumListAddSource
  kanban: EnumKanbanAddSource
  calendar: EnumCalendarAddSource
} = {
  list: EnumListAddSource.globalCreate,
  kanban: EnumKanbanAddSource.globalCreate,
  calendar: EnumCalendarAddSource.calendar,
}

export const AddMenuId = -1
export const ProjectListMenuId = -99

export enum EnumRole {
  /**
   * 管理员
   */
  admin = 'manager',
  /**
   * 项目成员
   */
  members = 'member',
  /**
   * 仅查看
   */
  viewOnly = 'viewer',
}

export enum EnumProjectStatus {
  /**
   *进行中
   */
  ongoing = 'in',
  /**
   * 暂停
   */
  pause = 'suspend',
  /**
   * 有风险
   */
  risk = 'risk',
  /**
   * 已完成
   */
  done = 'done',
  /**
   * 归档
   */
  archive = 'archive',
}

export enum GuideType {
  /**
   * 我关注的引导 单独的排除在外
   */
  menuFollow = 'follow',
  /**
   * 看板引导 暂时没有了
   */
  tabKanban = 'guide',
  /**
   * 筛选引导
   */
  filter = 'guide_filter',
  /**
   * 项目引导
   */
  project = 'guide_project',
  /**
   * 提示可建群 或 群新建成功
   */
  canCreateTeam = 'guide_can_create_team',
  /**
   * 群新建成功
   */
  createTeamSuccess = 'guide_create_team_success',
}

export enum EnumNotificationEvent {
  /**
   * 项目名称变更
   */
  PeojectChangeName = 0,
  /**
   * 项目状态变更
   */
  PeojectChangeStatus = 1,
  /**
   * 项目内新增任务
   */
  PeojectAddTask = 2,
  /**
   * 项目内删除任务
   */
  PeojectDeleteTask = 3,
  /**
   * 项目内任务完成状态变更
   */
  PeojectTaskCompletionStatus = 4,
  /**
   * 项目成员加入/移除
   */
  PeojectChangeMember = 5,
}
export const NotificationEventMap: Record<EnumNotificationEvent, { name?: string; bgName: string }> = {
  [EnumNotificationEvent.PeojectChangeName]: {
    //name: '项目名称变更',
    bgName: 'pjtzsl-name',
  },
  [EnumNotificationEvent.PeojectChangeStatus]: {
    //name: '项目状态变更',
    bgName: 'pjtzsl-state',
  },
  [EnumNotificationEvent.PeojectAddTask]: {
    //name: '项目内新增任务',
    bgName: 'pjtzsl-add-task',
  },
  [EnumNotificationEvent.PeojectDeleteTask]: {
    //name: '项目内删除任务',
    bgName: 'pjtzsl-del-task',
  },
  [EnumNotificationEvent.PeojectTaskCompletionStatus]: {
    //name: '项目内任务完成状态变更',
    bgName: 'pjtzsl-wc',
  },
  [EnumNotificationEvent.PeojectChangeMember]: {
    //name: '项目成员新增',
    bgName: 'pjtzsl-user',
  },
}

export const enum NOTIFICATION_TYPE {
  // 分配任务
  TASK_ASSIGN = 'task_assign',
  // 任务已完成
  TASK_COMPLETED = 'task_completed',
  // 任务已逾期
  TASK_EXPIRED = 'task_expired',
}

export const SUBTASK_MAX_COUNT = 50

export const enum REQUEST_ID {
  // 获取项目列表
  GET_PROJECT_LIST = 'getProjectList',
  // 获取任务列表
  GET_TASK_LIST = 'getTaskList',
}
