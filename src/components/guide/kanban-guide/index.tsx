import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/models/store';
import I18N from '@/utils/I18N';
import { CorlorMode } from '@/utils/platform';

import s from './index.less';

interface KanBanGuideProps {
  onClick?: () => void;
}

export const KanBanGuide: React.FC<KanBanGuideProps> = ({ onClick }) => {
  const { corlorMode, language } = useSelector((state: RootState) => ({
    corlorMode: state.user?.corlorMode,
    language: state.user.language,
  }));
  const imgSrc = useMemo(() => {
    return `https://popo.res.netease.com/popo-assets/todo/kb-${
      corlorMode === CorlorMode.light ? 'light' : 'dark'
    }-${language}.png`;
  }, [corlorMode, language]);
  return (
    <div className={s.wrapper}>
      <img src={imgSrc} alt={'img'} />
      <div className={s.title}>{I18N.auto.kanbanView}</div>
      <div className={s.des}>{I18N.auto.taskManagementAgain}</div>
      <div className={s.btnWrapper}>
        <div
          className={s.btn}
          onClick={(e) => {
            e.stopPropagation();
            onClick?.();
          }}
        >
          {I18N.auto.iGotIt}
        </div>
      </div>
    </div>
  );
};
