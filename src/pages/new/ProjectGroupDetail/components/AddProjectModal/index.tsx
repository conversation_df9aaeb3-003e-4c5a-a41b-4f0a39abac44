import { apiProjectFlowQueryPost, apiV3ProjectFlowQueryPost } from '@/api';
import { Checkbox, CommonSearch, IconProject, LoadMore, Tooltip } from '@/components/basic';
import { store } from '@/models/store';
import I18N from '@/utils/I18N';
import { useMemoizedFn, useRequest } from 'ahooks';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import s from './index.less';
import { CheckboxChangeEvent } from '@bedrock/components/lib/Checkbox';
import { ProjectInfo } from '@/types';
import { CheckHeavy } from '@bedrock/icons-react';
import classNames from 'classnames';

const AddProjectModal = forwardRef((_, ref) => {
  const searchListRef = useRef<HTMLDivElement>(null);
  const searchValue = useRef('');
  const [selectedIds, setSelectedIds] = useState(new Set<number>());
  const [searchData, setSearchData] = useState<{
    list: ProjectInfo[];
    searchId?: number;
    scrollId?: number;
    hasMore?: boolean;
  }>({ list: [] });

  const handleGetSearchList = async (params: {
    v: string;
    searchId?: number;
    scrollId?: number;
  }) => {
    const { v, searchId, scrollId } = params || {};
    const projectList = store.getState().project.projectList;
    if (!v && projectList.length) {
      return { list: projectList };
    } else {
      const param = {
        keyword: v || '',
        size: 50,
        scrollId: scrollId ? scrollId + '' : undefined,
        searchId: searchId ? searchId + '' : undefined,
      };
      return await apiProjectFlowQueryPost(param);
    }
  };

  const { runAsync: getSearchList } = useRequest(handleGetSearchList, {
    onSuccess: (res, params) => {
      const [param] = params;
      res.list = res.list.filter((item) => !item.isGroup);
      if (res.list.length < 50) {
        handleLoadMore({ hasMore: !!res.hasMore, searchId: res.searchId, scrollId: res.scrollId });
      }

      if (param?.scrollId || param?.searchId) {
        res.list = [...searchData.list, ...res.list];
      }

      setSearchData(res);
    },
  });

  const handleLoadMore = (params?: {
    hasMore: boolean;
    searchId?: number | string;
    scrollId?: number | string;
  }) => {
    const { hasMore, searchId, scrollId } = params || searchData;
    if (hasMore) {
      return getSearchList({ v: searchValue.current, searchId, scrollId });
    }
    return Promise.resolve();
  };

  const handleCheckChange = useMemoizedFn((e: CheckboxChangeEvent) => {
    const v = e.target.value;
    setSelectedIds((oldSet) => {
      if (oldSet.has(v)) {
        oldSet.delete(v);
      } else {
        oldSet.add(v);
      }
      return new Set(oldSet);
    });
  });

  useImperativeHandle(
    ref,
    () => {
      return {
        getSelectedIds: () => {
          return [...selectedIds];
        },
      };
    },
    [selectedIds]
  );

  return (
    <div className={s.search__container}>
      <CommonSearch
        // ref={commonSearchRef}
        className={s.search__input}
        placeholder={I18N.auto.searchForProjects}
        onSearch={(v) => getSearchList({ v })}
        // value={searchValue}
        onChange={(v) => {
          searchValue.current = v;
        }}
        fill={false}
        border={true}
        round={false}
        debounceTime={350}
        hasPrefix={true}
      />
      <div ref={searchListRef} className={s.search__list}>
        {searchData?.list?.map((item) => {
          if (item.groupId) {
            return null;
          }
          return (
            <Checkbox
              data-id={item.projectId}
              key={item.projectId}
              value={item.projectId}
              checked={selectedIds.has(item.projectId || 0)}
              onChange={handleCheckChange}
              disabled={!!item.groupId}
            >
              {({ checked }) => {
                return (
                  <div
                    className={classNames(s.project__item, 'flex-y-center', {
                      'custom-checkbox-card-checked': checked,
                    })}
                  >
                    <span
                      className={classNames(s.checkbox, 'rock-checkbox', {
                        'rock-checkbox-checked': checked,
                      })}
                    >
                      <span className="rock-checkbox-inner">{checked && <CheckHeavy />}</span>
                    </span>
                    <div className={`${s.project__content} flex-y-center`}>
                      <IconProject
                        className={s.project__icon}
                        fontSize={28}
                        name={item.icon}
                        bgColor={item.iconColor}
                        active
                      ></IconProject>
                      <Tooltip title={item.name} onlyEllipsis>
                        <div className={`${s.project__name} ellipsis`}>{item.name}</div>
                      </Tooltip>
                    </div>
                  </div>
                );
              }}
            </Checkbox>
          );
        })}

        {searchData?.hasMore && (
          <LoadMore
            className={s.load__more}
            canLoadMore={searchData.hasMore}
            scrollContent={searchListRef.current}
            moreFetcher={handleLoadMore}
          />
        )}
      </div>
    </div>
  );
});
export default AddProjectModal;
