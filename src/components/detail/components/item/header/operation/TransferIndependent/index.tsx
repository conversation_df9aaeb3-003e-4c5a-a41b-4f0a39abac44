import { apiTaskDetachSubtaskPost } from '@/api';
import { Icon, IconBtn, Message } from '@/components/basic';
import { Dispatch } from '@/models/store';
import { TodoInfo } from '@/types';
import I18N from '@/utils/I18N';
import { OperateChangetodo, OperateOut } from '@babylon/popo-icons';
import { FC } from 'react';
import { useDispatch } from 'react-redux';

interface ITransferIndependentProps {
  taskInfo: TodoInfo;
}
const TransferIndependent: FC<ITransferIndependentProps> = ({ taskInfo }) => {
  const dispatch = useDispatch<Dispatch>();
  const handleTransferIndependent = () => {
    apiTaskDetachSubtaskPost({
      taskId: taskInfo.taskId,
      parentTaskId: taskInfo.parentId,
    })
      .then(() => {
        Message.success({
          content: (
            <span className="flex-y-center" style={{ columnGap: '10px', lineHeight: '18px' }}>
              <Icon name="icon-taskstate_nodownhover" fontSize={18}></Icon>
              {I18N.auto.transferIndependentSuccess}
            </span>
          ),
        });

        // 更新数据
        dispatch.viewSetting.refreshDataByDataChange({
          refreshList: true,
          refreshCount: true,
        });
        dispatch.detail.getTodoDetail(taskInfo.taskId!);
      })
      .catch((e) => {
        console.log(e);
        Message.error({
          content: (
            <span className="flex-y-center" style={{ columnGap: '10px', lineHeight: '18px' }}>
              {I18N.auto.operationFailedPlease}
            </span>
          ),
        });
      });
  };

  return (
    <IconBtn
      className="ml-10"
      icon={<OperateChangetodo className="fs-16" />}
      title={I18N.auto.transferIndependent}
      onClick={handleTransferIndependent}
    ></IconBtn>
  );
};

export default TransferIndependent;
