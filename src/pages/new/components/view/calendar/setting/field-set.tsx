import React, { PropsWithChildren } from 'react';
import { DragDropContext, Draggable, Droppable, DropResult } from 'react-beautiful-dnd';
import { useDispatch, useSelector } from 'react-redux';

import { Icon, Switch } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { FieldsItem } from '@/types';
import { dndReorder } from '@/utils';
import { DisplayField, EnumField, FieldMap } from '@/utils/fields';

import s from './index.less';
export type Props = {
  className?: string;
};

const FieldSetting: React.FC<PropsWithChildren<Props>> = (props) => {
  const { viewTabList, displayFields } = useSelector((state: RootState) => ({
    viewTabList: state.viewSetting.viewTabList,
    displayFields: state.viewSetting.currentViewTab.displays!,
  }));

  const dispatch = useDispatch<Dispatch>();

  const getItemClassName = (isDragging: boolean) => {
    return isDragging ? s.dragging : s.dragItem;
  };
  // 拖拽计算结果
  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    if (result.source.index === result.destination.index) {
      return;
    }

    const _List = dndReorder(displayFields, result.source.index, result.destination.index);

    dispatch.viewSetting.changeDisplayFields({
      displays: _List,
    });
    // 不进行数据提交，因为服务端会权限校验，返回错误信息
    // dispatch.viewSetting
    //   .updateSetting({
    //     displays: _List,
    //   })
    //   .catch(() => {
    //     dispatch.viewSetting.changeDisplayFields({
    //       displays: displayFields,
    //     });
    //   });
  };
  const updateFieldListBySwitch = (v: boolean, item: FieldsItem) => {
    const _item = {
      ...item,
      visible: v ? 1 : 0,
    };
    const newList = displayFields.map((v) => {
      if (v.fieldName === _item.fieldName) {
        return _item;
      }
      return v;
    });

    dispatch.viewSetting.changeDisplayFields({
      displays: newList as DisplayField[],
    });

    // 不进行数据提交，因为服务端会权限校验，返回错误信息
    // dispatch.viewSetting
    //   .updateSetting({
    //     displays: newList,
    //   })
    //   .catch(() => {
    //     dispatch.viewSetting.changeDisplayFields({
    //       displays: displayFields,
    //     });
    //   });
  };

  return (
    <DragDropContext onDragEnd={onDragEnd} onDragStart={() => { }}>
      <Droppable droppableId="droppable">
        {(provided) => (
          <div {...provided.droppableProps} ref={provided.innerRef} className={s.dragList}>
            {displayFields.map((item: FieldsItem, index) => (
              <Draggable key={index} draggableId={String(index)} index={index}>
                {(provided, snapshot) => (
                  <div
                    className={getItemClassName(snapshot.isDragging)}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <div className={s.item}>
                      <div className={s.itemLeft}>
                        <Icon className={s.icon} name="icon-pc_plane_move"></Icon>
                        <div className={s.fieldName}>
                          {FieldMap?.[item.fieldName as EnumField]?.name}
                        </div>
                      </div>
                      <Switch
                        value={!!item.visible}
                        className={s.switch}
                        onChange={(v) => {
                          updateFieldListBySwitch(v, item);
                        }}
                      ></Switch>
                    </div>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default FieldSetting;
