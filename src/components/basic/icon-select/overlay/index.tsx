import classNames from 'classnames';
import React from 'react';

import { IconProject } from '@/components/basic';
import I18N from '@/utils/I18N';

import { iconColors, icons } from '../utils';
import s from './index.less';

export type Props = {
  className?: string;
  iconName?: string;
  iconBgColor?: string;
  onChangeIcon?: (v: string) => void;
  onChangeColor?: (v: string) => void;
};

const Overlay: React.FC<Props> = (props) => {
  const { iconName, iconBgColor, onChangeIcon, onChangeColor } = props;
  return (
    <div className={s.overlay}>
      <div className={s.desc}>{I18N.auto.selectIcon}</div>
      <div className={s.module}>
        <div className={s.box}>
          {icons.map((item, index) => {
            return (
              <div key={index} className="margin-5">
                <IconProject
                  className={classNames(s.iconbg, s.iconBtn)}
                  name={item.name}
                  onClick={(e) => {
                    e.stopPropagation();
                    onChangeIcon?.(item.name);
                  }}
                  active={iconName === item.name}
                  bgColor={iconName === item.name ? iconBgColor : ''}
                ></IconProject>
              </div>
            );
          })}
        </div>
      </div>
      <div className={classNames(s.desc, 'mt-20')}>{I18N.auto.chooseAColor}</div>
      <div className={s.module}>
        <div className={s.box}>
          {iconColors.map((item, index) => {
            return (
              <div key={index} className="margin-5">
                <IconProject
                  bgColor={item.color}
                  onClick={(e) => {
                    e.stopPropagation();
                    onChangeColor?.(item.color);
                  }}
                  name={item.color === iconBgColor ? 'pi_selected' : ''}
                  active={item.color === iconBgColor}
                  className={s.iconBtn}
                ></IconProject>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Overlay;
