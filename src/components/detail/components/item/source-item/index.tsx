import { useEffect, useState } from 'react';

import { Icon } from '@/components/basic';
import { DetailTodoInfo } from '@/types';
import { getSessionInfo, gotoSession, ServerSourceTypeMapKey } from '@/utils';
import I18N from '@/utils/I18N';

import s from './index.less';

export type Props = {
  className?: string;
  taskInfo?: DetailTodoInfo;
};

const DetailSourceItem = (props: Props) => {
  const { taskInfo } = props;
  const {
    source,
    sourceId,
    sourceAddr,
    sourceAddrTimestamp,
    sourceDisplayIcon,
    sourceDisplayName,
  } = taskInfo!;
  const [sourceInfo, setSourceInfo] = useState<{ avatarURL?: string; name?: string }>({});
  const clickSource = () => {
    if (source && sourceId) {
      try {
        gotoSession({
          source: source as ServerSourceTypeMapKey,
          sourceId,
          sourceAddr,
          sourceAddrTimestamp: String(sourceAddrTimestamp),
        });
      } catch (error) {
        console.error('gotoSession Error', error);
      }
    }
  };
  useEffect(() => {
    if (sourceDisplayIcon && sourceDisplayName) {
      setSourceInfo({
        avatarURL: sourceDisplayIcon,
        name: sourceDisplayName,
      });
    } else if (source && sourceId) {
      getSessionInfo({
        source: source as ServerSourceTypeMapKey,
        sourceId,
      }).then((ret) => {
        setSourceInfo(ret);
      });
    }
  }, [source, sourceDisplayIcon, sourceDisplayName, sourceId]);
  return (
    <span className={s.detailSourceItem}>
      <span className={s.tip}>{I18N.auto.createdInAConversation}</span>
      <span onClick={clickSource} className={s.name}>
        {sourceInfo.name}
      </span>
      <Icon name="icon-triangleLineRight_1" fontSize={12} />
    </span>
  );
};

export default DetailSourceItem;
