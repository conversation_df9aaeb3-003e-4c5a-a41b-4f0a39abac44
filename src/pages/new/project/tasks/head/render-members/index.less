.renderMembersWrapper {
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  &:hover {
    background-color: var(--aBlack4);
  }
}
.renderMembers {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 24px;
  .avatarBox {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 28px;
    border: 2px solid var(--bgBottom);
    border-radius: 28px;
    z-index: 1;
    &:not(:first-child) {
      margin-left: -12px;
    }
    .avatar {
      height: 24px;
      width: 24px;
      border-radius: 24px;
      background: url('~@/assets/images/people.png') center center no-repeat;
      background-size: 100%;
      :global {
        .rock-icon {
          font-size: 24px;
        }
      }
    }
  }
  .countWrapper {
    height: 28px;
    width: 28px;
    border: 2px solid var(--bgBottom);
    border-radius: 24px;
    margin-left: -12px;
    z-index: 2;
    background-color: var(--bgTop);
  }
  .count {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 10px;
    color: var(--TextSecondary);
    background-color: var(--aBlack6);
    margin-right: 8px;
  }
  .fs11 {
    font-size: 11px;
  }
}
.desc {
  margin-left: 4px;
  font-size: 13px;
  color: var(--TextSecondary);
}
