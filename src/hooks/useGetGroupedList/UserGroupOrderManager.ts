// 用户分组排序管理器（单例模式）
export class UserGroupOrderManager {
  static instance: UserGroupOrderManager
  orderMap = new Map<string, number>()
  nextOrder = 1

  static getInstance(): UserGroupOrderManager {
    if (!UserGroupOrderManager.instance) {
      UserGroupOrderManager.instance = new UserGroupOrderManager()
    }
    return UserGroupOrderManager.instance
  }

  getOrder(groupId: string): number {
    if (!this.orderMap.has(groupId)) {
      this.orderMap.set(groupId, this.nextOrder++)
    }
    return this.orderMap.get(groupId)!
  }

  reset(): void {
    this.orderMap.clear()
    this.nextOrder = 1
  }
}