import classNames from 'classnames'
import React, { useContext } from 'react'

import { Icon } from '@/components/basic'
import { Cell, Row } from '@/components/table/components'
import { TableCheckbox } from '@/components/table/components/table-checkbox'
import { TableContext } from '@/components/table/context'
import { DrawerClick<PERSON>rign<PERSON><PERSON>, Drawer<PERSON><PERSON>in } from '@/utils/const'
import I18N from '@/utils/I18N'

import s from './index.less'
import { ToolAdd1 } from '@babylon/popo-icons'
import { Plus } from '@bedrock/icons-react'
import { rIC } from '@/utils/requestIdleCallback'
export type Props<RecordType = any> = {
  className?: string
  data?: RecordType[]
  itemData: RecordType
  hasCheckbox?: boolean
  onRowClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
  onMouseDown?: React.MouseEventHandler<HTMLDivElement>
  onAddBtnClick?: () => void
}
const TableCell: React.FC<Props> = React.forwardRef((props, refs) => {
  const { data, itemData, hasCheckbox, onRowClick, onMouseDown, onAddBtnClick } = props
  const { onSelect, columns } = useContext(TableContext)
  return (
    <Row className={`${s.addrow} addrow`} ref={refs} onRowClick={onRowClick} onMouseDown={onMouseDown}>
      {onSelect ? (
        <Cell>
          <div className="pl-8">
            {hasCheckbox ? <TableCheckbox data={data} itemData={itemData}></TableCheckbox> : null}
          </div>
        </Cell>
      ) : null}
      <Cell className={classNames('todo-td-right-no-border')}>
        <div
          className={classNames(s.addRowCell)}
          onClick={e => {
            ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd
            rIC(onAddBtnClick)
          }}
        >
          <Plus className="fs-16 mr-10" />
          {I18N.auto.newTask}
        </div>
      </Cell>
      {/* {columns?.map((item, k: number) => {
        let _className = '';
        if (item.fixed === 'left') {
          _className = 'todo-td-fixed-left';
        }
        if (item.fixed === 'right') {
          _className = 'todo-td-fixed-right todo-td-left-no-border';
        }
        if (k === 0) {
          return (
            <Cell key={k} className={classNames('todo-td-right-no-border', _className)}>
              <div
                className={classNames(s.addRowCell)}
                onClick={(e) => {
                  (e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd;
                  onAddBtnClick?.();
                }}
              >
                <Icon name="icon-sys_add" className="mr-14"></Icon>
                {I18N.auto.newTask}
              </div>
            </Cell>
          );
        }
        return (
          <Cell key={k} className={classNames('todo-td-right-no-border', _className)}>
            <div
              className={classNames(s.addRowCell)}
              onClick={(e) => {
                (e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.listAdd;
                onAddBtnClick?.();
              }}
            ></div>
          </Cell>
        );
      })} */}
    </Row>
  )
})

export default TableCell
