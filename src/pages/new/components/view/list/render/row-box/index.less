.rowbox {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  border: 1px solid transparent;
  cursor: pointer;
  :global {
    .rock-select-arrow {
      display: none;
    }
  }
  &.focused {
    border: 1px solid var(--Brand600);
  }
  .placeholder {
    position: absolute;
    flex-shrink: 0;
    padding-left: 6px;
    display: none;
    color: var(--IconQuartus);
    z-index: 1;
    &.rightAlign {
    }
  }
  .rowboxWrap {
    display: flex;
    padding-left: 6px;
    height: 100%;
    width: 100%;
    position: relative;
    min-width: 1px;

    &.disabled::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      //z-index: 999;
    }
  }
  .iconWrap {
    width: 22px;
    min-width: 22px;
    height: 22px;
    margin-right: 6px;
    flex-shrink: 0;
    position: relative;
    z-index: 1000;

    :global {
      .icon {
        color: var(--IconTertiary);
      }
    }
  }
  .delete {
    flex-shrink: 0;
    display: none;
    width: 22px;
    height: 22px;
    font-size: 12px;
  }
  &:hover {
    .delete,
    .placeholder {
      display: block;
    }
    :global {
      .rock-select-arrow {
        display: block;
      }
    }
  }
}
.left {
  flex: 1;
}
.right {
  width: 14px;
  font-size: 14px;
  flex-shrink: 0;
  transform: rotate(90deg);
  color: var(--IconSecondary);
}
.disabled {
  cursor: default !important;
}
