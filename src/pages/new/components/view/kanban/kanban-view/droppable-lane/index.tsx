import { UniqueIdentifier } from '@dnd-kit/core';
import { AnimateLayoutChanges, defaultAnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import React from 'react';

import { TaskGroupItem } from '@/types';

import LaneContainer from '../lane';

export interface DroppableContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  horizontal?: boolean;
  hover?: boolean;
  scrollable?: boolean;
  onClick?(): void;
  onRemove?(): void;
  onAdd?: (v: string) => void;
  disabled?: boolean;
  id: string | number;
  items: TaskGroupItem[];
}

const animateLayoutChanges: AnimateLayoutChanges = (args) =>
  defaultAnimateLayoutChanges({ ...args, wasDragging: true });

const DroppableLane: React.FC<DroppableContainerProps> = ({
  children,
  disabled,
  id,
  items,
  style,
  ...props
}) => {
  const { active, attributes, isDragging, listeners, over, setNodeRef, transition, transform } =
    useSortable({
      id,
      data: {
        type: 'container',
        children: items,
      },
      animateLayoutChanges,
    });

  return (
    <LaneContainer
      ref={disabled ? undefined : setNodeRef}
      {...props}
      style={{
        ...style,
        transition,
        transform: CSS.Translate.toString(transform),
        opacity: isDragging ? 0.5 : undefined,
      }}
      handleProps={{
        ...attributes,
        ...listeners,
      }}
      id={id}
    >
      {children}
    </LaneContainer>
  );
};

export default DroppableLane;
