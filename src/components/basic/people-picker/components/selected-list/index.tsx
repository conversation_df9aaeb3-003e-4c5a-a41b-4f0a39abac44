import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import PeopleList from '../people-list';
import s from './index.less';

export type Props = {
  className?: string;
  search?: boolean; //true 表示支持筛选
  resultGroup?: boolean; // true表示结果集支持分组
  list: UserInfo[];
  value?: UserInfo[];
  emptyTip?: React.ReactNode;
  hasMinimum?: boolean;
  multiple?: boolean;
  onChange: (users: UserInfo[], isSelfDelete?: boolean) => void;
  currentUser?: UserInfo;
  canSelect?: boolean; //全部 通过select 选择和取消
  canDelete?: boolean; //单独的删除
  canDeleteSelf?: boolean; //能否单独删除自己
};

const SearchList: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    list,
    value,
    className,
    multiple,
    onChange,
    emptyTip,
    currentUser,
    canSelect = true,
    canDelete = false,
    canDeleteSelf = false,
  } = props;

  return (
    <div className={classNames(s.select, className)}>
      {list.length ? (
        <PeopleList
          value={value}
          onChange={onChange}
          list={list}
          emptyTip={emptyTip}
          multiple={multiple}
          canSelect={canSelect}
          canDelete={canDelete}
          canDeleteSelf={canDeleteSelf}
          currentUser={currentUser}
        ></PeopleList>
      ) : (
        <div className={s.noData}>{I18N.auto.noDataAvailable}</div>
      )}
    </div>
  );
};

export default SearchList;
