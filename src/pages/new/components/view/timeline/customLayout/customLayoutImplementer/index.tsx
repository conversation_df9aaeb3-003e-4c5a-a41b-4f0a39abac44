import { VGroup, VImage, VText } from '@visactor/vtable'
import * as VTable from '@visactor/vtable'
import { BaseTableAPI } from '@visactor/vtable/es/ts-types/base-table'
import { IGroupAttribute } from '@visactor/vtable/es/vrender'
import ReactDom from 'react-dom/client'

import { OneAndMoreServerParamsType, OneAndMoreType, TaskTableRowType } from '@/utils/const'

const centerStyle: Partial<IGroupAttribute> = {
  display: 'flex',
  flexWrap: 'nowrap',
  justifyContent: 'flex-start',
  alignItems: 'center',
  alignContent: 'center',
}

const defaultAvatar = 'https://popo.gsf.netease.com/default_user.png'

export const createImplementer = (
  args: VTable.TYPES.CustomRenderFunctionArg<BaseTableAPI>,
  { userInfo, themeTokensRef }: { userInfo?: any; themeTokensRef?: any },
) => {
  const { table, row, col, rect } = args
  const { height, width } = rect || table.getCellRect(col, row)
  const record = table.getRecordByRowCol(col, row)

  const { assignees, assigneeCount } = record

  const isCreateRow = record._rowType === TaskTableRowType.addBtn

  if (isCreateRow) {
    return {
      rootContainer: null,
    }
  }

  const completeType =
    record.completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one

  const showFinishedIcon = completeType === OneAndMoreType.all
  if (assignees?.length) {
    const index = assignees?.findIndex(item => item.uid === userInfo?.uid)
    if (index !== -1) {
      const targetObject = assignees.splice(index, 1)[0] // 取出指定的对象
      assignees.unshift(targetObject) // 将该对象放到数组首位
    }
  }

  const length = assigneeCount || assignees?.length || 0

  const isImplementersOverflow = length > 3

  const showImplementers = isImplementersOverflow ? assignees.slice(0, 2) : assignees
  if (isImplementersOverflow) {
    showImplementers.push({ type: 'ellipsis', avatarUrl: '' })
  }

  const container = (
    <VGroup
      attribute={{
        width,
        height,
        ...centerStyle,
        visibleAll: true,
        boundsPadding: [0, 8, 0, 8],
        id: 'container',
      }}
    >
      {showImplementers?.map((item, index) => {
        const isEllipsis = item.type === 'ellipsis'

        const img = new Image()
        img.crossOrigin = 'anonymous'

        img.onerror = () => {
          img.src = defaultAvatar
        }

        if (item.avatarUrl?.includes('default_user_icon')) {
          img.src = defaultAvatar
        }

        if (item.avatarUrl) {
          img.src = `${item.avatarUrl}?fop=imageView/0/w/100/h/100`
        } else {
          img.src = defaultAvatar
        }

        return (
          <VGroup
            key={index}
            attribute={{
              width: 20,
              height: 20,
              x: 0,
              y: 0,
              background: isEllipsis ? themeTokensRef.current.aBlack6 : 'transparent',
              // ...centerStyle,
              cornerRadius: 10,
            }}
          >
            {item.type === 'ellipsis' ? (
              <VGroup
                attribute={{
                  width: 20,
                  height: 20,
                  ...centerStyle,
                  justifyContent: 'center',
                }}
              >
                <VText
                  attribute={{
                    text: `+`,
                    fontSize: 13,
                    textAlign: 'center',
                    fontWeight: 500,
                    fill: themeTokensRef.current['TextSecondary'],
                    boundsPadding: [0, 1, 0, 0],
                  }}
                />
                <VText
                  attribute={{
                    text: `${length - 2}`,
                    fontSize: 10,
                    textAlign: 'center',
                    fontWeight: 500,
                    lineHeight: 1.2,
                    fill: themeTokensRef.current['TextSecondary'],
                  }}
                />
              </VGroup>
            ) : (
              <VImage
                attribute={{
                  // id: 'avatar',
                  width: 20,
                  height: 20,
                  image: img,
                  boundsPadding: [0, 3, 0, 0],
                  cornerRadius: 10,
                  cursor: 'pointer',
                }}
              />
            )}
            {showFinishedIcon && item.finished && (
              <VImage
                attribute={{
                  dx: 13,
                  dy: 12,
                  width: 8,
                  height: 8,
                  zIndex: 1,
                  image:
                    '<svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="10" height="10" rx="5" fill="#019E33"/><path d="M2.5 4.5L4.5 6.5L8 3" stroke="white" stroke-width="2"/></svg>',
                }}
              />
            )}
          </VGroup>
        )
      })}
    </VGroup>
  )

  return {
    rootContainer: (
      <VGroup
        attribute={{
          id: 'container',
          width,
          height,
          ...centerStyle,
          visibleAll: true,
        }}
      >
        {container}
      </VGroup>
    ),
    renderDefault: false,
  }
}

export const onTaskNameCellClick = (
  args: VTable.TYPES.MousePointerCellEvent,
  { listTableInstance, userInfo, dispatch }: { listTableInstance?: VTable.ListTable; userInfo?: any; dispatch?: any },
) => {
  console.log('click_cell', args)

  const { col, row, target } = args
  const record = listTableInstance?.getRecordByRowCol(col, row)
  // 完成状态 icon
  if (col === 0 && row >= 1 && target?.id === 'status-icon') {
    const rect = listTableInstance?.getCellRect(col, row) || { top: 0, bottom: 0 }
    const [canSetComplete, canComplete] = getPermissions(record?.permissions)

    const { completeCondition, participantCount, finishedParticipantCount, selfFinished, finished } = record

    const _oneAndMoreType =
      completeCondition === OneAndMoreServerParamsType.all ? OneAndMoreType.all : OneAndMoreType.one

    const isThatLastOne = isThatLastOneFn({
      oneAndMoreType: _oneAndMoreType,
      participantCount: participantCount || 0,
      finishedParticipantCount: finishedParticipantCount || 0,
      selfFinished: selfFinished,
      finished: finished,
    })

    const status = getTaskStatusFn({
      oneAndMoreType: _oneAndMoreType,
      userInfo: userInfo!,
      isThatLastOne,
      isManager: !!canSetComplete,
      ...record,
    })

    if (!canComplete) {
      return
    }

    const selectOperationMethod = (params?: any = {}) => {
      selectOperationMethodFn({
        loadingRef: { current: false },
        dispatch,
        isThatLastOne,
        userInfo: userInfo!,
        visibleDetail: false,
        taskState: status,
        ...record,
        ...params,
      })
    }

    if (status === TaskCompleteStatus.InstantaneousFinished || status === TaskCompleteStatus.InstantaneousUnfinished) {
      selectOperationMethod()
      return
    }
    const scrollTop = listTableInstance?.scrollTop || 0

    const div = document.createElement('div')
    const root = ReactDom.createRoot(div)
    root.render(
      <TaskStatusMenu
        loadingRef={{ current: false }}
        taskState={status}
        selectOperationMethod={selectOperationMethod}
        dispatch
        isThatLastOne
        userInfo={userInfo}
        visibleDetail={false}
        {...record}
      />,
    )

    listTableInstance?.showDropDownMenu(col, row, {
      content: div,
      position: {
        x: 0,
        y: rect?.top,
      },
      referencePosition: {
        rect: {
          left: 0,
          top: rect?.top - scrollTop,
          width: 15,
          height: 15,
          right: 15,
          bottom: rect?.bottom - 7 - scrollTop,
        },
      },
    })
  }
}
