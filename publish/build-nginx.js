let { getAcmNginxConfigContent, updateAcmNginxConfigContent } = require('./acm');
const path = require('path');
const fs = require('fs-extra');
const minimist = require('minimist');

const Const = require('./const');
const { publishSuccessToSend } = require('./popo');

//nginx配置模板
async function genConf() {
  const argv = minimist(process.argv.slice(2));
  const { grayscale_tag, version_tag, env_tag, cimmit_user, ci_pipeline_url } = argv;
  let versionTag = version_tag;
  const code = fs.readFileSync(path.resolve('./publish/nginx_tel.conf'), 'utf-8');
  //获取远程acm上的nginx配置文件
  const nginxContent = await getAcmNginxConfigContent(argv);
  let template = code.replace(/\$API/g, Const.API[env_tag].API || Const.API.production.API);
  const grayscalePrefix = 'set $grayscale_version_tag ';
  const prodPrefix = 'set $default_version_tag ';
  const grayscaleReg = /set \$grayscale_version_tag ([a-zA-Z0-9_-]+);/;
  const prodReg = /set \$default_version_tag ([a-zA-Z0-9_-]+);/;

  if (grayscale_tag) {
    template = template.replace(grayscaleReg, `${grayscalePrefix}${version_tag};`);
    // 灰度保留正式路径
    const data = nginxContent.match(prodReg);
    if (data && data[1]) {
      versionTag = data[1];
    }
    template = template.replace(prodReg, `${prodPrefix}${versionTag};`);
  } else {
    template = template.replace(prodReg, `${prodPrefix}${version_tag};`);
    // 正式发布保留灰度路径
    const data = nginxContent.match(grayscaleReg);
    if (data && data[1]) {
      versionTag = data[1];
    }
    template = template.replace(grayscaleReg, `${grayscalePrefix}${version_tag};`);
  }
  await updateAcmNginxConfigContent({
    ...argv,
    content: template,
  });
  // 发布消息通知
  publishSuccessToSend({ cimmit_user, env_tag, grayscale_tag, version_tag, ci_pipeline_url });
  return true;
}

genConf();

// npm run genNginxConf -- --version_tag='202210311237_689a0135'  --project='popo' --auth_user='_popo' --auth_key='7af18c032a31460daf9af2846afbd742' --acm_host='acm.nie.netease.com'  --acm_group='popo_open_fe' --acm_namespace='test' --acm_config_name='popo-tobedone-fe.conf'
// const params = {
//   acm_group: 'popo_open_fe',
//   acm_config_name: 'popo-tobedone-fe.conf',
//   auth_user: '_popo',
//   auth_key: '7af18c032a31460daf9af2846afbd742',
//   acm_host: 'acm.nie.netease.com',
//   acm_namespace: 'test',
//   project: 'popo',
//   versionTag: '202210311237_689a0135',
// };

// npm run genNginxConf -- --grayscale_tag=gray --env_tag=staging --version_tag=202407031735_b3d71c0e --cimmit_user=<EMAIL> --project=popo --auth_user=_popo --auth_key=7af18c032a31460daf9af2846afbd742 --acm_host=int-acm.nie.netease.com --acm_group=popo_open_fe --acm_namespace=staging --acm_config_name=popo-tobedone-fe.conf
