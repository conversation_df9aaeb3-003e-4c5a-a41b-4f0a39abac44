import { defineConfig } from 'umi';
import { CDN } from './publish/const';
const minimist = require('minimist');

const argv = minimist(process.argv.slice(2));

const versiontag = argv.version_tag;

export default defineConfig({
  publicPath: `${CDN.host}${CDN.projectName}/${versiontag}/dist/`,
  proxy: {
    '/api': {
      target: 'https://test-ncc.popo.netease.com',
      changeOrigin: true,
    },
  },
  devtool: 'eval-source-map',
});
