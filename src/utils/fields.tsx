import {
  Data16Calendar,
  Data16Cjuser,
  Data16Fllowuser,
  Data16Pojectline,
  Data16Redio,
  Data16Timestar,
  Data16Timestop,
  Data16Users,
} from '@babylon/popo-icons'
import { Dayjs } from 'dayjs'

import { getIsSessionTasks, isTaskMenuId } from '@/models/utils'
import { CustomField, FieldTypeEnum } from '@/types/custom-field'
import I18N from '@/utils/I18N'

import { EnumFieldVisible, Finished, OrderCustom, PPTimeFormat, TaskNavigatorType } from './const'

export enum SelectType {
  /**
   * 执行人/分配人/关注人
   */
  people,
  /**
   * 创建时间/开始时间/提醒时间
   */
  time,
  /**
   * 暂时不需要 完成状态
   */
  status,
  /**
   * 优先级
   */
  priority,
  /**
   * 所属项目
   */
  project,
  /**
   * 默认
   */
  text,
  /**
   * 截止时间
   * */
  deadline,
  /**
   * 自定义字段，单选、多选
   * */
  customOptions,
}
export interface IField {
  name: string
  value: string
  renderType?: SelectType
  customField?: CustomField
  icon?: JSX.Element
}

export type DisplayField = {
  name?: string // 显示名称
  fieldName: string //服务端字段,或者自定义字段id
  fieldType?: FieldTypeEnum
  visible: EnumFieldVisible
  id?: any
  customFieldId?: any
}

/**
 * 系统字段
 */
export enum EnumField {
  /**
   * 任务名称
   */
  taskName = 'taskName',
  /**
   * 创建人
   */
  assigner = 'assigner',
  /**
   * 执行人
   */
  assignee = 'assignee',
  /**
   * 关注人
   */
  follower = 'follower',
  /**
   * 创建时间
   */
  createTime = 'createTime',
  /**
   * 截止时间
   */
  deadline = 'deadline',
  /**
   * 开始时间
   */
  startTime = 'startTime',
  /**
   * 提醒时间
   */
  alarmTime = 'alarmTime',
  /**
   * 完成状态
   */
  finished = 'finished',
  /**
   * 优先级
   */
  priority = 'priority',
  /**
   * 所属项目
   */
  project = 'project',
}
//自定义字段的fieldName值
export const CustomFieldName = 'customField'

export const attrTaskName: IField = {
  name: I18N.auto.taskName,
  value: EnumField.taskName,
  renderType: SelectType.text,
}

export const attrAssigner: IField = {
  name: I18N.auto.assignedBy,
  value: EnumField.assigner,
  renderType: SelectType.people,
  icon: <Data16Cjuser></Data16Cjuser>,
}
export const attrAssignee: IField = {
  name: I18N.auto.assignedTo,
  value: EnumField.assignee,
  renderType: SelectType.people,
  icon: <Data16Users></Data16Users>,
}

export const attrFollower: IField = {
  name: I18N.auto.followPeople,
  value: EnumField.follower,
  renderType: SelectType.people,
  icon: <Data16Fllowuser></Data16Fllowuser>,
}
export const attrDeadline: IField = {
  name: I18N.auto.deadline_2,
  value: EnumField.deadline,
  renderType: SelectType.deadline,
  icon: <Data16Timestop></Data16Timestop>,
}

export const attrStartTime: IField = {
  name: I18N.auto.startTime,
  value: EnumField.startTime,
  renderType: SelectType.time,
  icon: <Data16Timestar></Data16Timestar>,
}

export const attrAlarmTime: IField = {
  name: I18N.auto.reminderTime,
  value: EnumField.alarmTime,
  renderType: SelectType.time,
}

export const attrCreatTime: IField = {
  name: I18N.auto.creationTime,
  value: EnumField.createTime,
  renderType: SelectType.time,
  icon: <Data16Calendar></Data16Calendar>,
}

export const attrFinished: IField = {
  name: I18N.auto.completionStatus,
  value: EnumField.finished,
  renderType: SelectType.status,
}
export const attrPriority: IField = {
  name: I18N.auto.priority,
  value: EnumField.priority,
  renderType: SelectType.priority,
  icon: <Data16Redio></Data16Redio>,
}

export const attrProject: IField = {
  name: I18N.auto.belongingProject,
  value: EnumField.project,
  renderType: SelectType.project,
  icon: <Data16Pojectline></Data16Pojectline>,
}

export const FieldMap = {
  [EnumField.taskName]: attrTaskName,
  [EnumField.assignee]: attrAssignee,
  [EnumField.assigner]: attrAssigner,
  [EnumField.follower]: attrFollower,
  [EnumField.deadline]: attrDeadline,
  [EnumField.startTime]: attrStartTime,
  [EnumField.createTime]: attrCreatTime,
  [EnumField.alarmTime]: attrAlarmTime,
  [EnumField.finished]: attrFinished,
  [EnumField.priority]: attrPriority,
  [EnumField.project]: attrProject,
}

export const taskSelectFields = [
  attrAssignee,
  attrAssigner,
  attrFollower,
  attrDeadline,
  attrStartTime,
  attrAlarmTime,
  attrProject, //在项目视图使用需要排除
  attrFinished,
  attrPriority,
  // attrCreatTime,
]

export const defaultFieldOrder = taskSelectFields

export enum QuickFilter {
  unfinished = 'unfinished',
  finished = 'finished',
  deadlineToday = 'today_deadline',
  deadlineNextweek = 'next_week_deadline',
  this_week_deadline = 'this_week_deadline',
  assignToMe = 'assign_to_me',
}

export type IQuickFilter = {
  fieldName: QuickFilter
  name: string
  iconName: string
  checked?: boolean
}

export const QuickFilterList: IQuickFilter[] = [
  {
    fieldName: QuickFilter.unfinished,
    name: I18N.auto.hangInTheAir,
    iconName: 'icon-Soft_qk_nodown',
  },
  {
    fieldName: QuickFilter.finished,
    name: I18N.auto.completed,
    iconName: 'icon-Soft_qk_down',
  },
  {
    fieldName: QuickFilter.assignToMe,
    name: I18N.auto.assignToMe,
    iconName: 'icon-Soft_qk_fp',
  },
  {
    fieldName: QuickFilter.deadlineToday,
    name: I18N.auto.deadlineToday,
    iconName: 'icon-Soft_qk_td',
  },
  {
    fieldName: QuickFilter.this_week_deadline,
    name: I18N.auto.thisWeeksDeadline,
    iconName: 'icon-Soft_qk_week',
  },
  // {
  //   fieldName: QuickFilter.deadlineNextweek,
  //   name: I18N.auto.deadlineNextWeek,
  //   iconName: 'icon-Soft_qk_week',
  // },
]

export enum FilterTime {
  /**
   * 今天之前
   */
  beforeToday = 'before-today',
  /**
   * 今天
   */
  today = 'today',
  /**
   * 明天
   */
  tomorrow = 'tomorrow',
  /**
   * 下周
   */
  thisWeek = 'this-week',
  /**
   * 下周
   */
  nextWeek = 'next-week',
  /**
   * 下个月
   */
  nextMonth = 'next-month',
  /**
   * 未设置
   */
  unset = 'not-set',
}
/**
 * 时间 - 今天之前
 */
export const TimeBeforeToday = {
  name: I18N.auto.beforeToday,
  value: FilterTime.beforeToday,
}

/**
 * 时间 - 今天
 */
export const TimeToday = {
  name: I18N.auto.today,
  value: FilterTime.today,
}

/**
 * 时间 - 明天
 */
export const TimeTomorrow = {
  name: I18N.auto.tomorrow,
  value: FilterTime.tomorrow,
}
/**
 * 时间 - 本周
 */
export const TimeThisWeek = {
  name: I18N.auto.thisWeek,
  value: FilterTime.thisWeek,
}
/**
 * 时间 - 下周
 */
export const TimeNextWeek = {
  name: I18N.auto.nextWeek,
  value: FilterTime.nextWeek,
}
/**
 * 时间 - 下个月
 */
export const TimeNextMonth = {
  name: I18N.auto.nextMonth,
  value: FilterTime.nextMonth,
}
/**
 * 时间 - 未设置
 */
export const TimeUnset = {
  name: I18N.auto.notSet,
  value: FilterTime.unset,
}

/**
 * 时间 - 今天之前
 */
export const DeadlineTimeBeforeToday = {
  name: I18N.auto.cutOffBeforeToday,
  value: FilterTime.beforeToday,
}

/**
 * 时间 - 今天
 */
export const DeadlineTimeToday = {
  name: I18N.auto.deadlineToday_2,
  value: FilterTime.today,
}

/**
 * 时间 - 明天
 */
export const DeadlineTimeTomorrow = {
  name: I18N.auto.deadlineTomorrow,
  value: FilterTime.tomorrow,
}
/**
 * 时间 - 本周
 */
export const DeadlineTimeThisWeek = {
  name: I18N.auto.thisWeeksDeadline_2,
  value: FilterTime.thisWeek,
}
/**
 * 时间 - 下周
 */
export const DeadlineTimeNextWeek = {
  name: I18N.auto.deadlineNextWeek_2,
  value: FilterTime.nextWeek,
}
/**
 * 时间 - 下个月
 */
export const DeadlineTimeNextMonth = {
  name: I18N.auto.deadlineNextMonth,
  value: FilterTime.nextMonth,
}
/**
 * 时间 - 未设置
 */
export const DeadlineTimeUnset = {
  name: I18N.auto.noDeadlineSet,
  value: FilterTime.unset,
}

/**
 * 时间选项下拉组件
 */
export const TimeSelectOptions = [
  TimeBeforeToday,
  TimeToday,
  TimeTomorrow,
  TimeThisWeek,
  TimeNextWeek,
  TimeNextMonth,
  TimeUnset,
]

/**
 * 截止时间选项下拉组件
 */
export const DeadlineTimeSelectOptions = [
  DeadlineTimeBeforeToday,
  DeadlineTimeToday,
  DeadlineTimeTomorrow,
  DeadlineTimeThisWeek,
  DeadlineTimeNextWeek,
  DeadlineTimeNextMonth,
  DeadlineTimeUnset,
]

export const statusUnfinished = {
  name: I18N.auto.unfinishedTasks,
  value: `${Finished.unFinished}`,
}

export const statusFinished = {
  name: I18N.auto.completedTasks,
  value: `${Finished.finished}`,
}

/**
 * 完成状态选项下拉组件
 */
export const StatusSelectOptions = [statusUnfinished, statusFinished]

export enum Priority {
  /**
   *紧急
   */
  Urgent = 1,
  /**
   *高
   */
  High = 2,
  /**
   *中
   */
  Medium = 3,
  /**
   *低
   */
  Low = 4,
  /**
   *未设置
   */
  Unset = 0,
}

export enum FinishedStatus {
  Finished = 1,
  UnFinished = 0,
}

export interface IPriorityField {
  name: string
  value: string
  iconName: string
  className: string
}

/**
 * 优先级 - 紧急
 */
export const PriorityUrgent: IPriorityField = {
  name: I18N.auto.urgent,
  value: `${Priority.Urgent}`,
  iconName: 'icon-kit_priority_priority_urgent',
  className: 'com-level-urgent',
}
/**
 * 优先级 - 高
 */
export const PriorityHigh: IPriorityField = {
  name: I18N.auto.high,
  value: `${Priority.High}`,
  iconName: 'icon-kit_priority_priority_high',
  className: 'com-level-high',
}
/**
 * 优先级 - 中
 */
export const PriorityMedium: IPriorityField = {
  name: I18N.auto.in,
  value: `${Priority.Medium}`,
  iconName: 'icon-kit_priority_priority_medium',
  className: 'com-level-medium',
}
/**
 * 优先级 - 低
 */
export const PriorityLow: IPriorityField = {
  name: I18N.auto.low,
  value: `${Priority.Low}`,
  iconName: 'icon-kit_priority_priority_low',
  className: 'com-level-low',
}
/**
 * 优先级 - 未设置
 */
export const PriorityUnset: IPriorityField = {
  name: I18N.auto.notSet,
  value: `${Priority.Unset}`,
  iconName: 'icon-kit_priority_priority_no',
  className: 'com-level-unset',
}

/**
 * 优先级下拉选项
 */
export const PriorityOptions = [PriorityUrgent, PriorityHigh, PriorityMedium, PriorityLow, PriorityUnset]

export type SortItem = {
  name: string
  value: string
  icon: string
}

export const CustomSort = {
  name: I18N.auto.customSorting,
  value: OrderCustom,
  icon: 'icon-soft_px',
}
export const SortListData: SortItem[] = [
  {
    name: I18N.auto.customSorting,
    value: OrderCustom,
    icon: 'icon-soft_px',
  },
  {
    name: I18N.auto.creationTime,
    value: EnumField.createTime,
    icon: '',
  },
  {
    name: I18N.auto.deadline,
    value: EnumField.deadline,
    icon: 'icon-details_data_calendar',
  },

  {
    name: I18N.auto.taskName,
    value: EnumField.taskName,
    icon: '',
  },
  {
    name: I18N.auto.assignedBy,
    value: EnumField.assigner,
    icon: 'icon-tage_assigner_line',
  },
  {
    name: I18N.auto.assignedTo,
    value: EnumField.assignee,
    icon: 'icon-details_data_user',
  },
  {
    name: I18N.auto.priority,
    value: EnumField.priority,
    icon: '',
  },
]

export type IGroupItem = {
  title: string
  key?: string | Priority
}

//分组条件枚举
export enum EnumGroupBy {
  /**
   * 截止时间
   */
  DEADLINE = 'deadline',
  /**
   * 执行人
   */
  ASSIGNEE = 'assignee',
  /**
   * 优先级
   */
  PRIORITY = 'priority',
  /**
   * 所属项目
   */
  PROJECT = 'project',
  /**
   * 完成状态
   */
  FINISHED = 'finished',
}

//固定分组
export enum EnumFieldNONE {
  NONE = 'NONE',
}

export enum EnumDeadlineGroup {
  EXPIRED = 'EXPIRED',
  TODAY = 'TODAY',
  TOMORROW = 'TOMORROW',
  FUTURE7DAYS = 'FUTURE_7_DAYS',
  FUTURE = 'FUTURE',
  NONE = EnumFieldNONE.NONE,
}

export const DeadlineGroups = [
  EnumDeadlineGroup.EXPIRED,
  EnumDeadlineGroup.TODAY,
  EnumDeadlineGroup.TOMORROW,
  EnumDeadlineGroup.FUTURE7DAYS,
  EnumDeadlineGroup.FUTURE,
  EnumDeadlineGroup.NONE,
]
/**
 *
 * @param time 按时间分组处理分组数据,预留参数
 * @returns
 */
export const getTimeGroupMap = (time: Dayjs) => {
  return {
    [EnumDeadlineGroup.EXPIRED]: {
      title: I18N.auto.past,
      defaultParams: {
        time: time.add(-1, 'day').valueOf(),
        format: PPTimeFormat.olayDay,
      },
    },
    [EnumDeadlineGroup.TODAY]: {
      title: I18N.auto.today,
      defaultParams: { time: time.valueOf(), format: PPTimeFormat.olayDay },
    },
    [EnumDeadlineGroup.TOMORROW]: {
      title: I18N.auto.tomorrow,
      defaultParams: {
        time: time.add(1, 'day').valueOf(),
        format: PPTimeFormat.olayDay,
      },
    },
    [EnumDeadlineGroup.FUTURE7DAYS]: {
      title: I18N.auto.futureDays,
      defaultParams: {
        time: time.add(7, 'day').valueOf(),
        format: PPTimeFormat.olayDay,
      },
    },
    [EnumDeadlineGroup.FUTURE]: {
      title: I18N.auto.inTheFuture,
      defaultParams: {
        time: time.add(8, 'day').valueOf(),
        format: PPTimeFormat.olayDay,
      },
    },
    [EnumDeadlineGroup.NONE]: {
      title: I18N.auto.notArranged,
      defaultParams: { time: 0, format: PPTimeFormat.noDate },
    },
  }
}

export const getPriorityGroupMap = () => {
  return {
    [Priority.Urgent]: {
      title: I18N.auto.urgent,
      defaultParams: { priority: Priority.Urgent },
    },
    [Priority.High]: {
      title: I18N.auto.high,
      defaultParams: { priority: Priority.High },
    },
    [Priority.Medium]: {
      title: I18N.auto.in,
      defaultParams: { priority: Priority.Medium },
    },
    [Priority.Unset]: {
      title: I18N.auto.nothing,
      defaultParams: { priority: Priority.Unset },
    },
    [Priority.Low]: {
      title: I18N.auto.low,
      defaultParams: { priority: Priority.Low },
    },
  }
}

export const getFinishedGroupMap = () => {
  return {
    [FinishedStatus.UnFinished]: {
      title: I18N.auto.hangInTheAir,
      defaultParams: { finished: FinishedStatus.UnFinished },
    },
    [FinishedStatus.Finished]: {
      title: I18N.auto.completed,
      defaultParams: { finished: FinishedStatus.Finished },
    },
  }
}

const basicDisplayFieldType: { [key in EnumField]?: FieldTypeEnum } = {
  [EnumField.deadline]: FieldTypeEnum.datetime,
  [EnumField.assignee]: FieldTypeEnum.user,
  [EnumField.priority]: FieldTypeEnum.option,
  [EnumField.createTime]: FieldTypeEnum.datetime,
  [EnumField.follower]: FieldTypeEnum.user,
  [EnumField.assigner]: FieldTypeEnum.user,
  [EnumField.startTime]: FieldTypeEnum.datetime,
  [EnumField.project]: FieldTypeEnum.option,
}

/**displayFields和自定义字段合并 */
export function combineDisplayFields(
  displays: DisplayField[],
  customFields: CustomField[],
  navigatorId?: TaskNavigatorType | string | number,
) {
  const _basicList = [
    EnumField.follower,
    EnumField.deadline,
    EnumField.assignee,
    EnumField.assigner,
    EnumField.startTime,
    EnumField.priority,
    EnumField.createTime,
  ]
  if (isTaskMenuId(navigatorId) || getIsSessionTasks()) {
    _basicList.push(EnumField.project)
  }
  let basicList: DisplayField[] = _basicList
    .map(key => {
      let filed = FieldMap[key]
      const visible =
        getIsSessionTasks() && key == EnumField.project
          ? 1
          : [EnumField.deadline, EnumField.assignee, EnumField.priority, EnumField.createTime].includes(key)
          ? 1
          : 0
      return {
        name: filed.name,
        fieldName: key as string,
        id: key,
        fieldType: basicDisplayFieldType[key],
        visible,
      }
    })
    .concat(
      customFields.map(f => {
        return {
          id: f.fieldId,
          name: f.name,
          customFieldId: f.fieldId,
          customFieldVersion: f.fieldVersion,
          fieldName: CustomFieldName,
          visible: 1,
          fieldType: f.type,
        }
      }),
    )
  if (!displays?.length) {
    return basicList
  } else {
    const newList = displays.map(display => {
      display.fieldType = basicDisplayFieldType[display.fieldName as EnumField]
      let index = basicList.findIndex(f => f.id == display.customFieldId || f.id == display.fieldName)

      if (index > -1) {
        let field = basicList[index]
        basicList.splice(index, 1)
        field.visible = display.visible!
        return field
      } else {
        return
      }
    })

    return (newList.filter(Boolean) as DisplayField[]).concat(basicList)
  }
}
