import React, { useRef } from 'react';

import s from './index.less';
import TitleInput from './title-input';

export type Props = {
  className?: string;
  value?: string;
  onChange?: (v: string) => void;
  onEnter?: () => void;
};

const AddRow: React.FC<Props> = (props) => {
  const { value, onChange, onEnter } = props;

  return (
    <TitleInput
      className={s.addInput}
      value={value}
      onChange={(v) => {
        onChange?.(v);
      }}
      onEnter={onEnter}
    ></TitleInput>
  );
};

export default AddRow;
