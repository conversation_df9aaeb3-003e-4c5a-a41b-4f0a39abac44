.container {
  width: 280px;
}

.container__header {
  border-bottom: unset;
  padding: 0 20px;
}
.content {
  padding: 4px 16px 16px;
}

.list__container {
  background-color: var(--bgTop);
  min-width: 172px;
  border-radius: 0.08rem;
  box-shadow: var(--ComBoxShadow);
  border: 1px solid var(--aBlack12);
  padding: 4px;
}

.selected__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  column-gap: 4px;

  &:not(:last-child) {
    margin-bottom: 16px;
  }

  .select {
    width: 210px;
  }

  .close {
    :global {
      .icon {
        margin-right: 0;
      }
    }
  }
}
