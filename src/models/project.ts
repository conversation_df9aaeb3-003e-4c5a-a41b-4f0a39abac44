import { createModel } from '@rematch/core';

import {
  ApiProjectCreatePostResponse,
  apiProjectFlowQueryPost,
  ApiProjectFlowQueryPostRequest,
  apiProjectGetGet,
  apiProjectGroupAddMemberPost,
  apiProjectGroupCreatePost,
  apiProjectGroupDeleteMemberPost,
  apiProjectGroupRenamePost,
  apiProjectGroupResortPost,
  apiProjectMemberListGet,
  apiV3ProjectFlowQueryPost,
  apiV3ProjectListPinGet,
} from '@/api';
import { getRandomIcon } from '@/components/basic/icon-select/utils';
import { CacheAddinfo, Member, Pagination, ProjectInfo, ProjectItemStatus } from '@/types';
import { getStorage, setStorage, StorageType } from '@/utils';
import Const, { AddMenuId, EnumEmitter, EnumRole, ProjectListMenuId } from '@/utils/const';

import type { RootModel } from '.';
import { getIsInSession, getPageIsProjectGroup, tfProject } from './utils';
import I18N from '@/utils/I18N';
import { POPOBridgeEmitter } from '@popo-bridge/web';

interface ProjectState {
  projectList: ProjectInfo[];
  total: number;
  pingProjectList: ProjectInfo[];
  cacheAddinfo?: CacheAddinfo;
  keyword?: string;
  pagination: Pagination;
  projectInfo: ProjectInfo;
  memberList?: Member[];
  createTeamInfo?: ApiProjectCreatePostResponse['teamVo'];
  loading?: boolean;
  listLoading?: boolean;
  expandedMap: Record<string, boolean>;
}
const DefaultPagination = {
  size: 50,
  more: false,
  order: undefined,
  orderBy: undefined,
};

const initData: ProjectState = {
  projectList: [],
  total: 0,
  pingProjectList: [],
  cacheAddinfo: undefined,
  keyword: '',
  pagination: Object.assign({}, DefaultPagination) as Pagination,
  projectInfo: {} as ProjectInfo,
  memberList: [],
  createTeamInfo: {},
  loading: true,
  listLoading: true,
  expandedMap: {},
};

export const getEmptyProject = ({ groupId }: { groupId?: number }) => {
  const icon = getRandomIcon();
  const data: CacheAddinfo = {
    projectId: AddMenuId,
    name: '',
    icon: icon.icon,
    iconColor: icon.iconColor,
    members: [],
    createTeam: false,
    status: ProjectItemStatus.project_creating,
    groupId,
  };
  return data;
};

/**
 * 新增空的分组，用在【左侧项目列表新增分组】和【项目管理中新增分组】
 * @param srcList
 * @returns
 */
const addProjectGroupEmptyCommon = (srcList: ProjectInfo[]) => {
  const id = `ProjectGroup-${Date.now()}`;
  const emptyGroup: ProjectInfo = {
    projectId: id,
    id: id,
    name: '',
    isGroup: true,
    status: ProjectItemStatus.editing,
  };
  return [emptyGroup, ...srcList];
};

const createProjectGroupCommon = async ({
  srcList,
  newProjectInfo,
}: {
  srcList: ProjectInfo[];
  newProjectInfo: ProjectInfo;
}) => {
  const firstItem = srcList[0];
  const isValidEditingGroup = firstItem?.isGroup && firstItem.status === ProjectItemStatus.editing;
  if (!newProjectInfo?.name) {
    if (isValidEditingGroup) {
      srcList.shift();
      return srcList;
    }
  } else {
    return apiProjectGroupCreatePost({
      name: newProjectInfo.name,
    })
      .then(({ projectId }) => {
        if (isValidEditingGroup) {
          srcList[0] = {
            ...srcList[0],
            name: newProjectInfo.name,
            projectId,
            isGroup: true,
            status: 'normal',
          };
        } else {
          srcList.unshift({
            name: newProjectInfo.name,
            ...newProjectInfo,
            projectId,
            isGroup: true,
            status: 'normal',
          });
        }

        return [...srcList];
      })
      .catch((err) => {
        srcList[0] = {
          ...srcList[0],
          name: newProjectInfo.name,
          isGroup: true,
          status: 'normal',
        };
        return [...srcList];
      });
  }
};

const removeProjectItem = ({
  projectInfo,
  list: srcList,
}: {
  projectInfo: ProjectInfo | CacheAddinfo;
  list?: ProjectInfo[];
}) => {
  const { projectId, groupId } = projectInfo;
  if (groupId) {
    // 1. 先通过groupId找到对应的group
    const group = srcList?.find((item) => item.projectId === groupId);
    if (group) {
      // 2. 再通过groupId找到对应的project
      group.groupProjects = group.groupProjects?.filter((item) => item.projectId !== projectId);

      if (group.groupProjects?.length === 0) {
        group.groupProjects = [
          {
            status: ProjectItemStatus.placeholder,
            groupId: ProjectItemStatus.placeholder,
            projectId: ProjectItemStatus.placeholder,
            id: ProjectItemStatus.placeholder,
            name: I18N.auto.canDragToGaoup,
          },
        ];
      }

      group.children = group.groupProjects;

      return [...srcList];
    }
  } else {
    return srcList?.filter((item) => item.projectId !== projectId);
  }
};

const insertProjectItemToFirst = ({
  insertData,
  list,
  onBeforeInsert = () => true,
}: {
  insertData: ProjectInfo | CacheAddinfo;
  list: ProjectInfo[];
  onBeforeInsert?: (list: ProjectInfo[] | ProjectInfo) => boolean;
}) => {
  if (insertData.groupId) {
    const group = list.find((item) => item.projectId === insertData.groupId);
    if (group && onBeforeInsert(group)) {
      group.groupProjects = group.groupProjects || [];
      group.groupProjects.unshift(insertData);
    }
  } else {
    list.unshift(insertData);
  }
  return list;
};

export const project = createModel<RootModel>()({
  state: initData,
  reducers: {
    initData(state) {
      return {
        ...initData,
      };
    },
    setData(state, payload: Partial<ProjectState>) {
      return { ...state, ...payload };
    },
    setProjectList(state, payload: ProjectInfo[]) {
      return { ...state, projectList: payload };
    },
    setCacheAddinfo(state, payload: CacheAddinfo | undefined) {
      const { cacheAddinfo: preCacheAddinfo, pingProjectList } = state;

      if (!payload) {
        if (preCacheAddinfo?.projectId) {
          const list = removeProjectItem({
            projectInfo: preCacheAddinfo,
            list: [...pingProjectList],
          });
          return { ...state, pingProjectList: list || [], cacheAddinfo: undefined };
        }
      }

      return { ...state, cacheAddinfo: payload };
    },
  },
  effects: (dispatch) => ({
    gotoAddProject({ groupId, source } = {}, state) {
      let cachedAddInfo = null;
      let list = [...state.project.pingProjectList];

      const datastr = getStorage(Const.ProjectAddItem, StorageType.local);
      if (!datastr) {
        const data = getEmptyProject({ groupId });
        data.source = source;
        cachedAddInfo = data;
      } else {
        cachedAddInfo = JSON.parse(datastr);
        list =
          removeProjectItem({
            projectInfo: cachedAddInfo,
            list,
          }) || [];
        if (cachedAddInfo.groupId !== groupId) {
          cachedAddInfo.groupId = groupId;
        }
      }

      const newList = insertProjectItemToFirst({
        insertData: cachedAddInfo,
        list,
        onBeforeInsert: (group) => {
          if (!Array.isArray(group)) {
            if (group.groupProjects?.[0]?.status === ProjectItemStatus.project_creating) {
              return false;
            }
            if (group.groupProjects?.[0]?.status === ProjectItemStatus.placeholder) {
              group.groupProjects.shift();
            }
            return true;
          }
          return true;
        },
      });

      dispatch.project.setData({
        pingProjectList: newList,
      });

      dispatch.project.setCacheAddinfo(cachedAddInfo);
      setStorage(Const.ProjectAddItem, JSON.stringify(cachedAddInfo), StorageType.local);

      POPOBridgeEmitter.emit(EnumEmitter.TreeExpandById, { id: groupId, expandOnly: true });

      dispatch.viewSetting.openNavigator({
        navigatorId: AddMenuId,
      });
    },
    addProjectGroupEmpty({ isPin = true }, state) {
      const listKey = isPin ? 'pingProjectList' : 'projectList';

      dispatch.project.setData({
        [listKey]: addProjectGroupEmptyCommon(state.project[listKey]),
      });
    },
    async createProjectGroup(opt: { name: string; isPin: boolean }, state) {
      const listKey = opt.isPin ? 'pingProjectList' : 'projectList';

      return createProjectGroupCommon({
        srcList: state.project[listKey],
        newProjectInfo: {
          projectId: AddMenuId,
          name: opt.name,
          type: 'group',
          id: AddMenuId,
        },
      }).then((list) => {
        dispatch.project.setData({
          [listKey]: list,
        });
        if ([ProjectListMenuId, 'all'].includes(state.viewSetting.navigatorId)) {
          dispatch.project.getProjectList({ page: 1 });
        }
        dispatch.project.getPingProjectList({});
        return list;
      });
    },
    updataList({ list, item: newItemData }, state) {
      const { isGroup, groupId, projectId } = newItemData;
      let newList = list;
      if (isGroup || !groupId) {
        const itemIndex = list.findIndex((item: ProjectInfo) => item.projectId === projectId);
        if (itemIndex !== -1) {
          newList = [...list];
          newList[itemIndex] = {
            ...list[itemIndex],
            ...newItemData,
          };
        } else {
          // 可能是分组内的项目，但是没有groupId
          list.some((group: ProjectInfo) => {
            const itemIndex = group.groupProjects?.findIndex(
              (item: any) => item?.projectId === newItemData.projectId
            );
            if (typeof itemIndex === 'number' && itemIndex !== -1 && group.groupProjects?.length) {
              const newGroupProjects = [...group.groupProjects] as ProjectInfo[];
              newGroupProjects[itemIndex] = {
                ...group.groupProjects[itemIndex],
                ...newItemData,
              };
              group.groupProjects = newGroupProjects;
              group.children = newGroupProjects;
              newList = [...list];
              return true;
            }
            return false;
          });
        }
      } else {
        const groupData = list.find((item) => item.projectId === groupId);
        if (groupData) {
          const itemIndex = groupData.groupProjects?.findIndex(
            (item: any) => item?.projectId === newItemData.projectId
          );
          if (itemIndex !== -1 && groupData.groupProjects) {
            const newGroupProjects = [...groupData.groupProjects];
            newGroupProjects[itemIndex] = {
              ...groupData.groupProjects[itemIndex],
              ...newItemData,
            };
            groupData.groupProjects = newGroupProjects;
            groupData.children = newGroupProjects;
            newList = [...list];
          }
        } else {
          const projectDataIndex = list.findIndex(
            (item) => item.projectId === newItemData.projectId
          );
          if (projectDataIndex > -1) {
            const projectData = list[projectDataIndex];
            const newList = [...list];
            newList[projectDataIndex] = Object.assign(projectData, newItemData);

            dispatch.project.setProjectList(newList);
          }
        }
      }
      return newList;
    },
    updateProjectList(opt: Partial<ProjectInfo>, state) {
      const list = dispatch.project.updataList({
        list: state.project.projectList,
        item: opt,
      });
      dispatch.project.setProjectList(list);
    },
    updatePingProjectList(opt: Partial<ProjectInfo>, state) {
      const list = dispatch.project.updataList({
        list: state.project.pingProjectList,
        item: opt,
      });
      dispatch.project.setData({
        pingProjectList: list,
      });
    },
    removeProjectOfList(projectId: number, state) {
      const list = state.project.projectList.filter((item) => item.projectId !== projectId);
      dispatch.project.setProjectList(list);
    },
    deletePingProject(opt: { projectId?: number }, state) {
      const list = state.project.pingProjectList.filter((item) => item.projectId !== opt.projectId);
      dispatch.project.setData({
        pingProjectList: list,
      });
    },
    async getPingProjectList({ } = {}, state) {
      try {
        const {
          items: projects = [],
          totalGroupCount,
          totalProjectCount,
        } = await apiV3ProjectListPinGet({}, { errorSilent: getIsInSession() });

        const totalCount = (totalGroupCount || 0) + (totalProjectCount || 0);
        let cacheAddinfo;
        const datastr = getStorage(Const.ProjectAddItem, StorageType.local);
        if (datastr) {
          try {
            cacheAddinfo = JSON.parse(datastr);
          } catch (error) {
            console.log(error);
          }
        }
        if (cacheAddinfo?.projectId) {
          const { groupId } = cacheAddinfo || {};
          if (groupId) {
            const group = projects.find((item) => item.projectId === groupId);
            if (group) {
              group.groupProjects = group.groupProjects || [];
              group.groupProjects.unshift(cacheAddinfo);
            }
          } else {
            projects.unshift(cacheAddinfo);
          }
        }
        const list = tfProject(projects) as ProjectInfo[];

        dispatch.project.setData({
          total: totalCount, //不是ping的数量,是项目的总数
          pingProjectList: list,
        });
        return { projects, totalCount };
      } catch (error) {
        console.error('get pin list error:', error);
        dispatch.project.setData({
          total: 0, //不是ping的数量,是项目的总数
          pingProjectList: [],
        });
      }
    },
    async getProjectList(options: { page?: number; size?: number }, state) {
      dispatch.project.setData({
        listLoading: true,
      });
      const { keyword, projectList, pagination } = state.project;
      const { page, size } = options;
      const param: ApiProjectFlowQueryPostRequest = {
        keyword,
        size: size || DefaultPagination.size,
      };
      if (page !== 1 && !!projectList.length) {
        param.scrollId = pagination.scrollId;
        param.searchId = pagination.searchId;
      } else {
        param.scrollId = undefined;
        param.searchId = undefined;
      }

      const request: any = !!keyword ? apiProjectFlowQueryPost : apiV3ProjectFlowQueryPost;

      const { list: _list = [], hasMore, searchId, scrollId } = await request(param);
      let list: ProjectInfo[] = tfProject(_list) as ProjectInfo[];
      if (page !== 1) {
        list = projectList.concat(list);
      }
      dispatch.project.setData({
        projectList: list,
        pagination: {
          ...pagination,
          more: hasMore,
          searchId,
          scrollId,
        },
        listLoading: false,
      });
      return {
        list,
        hasMore,
      };
    },
    async getProject(opt: { id: string; hideLoading?: boolean }) {
      const { id, hideLoading } = opt;
      if (!hideLoading) {
        dispatch.project.setData({
          loading: true,
        });
      }
      const data = await apiProjectGetGet({ projectId: id });

      dispatch.project.setData({
        projectInfo: tfProject(data) as ProjectInfo,
        loading: false,
      });
    },
    async getMemberList(id: string, state) {
      const data = await apiProjectMemberListGet({ projectId: id });
      dispatch.project.setData({
        memberList: data.map((item) => ({
          ...item,
          uid: item.memberId!,
          sessionType: item.memberType!,
          name: item.name!,
          avatarUrl: item.pic!,
          role: item.role as EnumRole.admin,
        })),
      });
    },
    removeItem({ projectInfo, isPin }: { projectInfo: ProjectInfo; isPin?: boolean }, state) {
      const listKey = isPin ? 'pingProjectList' : 'projectList';

      const srcList = state.project[listKey];
      const list = removeProjectItem({
        projectInfo,
        list: srcList,
      });

      dispatch.project.setData({
        [listKey]: list,
      });
    },
    insertProjectItem(
      {
        insertData,
        newGroupId,
        preId,
        tailId,
        oldGroupId,
        overGroupId,
        isPin,
      }: {
        isPin: boolean;
        insertData: ProjectInfo;
        newGroupId?: number;
        preId?: number;
        tailId?: number;
        oldGroupId?: number;
        overGroupId?: number;
      },
      state
    ) {
      const listKey = isPin ? 'pingProjectList' : 'projectList';
      const srcList = state.project[listKey];
      if (newGroupId || overGroupId) {
        const group = srcList.find((item) => item.projectId === (newGroupId || overGroupId));
        if (group) {
          if (preId) {
            const preIndex = group.groupProjects?.findIndex((item) => item.projectId === preId);
            if (preIndex !== -1) {
              group.groupProjects?.splice(preIndex + 1, 0, insertData);
            }
          } else if (tailId) {
            const tailIndex = group.groupProjects?.findIndex((item) => item.projectId === tailId);
            if (tailIndex !== -1) {
              group.groupProjects?.splice(tailIndex, 0, insertData);
            }
          } else {
            group.groupProjects?.unshift(insertData);
          }
          dispatch.project.setData({
            [listKey]: [...srcList],
          });
        }
      } else {
        if (preId) {
          const preIndex = srcList.findIndex((item) => item.projectId === preId);
          if (typeof preIndex === 'number' && preIndex !== -1) {
            srcList.splice(preIndex + 1, 0, insertData);
            dispatch.project.setData({
              [listKey]: [...srcList],
            });
          }
        } else {
          srcList.unshift(insertData);
          dispatch.project.setData({
            [listKey]: [...srcList],
          });
        }
      }

      // if (insertIndexes.length === 1) {
      //   const index = insertIndexes[0];
      //   srcList.splice(index, 0, insertData);
      //   dispatch.project.setData({
      //     [listKey]: [...srcList],
      //   });
      // } else if (insertIndexes.length > 1) {
      //   const group = dispatch.project.getGroupByIndexes({
      //     indexes: insertIndexes,
      //     list: srcList,
      //   });

      //   if (group) {
      //     const index = insertIndexes[insertIndexes.length - 1];
      //     if (index) {
      //       group.groupProjects?.splice(index, 0, insertData);
      //       dispatch.project.setData({
      //         [listKey]: [...srcList],
      //       });
      //     }
      //   }
      // }
    },
    async addToGroup(
      { projectInfo, groupInfo }: { projectInfo: ProjectInfo; groupInfo: ProjectInfo },
      state
    ) {
      return apiProjectGroupAddMemberPost({
        projectId: projectInfo.projectId!,
        groupId: groupInfo.groupId!,
      }).then((res) => {
        dispatch.project.getPingProjectList();
        if (state.project.projectList.length) {
          dispatch.project.getProjectList({ page: 1 });
        }
        return res;
      });
    },
    async removeFromGroup({ projectInfo }: { projectInfo: ProjectInfo }, state) {
      if (!projectInfo.groupId) {
        return;
      }
      return apiProjectGroupDeleteMemberPost({
        projectId: projectInfo.projectId!,
        groupId: projectInfo.groupId!,
      }).then((res) => {
        dispatch.project.getPingProjectList({});
        if ([ProjectListMenuId, 'all'].includes(state.viewSetting.navigatorId)) {
          dispatch.project.getProjectList({ page: 1, size: state.project.projectList.length });
        }
        return res;
      });
    },
    editGroup({ projectInfo, isPin }: { projectInfo: ProjectInfo; isPin: boolean }, state) {
      const listKey = isPin ? 'pingProjectList' : 'projectList';
      const srcList = state.project[listKey];

      const groupData = srcList.find((item) => item.projectId === projectInfo.projectId);
      if (groupData) {
        groupData.status = ProjectItemStatus.editing;
        dispatch.project.setData({
          [listKey]: [...srcList],
        });
      }
    },
    async projectSort(
      params: {
        newGroupId?: number;
        currentId?: number;
        preId?: number;
        tailId?: number;
        oldGroupId?: number;
      },
      state
    ) {
      // const { isPin, activeItem } = params;
      // dispatch.project.removeItem({ projectInfo: activeItem, isPin });
      // dispatch.project.insertProjectItem({
      //   insertData: activeItem,
      //   ...params,
      // });
      return apiProjectGroupResortPost({
        currentId: params.currentId,
        newGroupId: params.newGroupId,
        preId: params.preId,
        tailId: params.tailId,
        oldGroupId: params.oldGroupId,
      }).then(() => {
        dispatch.project.getPingProjectList({});
        if ([ProjectListMenuId, 'all'].includes(state.viewSetting.navigatorId)) {
          dispatch.project.getProjectList({ page: 1, size: state.project.projectList.length });
        } else if (getPageIsProjectGroup()) {
          POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, {
            groupId: state.viewSetting.navigatorId,
          });
        }
      });
    },
    updateGroupName({ projectId, name, isPin = true }, state) {
      const listKey = isPin ? 'pingProjectList' : 'projectList';
      const srcList = state.project[listKey];

      const groupData = srcList.find((item) => item.projectId === projectId);
      const preName = groupData?.name;
      if (groupData) {
        groupData.name = name;
        delete groupData.status;
        dispatch.project.setData({
          [listKey]: [...srcList],
        });
      }
      if (preName === name) {
        return;
      }
      apiProjectGroupRenamePost({
        groupId: projectId,
        name,
      }).then(() => {
        dispatch.project.getPingProjectList();

        if (state.viewSetting.navigatorId === projectId) {
          POPOBridgeEmitter.emit(EnumEmitter.ProjectGroupChange, { groupId: projectId });
        } else if ([ProjectListMenuId, 'all'].includes(state.viewSetting.navigatorId)) {
          dispatch.project.getProjectList({ page: 1 });
        }
      });
    },
  }),
});
