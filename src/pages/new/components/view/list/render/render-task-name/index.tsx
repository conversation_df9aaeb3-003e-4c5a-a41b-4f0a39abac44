import classNames from 'classnames'
import React, { memo, useMemo, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'

import { apiTodoUpdateTitlePost } from '@/api'
import { IconBtn } from '@/components/basic'
import ComFieldText from '@/components/basic-project/com-field-text'
import TodoStatus from '@/components/basic-task/todo-status'
import { Dispatch, store } from '@/models/store'
import { TaskInfo } from '@/types'
import {
  DrawerClickOrignKey,
  DrawerOrigin,
  EventCategory,
  OneAndMoreServerParamsType,
  TITLE_MAX_LENGTH,
} from '@/utils/const'
import I18N from '@/utils/I18N'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import RowOperate from '../row-operate'
import s from './index.less'
import RenderTaskNameWrap from './render-task-name-wrap'
import RenderSubCount from './RenderSubCount'
import RenderOperation from './RenderOperation'
import { Row } from '@tanstack/react-table'
import useGetPermissions from '@/hooks/useGetPermissions'
import { getFinishStatus } from '../../utils'

interface Props {
  visibleDetail?: boolean
  editable?: boolean
  taskInfo?: TaskInfo
  tableRowData?: Row<any>
  /** 来自于哪个页面, 任务详情中的子任务允许编辑（有权限前提） */
  from?: string
  size?: 'small' | 'default' | 'large'
  showViewBtn?: boolean
  showViewText?: boolean
}

const RenderTaskName: React.FC<Props> = props => {
  const {
    taskInfo,
    tableRowData,
    from,
    size = 'default',
    editable = true,
    showViewBtn = true,
    showViewText = true,
  } = props
  const {
    taskId,
    title,
    selfFinished,
    finished,
    completeCondition,
    commentCount,
    finishedParticipantCount = 0,
    participantCount = 0,
  } = taskInfo!
  const inputRef = useRef<{ setValue: (v: string) => void }>({
    setValue: v => {},
  })
  const domRef = useRef<HTMLDivElement>()
  //TODO 是否是编辑态 需要提取到列表数据中, 这样更好管理 待优化  后续是否是选中态可以和编辑态合并, 枚举区分
  const [editding, setEditding] = useState<boolean>(false)
  // const [focus, setFocus] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>()

  let permissions = useGetPermissions({ taskId })
  // permissions = permissions || taskInfo?.permissions;
  const changeEditding = (v: boolean) => {
    setEditding(v)

    const dom = domRef.current?.closest?.('.todo-td-fixed-left')
    if (v) {
      if (dom) {
        dom.style.zIndex = 999
      }
    } else {
      if (dom) {
        dom.style.zIndex = ''
      }
    }
  }
  const change = (v?: string) => {
    changeEditding(false)
    if (v && v.length > TITLE_MAX_LENGTH) {
      return
    }
    // const copyValue = item.title;
    // dispatch.batch.exitBatchMode();
    if (v && v !== title) {
      dispatch.task.updateTaskInfo({
        taskInfo: Object.assign(taskInfo, { title: v }),
        actionType: !v ? 'delete' : 'update',
      })
      if (v) {
        apiTodoUpdateTitlePost({
          taskId: taskId,
          title: v,
        })
          .then(() => {
            // updateItem({
            //   title: v,
            // });
            setTimeout(() => {
              dispatch.viewSetting.refreshDataByDataChange({
                refreshList: true,
                refreshCount: false,
                detail: {
                  taskId,
                  ...taskInfo,
                  title: v,
                },
              })
            }, 16)
          })
          .catch(error => {
            console.log(error) // onChange?.(copyValue);
          })
      }
    } else {
      //onChange?.(copyValue);
    }
  }

  const participantCountStr = useMemo(() => {
    return !!participantCount && participantCount > 0 ? `${finishedParticipantCount}/${participantCount}` : ''
  }, [participantCount, finishedParticipantCount])

  const showProgress = useMemo(() => {
    return (
      !editding &&
      !!participantCountStr &&
      completeCondition === OneAndMoreServerParamsType.all &&
      participantCount >= 2
    )
  }, [editding, participantCountStr, completeCondition, participantCount])

  const disabledTodoStatus = useMemo(() => {
    const CAN_COMPLETE = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_COMPLETE,
    })
    return !CAN_COMPLETE
  }, [permissions, taskInfo?.taskId])

  const disabled = useMemo(() => {
    const CAN_EDIT = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_EDIT,
    })
    return !CAN_EDIT
  }, [permissions, taskInfo?.taskId])

  const disabledView = useMemo(() => {
    const CAN_VIEW = validatesPermission({
      permissions: permissions,
      key: TaskPermissionEnum.CAN_VIEW,
    })
    return !CAN_VIEW
  }, [permissions, taskInfo?.taskId])

  // 直接获取store中detail的visibleDetail
  const { visibleDetail } = store.getState().detail
  const taskEditable = useMemo(() => {
    if (from === 'detail' && !disabled) {
      return true
    }
    return !visibleDetail
  }, [from, visibleDetail, disabled])

  const memoFinished = useMemo(() => {
    return getFinishStatus({ completeCondition, selfFinished, finished })
  }, [selfFinished, finished, completeCondition])

  return (
    <RenderTaskNameWrap
      disabled={disabledView}
      onClick={e => {
        e.stopPropagation()
        ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.list
        dispatch.detail.openDetail(taskId!)
      }}
      statusNode={
        <RowOperate className={s.operate}>
          <TodoStatus disabled={!taskInfo?.editable && disabledTodoStatus} taskInfo={taskInfo} />
        </RowOperate>
      }
    >
      <div ref={domRef} className={classNames(s.titleWarp, 'title__wrap', { ['pr-6']: !editding })}>
        <div
          className={classNames(s.titleWarpLeft, {
            [s.onlyLeft]: editding,
            [s.disabled]: disabled || !editable,
            [s.finished]: memoFinished,
            [s.fullWidth]: editding,
          })}
        >
          <div
            className={classNames(s.listTitleEditorWrapper, {
              [s.fullWidth]: editding,
              [s[size]]: size,
            })}
            onClick={e => {
              e.stopPropagation()
            }}
          >
            <ComFieldText
              key={title}
              ref={inputRef}
              className={classNames(s.listTitleEditor, 'listTitleEditor', {
                [s.fullWidth]: editding,
              })}
              contentClassName={`${s.listTitleContent} listTitleContent`}
              focusedClassName={classNames({ [s.focused]: editding })}
              value={title}
              onChange={change}
              editable={taskEditable && editable}
              disabled={disabled}
              editding={editding}
              onChangeEditding={v => {
                changeEditding(v)
              }}
              onBlur={e => {
                if (e.target.value) {
                  change(e.target.value)
                } else {
                  inputRef.current.setValue(title)
                }
                changeEditding(false)
              }}
            />
          </div>

          <div className={s.taskname__extra}>
            {!editding && !disabledView && <RenderSubCount dispatch={dispatch} taskInfo={taskInfo} />}

            {!editding && !!commentCount && !disabledView && (
              <span className={classNames(s.commentCount)}>
                <IconBtn
                  placement="top"
                  title={I18N.auto.viewComments}
                  iconClassName="taskname__extra--icon"
                  className="taskname__extra--iconBtn"
                  iconName={'icon-quick_chat'}
                  onClick={e => {
                    ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.list
                    // 默认打开评论
                    dispatch.record.setEventCategory(EventCategory.comment)
                    dispatch.detail.openDetail(taskId!)
                  }}
                >
                  <span className={'pl-3'}>{commentCount}</span>
                </IconBtn>
              </span>
            )}

            {showProgress && !disabledView && (
              <span className={classNames(s.participantCount)}>
                <IconBtn
                  placement="top"
                  iconClassName="taskname__extra--icon"
                  className="taskname__extra--iconBtn"
                  title={I18N.template(I18N.auto.humanCompletion, { val1: participantCountStr })}
                  iconName={'icon-quick_jd'}
                >
                  <span className={'pl-3'}>{participantCountStr}</span>
                </IconBtn>
              </span>
            )}
          </div>
        </div>
        {!editding && (
          <RenderOperation
            showViewText={showViewText}
            showViewBtn={showViewBtn}
            tableRowData={tableRowData}
            taskInfo={taskInfo}
            disabledView={disabledView}
            disabled={disabled}
          />
        )}
      </div>
    </RenderTaskNameWrap>
  )
}

export default memo(RenderTaskName)
