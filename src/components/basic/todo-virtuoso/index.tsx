import classNames from 'classnames';
import React, { PropsWithChildren, useMemo } from 'react';
import type { ListRange } from 'react-virtuoso';
import { GroupedVirtuoso, Virtuoso } from 'react-virtuoso';

import { ListGroupItem } from '@/types';

import CollapseBtn from './collapse-btn';
import s from './index.less';
export { ListRange };
export type Props = {
  isGroup?: boolean;
  atBottomStateChange?: (atBottom: boolean) => void;
  groupData: ListGroupItem[];
  data?: any[];
  renderItem: (index: number, groupIndex: number) => React.ReactNode;
  onChange?: (v: ListGroupItem[]) => void;
  rangeChanged?: (range: ListRange) => void;
};

const CustomizeTopItemList: React.FC<PropsWithChildren<any>> = (props) => {
  const { children } = props;
  return <div>{children}</div>;
};

const TodoVirtuoso: React.FC<PropsWithChildren<Props>> = (props) => {
  const { isGroup, groupData, data, atBottomStateChange, rangeChanged, renderItem, onChange } =
    props;
  const groupCounts = useMemo(() => {
    return groupData?.map((item) => {
      if (item.collapsed) {
        return 0;
      } else {
        return item.count;
      }
    });
  }, [groupData]);
  if (!groupData.length && !data?.length) {
    return null; // 如果没有数据提前渲染虚拟区域,组件不会自动调整分组和不分组的区别
  }

  return isGroup ? (
    <GroupedVirtuoso
      id={'scroll-content-group'}
      style={{ height: '100%', overflowX: 'hidden' }}
      atBottomThreshold={150}
      atBottomStateChange={atBottomStateChange}
      rangeChanged={rangeChanged}
      groupCounts={groupCounts}
      components={{
        TopItemList: CustomizeTopItemList, //去除分组项的悬浮
      }}
      groupContent={(index: number, a) => {
        const ListGroupItem = groupData?.[index];
        const { todoList } = ListGroupItem;
        const taskIds = todoList?.map((item) => item.taskId!);
        return (
          <CollapseBtn
            className={classNames({ [s.itemhidden]: ListGroupItem?.hidden })}
            text={ListGroupItem?.name}
            taskIds={taskIds}
            value={ListGroupItem?.collapsed}
            onChange={(fold) => {
              ListGroupItem.collapsed = fold;
              groupData[index] = ListGroupItem;
              onChange?.([...groupData]);
            }}
          ></CollapseBtn>
        );
      }}
      increaseViewportBy={{ top: 20, bottom: 20 }}
      itemContent={renderItem}
    ></GroupedVirtuoso>
  ) : (
    <Virtuoso
      data={data}
      id={'scroll-content-group'}
      style={{ height: '100%', overflowX: 'hidden' }}
      atBottomThreshold={150}
      atBottomStateChange={atBottomStateChange}
      rangeChanged={rangeChanged}
      increaseViewportBy={{ top: 20, bottom: 20 }}
      itemContent={renderItem}
    ></Virtuoso>
  );
};

export default TodoVirtuoso;
