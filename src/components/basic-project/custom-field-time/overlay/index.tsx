import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

import { DatePicker } from '@/components/basic';
import { PickerDateSelect } from '@/components/basic/popo-convergence-date-picker/convergence-date-picker/components/time-header';
import {
  getDayStart,
  getTimePeriodMilliseconds,
  PPTimeFormat,
} from '@/components/basic/popo-convergence-date-picker/utils';
import { TimeValueAndTimeNum } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  value?: Dayjs;
  timeFormat?: PPTimeFormat;
  onChange?: (time: Dayjs) => void;
}
const getTimeNum = (value: Dayjs) => {
  return value ? getTimePeriodMilliseconds(dayjs(value)) : 0;
};
const Overlay: React.FC<Props> = (props) => {
  const { value, timeFormat = PPTimeFormat.olayDay, onChange } = props;
  const [timeValue, setTimeValue] = useState<TimeValueAndTimeNum>({});
  const changeDate = (v: Dayjs) => {
    let timeNum = timeValue.timeNum || 0;
    if (timeFormat === PPTimeFormat.dateAndTime && !timeNum) {
      timeNum = getTimeNum(dayjs());
    }
    const _timeValue = {
      time: getDayStart(v).valueOf(),
      timeNum: timeNum,
    };
    setTimeValue(_timeValue);
    onChange?.(dayjs(_timeValue.time + _timeValue.timeNum));
  };
  const changeTime = (v: TimeValueAndTimeNum) => {
    setTimeValue(v);
    if (timeFormat) {
      onChange?.(dayjs(v.time! + v.timeNum!));
    } else {
      onChange?.(dayjs(v.time! + getTimeNum(dayjs(v.time))));
    }
  };
  useEffect(() => {
    if (value) {
      const _timeValue = {
        time: getDayStart(value).valueOf(),
        timeNum: getTimeNum(dayjs(value)),
      };
      setTimeValue(_timeValue);
    }
  }, [value]);
  const memoTime = useMemo(() => {
    return timeValue.time ? dayjs(timeValue.time) : undefined;
  }, [timeValue.time]);
  console.log(timeValue.time);
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
      className={classNames('com-dropdown-select', s.overlay)}
    >
      <div className={s.top}>
        <PickerDateSelect
          datePlaceholder={I18N.auto.selectDate}
          timePlaceholder={I18N.auto.time_2}
          value={timeValue}
          showTime={timeFormat === PPTimeFormat.dateAndTime}
          active={true}
          onChange={(v) => {
            changeTime(v);
          }}
        ></PickerDateSelect>
      </div>
      <div className={s.panel}>
        <DatePicker.Panel picker="date" value={memoTime} onChange={changeDate} />
      </div>
    </div>
  );
};

export default Overlay;
