// eslint-disable-next-line simple-import-sort/imports
import { Modal as BedrockModal } from '@bedrock/components';
import type { ModalFuncProps, ModalProps } from '@bedrock/components/es/Modal/index';

import './index.less';

type Props = ModalProps;

type Status = 'info' | 'success' | 'error' | 'warning';

export default function Modal(props: Props) {
  return <BedrockModal maskClosable={false} closable={false} {...props}></BedrockModal>;
}

interface ConfirmModalProps extends ModalFuncProps {
  warningTip?: string;
}

Modal.confirm = function (props: ConfirmModalProps, status: Status = 'info') {
  const { width = 520, ...restProps } = props;
  return BedrockModal.confirm(
    {
      ...restProps,
      width,
      className: `${props.className || ''}`,
    },
    status
  );
};

Modal.warning = BedrockModal.warning;
Modal.info = BedrockModal.info;
Modal.destroyAll = BedrockModal.destroyAll;
Modal.open = BedrockModal.open;
