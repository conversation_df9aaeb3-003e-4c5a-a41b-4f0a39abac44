import { pp } from '@popo-bridge/web'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'
import _ from 'lodash'

import { TaskTime, ViewTab } from '@/types'

import { PPTimeFormat, ServerSourceTypeMap, ServerSourceTypeMapKey, ViewType } from './const'
import { planView2ScaleType } from '@/pages/new/components/view/timeline/utils'
import { combineDisplayFields, DisplayField } from './fields'

export type { ServerSourceTypeMapKey }

export function isControlled(value?: any) {
  return !(typeof value === 'undefined' || value === null)
}

export function BFS<T extends { children?: any[] }>(data: T[], fn: (item: T) => any) {
  let arr = [...data]
  for (let i = 0; i < arr.length; i++) {
    let item = arr[i]
    fn(item)
    if (item.children) {
      arr.push(...item.children)
    }
  }
}
export function DFS<T extends { children?: any[] }>(data: T[], fn: (item: T) => any) {
  data.forEach(item => {
    fn(item)
    if (item.children && item.children.length) {
      DFS(item.children, fn)
    }
  })
}
export function deleteObjArrItem(arr: any[], item: any, key: string) {
  let index = arr.findIndex(o => o[key] === item[key])
  if (index > -1) {
    arr.splice(index, 1)
  }
  return index > -1
}

export enum StorageType {
  local = 'local',
  session = 'session',
}

export function getStorage(key: string, type = StorageType.session) {
  let text
  if (type === StorageType.local) {
    text = localStorage.getItem(key)
  } else {
    text = sessionStorage.getItem(key)
  }
  if (text) {
    try {
      return JSON.parse(text)
    } catch (error) {
      return text
    }
  }
  return text
}

export function setStorage(key: string, v: any, type = StorageType.session) {
  let text = ''
  try {
    text = JSON.stringify(v)
  } catch (error) {
    console.log(error)
  }
  if (type === StorageType.local) {
    localStorage.setItem(key, text)
  } else {
    sessionStorage.setItem(key, text)
  }
}

export function removeStorage(key: string, type = StorageType.session) {
  if (type === StorageType.local) {
    localStorage.removeItem(key)
  } else {
    sessionStorage.removeItem(key)
  }
}

export function getUrlParams(url: string) {
  const reg = /([^&?#]+)=([^&?#]+)/g
  const obj: Record<string, string> = {}
  url.replace(reg, (...arg) => {
    const [, arg1, arg2] = arg
    obj[arg1] = arg2
    return ''
  })
  return obj
}

export interface SessionOption {
  source: ServerSourceTypeMapKey
  sourceId: string
  sourceAddr?: string
  sourceAddrTimestamp?: string
}

export async function getSessionInfo(opt: SessionOption) {
  const { source, sourceId } = opt
  const { avatarURL, name = ' ' } = await pp.getSessionInfo({
    id: sourceId,
    type: ServerSourceTypeMap[source],
  })
  let _avatarURL = avatarURL
  //source 2 多人会话  5文件传输助手
  if (source === 2) {
    _avatarURL = require('@/assets/images/multi_talker.png')
  }
  if (source === 5) {
    _avatarURL = require('@/assets/images/transfer.png')
  }
  return {
    avatarURL: _avatarURL,
    name,
  }
}

export async function gotoSession(opt: SessionOption) {
  const { source, sourceId, sourceAddr, sourceAddrTimestamp } = opt
  let params: pp.OpenMessageSessionParams = {
    id: sourceId,
    type: ServerSourceTypeMap[source],
  }
  if (sourceAddr) {
    params = {
      ...params,
      msgUuid: sourceAddr,
      msgTime: Number(sourceAddrTimestamp),
    }
  }
  pp.openMessageSession(params)
}

export const onPageVisibleChange = (func: (isShow: boolean) => void) => {
  function handleVisibilityChange() {
    const isShow = document.visibilityState === 'visible'
    func(isShow)
  }
  document.addEventListener('visibilitychange', handleVisibilityChange)
}

export const text2Href = (v = ''): string => {
  return v?.replace(
    /(((?:https?:(?:\/\/)?)(?:[-;:&=\\+\\$,\w]+@)?[A-Za-z0-9.-]+|(?:www\.)([A-Za-z0-9.-])+)((?:\/[\\+~%\\/.\w-_#,]*)?\??(?:[-\\+=&;%@.\w_]*)#?(?:[\w]*))?)/g,
    (match, $1) => {
      if (/^https?/.test(match)) {
        return `<a href="${$1}" data-popo="tag" contenteditable="false" target="_blank">${$1}</a>`
      } else {
        return `<a href="https://${$1}" data-popo="tag" contenteditable="false" target="_blank">${$1}</a>`
      }
    },
  )
}

export const href2Text = (v: string): string => {
  return v.replace(/<a href=".*" data-popo="tag" contenteditable="false" target="_blank">(.*?)<\/a>/g, '$1')
  // .replace(/[\n]$/, '');
}

export const pxToRem = (px: number) => {
  let baseFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
  return px / baseFontSize + 'rem'
}

export const dndReorder = (list: any[], startIndex: number, endIndex: number) => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

export function arraysEqualIgnoreOrder(arr1: string[], arr2: string[]) {
  if (arr1.length !== arr2.length) {
    return false
  }

  const set1 = new Set(arr1)
  const set2 = new Set(arr2)

  return set1.size === set2.size && [...set1].every(value => set2.has(value))
}

export function isUpdateTime(curValue: TaskTime, preValue: TaskTime) {
  const { startTime: _startTime, deadline: _deadline, rrule: _rrule, timeFormat: _timeFormat, alarm: _alarm } = curValue
  const { startTime, deadline, rrule, timeFormat, alarm: alarm } = preValue
  // 判断数据是否需要变更
  if (
    _startTime == startTime &&
    rrule == _rrule &&
    _alarm?.time == alarm?.time &&
    _alarm?.rrule == alarm?.rrule &&
    timeFormat == _timeFormat
  ) {
    if (timeFormat === PPTimeFormat.olayDay) {
      //
      if (dayjs(_deadline).isSame(dayjs(deadline), 'day')) {
        return false
      }
      return true
    } else {
      if (_deadline == deadline) {
        return false
      }
      return true
    }
  }
  return true
}

export function randomString(length: number) {
  const code = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
  let result = ''
  for (let index = 0; index < length; index++) {
    result += code[Math.floor(Math.random() * code.length)]
  }
  return result
}

export function compareViews(view1: ViewTab, view2: ViewTab): boolean {
  // 使用 Lodash 的 _.omit 方法来忽略 customFieldId 和 permissions 字段
  const omittedView1 = _.omit(view1, ['customFieldId', 'permissions', 'name'])
  const omittedView2 = _.omit(view2, ['customFieldId', 'permissions', 'name'])

  // 筛选 conditions 中 values 不为空的项
  const filteredView1 = {
    ...omittedView1,
    conditions: omittedView1.conditions
      ? _.filter(omittedView1.conditions, condition => condition?.values?.length ?? 0 > 0)
      : [],
  }

  const filteredView2 = {
    ...omittedView2,
    conditions: omittedView2.conditions
      ? _.filter(omittedView2.conditions, condition => condition?.values?.length ?? 0 > 0)
      : [],
    displays: omittedView2.displays?.map(item => {
      const temp = { ...item }
      delete temp?.id
      delete temp?.name
      return temp
    }),
  }
  // console.log('omittedView1>>>omittedView2', omittedView1, omittedView2);

  // 使用 Lodash 的 _.isEqual 方法来比较两个处理后的对象
  return _.isEqual(filteredView1, filteredView2)
}

export function isGray() {
  // 测试服默认全是灰度
  if (window.location.href.includes('test-todo.popo.netease.com')) {
    return true
  }

  const grayscaleTag = Cookies.get('grayscale_tag')

  return grayscaleTag?.includes('grayscale')
}

/**
 * 解析视图配置, 将字符床格式的viewConfig转换成对象, 并根据不同的视图类型, 初始化默认配置
 * @param viewSetting 视图配置
 * @returns 带有解析后的视图配置的视图面板
 */
export function parseViewConfig(viewSetting: ViewTab) {
  const { viewConfig } = viewSetting
  let parsedViewConfig: any = {}
  if (viewConfig) {
    try {
      parsedViewConfig = JSON.parse(viewConfig)
    } catch (error) {
      console.log(error)
    }
  }

  // 初始化看板默认配置
  if (viewSetting.viewType === ViewType.kanban) {
    // 初始化看板卡片默认配置
    const defaultCardConfig = {
      cardConfig: {
        showCompleteIcon: true,
      },
    }

    if (!parsedViewConfig.cardConfig) {
      parsedViewConfig = {
        ...defaultCardConfig,
        ...parsedViewConfig,
      }
    }
  }

  viewSetting.parsedViewConfig = parsedViewConfig
  return viewSetting
}

// 比较两个conditions对象数组是否一致
// 顺序不影响判断结果
export function compareConditions(arr1 = [], arr2 = []) {
  // 处理两个数组都为空的情况
  if (arr1.length === 0 && arr2.length === 0) return true

  // arr1所有字段values都为空且arr2为空数组
  const allEmptyInArr1 = arr1.every(item => item?.values?.length === 0)
  if (allEmptyInArr1 && arr2.length === 0) return true

  if (arr1.length !== arr2.length) return false

  const map = new Map(arr2.map(item => [item.fieldName, item]))

  return arr1.every(item1 => {
    const item2 = map.get(item1?.fieldName)
    if (!item2) return false

    const sorted1 = [...item1?.values].sort()
    const sorted2 = [...item2?.values].sort()

    return JSON.stringify(sorted1) === JSON.stringify(sorted2)
  })
}

// 比较两个字段配置displays是否一致
// 顺序影响结果
export function compareDisplays(arr1 = [], arr2 = []) {
  if (arr1.length !== arr2.length) return false

  return arr1.every((item1, index) => {
    const item2 = arr2[index]
    if (!item2) return false

    return item1?.id === item2?.id && item1?.visible === item2?.visible
  })
}

// 更新后的比较函数
export function compareQueryGroupBys(arr1, arr2) {
  // 将 undefined 转换为空数组统一处理
  const a = arr1 ?? []
  const b = arr2 ?? []

  // 处理两个都是空数组的情况
  if (a.length === 0 && b.length === 0) return true

  // 处理长度不一致的情况
  if (a.length !== b.length) return false

  // 转换为 fieldName 集合进行比较
  const fieldsA = new Set(a.map(item => item.customFieldId || item.fieldName))
  const fieldsB = new Set(b.map(item => item.customFieldId || item.fieldName))

  return fieldsA.size === fieldsB.size && [...fieldsA].every(field => fieldsB.has(field))
}

export const isVerticalInViewport = (element: Element, container?: Element | null) => {
  const rect = element?.getBoundingClientRect() || { top: 0, bottom: 0 }
  if (container) {
    const containerRect = container.getBoundingClientRect()
    const dt = rect.top - containerRect.top
    const db = rect.bottom - containerRect.bottom
    const isInViewport = dt >= 0 && db <= 0
    return {
      isInViewport,
      dt,
      db,
    }
  }
  return {
    isInViewport: rect.top >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight),
    dt: rect.top,
    db: rect.bottom,
  }
}

export const getViewChanged = (state, currentView: ViewTab) => {
  const { customFields, navigatorId } = state.viewSetting
  const {
    initialSearchParams,
    conditions,
    querySort,
    queryGroupBy,
    queryGroupBys,
    displays = [],
    planViewMode,
    parsedViewConfig,
  } = currentView
  const { cardConfig } = parsedViewConfig || {}
  const displayList = combineDisplayFields(displays as DisplayField[], customFields, navigatorId)

  // 比较卡片配置(cardConfig)是否一致
  const isParsedViewConfigChanged = () => {
    const showCompleteIconEqual = cardConfig?.showCompleteIcon === initialSearchParams?.cardConfig?.showCompleteIcon
    // 处理卡片配置中siderColor为空的情况
    const sideColorEqual =
      (cardConfig?.siderColor ?? '') === (initialSearchParams?.cardConfig?.siderColor ?? '') ||
      (!cardConfig?.siderColor && !initialSearchParams?.cardConfig?.siderColor)
    return !(showCompleteIconEqual && sideColorEqual)
  }

  // 比较字段配置(displays)是否一致
  const isDisplaysChanged = () => {
    return !compareDisplays(displayList, initialSearchParams!.displays)
  }

  // 比较年月模式(playViewMode)是否一致
  const isPlanViewModeChanged = () => {
    return planView2ScaleType(planViewMode) !== initialSearchParams?.planViewMode
  }

  // 比较筛选(conditions)是否一致
  const isConditionsChanged = () => {
    return !compareConditions(conditions, initialSearchParams!.conditions)
  }

  // 比较排序(querySort)是否一致
  const isQuerySortChanged = () => {
    return !_.isEqual(querySort, initialSearchParams?.querySort)
  }

  // 比较单选分组(queryGroupBy)是否一致
  const isQueryGroupByChanged = () => {
    return !_.isEqual(queryGroupBy, initialSearchParams?.queryGroupBy)
  }

  // 比较多选分组(queryGroupBys)是否一致
  const isQueryGroupBysChanged = () => {
    return !compareQueryGroupBys(queryGroupBys, initialSearchParams?.queryGroupBys)
  }

  return (
    isParsedViewConfigChanged() ||
    isDisplaysChanged() ||
    isPlanViewModeChanged() ||
    isConditionsChanged() ||
    isQuerySortChanged() ||
    isQueryGroupByChanged() ||
    isQueryGroupBysChanged() ||
    false
  )
}
export function insertTextAtCursor(text) {
  const selection = window.getSelection()

  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents() // 可选：删除选中内容
    const textNode = document.createTextNode(text)
    range.insertNode(textNode)

    // 移动光标到插入文本之后
    range.setStartAfter(textNode)
    range.collapse(true)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

export function toHttps(url: string): string {
  if (!url) return url // 处理空输入
  const lowerCaseUrl = url.toLowerCase() // 统一转小写检查协议
  if (lowerCaseUrl.startsWith('http://')) {
    // 替换http协议为https（保留原URL的大小写）
    return 'https://' + url.slice(7)
  }
  return url // 非http协议或已为https的URL直接返回
}
