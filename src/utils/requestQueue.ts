export class RequestQueue {
  queue: (() => Promise<any>)[] = [];
  pendingCount = 0;
  maxConcurrent = 3;

  constructor(maxConcurrent?: number) {
    if (maxConcurrent) {
      this.maxConcurrent = maxConcurrent;
    }
  }

  addRequest(request: () => Promise<any>): Promise<any> {
    return new Promise((resolve, reject) => {
      this.queue.push(() =>
        request()
          .then(resolve)
          .catch(reject)
          .finally(() => {
            this.pendingCount--;
            this.checkQueue();
          })
      );
      this.checkQueue();
    });
  }

  checkQueue() {
    if (this.pendingCount < this.maxConcurrent && this.queue.length > 0) {
      this.pendingCount++;
      const request = this.queue.shift()!;
      request();
    }
  }
}
