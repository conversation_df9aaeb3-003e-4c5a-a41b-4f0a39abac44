.subtasks {
  padding-left: 24px;
  padding-right: 24px;

  .title {
    justify-content: space-between;
    margin-bottom: 12px;
    .label {
      flex: 1;
      color: var(--TextSecondary-ongrey);
      font-size: 14px;
      line-height: 20px;
    }
    .option {
      flex-shrink: 0;
      font-size: 13px;
      line-height: 20px;
      color: var(--TextSecondary);
      cursor: pointer;
      border-radius: 6px;
      padding: 2px 6px;

      & > img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }

      &:hover {
        background-color: var(--aBlack6);
      }
    }
  }

  .tasks {
    border: 1px solid var(--aBlack6);
    border-radius: 8px;

    :global {
      .rock-checkbox-group {
        display: flex;
        flex-direction: column;
        .rock-checkbox-wrapper {
          margin: 0;

          &:last-child {
            .guess__item {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}

.context__menu {
  padding: 4px;
  background-color: var(--bgTop);
  border: 1px solid var(--aBlack12);
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  width: 117px;
  margin: 0;
  padding: 0;

  .menu {
  }

  .danger {
    color: var(--R500);
  }

  .text {
    font-size: 13px;
    line-height: 20px;
  }

  .item {
    height: 36px !important;
  }
}

.sub__process {
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 20px;
}

.sub__process--bar {
  width: 60px;
  height: 8px;
  border-radius: 12px;
  background-color: var(--aBlack12);
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: 60px;
    height: 8px;
    background-color: var(--Brand500);
    transform: translateX(var(--percent));
  }
}

.guess__item {
  min-height: 36px;
  padding: 0.06rem 0.08rem 0.06rem 0.22rem;
  border-bottom: 1px solid var(--aBlack6);

  &--title {
    font-size: 14px;
    color: var(--TextPrimary-strong);
  }

  :global {
    .rock-checkbox {
      top: 0px;
    }
  }
}

.skeleton__circle {
  background-color: var(--aBlack8);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 5px;
  margin-bottom: 5px;
}

.skeleton__rectangle {
  width: 218px;
  height: 8px;
  border-radius: 2px;
  overflow: hidden;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.12) 100%);
  animation: linear__highlight 1.5s infinite;
  background-size: 200% 100%;
}

@keyframes linear__highlight {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}
