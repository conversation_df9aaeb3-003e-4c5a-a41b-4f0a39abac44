import React, { PropsWithChildren } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiProjectViewLayoutPost } from '@/api';
import { apiNavigatorViewDeleteDelete, apiNavigatorViewUpdateNamePost } from '@/api';
import { Message } from '@/components/basic';
import DnDList from '@/components/dndkit/dnd-list';
import { Dispatch, RootState } from '@/models/store';
import { ViewTab } from '@/types';
import I18N from '@/utils/I18N';

import DragItem from './drag-item';
import s from './index.less';
export type Props = {
  className?: string;
};

const SortList: React.FC<PropsWithChildren<Props>> = (props) => {
  const { projectInfo, viewTabList, currentViewTab } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo!,
    permissions: state.viewSetting.permissions,
    viewTabList: state.viewSetting.viewTabList,
    currentViewTab: state.viewSetting.currentViewTab,
  }));

  const dispatch = useDispatch<Dispatch>();

  const changeCurrentViewTab = (item: ViewTab) => {
    setTimeout(() => {
      dispatch.viewSetting.toggeleView(item);
    }, 0);
  };

  const renameViewTab = (name: string, viewId: number) => {
    apiNavigatorViewUpdateNamePost({ viewId: viewId, name }).then((res) => {
      const _list = viewTabList?.map((item) => {
        if (item.viewId === viewId) {
          return { ...item, name };
        }
        return item;
      });
      dispatch.viewSetting.setViewTabList(_list!);
    });
  };

  const onDelete = (id: number | string) => {
    apiNavigatorViewDeleteDelete({ viewId: String(id) })
      .then(() => {
        console.log('onDelete', id);
        let index = 0;
        const _list = viewTabList?.filter((item, idx) => {
          if (item.id === id) {
            index = idx;
          }
          return item.id !== id;
        });
        dispatch.viewSetting.setViewTabList(_list!);
        //如果删除的视图是当前视图
        if (currentViewTab.viewId === id) {
          changeCurrentViewTab(_list![index - 1 < 0 ? 0 : index - 1]);
        }

        Message.success(I18N.auto.deleted);
      })
      .catch(() => {
        Message.success(I18N.auto.deleteFailed);
      });
  };

  return (
    <>
      <DnDList
        list={viewTabList!}
        onDragEnd={(item, list) => {
          const ids = list.map((item) => item.id);
          // 调用接口更新tab顺序
          dispatch.viewSetting.setData({
            viewTabList: list,
          });
          apiProjectViewLayoutPost({
            projectId: projectInfo.projectId!,
            layout: { viewIds: ids },
          });
        }}
        renderItem={(item, index) => {
          return (
            <DragItem
              key={item.id}
              viewTab={item as ViewTab}
              viewList={viewTabList}
              className={s.originalItem}
              onDelete={onDelete}
              renameViewTab={renameViewTab}
            />
          );
        }}
        renderOverlayItem={(item) => {
          return (
            <DragItem
              key={item?.id}
              viewTab={item as ViewTab}
              className={s.draggingItem}
              onDelete={onDelete}
              renameViewTab={renameViewTab}
            />
          );
        }}
      ></DnDList>
    </>
  );
};

export default SortList;
