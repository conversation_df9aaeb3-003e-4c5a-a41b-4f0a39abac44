import classNames from 'classnames';

import { Icon } from '@/components/basic';

import s from './index.less';

export type MenuselectItem = {
  name: string;
  value: string | number;
};

interface TabsProps {
  data: MenuselectItem[];
  onChange: (v: string) => void;
  className?: string;
  value?: string;
}

const Menuselect: React.FC<TabsProps> = (props) => {
  const { value, data, onChange, className } = props;
  return (
    <div className={classNames(s.menuselect, className)}>
      {data.map((item, index) => {
        const active = value === item.value;
        return (
          <div
            key={index}
            className={classNames(s.item, { [s.active]: active })}
            onClick={() => {
              onChange?.(item.value);
            }}
          >
            <span className={s.left}>{item.name}</span>
            <span className={s.right}>
              {active ? <Icon className={s.icon} name="icon-sys_check"></Icon> : null}
            </span>
          </div>
        );
      })}
    </div>
  );
};
export default Menuselect;
