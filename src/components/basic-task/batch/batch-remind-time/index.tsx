import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTodoBatchUpdateAlarmPost } from '@/api';
import POPODatePicker, { QuickType, TimeValue } from '@/components/basic/popo-date-picker';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
  disabled?: boolean;
};
const BatchTime: React.FC<Props> = (props) => {
  const { disabled } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { batchList } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onChange = (v: TimeValue) => {
    const taskIds = batchList.map((item) => item.taskId!);
    // 请求批量操作
    apiTodoBatchUpdateAlarmPost({
      taskIds: taskIds,
      alarm: {
        alarmTimestamp: v.time!,
        alarmRrule: v.rrule,
        selectedOption: v.selectedOption,
      },
    }).then(() => {
      // 不改变列表结果,只刷新列表
      dispatch.viewSetting.refreshData({ page: 1 });
      dispatch.batch.setBatchList([]);
      dispatch.batch.setBatchMode(false);
    });
  };
  const showDelete = useMemo(() => {
    return batchList.some((item) => item.alarm?.alarmTimestamp);
  }, [batchList]);
  return disabled ? (
    <BatchItem
      disabled={disabled}
      name={I18N.auto.remind}
      iconName="icon-pc_details_remind"
    ></BatchItem>
  ) : (
    <POPODatePicker
      visible={visible}
      onVisible={setVisible}
      showDelete={showDelete}
      quickType={QuickType.remind}
      onChange={(v: TimeValue) => {
        onChange(v);
      }}
      className={s.batchTime}
    >
      <BatchItem name={I18N.auto.reminderTime} iconName="icon-pc_details_remind"></BatchItem>
    </POPODatePicker>
  );
};

export default BatchTime;
