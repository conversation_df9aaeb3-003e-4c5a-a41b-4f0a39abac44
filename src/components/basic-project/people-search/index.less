.peoplePickerDropdown {
  &:global(.rock-dropdown-trigger-default) {
    width: 100%;
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    background-color: transparent !important;
    > span {
      width: 100%;
    }
  }
}

.addPeople {
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 6px;

  .input {
    flex: 1;
    height: 34px;
    background-color: transparent !important;
    padding-right: 0 !important;
    :global {
      .rock-input-suffix {
        padding-right: 8px;
        transition: background-color 0.3s;
        &:hover {
          background-color: var(--aBlack6);
        }
      }

      .rock-input::placeholder {
        color: var(--TextTertiary) !important;
        font-size: 14px;
      }
    }
  }
  .line {
    flex-shrink: 0;
    margin: 0;
    height: 34px;
    margin-right: 10px;
  }
}

.panel {
  width: 380px;
  cursor: auto;
}
.searchingPanel {
  max-height: 220px;
  min-height: 220px;
}
.container {
  display: flex;
  flex: 1;
  min-height: 0px;
}
