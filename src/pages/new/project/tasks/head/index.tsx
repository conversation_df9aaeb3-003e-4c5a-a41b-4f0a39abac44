import { OperateNoti } from '@babylon/popo-icons';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { history, useParams } from 'umi';

import { apiProjectUpdateStatePost, apiProjectUpdateTitleIconPost } from '@/api';
import {
  Button,
  Divider,
  Icon,
  IconBtn,
  InputAutoWidth,
  RenderPeoples,
  ToggleMenu,
} from '@/components/basic';
import IconSelect from '@/components/basic/icon-select';
import Separate from '@/components/basic/separate';
import { ProjectStatus } from '@/components/basic-project';
import { ProjectShare } from '@/components/basic-project/more-option';
import { Dispatch, RootState } from '@/models/store';
import { ProjectInfo } from '@/types';
import { setStorage, StorageType } from '@/utils';
import Const, { BaseIconSizeEnum, EnumProjectStatus } from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import { validatesVersion } from '@/utils/validate-version';

import s from './index.less';
import InvitationGroup from './invitation-group';
import MoreOption from './more-option';
import RenderMembers from './render-members';
import NotifyOption from './NotifyOption';
import EnterGroup from './EnterGroup';

interface Props {
  className?: string;
}

const Head: React.FC<Props> = (props) => {
  const { className } = props;
  const params = useParams();
  const { id } = params as { id: string };
  const [name, setName] = useState<string>('');
  const dispatch = useDispatch<Dispatch>();

  const { projectInfo, permissions } = useSelector((state: RootState) => ({
    projectInfo: state.project.projectInfo,
    permissions: state.viewSetting.permissions,
  }));

  const updateProjectInfo = (v: Partial<ProjectInfo>) => {
    const copyData = projectInfo;

    const data = {
      ...projectInfo,
      ...v,
    };
    if (!data.name?.trim()) {
      return;
    }
    dispatch.project.setData({
      projectInfo: data,
    });
    //更新ping列表数据
    dispatch.project.updatePingProjectList(data);
    apiProjectUpdateTitleIconPost({
      projectId: projectInfo?.projectId,
      name: v.name,
      icon: v.icon,
      iconColor: v.iconColor,
    }).catch(() => {
      const data = {
        ...copyData,
      };
      dispatch.project.setData({
        projectInfo: data,
      });
      dispatch.project.updatePingProjectList(data);
    });
  };
  const changeProjectStatus = (v: EnumProjectStatus) => {
    apiProjectUpdateStatePost({
      projectId: Number(id),
      state: v,
    }).then(() => {
      const data = {
        ...projectInfo,
        state: v,
      };
      dispatch.project.setData({
        projectInfo: data,
      });
    });
  };
  useEffect(() => {
    setName(projectInfo?.name || '');
  }, [projectInfo?.name]);
  useEffect(() => {
    setStorage(Const.RetryReloadPageTimes, 0, StorageType.local);
  }, []);

  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_SHARE, CAN_SET_STATE, CAN_CREATE_IM_TEAM, CAN_ENTER_IM_TEAM] =
      validatesPermission({
        permissions: permissions,
        key: [
          ProjectPermissionEnum.CAN_EDIT,
          ProjectPermissionEnum.CAN_SHARE,
          ProjectPermissionEnum.CAN_SET_STATE,
          ProjectPermissionEnum.CAN_CREATE_IM_TEAM,
          ProjectPermissionEnum.CAN_ENTER_IM_TEAM,
        ],
      }) as boolean[];
    return {
      CAN_EDIT,
      CAN_SHARE,
      CAN_SET_STATE,
      CAN_CREATE_IM_TEAM,
      CAN_ENTER_IM_TEAM,
    };
  }, [permissions]);

  const showShare = useMemo(() => {
    return validatesVersion('openCalendarCreatePage');
  }, []);

  return (
    <div className={classNames(s.head, className)}>
      <InvitationGroup projectInfo={projectInfo}></InvitationGroup>
      <div className={classNames(s.projectHead, 'task-project-head')}>
        <div className={`${s.headLeft}`}>
          <ToggleMenu></ToggleMenu>
          <IconSelect
            className={classNames(s.projectIcon)}
            iconName={projectInfo.icon}
            iconBgColor={projectInfo.iconColor}
            onChangeIcon={(v) => {
              updateProjectInfo({
                ...projectInfo,
                icon: v,
              });
            }}
            onChangeColor={(v) => {
              updateProjectInfo({
                ...projectInfo,
                iconColor: v,
              });
            }}
            fontSize={24}
            disabled={!memoPermissions.CAN_EDIT}
          ></IconSelect>
          <div className={classNames(s.projectName, 'ml-8 mr-8')}>
            <InputAutoWidth
              value={name}
              onChange={(v) => {
                setName(v);
              }}
              // debounceChange={(v) => {
              //   console.log(22222);
              // }}
              placeholder=" "
              onBlur={(v) => {
                if (v.target.value.trim()) {
                  //请求接口修改任务名称
                  updateProjectInfo({
                    ...projectInfo,
                    name: v.target.value.trim(),
                  });
                } else {
                  setName(projectInfo.name!);
                }
              }}
              disabled={!memoPermissions.CAN_EDIT}
              maxLength={100}
            ></InputAutoWidth>
          </div>
          <ProjectStatus
            disabled={!memoPermissions.CAN_SET_STATE}
            className={s.status}
            onChange={changeProjectStatus}
            projectInfo={projectInfo}
          ></ProjectStatus>
        </div>
        <div className={s.headRight}>
          <RenderMembers
            list={projectInfo?.members || []}
            count={projectInfo?.memberCount}
            maxShowCount={5}
            avatarClassName="mr-4"
            canShare={memoPermissions.CAN_SHARE && showShare}
            className={classNames(s.members)}
          />

          {memoPermissions.CAN_CREATE_IM_TEAM && <NotifyOption />}
          {!memoPermissions.CAN_CREATE_IM_TEAM &&
            memoPermissions.CAN_ENTER_IM_TEAM &&
            projectInfo.tid && <EnterGroup projectInfo={projectInfo} />}

          {/* {memoPermissions.CAN_SHARE && showShare ? (
            <ProjectShare className="ml-10" projectInfo={projectInfo}>
              <Button type="primary" className={classNames(s.btn)}>
                <Icon name="icon-details_nav_share" className="mr-4"></Icon>
                {I18N.auto.share}
              </Button>
            </ProjectShare>
          ) : null} */}
          {/* <Button
            type="checked-neutral"
            className={classNames(s.btn, 'ml-10')}
            onClick={() => {
              if (projectInfo?.projectId) {
                history.push(`/new/project/${projectInfo.projectId}/notification`);
              }
            }}
          >
            <div className="flex-center">
              <OperateNoti className="fs-16 mr-4"></OperateNoti>
              {I18N.auto.subscriptionNotification}
            </div>
          </Button> */}
          {!!permissions.length ? (
            <MoreOption projectInfo={projectInfo} permissions={permissions}>
              <IconBtn
                title={I18N.auto.more}
                placement="top"
                iconName="icon-sys_more"
                size={BaseIconSizeEnum.large}
              />
            </MoreOption>
          ) : null}
        </div>
      </div>
      <Divider type="vertical"></Divider>
      <Separate />
    </div>
  );
};
export default Head;
