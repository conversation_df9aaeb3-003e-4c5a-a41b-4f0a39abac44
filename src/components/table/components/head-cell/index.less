.wrap {
  display: flex;
  align-items: center;
  width: 100%;
}
.headSort {
  cursor: pointer;
}
.sortableWrap {
  display: flex;
  align-items: center;
}
.content {
  display: flex;
  align-items: center;
  overflow: hidden;
}
.title {
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;

  .name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    font-weight: 400;
    color: var(--TextSecondary);
  }
}
.sortBox {
  width: 20px;
  height: 20px;
}
.prefix,
.suffix {
  flex-shrink: 0;
}
.content {
  flex: 1;
}
.sort {
  display: none;
  flex-direction: column;
  height: 20px;
  width: 30px;
  transform: scale(0.3) translateX(-16px) translateY(-6px);
  cursor: pointer;
  .sortIcon {
    font-size: 10px;
    cursor: pointer;
  }
}

.show {
  display: flex;
}

.active {
  color: var(--Brand600);
}
