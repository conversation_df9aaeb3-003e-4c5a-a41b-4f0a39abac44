import classNames from 'classnames';
import React from 'react';

import { Divider, Icon } from '@/components/basic';
import { ProjectInfo } from '@/types';
import { EnumRole } from '@/utils/const';
import I18N from '@/utils/I18N';

import { ExitProject } from '../../more-option';
import s from './index.less';

export type Option = {
  name: string;
  value: EnumRole;
  desc?: string;
};

export type Props = {
  className?: string;
  options?: Option[];
  value?: EnumRole;
  onChange: (v: EnumRole, option: Option) => void;
  showExit?: boolean;
  onExit?: () => void;
  showRemove?: boolean;
  onRemove?: () => void;
  projectInfo?: ProjectInfo;
};

const Overlay: React.FC<Props> = (props) => {
  const { value, projectInfo, options, onChange, showExit, showRemove, onRemove, onExit } = props;
  return (
    <div className={s.overlay}>
      {options?.map((item, index) => {
        const active = value === item.value;
        return (
          <div
            key={index}
            className={s.item}
            onClick={() => {
              onChange(item.value, item);
            }}
          >
            {active ? <Icon className={s.activeIcon} name="icon-sys_check"></Icon> : null}
            <div>
              <div className={s.name}>{item.name}</div>
              <div className={s.desc}>{item.desc}</div>
            </div>
          </div>
        );
      })}
      {showExit ? (
        <ExitProject
          projectInfo={projectInfo}
          onExitSuccess={() => {
            onExit?.();
          }}
        >
          <div>
            <Divider type="horizontal" className="mt-4 mb-4"></Divider>
            <div className={classNames(s.item, s.del)}>
              <div className={s.name}>{I18N.auto.exitTheProject}</div>
            </div>
          </div>
        </ExitProject>
      ) : null}
      {showRemove ? (
        <div>
          <Divider type="horizontal" className="mt-4 mb-4"></Divider>
          <div
            className={classNames(s.item, s.del)}
            onClick={() => {
              onRemove?.();
            }}
          >
            <div className={s.name}>{I18N.auto.removePermissions}</div>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default Overlay;
