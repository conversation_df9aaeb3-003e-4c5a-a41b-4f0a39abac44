import { MentionExtraData } from "@/components/basic-task/comment/editor/utils"
import I18N from "@/utils/I18N"
import { NodeSpec } from "prosemirror-model"

const rootClassName = "prosemirror-mention"
const atMeClassName = "prosemirror-mention-me"

const AtNodeSpecs: NodeSpec = {
  popoAt: {
    attrs: {
      uid: {},
      name: {},
      avatarUrl: {},
      attype: {},
    },

    group: "inline",
    inline: true,
    selectable: false,
    atom: true,

    toDOM: (node) => [
      "span",
      {
        class: [rootClassName, MentionExtraData.userInfo?.uid === node.attrs.uid ? atMeClassName : null].filter(Boolean).join(' '),
        "data-mention-uid": node.attrs.uid,
        "data-mention-name": node.attrs.name,
        "data-mention-avatarUrl": node.attrs.avatarUrl,
        "data-mention-attype": node.attrs.attype,
      },
      MentionExtraData.userInfo?.uid === node.attrs.uid ? `${I18N.auto.me}` : `@${node.attrs.name}`,
    ],

    parseDOM: [
      {
        tag: `span.${rootClassName}`,

        getAttrs: (dom: any) => {
          const uid = dom.getAttribute("data-mention-uid")
          const name = dom.getAttribute("data-mention-name")
          const avatarUrl = dom.getAttribute("data-mention-avatarUrl") || ""
          const attype = dom.getAttribute("data-mention-attype")

          return { uid, name, avatarUrl, attype }
        },
      },
    ],
  },
}
export default AtNodeSpecs
