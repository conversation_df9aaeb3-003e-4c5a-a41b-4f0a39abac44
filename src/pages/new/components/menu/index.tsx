import { Data16Pojectline } from '@babylon/popo-icons';
import classNames from 'classnames';
import { useEffect, useMemo, useRef, useState, memo } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useNavigate, useParams } from 'umi';

import { Button, Divider, Icon, IconBtn } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { ProjectInfo } from '@/types';
import { getStorage, getUrlParams, StorageType } from '@/utils';
import Const, {
  EnumEmitter,
  ProjectListMenuId,
  TaskNavigatorMap,
  TaskNavigatorType,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { EnumTrackeKey } from '@/utils/skyline';

import Footer from './footer';
import s from './index.less';
import RenderItem from './render-item';
import PersistMenu from './PersistMenu';
import CreateProjectDropdown from '@/components/CreateProjectDropdown';
import ProjectGroupGuide from '@/components/guide/project-group-guide';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import { SortableTree } from '@/components/SortableTree';
import { usePrevious } from 'ahooks';
import { debounce } from 'lodash';

const PageLeft: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const { showMenu, allTaskCount, navigatorId, pingProjectList, total, user } = useSelector(
    (state: RootState) => ({
      showMenu: state.user.showMenu,
      allTaskCount: state.viewSetting.allTaskCount,
      navigatorId: state.viewSetting.navigatorId,
      pingProjectList: state.project.pingProjectList,
      total: state.project.total,
      user: state.user,
    }),
    shallowEqual
  );

  const previousNavigatorIdRef = useRef(null);

  const sortableTreeRef = useRef<any>(null);

  const { cacheAddinfo } = useSelector(
    (state: RootState) => ({
      cacheAddinfo: state.project.cacheAddinfo || {},
    }),
    (prev, next) => {
      return prev.cacheAddinfo?.projectId === next.cacheAddinfo?.projectId;
    }
  );

  const params = useParams();
  const dispatch = useDispatch<Dispatch>();

  const scrollDiv = useRef<HTMLDivElement>(null);

  const showCreateProject = useMemo(() => {
    const cacheProjectId = cacheAddinfo?.projectId;
    if (cacheProjectId && !pingProjectList?.length) {
      pingProjectList.push(cacheAddinfo);
    }
    return total > 0 || cacheProjectId;
  }, [total, cacheAddinfo, pingProjectList]);

  const toggleShowMenu = () => {
    if (showMenu) {
      //埋点: 导航展开收起  埋收起即可
      dispatch.user.tracking({ key: EnumTrackeKey.MainNavigationExpandCollapse });
    }
    dispatch.user.setData({
      showMenu: !showMenu,
    });
  };

  const selectMenu = (menu: TaskNavigatorType | number, isGroup: boolean) => {
    // number类型表示项目ID
    if (navigatorId === menu) {
      return;
    }
    dispatch.detail.closeDetail({});
    dispatch.viewSetting.openNavigator({
      navigatorId: menu,
      isGroup,
    });
    //路由切换
    dispatch.user.tracking({ key: EnumTrackeKey.ClickNavigator, navigatorId: menu });
  };

  const onAddProject = () => {
    dispatch.project.gotoAddProject();
  };

  const handleAddGroup = () => {
    dispatch.project.addProjectGroupEmpty({});
    sortableTreeRef.current?.scrollToIndex(0);
  };

  const handleOpenNavigator = ({ taskId, navigatorId }) => {
    //暂时不以路由双向适配, 都是动态根据导航去openNavigator
    if (!navigatorId || navigatorId === 'undefined' || navigatorId === ProjectListMenuId) {
      ///new/project/:id/tasks id是projectId
      const { id } = params as { id: string };
      if (id && location.pathname.includes('new/project')) {
        //之前打开导航,如果说不好ping列表数据,左侧导航不会显示选中
        dispatch.viewSetting.openNavigator({
          navigatorId: Number(id),
        });
      } else {
        dispatch.viewSetting.openNavigator({
          navigatorId: TaskNavigatorType.assignToMe,
          taskId: taskId,
        });
      }
    } else {
      dispatch.viewSetting.openNavigator({
        navigatorId: navigatorId,
        taskId: taskId,
      });
    }
    const datastr = getStorage(Const.ProjectAddItem, StorageType.local);
    if (datastr) {
      try {
        const item = JSON.parse(datastr);
        dispatch.project.setCacheAddinfo(item);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const handleDragEnd = (treeData, { preId, nextId, activeId, newParentId, oldParentId }) => {
    const params: any = {
      preId,
      tailId: nextId,
      newGroupId: newParentId,
      oldGroupId: oldParentId,
      currentId: activeId,
    };
    dispatch.project.projectSort(
      Object.keys(params).reduce((acc: any, key) => {
        if (typeof params[key] === 'number') {
          acc[key] = params[key];
        }
        return acc;
      }, {})
    );
  };

  useEffect(() => {
    const isFromMessageCard = location.pathname === '/list';
    if (!isFromMessageCard) {
      dispatch.project.getPingProjectList().then(() => {
        setLoading(false);
      });
      dispatch.viewSetting.getNavigatorTaskCount();
    }
    const navigatorId = getStorage(`${Const.NavigatorId}`, StorageType.local);
    //左侧导航有个人任务类,有项目导航、有新建项目和全部项目导航,也有项目打开没有导航的情况,
    const { id: taskId } = getUrlParams(location.search);

    if (isFromMessageCard) {
      dispatch.detail.getTodoDetail({ taskId, onlyDetail: true }).then((taskDetail) => {
        const projectId = taskDetail?.project?.projectId;
        if (projectId) {
          dispatch.viewSetting.openNavigator({
            navigatorId: projectId,
            taskId: taskId,
          });
        } else {
          dispatch.viewSetting.openNavigator({
            navigatorId: TaskNavigatorType.assignToMe,
            taskId: taskId,
          });
        }
      });
    } else {
      // 非消息卡片进入, 且当前没有导航, 且有projectId, 则打开导航
      handleOpenNavigator({ taskId, navigatorId });
    }
  }, []);

  const scrollToIndex = debounce(() => {
    previousNavigatorIdRef.current = navigatorId;
    // 焦点菜单自动滚动视图
    if (typeof navigatorId === 'number' && navigatorId > 0) {
      sortableTreeRef.current?.scrollToIndexById(navigatorId);
    }
  }, 1000);

  useEffect(() => {
    if (previousNavigatorIdRef.current == navigatorId) {
      return;
    }
    scrollToIndex();
  }, [navigatorId, pingProjectList]);

  return (
    <div className={classNames(s.navigator)}>
      <div className={s.head}>
        <div className={s.title}>{I18N.auto.task}</div>
        {!!showMenu && (
          <IconBtn
            title={showMenu ? I18N.auto.putAwayTheDayAndWait : I18N.auto.expandTheDayOfWaiting}
            iconClassName={s.toggleShowMenuIcon}
            className="flex-no-shrink"
            iconName={showMenu ? 'icon-tage_shouqi' : 'icon-tage_zhankai'}
            onClick={toggleShowMenu}
          />
        )}
      </div>
      <div className={classNames(s.navigatorBox)}>
        <div className={classNames(s.taskMenu)}>
          <div className={classNames(s.menu)}>
            <div
              className={classNames(s.menuItem, {
                [s.active]: navigatorId === TaskNavigatorType.allTask,
              })}
              onClick={() => {
                selectMenu(TaskNavigatorType.allTask);
              }}
            >
              <Icon name="icon-tage_all_line"></Icon>
              <div className={s.menuName}>
                <div>{TaskNavigatorMap[TaskNavigatorType.allTask].name}</div>
                <span
                  className={classNames(s.count, {
                    [s.hidden]: !allTaskCount,
                  })}
                >
                  {allTaskCount}
                </span>
              </div>
            </div>
            <Divider
              className={classNames(s.line)}
              style={{ marginLeft: 6, marginRight: 6 }}
              type="horizontal"
            ></Divider>
            <div className={s.desc}>{I18N.auto.shortcuts}</div>
            {/* 分配给我 */}
            <PersistMenu
              icon="icon-tage_implement_line"
              name={TaskNavigatorMap[TaskNavigatorType.assignToMe].name}
              onClick={() => {
                selectMenu(TaskNavigatorType.assignToMe);
              }}
              active={navigatorId === TaskNavigatorType.assignToMe}
            />
            {/* 我关注的 */}
            <PersistMenu
              icon="icon-tage_pin_line"
              name={TaskNavigatorMap[TaskNavigatorType.myFollow].name}
              onClick={() => {
                selectMenu(TaskNavigatorType.myFollow);
              }}
              active={navigatorId === TaskNavigatorType.myFollow}
            />
            {/* 我创建的 */}
            <PersistMenu
              icon="icon-tage_assigner_line"
              name={TaskNavigatorMap[TaskNavigatorType.myCreate].name}
              onClick={() => {
                selectMenu(TaskNavigatorType.myCreate);
              }}
              active={navigatorId === TaskNavigatorType.myCreate}
            />
          </div>
        </div>
        <Divider
          className={classNames(s.line)}
          style={{ marginLeft: 18, marginRight: 18 }}
          type="horizontal"
        />
        <div className={s.projectMenu}>
          {/* 全部项目 */}
          <div draggable={false} className={classNames(s.projectHead)}>
            <div className={s.project}>{I18N.auto.project}</div>
            <CreateProjectDropdown
              options={{ onCreateGroup: handleAddGroup, onCreateProject: onAddProject }}
            >
              <Button
                type="text-subtle"
                size="xSmall"
                id="add_project_or_group"
                icon={<Icon name="icon-sys_add" className={s.addIcon} />}
              >
                <ProjectGroupGuide user={user} />
              </Button>
            </CreateProjectDropdown>
          </div>
          <PersistMenu
            active={navigatorId === ProjectListMenuId}
            icon={<Data16Pojectline />}
            name={I18N.auto.viewAllItems}
            onClick={() => {
              selectMenu(ProjectListMenuId);
            }}
          />

          <div className={classNames(s.menu, s.auto)} ref={scrollDiv}>
            <SortableTree<ProjectInfo>
              ref={sortableTreeRef}
              collapsible
              draggable
              onDragEnd={handleDragEnd}
              isPin={true}
              selectedId={navigatorId}
              scrollContainer={scrollDiv.current}
              indentationWidth={24}
              // indicator={true}
              itemRender={(item) => (
                <RenderItem
                  isSortable={false}
                  key={item.id}
                  active={navigatorId === item.projectId}
                  // activeIndex={options?.activeIndex}
                  onClick={() => {
                    selectMenu?.(item.projectId!, item.isGroup);
                  }}
                  item={item}
                />
              )}
              treeData={pingProjectList}
              estimateSize={(index, flattenedItems) => {
                return 34 * (window.fontSizeScaleRatio || 1);
              }}
            />

            {!showCreateProject && !loading ? (
              <div className={s.add} onClick={onAddProject}>
                <div className={s.addBg}></div>
                <div className={s.addDesc}>{I18N.auto.inviteTheTeamToBecome}</div>
                <Button className={s.addBtn}>{I18N.auto.newProject}</Button>
              </div>
            ) : null}

            {/* <ProjectList
              setTreeRef={(node) => (treeRef.current = node)}
              treeData={pingProjectList}
              className={s.pin__list}
              isPin
              height={treeHeight}
              titleRender={(node: ProjectInfo) => {
                return (
                  <RenderItem
                    isSortable={false}
                    key={node.id}
                    active={navigatorId === node.projectId}
                    // activeIndex={options?.activeIndex}
                    onClick={() => {
                      if (!node.isGroup) {
                        selectMenu?.(node.projectId!);
                      }
                    }}
                    item={node}
                  />
                );
              }}
            /> */}
            {/* <DnDList<ProjectInfo>
              wholeRowHandle={false}
              list={pingProjectList}
              onDragEnd={handleDragEnd}
              onDragOver={handleDragOver}
              fieldNames={{
                parentId: 'groupId',
                children: 'groupProjects',
              }}
              customRenderItem={(item: ProjectInfo, index, options) => {
                return (
                  <RenderItem
                    isSortable={false}
                    key={item.id}
                    active={navigatorId === item.projectId}
                    activeIndex={options?.activeIndex}
                    onClick={() => {
                      selectMenu(item.projectId!);
                    }}
                    item={item}
                  />
                );
              }}
              renderOverlayItem={(item) => {
                return <RenderOverlayItem item={item} />;
              }}
            /> */}
          </div>
          {/* {total ? (
               <div className={classNames(s.menu, 'mt-2')}>
                 <div
                   className={classNames(s.menuItem, s.all, {
                     [s.active]: navigatorId === ProjectListMenuId,
                   })}
                   onClick={() => {
                     selectMenu(ProjectListMenuId);
                   }}
                 >
                   {I18N.auto.viewAllItems}
                   <Icon className="ml-10" name={'icon-triangleLineRight_1'} />
                 </div>
               </div>
              ) : null} */}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
};
export default memo(PageLeft);
