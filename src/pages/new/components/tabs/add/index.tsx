import { useState } from 'react';

import { DropdownSelect, IconBtn } from '@/components/basic';
import I18N from '@/utils/I18N';

import { ViewType } from '../interface';
import s from './index.less';

interface TabsProps {
  defaultKey?: string | number;
  onChange: (type: ViewType) => void;
}

const Add: React.FC<TabsProps> = (props) => {
  const { onChange } = props;
  const [menuList] = useState([
    {
      name: I18N.auto.bulletinBoard,
      key: ViewType.kanban as unknown as string,
    },
    {
      name: I18N.auto.list,
      key: ViewType.list as unknown as string,
    },
  ]);

  return (
    <DropdownSelect
      data={menuList}
      onChange={(value) => {
        if (value === 'list') {
          onChange(ViewType.list);
        } else {
          onChange(ViewType.kanban);
        }
      }}
      tooltip={I18N.auto.addNewTab}
    >
      <div className={s.tabAdd}>
        <IconBtn iconName="icon-sys_add"></IconBtn>
      </div>
    </DropdownSelect>
  );
};
export default Add;
