import classNames from 'classnames'
import React, { useMemo, useRef } from 'react'

import { Icon, Image } from '@/components/basic'
import { UserInfo } from '@/types'
const peopleLight = require('@/assets/images/people.png')

import { pp } from '@popo-bridge/web'
import { useSelector } from 'react-redux'

import { RootState } from '@/models/store'
import { filepickerFop } from '@/utils/const'

import s from './index.less'

export type Props = {
  className?: string
  list: UserInfo[]
  showPercent?: boolean
  count?: number // 不用list的长度, 是因为在列表页面最多只返回两条数据
  maxShowCount?: number
  showFinishedIcon?: boolean
  avatarClassName?: string
  showCountIcon?: boolean
  renderCountIcon?: (v: number) => React.ReactNode
  showAddIcon?: boolean
  finishPercentTip?: React.ReactNode
  onClick?: React.MouseEventHandler<HTMLDivElement>
  canOpenUserProfile?: boolean
  size?: 'small' | 'default'
}
export default function RenderPeoples(props: Props) {
  const {
    list,
    count = 0,
    finishPercentTip,
    showAddIcon,
    showCountIcon = true,
    renderCountIcon,
    maxShowCount = 2,
    showFinishedIcon,
    className,
    avatarClassName = 'mr-6',
    onClick,
    canOpenUserProfile = true,
    size = 'default',
  } = props

  const { userInfo } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
  }))

  const memoList = useMemo(() => {
    let _maxShowCount = maxShowCount
    if (count > maxShowCount) {
      _maxShowCount = _maxShowCount - 1
    }
    const index = list?.findIndex(item => item.uid === userInfo?.uid)
    if (index !== -1) {
      const targetObject = list.splice(index, 1)[0] // 取出指定的对象
      list.unshift(targetObject) // 将该对象放到数组首位
    }

    return list?.slice(0, _maxShowCount).map((item, index) => {
      return (
        <div
          className={classNames(s.avatarBox, 'avatarBox', avatarClassName)}
          key={index}
          onClick={e => {
            if (canOpenUserProfile) {
              e.stopPropagation()
              pp.openUserProfile({
                uid: item.uid!,
              })
            }
          }}
        >
          <Image
            className={classNames(s.avatar, 'todo-rendedr-peoples-avatar')}
            src={`${item.avatarUrl!}${filepickerFop}`}
            //src={`${item.avatarUrl!}`}
            errorSrc={peopleLight}
            key={item.uid}
          ></Image>
          {item.finished && showFinishedIcon ? <i className={classNames(s.finishedIcon)}></i> : null}
        </div>
      )
    })
  }, [list, maxShowCount, showFinishedIcon, avatarClassName, count, userInfo?.uid, canOpenUserProfile])

  const ref = useRef<any>()

  return (
    <div
      className={classNames(s.renderPeoplesWrapper, className, s.detailRenderPeopleWrapper)}
      ref={ref}
      onClick={onClick}
    >
      {list.length ? (
        <>
          <div className={classNames(s.renderPeoples, 'peoples__container', s[size])}>
            {memoList}
            {count > maxShowCount && showCountIcon ? (
              <div
                className={classNames(s.count, 'todo-rendedr-peoples-count', {
                  [s.fs11]: count >= 100,
                })}
              >
                {renderCountIcon ? renderCountIcon(count) : `+${count - maxShowCount + 1}`}
              </div>
            ) : null}
            {showAddIcon && (
              <div className={s.addIcon}>
                <Icon name="icon-sys_add" fontSize={16} />
              </div>
            )}
          </div>

          {finishPercentTip}
        </>
      ) : null}
    </div>
  )
}
