import { Icon } from '@/components/basic';
import { Button } from '@bedrock/components';
import { openSession } from '../NotifyOption';
import { FC } from 'react';
import I18N from '@/utils/I18N';

interface IEnterGroupProps {
  projectInfo: any;
}

const EnterGroup: FC<IEnterGroupProps> = ({ projectInfo }) => {
  const handleEnterGroup = () => {
    openSession({ teamId: projectInfo.tid });
  };

  return (
    <Button
      onClick={handleEnterGroup}
      icon={<Icon name="icon-details_nav_incart" />}
      type="checked-neutral"
    >
      {I18N.auto.enterGroup}
    </Button>
  );
};
export default EnterGroup;
