import React, { useMemo, useState } from 'react';

import { RenderPeoplesAll } from '@/components/basic';
import Executor from '@/components/basic/people-picker/executor-new';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { useSelector } from 'react-redux';
import { RootState } from '@/models/store';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';
import { TaskTableRowType } from '@/utils/const';
import useGetPermissions from '@/hooks/useGetPermissions';

export type Props = {
  className?: string;
  hasArrow?: boolean;
  value?: UserInfo[];
  onChange: (v: UserInfo[]) => void;
  assignerUid: string;
  onOpen?: (v: boolean, itemName: string) => void;
};

const AddRow: React.FC<Props> = (props) => {
  const { hasArrow, value, onChange, assignerUid, onOpen } = props;

  const [visible, setVisible] = useState<boolean>(false);
  const onDelete = (uid: string) => {
    const _list = value?.filter((item) => item.uid !== uid) || [];
    onChange(_list);
  };

  const { userInfo, taskInfo } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
    taskInfo: state.detail.taskInfo,
  }));

  const { taskId, _rowType: rowType = TaskTableRowType.add } = taskInfo
  const permission = useGetPermissions({ taskId });

  const assigneeUids = useMemo(() => {
    return value?.map((item) => item.uid!) || [];
  }, [value]);

  const handleCancelUpdate = () => {
    setVisible(false);
    return Promise.resolve();
  };

  const handleConfirmUpdate = () => {
    setVisible(false);
    onChange(value || []);
    return Promise.resolve();
  };

  const memoPermissions = useMemo(() => {
    const [CAN_VIEW, CAN_EDIT, CAN_SET_COMPLETE_MODE, CAN_CANCEL_OBSERVER] = validatesPermission({
      permissions: permission,
      key: [
        TaskPermissionEnum.CAN_VIEW,
        TaskPermissionEnum.CAN_EDIT,
        TaskPermissionEnum.CAN_SET_COMPLETE_MODE,
        TaskPermissionEnum.CAN_CANCEL_OBSERVER,
      ],
    }) as boolean[];
    return {
      CAN_VIEW: CAN_VIEW || rowType === TaskTableRowType.add,
      CAN_EDIT: CAN_EDIT || rowType === TaskTableRowType.add,
      CAN_SET_COMPLETE_MODE: CAN_SET_COMPLETE_MODE || rowType === TaskTableRowType.add,
      CAN_CANCEL_OBSERVER: CAN_CANCEL_OBSERVER || rowType === TaskTableRowType.add,
      disabled: !CAN_EDIT && rowType !== TaskTableRowType.add,
    };
  }, [permission, rowType]);

  return (
    <PeoplePickerContext.Provider
      value={{
        assignerUid: assignerUid,
        assigneeUids: assigneeUids,
        currentUser: userInfo,
        isAssigneeUpdated: !!assigneeUids.length,
        canEditCompleteCondition: memoPermissions.CAN_SET_COMPLETE_MODE,
        onCancel: handleCancelUpdate,
        onConfirm: handleConfirmUpdate,
      }}
    >
      <Executor
        placeholder={I18N.auto.addTo}
        visible={visible}
        onVisible={(v) => {
          setVisible(v);
          onOpen?.(v, 'people');
        }}
        assignerUid={assignerUid}
        search
        value={value}
        onChange={onChange}
        hasArrow={hasArrow}
        resultGroup={false}
      >
        {value?.length ? (
          <RenderPeoplesAll
            showFinishedIcon
            list={value}
            onDelete={onDelete}
            className={s.addRenderPeoples}
          ></RenderPeoplesAll>
        ) : null}
      </Executor>
    </PeoplePickerContext.Provider>
  );
};

export default AddRow;
