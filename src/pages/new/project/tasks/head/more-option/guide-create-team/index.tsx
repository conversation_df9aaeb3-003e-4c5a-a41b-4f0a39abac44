import { TooltipPlacement } from '@bedrock/components/lib/Tooltip';
import { PropsWithChildren, useEffect, useMemo } from 'react';

import { Guide } from '@/components/basic';
import useGuide, { TaskGuide } from '@/components/guide/useGuide';
import { Permission } from '@/types';
import { GuideType } from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  className?: string;
  visible?: boolean;
  onOk?: () => void;
  placement?: TooltipPlacement;
  hasTeam?: boolean;
  permissions?: Permission[];
}

const GuideCreateTeam: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, onOk } = props;
  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.canCreateTeam,
  });
  const steps = useMemo(() => {
    return [
      {
        title: I18N.auto.createANewProjectGroup,
        content: I18N.auto.atProjectInitiation,
        selector: '.guide_create_team',
        placement: 'topRight',
      },
    ];
  }, []);
  useEffect(() => {
    TaskGuide.addListener(GuideType.canCreateTeam, () => {
      getGuideData().then((ret) => {
        //不显示引导
        if (!ret) {
          TaskGuide.removeGuide(GuideType.canCreateTeam, true);
        }
      });
    });
    TaskGuide.start(GuideType.canCreateTeam);
  }, []);
  const end = () => {
    handleHideGuide(); //内部会调用removeGuide
    onOk?.();
  };
  return (
    <>
      <Guide
        visible={showGuide}
        scrollType="fixed"
        padding={2}
        beforeText={I18N.auto.iGotIt}
        nextText={I18N.auto.iGotIt}
        stepText=" "
        steps={steps}
        afterStepChange={(step: any, index: number) => {
          if (index >= 1) {
            document.body.style.overflow = '';
            end();
          }
        }}
        onClose={() => {}}
        beforeStepChange={(step: any, index: number) => {
          document.body.style.overflow = 'hidden';
        }}
        onMaskClick={() => {
          document.body.style.overflow = '';
          end();
        }}
        maskClassName={s.mask}
        popoverClassName={s.guidePopover}
      />

      <div className="guide_create_team">{children}</div>
    </>
  );
};

export default GuideCreateTeam;
