import classNames from 'classnames';
import React, { PropsWithChildren, useEffect, useState } from 'react';

import { Icon, Input, Popover } from '@/components/basic';
import { KanBanGuide } from '@/components/guide/kanban-guide';
import useGuide from '@/components/guide/useGuide';
import { ViewTab } from '@/types';
import { GuideType } from '@/utils/const';

import Edit from '../edit';
import { Distribution, EditType, ViewType } from '../interface';
import s from './index.less';

const IconMap = {
  [ViewType.list]: 'icon-view_list_line',
  [ViewType.kanban]: 'icon-view_kanban_line',
  [ViewType.calendar]: 'icon-view_calendar_line',
  [ViewType.timeline]: 'icon-view_calendar_line',
};
export interface TabItemProps {
  onEdit?: (v: string) => void;
  onDelete?: () => void;
  currentId?: string | number;
  onSelect?: (id: string | number) => void;
  getBtnRef?: any;
  item: ViewTab;
  isEdit?: boolean;
  onChangeEditStatus?: (id: string | number | undefined) => void;
}

const TabItem: React.FC<PropsWithChildren<TabItemProps>> = (props) => {
  const {
    onEdit: baseEdit,
    onDelete,
    currentId,
    getBtnRef,
    item,
    onSelect,
    isEdit,
    onChangeEditStatus,
  } = props;

  const [value, setValue] = useState(item.name);
  const { showGuide, getGuideData, handleHideGuide } = useGuide({
    guideType: GuideType.tabKanban,
  });
  const onEdit = (v: EditType) => {
    if (v === EditType.rename) {
      onChangeEditStatus?.(item.id);
    } else {
      onDelete?.(item.id);
    }
  };
  const onBlur = (e) => {
    const v = e.target.value;
    onChangeEditStatus?.(undefined);
    // TODO 调用接口
    //失败
    baseEdit?.(v);
    setValue(item.name);
  };

  useEffect(() => {
    if (item.type === ViewType.kanban) {
      getGuideData();
    }
  }, [item.type]);

  return (
    <Popover
      arrowPointAtCenter
      displayType="primary"
      placement={'bottomLeft'}
      trigger={'click'}
      visible={showGuide}
      disabled={item.type !== ViewType.kanban}
      content={<KanBanGuide onClick={() => handleHideGuide()} />}
      onVisibleChange={(visible) => {
        if (!visible) {
          handleHideGuide();
        }
      }}
    >
      <div
        className={classNames('todo-tab-nav', s.tab, {
          [s.active]: currentId === item.id,
        })}
        onClick={() => {
          onSelect?.(item.id);
        }}
        ref={getBtnRef ? getBtnRef(item.id) : undefined}
      >
        <Icon name={IconMap[item.type]} className="mr-4"></Icon>
        {isEdit ? (
          <Input
            autoFocus
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            onBlur={onBlur}
          ></Input>
        ) : (
          item.name
        )}
        {item.distribution === Distribution.custom && currentId === item.id && !isEdit ? (
          <Edit iconClassName={s.icon} onChange={onEdit}></Edit>
        ) : null}
      </div>
    </Popover>
  );
};

export default TabItem;
