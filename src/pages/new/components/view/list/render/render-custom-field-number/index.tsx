import React, { useEffect, useState } from 'react';

import { FieldNumberInput } from '@/components/basic-project';
import { TodoInfo } from '@/types';
import { CustomField, NumberTypeField } from '@/types/custom-field';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  value?: number;
  taskInfo: TodoInfo;
  filedInfo: CustomField<NumberTypeField>;
  onChange?: (num?: number) => void;
  disabled?: boolean;
}

const RenderCustomFieldNumber: React.FC<Props> = (props) => {
  let { taskInfo, filedInfo, disabled, value, onChange } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [localValue, setLocalValue] = useState(value);
  const changeVisible = (v: boolean) => {
    setVisible(v);
  };
  const change = (v: number | string) => {
    setLocalValue(v ? +v : undefined);
  };

  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value);
    }
  }, [value]);

  return (
    <AddRowBox
      tipTitle=""
      placeholder={!value && value !== 0 && !visible ? '-' : ''}
      focused={visible}
      disabled={disabled}
      taskId={taskInfo.taskId}
      onClick={(e) => {
        if (!visible && !disabled) {
          changeVisible(true);
          setLocalValue(value);
        }
      }}
    >
      <div className={`${s.customFieldNumber} `}>
        <FieldNumberInput
          value={localValue}
          onChange={change}
          noRadius
          align="right"
          type={filedInfo.format}
          onBlur={() => {
            changeVisible(false);
            onChange?.(localValue);
          }}
        ></FieldNumberInput>
      </div>
    </AddRowBox>
  );
};

// function renderNumber(value: string | number | undefined, format: FieldNumberFormatEnum) {
//   switch (format) {
//     case FieldNumberFormatEnum.int:
//       return value;
//     case FieldNumberFormatEnum.float1:
//       return value || value === 0 ? (+value).toFixed(1) : '';
//     case FieldNumberFormatEnum.float2:
//       return value || value === 0 ? (+value).toFixed(2) : '';
//     case FieldNumberFormatEnum.percent:
//       return value || value === 0 ? (+value * 100).toFixed(0) + ' %' : '';
//     case FieldNumberFormatEnum.percent2:
//       return value || value === 0 ? (+value * 100).toFixed(2) + ' %' : '';
//     default:
//       return value;
//   }
// }

export default RenderCustomFieldNumber;
