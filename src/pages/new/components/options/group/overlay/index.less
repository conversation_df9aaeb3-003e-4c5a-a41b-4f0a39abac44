.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 36px;
  padding: 0 8px;
  cursor: pointer;
  color: var(--TextPrimary);
  &:hover {
    background-color: var(--aBlack6);
    border-radius: 4px;
  }
  .left {
    flex: 1;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .right {
    display: flex;
    justify-content: flex-end;
    width: 30px;
    flex-shrink: 0;
  }
}

.active {
  .icon {
    color: var(--Brand600);
  }
}
