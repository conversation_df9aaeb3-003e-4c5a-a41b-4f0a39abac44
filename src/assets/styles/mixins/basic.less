.clear-fix () {
  &:after {
    content: '';
    display: block;
    clear: both;
  }
}

.ellipsis (@max-width: 100%) {
  max-width: @max-width;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.line-ellipsis (@num: 3, @max-width: 100%) {
  max-width: @max-width;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @num;
  /* autoprefixer: off */
  -webkit-box-orient: vertical;
}

.size(@width, @height: @width) {
  width: @width;
  height: @height;
}

.caret-right(@width: 10px, @color: #999) {
  border-top: @width solid transparent;
  border-bottom: @width solid transparent;
  border-left: @width solid @color;
  width: 0;
  height: 0;
}

.caret-left(@width: 10px, @color: #999) {
  border-top: @width solid transparent;
  border-right: @width solid @color;
  border-bottom: @width solid transparent;
  width: 0;
  height: 0;
}

.absolute(@top: 0, @right: 0, @bottom: 0, @left: 0) {
  position: absolute;
  top: @top;
  right: @right;
  left: @left;
  bottom: @bottom;
}

.fixed(@top: 0, @right: 0, @bottom: 0, @left: 0) {
  position: fixed;
  top: @top;
  right: @right;
  left: @left;
  bottom: @bottom;
}

.line-height(@height) {
  height: @height;
  line-height: @height;
}

.circle(@width: 1px, @color: var(--line-b)) {
  border: @width solid @color;
  border-radius: 50%;
}

.mask(@opacity: 0.4, @zIndex: 10) {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: @zIndex;
  background: rgba(0, 0, 0, @opacity);
}

.ellipsis() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
