import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiProjectModifyMemberPost, apiProjectRemoveMemberPost } from '@/api';
import { AvatarPeople, Tooltip } from '@/components/basic';
import { SwitchPermission } from '@/components/basic-project';
import PageErrorBoundary from '@/components/page-error-boundary';
import TodoTable from '@/components/table';
import { Dispatch, RootState } from '@/models/store';
import { Member, ProjectInfo } from '@/types';
import { EnumRole, TaskNavigatorType } from '@/utils/const';
import { getComFormat } from '@/utils/date-format';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';
import { CorlorMode } from '@/utils/platform';

import Head from './head';
import s from './index.less';

const Members: React.FC = () => {
  const { memberList, corlorMode, projectInfo, permissions, userInfo } = useSelector(
    (state: RootState) => ({
      memberList: state.project.memberList,
      corlorMode: state.user.corlorMode,
      projectInfo: state.project.projectInfo,
      permissions: state.viewSetting.permissions,
      userInfo: state.user.userInfo,
    })
  );

  const dispatch = useDispatch<Dispatch>();
  const updateMembers = (member: Member, isDelete?: boolean) => {
    let members = memberList;
    if (isDelete) {
      members = memberList?.filter((item) => item.uid !== member.uid);
      dispatch.project.getProject({
        id: String(String(projectInfo?.projectId)),
        hideLoading: true,
      });
    } else {
      members = memberList?.map((item) => {
        if (item.uid === member.uid) {
          return member;
        }
        return item;
      });
    }

    dispatch.project.setData({
      memberList: members,
    });
  };
  const update = useMemoizedFn((member: Member) => {
    apiProjectModifyMemberPost({
      projectId: projectInfo.projectId,
      members: [
        {
          memberId: member.uid,
          memberType: member.sessionType,
          role: member.role,
        },
      ],
    }).then(() => {
      updateMembers(member);
    });
  });

  const remove = useMemoizedFn((member: Member) => {
    apiProjectRemoveMemberPost({
      projectId: projectInfo.projectId,
      members: [
        {
          memberId: member.uid,
          memberType: member.sessionType,
          role: member.role,
        },
      ],
    }).then(() => {
      updateMembers(member, true);
    });
  });

  const onExit = useMemoizedFn(() => {
    dispatch.project.getPingProjectList();
    dispatch.viewSetting.openNavigator({
      navigatorId: TaskNavigatorType.assignToMe,
    });
  });

  const memoColums = useMemo(() => {
    const [CAN_ADD_MANAGER] = validatesPermission({
      permissions: permissions,
      key: [ProjectPermissionEnum.CAN_ADD_MANAGER],
    }) as boolean[];

    return [
      {
        title: <div className={s.headTaskName}>{I18N.auto.fullName}</div>,
        dataIndex: 'name',
        align: 'left',
        width: 'minmax(3.2rem, 1fr)',
        render: (text: string, item: Member) => {
          return (
            <div className={s.colum}>
              <AvatarPeople avatarUrl={item.avatarUrl} className={s.avatar}></AvatarPeople>
              <Tooltip title={item.name} onlyEllipsis>
                <span className={s.name}>{item.name}</span>
              </Tooltip>
              {item.isCreator ? <div className={s.createTag}>{I18N.auto.assignedBy}</div> : null}
            </div>
          );
        },
      },
      {
        title: <div className={s.headTaskName}>{I18N.auto.joinTime}</div>,
        dataIndex: 'addTime',
        align: 'left',
        width: '3rem',
        render: (v: string) => {
          return (
            <div className={classNames('pl-8', s.time)}>
              {dayjs(v).format(
                getComFormat({
                  diffYear: false,
                  time: dayjs(v),
                })
              )}
            </div>
          );
        },
      },
      {
        title: <div className={s.headTaskName}>{I18N.auto.role}</div>,
        dataIndex: 'role',
        align: 'left',
        width: '2rem',
        render: (v: EnumRole, item) => {
          const isMe = userInfo?.uid === item.uid;
          return (
            <div className={classNames('pl-8', s.time)}>
              <SwitchPermission
                value={v}
                onChange={(v) => {
                  update({
                    ...item,
                    role: v,
                  });
                }}
                showRemove={CAN_ADD_MANAGER && !isMe}
                onRemove={() => {
                  remove(item);
                }}
                onExit={() => {
                  onExit();
                }}
                showExit={isMe}
                permissions={permissions}
                // disabled={disabled}
                isMe={isMe}
                projectInfo={projectInfo}
              ></SwitchPermission>
            </div>
          );
        },
      },
    ];
  }, [permissions, userInfo, update, remove, onExit, projectInfo]);

  useEffect(() => {
    if (projectInfo.projectId) {
      dispatch.project.getMemberList(String(projectInfo.projectId));
      dispatch.viewSetting.updatePermissions({
        permissions: [
          ProjectPermissionEnum.CAN_ADD_MANAGER,
          ProjectPermissionEnum.CAN_ADD_EDITOR,
          ProjectPermissionEnum.CAN_ADD_VIEWER,
          ProjectPermissionEnum.CAN_REMOVE_MANAGER,
          ProjectPermissionEnum.CAN_REMOVE_EDITOR,
          ProjectPermissionEnum.CAN_REMOVE_VIEWER,
        ],
      });
    }
  }, [projectInfo.projectId]);

  return (
    <PageErrorBoundary
      errorId="Page-Project-Members-List-Render-Error"
      //@i18n-ignore
      errorMsg="项目详情成员列表渲染异常"
    >
      <div className={s.projectList}>
        <Head className={s.head} permissions={permissions}></Head>
        <div className={s.list}>
          <TodoTable<ProjectInfo>
            columns={memoColums}
            data={memberList}
            drag={false}
            dark={corlorMode === CorlorMode.dark}
          ></TodoTable>
        </div>
      </div>
    </PageErrorBoundary>
  );
};
export default Members;
