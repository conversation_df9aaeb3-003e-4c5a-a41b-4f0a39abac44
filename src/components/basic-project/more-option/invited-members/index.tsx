import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { apiProjectAddMemberPost, apiProjectCheckRolePost, apiProjectMemberListGet } from '@/api';
import { Message } from '@/components/basic';
import Modal from '@/components/basic/modal';
import { PeopleSearchContext } from '@/components/basic-project/people-search/context';
import { Dispatch } from '@/models/store';
import { Member, Permission, UserInfo } from '@/types';
import { EnumRole } from '@/utils/const';
import I18N from '@/utils/I18N';
import { ProjectPermissionEnum } from '@/utils/permission';

import { EditMember } from '../..';
import s from './index.less';

interface Props {
  disabled?: boolean;
  className?: string;
  onChange?: (v: Member[]) => void;
  projectId?: number;
  /**
   * 增加管理员
   */
  canAddManager?: boolean;
  /**
   * 增加项目成员
   */
  canAddEditor?: boolean;
  /**
   * 增加仅查看
   */
  canAddViewer?: boolean;
}

const InvitedMembers: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, onChange, projectId } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [oldMembers, setOldMembers] = useState<Member[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const dispatch = useDispatch<Dispatch>();
  const onSubmit = () => {
    // 更新接口
    apiProjectAddMemberPost({
      projectId: projectId,
      members: members.map((item) => ({
        role: item.role,
        memberId: item.uid,
        memberType: item.sessionType,
      })),
    })
      .then(() => {
        const _members = [...oldMembers].concat(members);
        if (location.href.includes('/project')) {
          dispatch.project.getProject({
            id: String(projectId),
            hideLoading: true,
          });
        }
        onChange?.(_members);
        Message.success(I18N.auto.invitationSuccessful);
      })
      .finally(() => {
        setVisible(false);
      });
  };
  const getData = async () => {
    apiProjectMemberListGet({ projectId: String(projectId) }).then((data) => {
      const memberList = data.map((item) => ({
        ...item,
        uid: item.memberId!,
        sessionType: item.memberType!,
        name: item.name!,
        avatarUrl: item.pic!,
        role: item.role as EnumRole.admin,
      }));
      setOldMembers(memberList);
    });
    apiProjectCheckRolePost({
      roles: [
        ProjectPermissionEnum.CAN_ADD_MANAGER,
        ProjectPermissionEnum.CAN_ADD_EDITOR,
        ProjectPermissionEnum.CAN_ADD_VIEWER,
        ProjectPermissionEnum.CAN_REMOVE_MANAGER,
        ProjectPermissionEnum.CAN_REMOVE_EDITOR,
        ProjectPermissionEnum.CAN_REMOVE_VIEWER,
      ],

      projectId: projectId,
    }).then((data) => {
      setPermissions(data as Permission[]);
    });
  };
  useEffect(() => {
    if (projectId && visible) {
      setMembers([]);
      getData();
    }
  }, [projectId, visible]);
  const extraItemTag = (item: UserInfo) => {
    const data = oldMembers.find((v) => v.uid === item.uid);
    if (data) {
      let tagName = '';
      if (data.role === EnumRole.admin) {
        tagName = I18N.auto.managementHasBeenGranted;
      } else if (data.role === EnumRole.members) {
        tagName = I18N.auto.projectAwarded;
      } else if (data.role === EnumRole.viewOnly) {
        tagName = I18N.auto.awardedForQueryOnly;
      }
      return {
        tagName: tagName,
        tooltipTitle: '',
        disabled: true,
      };
    }
    return {
      tagName: '',
      tooltipTitle: '',
      disabled: false,
    };
  };
  const memoOkButtonProps = useMemo(() => {
    return {
      disabled: !members.length,
    };
  }, [members]);
  return (
    <>
      {React.cloneElement(children, {
        onClick: () => {
          setVisible(!visible);
        },
      })}
      <Modal
        visible={visible}
        width={480}
        title={I18N.auto.inviteTheProjectToBecome}
        okText={I18N.auto.invitation}
        closable
        centered
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
        scrolled={false}
        // wrapClassName={s.modal}
        okButtonProps={memoOkButtonProps}
      >
        <PeopleSearchContext.Provider value={{ extraItemTag }}>
          <EditMember
            showLabel={false}
            className={s.item}
            value={members}
            onChange={(v) => {
              setMembers(v);
            }}
            permissions={permissions}
            excludeUsers={oldMembers}
          ></EditMember>
        </PeopleSearchContext.Provider>
      </Modal>
    </>
  );
};

export default InvitedMembers;
