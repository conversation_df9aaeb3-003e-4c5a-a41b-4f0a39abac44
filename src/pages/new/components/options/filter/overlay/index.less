.overlay {
  width: unset;

  .filterHead {
    height: 52px;
    padding: 0 20px;
    border-bottom: none;
  }
  .box {
    padding: 0 16px 20px 16px;
    position: relative;
    &.filterBox {
      padding-bottom: 0;
    }
    .boxTitle {
      color: var(--TextTertiary);
      margin-bottom: 8px;
      font-size: 13px;
      line-height: 20px;
      font-weight: 400;
    }
  }
  .footer {
    padding: 8px 16px 20px;
    position: sticky;
    bottom: 0;
    background-color: var(--bgTop);
  }
}
.quicker {
  white-space: nowrap;
  :global {
    .rock-btn {
      padding: 0 10px 0 8px;
      font-size: 13px;
      color: var(--TextSecondary);
    }
  }
}
.filterList {
  margin-bottom: 12px;
}
.filteritem {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 8px;
  :global {
    .rock-select {
      width: 100% !important;
    }
  }
  .attr {
    flex-shrink: 0;
    width: 160px;
    margin-right: 12px;
  }
  .value {
    flex: 1;
    margin-right: 12px;
    max-width: 272px;
  }
  .delete {
    flex-shrink: 0;
    margin-top: 4px;
  }
}
