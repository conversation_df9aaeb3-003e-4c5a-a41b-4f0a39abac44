import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Button, CommonSearch, Dropdown, Icon, Menu, ToggleMenu } from '@/components/basic';
import Separate from '@/components/basic/separate';
import { Dispatch } from '@/models/store';
import { setStorage, StorageType } from '@/utils';
import Const from '@/utils/const';
import I18N from '@/utils/I18N';

import s from './index.less';
import { ChevronDown } from '@bedrock/icons-react';
import { Data16Pojectline, OperateCollapse2 } from '@babylon/popo-icons';

interface Props {
  className?: string;
  renderTitle?: () => React.ReactNode;
  renderAction?: () => React.ReactNode;
  renderSearch?: () => React.ReactNode;
}

const Head: React.FC<Props> = (props) => {
  const { className, renderTitle, renderAction, renderSearch } = props;
  const [searchValue, setSearchValue] = useState<string>();
  const dispatch = useDispatch<Dispatch>();
  const createProject = () => {
    dispatch.project.gotoAddProject();
  };

  const createProjectGroup = () => {
    const scrollEl = document.querySelector('.project__list.scroll__container');
    if (scrollEl) {
      scrollEl.scrollLeft = 0;
      scrollEl.scrollTop = 0;
    }

    dispatch.project.addProjectGroupEmpty({ isPin: false });
  };

  useEffect(() => {
    setStorage(Const.RetryReloadPageTimes, 0, StorageType.local);
    return () => {
      dispatch.project.setData({
        keyword: undefined,
        projectList: [],
      });
    };
  }, []);

  const overlayBasic = () => {
    return (
      <Menu>
        <Menu.Item
          icon={<Data16Pojectline className={`${s.icon} fs-16`} />}
          key="A"
          onClick={createProject}
        >
          {I18N.auto.newProject}
        </Menu.Item>
        <Menu.Item
          icon={<OperateCollapse2 className={`${s.icon} fs-16`} />}
          key="B"
          onClick={createProjectGroup}
        >
          {I18N.auto.newProjectGroup}
        </Menu.Item>
      </Menu>
    );
  };

  return (
    <div className={classNames(s.head, className)}>
      <div className={s.headLeft}>
        <div className={'flex'} style={{ minWidth: 0 }}>
          <ToggleMenu></ToggleMenu>
          {renderTitle ? renderTitle() : <div className={s.title}>{I18N.auto.allProjects}</div>}
        </div>
      </div>
      {renderSearch ? (
        renderSearch()
      ) : (
        <CommonSearch
          // ref={commonSearchRef}
          className={s.search}
          placeholder={I18N.auto.searchForProjects}
          onSearch={(v) => {
            dispatch.project.setData({
              keyword: v,
            });
            dispatch.project.getProjectList({ page: 1 });
          }}
          value={searchValue}
          onChange={(v) => {
            setSearchValue(v);
          }}
          fill={false}
          border={true}
          round={false}
          debounceTime={350}
          hasPrefix={true}
        />
      )}
      {renderAction ? (
        renderAction()
      ) : (
        <Dropdown overlay={overlayBasic()}>
          <Button
            type="primary"
            className={classNames(s.btn)}
            icon={<Icon name="icon-sys_add" className="mr-4"></Icon>}
            suffixIcon={<ChevronDown className="rock-dropdown-down-arrow-icon" />}
          >
            {I18N.auto.new}
          </Button>
        </Dropdown>
      )}
      <Separate className={s.headRight} />
    </div>
  );
};
export default Head;
