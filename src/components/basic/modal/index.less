body {
  .rock-modal-wrap {
    --text-color-1: var(--TextPrimary);
    --component-bg: var(--bgTop);
    // 加上margin，在某些机型上，会出现抖动问题
    margin: unset !important;

    .rock-modal-content {
      border-radius: 8px;
      border: 1px solid var(--aBlack12);
      background-color: var(--bgTop);
    }

    .rock-modal-header {
      padding: 20px;
      border-radius: 8px 8px 0 0;
      .rock-modal-title {
        color: var(--TextPrimary);
        font-weight: 600;
        line-height: 18px;
        font-size: 18px;
      }
    }

    .rock-modal-body {
      //不要在这里设置背景色 在popo浏览器会闪白
      // background-color: var(--bgTop);
      border-radius: 8px;
      padding: 0 20px;
      .rock-modal-confirm-title {
        color: var(--TextPrimary);
      }
      .rock-modal-confirm-content {
        color: var(--TextTertiary);
      }
    }
    .rock-btn-md {
      padding: 0 12px;
      height: 28px;
    }
    .rock-modal-confirm-btns .rock-btn + .rock-btn {
      margin-left: 12px;
    }
    .rock-btn-secondary {
      background-color: var(--bgTop);
      border: 1px solid var(--aBlack12);
      color: var(--TextPrimary);
    }
    .rock-btn-primary {
      background-color: var(--Brand500);
      border: none;
      color: var(--absWhite);
    }
    .rock-modal-close {
      width: 24px;
      height: 24px;
      padding: 4px;
      .rock-icon-close {
        width: 16px;
        height: 16px;
        svg {
          font-size: 16px;
          color: var(--IconBlack);
        }
      }
    }
    .rock-modal-footer {
      padding: 20px;
      border-radius: 0 0 8px 8px;
      .rock-btn {
        height: 32px;
      }
    }
  }
  .rock-modal-confirm {
    .rock-modal-body {
      padding: 24px;
    }
  }
  .rock-modal-confirm-warning {
    .rock-modal-confirm-body .rock-modal-confirm-header > .rock-icon {
      color: var(--R600);
    }
    .rock-btn-primary {
      background-color: var(--R600);
      &:hover {
        background-color: var(--R500);
      }
    }
  }

  .rock-modal-mask {
    background-color: var(--adminmask);
  }
}
