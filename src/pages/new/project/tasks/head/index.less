.head {
  display: flex;
  align-items: center;
  padding: 16px 20px 8px 20px;
  flex-shrink: 0;
  min-width: 640px;
  .projectHead {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .headLeft {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .projectIcon {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      // font-size: 28px;
    }
    .projectName {
      // flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      overflow: hidden;
      margin-left: 4px;
      margin-right: 4px;
      input {
        font-weight: bold;
        font-size: 16px;
      }
    }
    .status {
      flex-shrink: 0;
    }
  }
  .headRight {
    display: flex;
    flex-shrink: 0;
    margin-left: 30px;
    column-gap: 10px;

    .members {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .more {
      width: 28px;
      height: 28px;
    }
  }
}
.btn {
  display: flex;
  align-items: center;
  height: 28px;
  padding-left: 10px;
  padding-right: 12px;
  &:global(.rock-btn-primary) {
    background-color: var(--Brand500);
    color: var(--absWhite);
    &:hover {
      background-color: var(--Brand600);
    }
  }
}
