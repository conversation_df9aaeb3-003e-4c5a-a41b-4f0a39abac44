body {
  .rock-calendar-picker-dropdown {
    .rock-picker-panel {
      width: 100%;
      .rock-picker-header {
        border-bottom: none;
        .rock-btn-text-subtle {
          color: var(--TextPrimary);
          &:not([disabled]):hover {
            background-color: var(--aBlack8);
          }
        }
        .rock-picker-header-view {
          color: var(--TextPrimary);
          button[type='button'] {
            color: var(--TextPrimary);
          }
        }
      }
      .rock-picker-body thead th {
        color: var(--TextQuartus);
      }
      .rock-picker-cell-in-view {
        color: var(--TextPrimary);
      }
      .rock-picker-cell-today .rock-picker-cell-inner {
        color: var(--Brand500);
      }
      .rock-picker-cell-inner:hover {
        background: var(--Brand500);
        color: var(--absWhite);
      }
    }
    .rock-picker-header {
      height: 32px;
    }
    .rock-picker-week-panel,
    .rock-picker-date-panel {
      thead th {
        padding-bottom: 0;
      }
      .rock-picker-cell {
        padding: 0;
      }
    }
    .rock-picker-date-panel {
      .rock-picker-cell-inner {
        font-size: 13px;
      }
      .rock-picker-content {
        width: 100%;
      }
      .rock-picker-cell-disabled .rock-picker-cell-inner {
        background-color: transparent;
        color: var(--TextQuartus);
        &:hover {
          background-color: transparent;
        }
      }
    }
    .rock-picker-year-panel,
    .rock-picker-month-panel,
    .rock-picker-decade-panel {
      .rock-picker-body {
        padding: 8px 8px;
      }
      .rock-picker-content {
        width: 100%;
      }
      .rock-picker-cell-inner {
        height: 24px;
        line-height: 24px;
      }
    }
    .rock-picker-decade-panel {
      .rock-picker-cell-inner {
        font-size: 13px;
        width: 78px;
      }
    }

    .rock-picker-body {
      padding: 8px 8px;
      thead {
        th {
          font-size: 13px;
          height: 28px;
        }
      }
      tbody td {
        height: 28px;
      }
    }
  }
}
