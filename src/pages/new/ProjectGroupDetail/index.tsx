import { Header } from './components';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Dispatch, RootState, store } from '@/models/store';
import { useRequest } from 'ahooks';
import { useParams } from 'umi';
import s from './index.less';
import { apiProjectGroupDetailPost } from '@/api';
import ProjectTree from '../components/ProjectTree';
import { POPOBridgeEmitter } from '@popo-bridge/web';
import { EnumEmitter } from '@/utils/const';
import { tfProjectItem } from '@/models/utils';

const ProjectGroupDetail = () => {
  const dispatch = useDispatch<Dispatch>();
  const { id } = useParams();
  const groupId = Number(id);

  const { more, projectList: srcProjectList } = useSelector((state: RootState) => {
    return {
      more: state.project.pagination.more,
      projectList: state.project.projectList,
    };
  });

  const handleGetGroupData = async () => {
    return apiProjectGroupDetailPost({ groupId: groupId }).then((res) => {
      res.projects = res.projects?.map((item) => {
        return tfProjectItem(item, {
          groupId,
        });
      });

      return res;
    });
  };

  const {
    data: groupData,
    runAsync: getGroupData,
    mutate: updateGroupData,
  } = useRequest(handleGetGroupData, {
    refreshDeps: [groupId],
  });

  const handleDragEnd = (treeData, { preId, nextId, activeId, newParentId, oldParentId }) => {
    const params: any = {
      preId,
      tailId: nextId,
      newGroupId: newParentId,
      oldGroupId: oldParentId,
      currentId: activeId,
    };
    dispatch.project
      .projectSort(
        Object.keys(params).reduce((acc: any, key) => {
          if (typeof params[key] === 'number') {
            acc[key] = params[key];
          }
          return acc;
        }, {})
      )
      .then(getGroupData);
  };

  const handleCreateProject = () => {
    dispatch.project.gotoAddProject({ groupId });
  };

  const loadMore = () => {
    if (store.getState().project.pagination.more) {
      return dispatch.project.getProjectList({});
    }
    return Promise.resolve();
  };

  useEffect(() => {
    function handleProjectChange(data: any) {
      if (data.groupId === groupId) {
        getGroupData();
      }
    }

    POPOBridgeEmitter.addListener(EnumEmitter.ProjectGroupChange, handleProjectChange);

    return () => {
      POPOBridgeEmitter.removeListener(EnumEmitter.ProjectGroupChange, handleProjectChange);
    };
  }, [getGroupData, groupId]);

  return (
    <div className={s.project__group}>
      <Header
        title={groupData?.name || ''}
        groupId={groupId}
        onAddProjectsSuccess={getGroupData}
        onCreateProject={handleCreateProject}
      />
      <ProjectTree
        handleDragEnd={handleDragEnd}
        canLoadMore={more}
        loadMore={loadMore}
        treeData={groupData?.projects || []}
      />
      {/* <div className={s.project__list} ref={listNodeRef}>
        <SortableTree<ProjectInfo>
          handlerRender={(item) => {
            return <OperateMove className="fs-16" />;
          }}
          collapsible
          draggable
          isPin={false}
          onDragEnd={handleDragEnd}
          canLoadMore={more}
          scrollContainer={listNodeRef.current}
          showCollapse={true}
          loadMore={loadMore}
          // indicator={true}
          itemRender={(item) => <ProjectItem projectInfo={item} />}
          treeData={groupData?.groupProjects || []}
          overlayStyle={{
            backgroundColor: 'var(--aBlack6)',
            transform: `translateY(25px)`,
          }}
          estimateSize={(index, flattenedItems) => {
            return 51;
          }}
          rowOffset={37}
        />
      </div> */}
    </div>
  );
};

export default ProjectGroupDetail;
