.list {
  position: relative;
  flex: 1;
  padding-bottom: 8px;
  margin-left: 20px;
  margin-right: 8px;
  overflow: auto;

  width: calc(100% - 28px);
  height: calc(100% - 64px);

  .columName {
    display: flex;
    align-items: center;
    overflow: hidden;
    height: 50px;
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .creating__input {
      position: absolute;
      z-index: 1;
      height: 100%;
      width: 100%;

      :global {
        .rock-input {
          color: var(--TextPrimary-strong);
          font-size: 14px;
        }
      }

      &:global(.rock-input-wrapper) {
        border-radius: 0;
      }

      &::before {
        border-radius: 0;
      }
    }

    &.editing {
      position: relative;
      width: 100%;
    }
  }
  .projectIcon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 12px;
  }
  .time {
    line-height: 22px;
    font-size: 13px;
    color: var(--TextSecondary);
  }

  .drag__handler {
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 20;
    cursor: pointer;
    // margin-right: 3px;
    opacity: 0;
    border-radius: 2px;
    width: 24px;

    &:hover {
      background-color: var(--aBlack6);
    }
  }
  :global {
    .sortable-tree {
      // width: fit-content;
      min-width: 1063px;
      max-height: calc(100% - 37px);
    }

    .sortable-tree-item {
      background-color: var(--bgBottom);
      border-bottom: 1px solid var(--aBlack6);
      transition: background-color 0.2s ease-in-out;
      &:not(.sortable-tree-item__disabled):hover {
        background-color: var(--aBlack6);
        .drag__handler {
          opacity: 1;
        }
      }
    }

    .sortable-tree-item__wrapper {
      // padding-left: calc(var(--spacing) + 0.3rem);
      .tree__item {
        padding-left: 28px;
      }
    }

    .todo-drag {
      top: 13px;
    }
    .todo-thead {
      .todo-th {
        border-top: none;
      }
    }
    .todo-table .todo-tr .todo-th,
    .todo-table .todo-tr .todo-td {
      border-right: none;
    }
    .todo-table .todo-tr .todo-th:hover {
      background-color: transparent;
    }
  }
}

.list__header {
  position: sticky;
  top: 0;
  z-index: 11;
  padding-left: 24px;
  border-bottom: 1px solid var(--aBlack6);
  min-width: 1063px;
  background-color: var(--bgBottom);

  .content {
    display: grid;
    grid-template-columns: minmax(3.2rem, 1fr) 1.6rem 1.6rem 1.8rem 0.8rem;
    grid-template-rows: 36px;
    column-gap: 0.16rem;
    align-items: center;
    padding-right: 20px;
    background-color: transparent;
    min-width: 100%;

    .head {
      color: var(--TextSecondary, #111111b8);
      font-size: 13px;
      line-height: 22px;
      padding-left: 6px;
    }
  }
}

.overlay {
  background-color: var(--bgBottom) !important;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px var(--aBlack12), 0px 2px 4px 0px var(--aBlack6);
  :global {
    .tree__item {
      background-color: var(--bgBottom) !important;
      border-radius: 0 !important;
      box-shadow: unset !important;
    }
  }
}
