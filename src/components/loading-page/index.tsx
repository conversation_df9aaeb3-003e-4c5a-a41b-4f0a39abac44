import classNames from 'classnames';
import { useState } from 'react';

import { Skeleton } from '@/components/basic';

import s from './index.less';

type Props = {
  className?: string;
  showHeader?: boolean;
  showOptions?: boolean;
};

const Loading: React.FC<Props> = (props) => {
  const { className, showHeader = true, showOptions = true } = props;
  const [list] = useState<any[]>(new Array(20).fill(1));
  return (
    <div className={classNames(s.loading, className)}>
      {showHeader && (
        <div className={s.head}>
          <div className={s.name}>
            <Skeleton.Avatar className={s.icon} active></Skeleton.Avatar>
            <Skeleton.Input className={s.text1} active></Skeleton.Input>
          </div>
          <div className={s.tabs}>
            <Skeleton.Input className={classNames(s.text2, 'mr-24')} active></Skeleton.Input>
            <Skeleton.Input className={classNames(s.text2, 'mr-24')} active></Skeleton.Input>
            <Skeleton.Input className={classNames(s.text2, 'mr-24')} active></Skeleton.Input>
            <Skeleton.Input className={classNames(s.text2, 'mr-24')} active></Skeleton.Input>
          </div>
        </div>
      )}
      {showOptions && (
        <div className={s.options}>
          <Skeleton.Input className={classNames(s.text3, 'mr-16')} active></Skeleton.Input>
          <Skeleton.Input className={classNames(s.text3, 'mr-16')} active></Skeleton.Input>
          <Skeleton.Input className={classNames(s.text3, 'mr-16')} active></Skeleton.Input>
        </div>
      )}
      <div className={s.list}>
        {list.map((item, index) => {
          return (
            <div className={s.item} key={index}>
              <Skeleton.Avatar className={s.avatar} active></Skeleton.Avatar>
              <Skeleton.Input className={classNames(s.text1, 'mr-16')} active></Skeleton.Input>
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default Loading;
