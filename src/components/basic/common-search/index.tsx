import classNames from 'classnames';
import debounce from 'lodash/debounce';
import React, { useEffect, useImperativeHandle, useMemo, useRef } from 'react';

import { Icon, Input, InputProps } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';

interface PageProps extends Omit<InputProps, 'onChange'> {
  placeholder?: string;
  className?: string;
  onSearch?: (value: string) => void;
  debounceTime?: number;
  border?: boolean;
  round?: boolean;
  value?: string;
  onChange?: (value: string | undefined) => void;
  autoFocus?: boolean;
  hasPrefix?: boolean;
}

const CommonSearch = React.forwardRef<
  {
    focus: () => void;
  },
  PageProps
>((props, refs) => {
  const {
    className,
    onSearch: propsSearch,
    onChange,
    placeholder = I18N.auto.search,
    debounceTime = 200,
    value,
    autoFocus,
    hasPrefix,
    ...resetProps
  } = props;

  const isCompositionRef = useRef<boolean>(false);
  const ref = useRef<any>();
  const latestKeyDownValueRef = useRef<string>('');

  const isCallBackRef = useRef(propsSearch);
  isCallBackRef.current = propsSearch;
  const search = useMemo(() => {
    //console.log('初始化debounce');
    return debounce((value: string) => {
      //console.log('执行debounce----', value);
      isCallBackRef.current?.(value);
    }, debounceTime);
  }, [debounceTime]);

  const onSearch = (v: string) => {
    if (!isCompositionRef.current) {
      //console.log('触发search----');
      search(v);
    }
  };
  useImperativeHandle(refs, () => ({
    focus: () => {
      ref.current?.focus?.();
    },
  }));
  useEffect(() => {
    if (autoFocus) {
      setTimeout(() => {
        ref.current?.focus?.();
      }, 1000);
    }
  }, [autoFocus]);
  return (
    <Input
      // size="small"
      {...resetProps}
      className={classNames(s.search, className)}
      allowClear
      // autoFocus 导致滚动条置顶 使用 ref.current?.focus?.()
      placeholder={placeholder}
      ref={ref}
      prefix={hasPrefix ? <Icon name="icon-soft_find"></Icon> : null}
      value={value}
      onChange={(e) => {
        if (
          ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(
            latestKeyDownValueRef.current
          )
        ) {
          return;
        }
        if (!isCompositionRef.current) {
          onSearch(e.target.value);
        }

        onChange?.(e.target.value);
      }}
      onCompositionStart={() => {
        isCompositionRef.current = true;
      }}
      onCompositionEnd={(e) => {
        isCompositionRef.current = false;
        //@ts-ignore
        if (e.target.value !== value) {
          onSearch(e.target.value);
        }
      }}
      onKeyDown={(e) => {
        latestKeyDownValueRef.current = e.key;
      }}
    />
  );
});

export default CommonSearch;
