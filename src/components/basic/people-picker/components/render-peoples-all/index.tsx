import classNames from 'classnames';

import { IconBtn, Image, Tooltip } from '@/components/basic';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';
const peopleLight = require('@/assets/images/people.png');

import { pp } from '@popo-bridge/web';

import { filepickerFop } from '@/utils/const';

import s from './index.less';

export type Props = {
  className?: string;
  list: UserInfo[];
  showFinishedIcon?: boolean;
  onDelete?: (uid: string) => void;
  showDelete?: boolean;
};
export default function RenderPeoples(props: Props) {
  const { list, showFinishedIcon, className, onDelete, showDelete } = props;
  return (
    <div className={classNames(s.renderPeoples, className)}>
      {list.map((item, index) => {
        return (
          <div key={index} className={s.item}>
            <div
              className={s.info}
              onClick={() => {
                if (item?.uid) {
                  pp.openUserProfile({ uid: item?.uid });
                }
              }}
            >
              <div className={s.infobox}>
                <div className={classNames(s.avatarBox)}>
                  <Image
                    className={classNames(s.avatar, 'todo-rendedr-peoples-avatar')}
                    src={`${item.avatarUrl!}${filepickerFop}`}
                    //src={`${item.avatarUrl!}`}
                    errorSrc={peopleLight}
                    key={item.uid}
                  ></Image>
                  {item.finished && showFinishedIcon ? (
                    <i className={classNames(s.finishedIcon)}></i>
                  ) : null}
                </div>
                <div className={s.name}>{item.name}</div>
              </div>
            </div>
            <div className={s.right}>
              <IconBtn
                title={I18N.auto.viewPersonalInformation}
                iconName="icon-pc_details_userid"
                onClick={() => {
                  if (item?.uid) {
                    pp.openUserProfile({ uid: item?.uid });
                  }
                }}
              ></IconBtn>
              {showDelete ? (
                <IconBtn
                  className={classNames(s.delete, 'ml-8')}
                  title={I18N.auto.delete}
                  iconName="icon-sys_close"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete?.(item.uid!);
                  }}
                ></IconBtn>
              ) : null}
            </div>
          </div>
        );
      })}
    </div>
  );
}
