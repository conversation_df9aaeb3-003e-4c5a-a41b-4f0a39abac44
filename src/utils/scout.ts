import { UserInfo } from '@/types';

import Const from './const';

interface Error {
  name: string;
  message: string;
  stack?: string;
}

class TaskScout {
  initFg = false;
  initScoutFg = 1;
  valid() {
    return process.env.UMI_ENV === Const.ENV_PRODUCTION;
  }
  initScout() {
    if (this.valid()) {
      //@ts-ignore
      if (window.ScoutContext && this.initScoutFg < 10) {
        this.initScoutFg = 10;
        //@ts-ignore
        window.ScoutContext.onLoad(() => {
          //@ts-ignore
          if (window.Scout) {
            //@ts-ignore
            window.Scout.init({
              appId: '33f71a1c',
            });
            this.initFg = true;
            console.log('Scout 初始化成功');
          }
        });
      } else {
        console.log('Scout 初始化异常');
        this.initScoutFg += 1;
        setTimeout(() => {
          this.initScout();
        }, 100);
      }
    }
  }
  initLoginUser(userinfo?: UserInfo) {
    if (userinfo && this.valid() && this.initFg) {
      //@ts-ignore
      if (window.Scout) {
        //@ts-ignore
        window.Scout.setUserData({ userName: userinfo.uid });
      }
    }
  }
  captureError(opt: Error) {
    if (this.valid() && this.initFg) {
      //@ts-ignore
      if (window.Scout) {
        //@ts-ignore
        window.Scout.captureError({ name: opt.name, message: opt.message, stack: opt.stack });
      }
    }
  }
}

export default new TaskScout();
