.popOverlay {
  :global {
    .rock-popover-inner {
      border-radius: 8px;
      .rock-popover-inner-content {
        padding: 0;
      }
    }
  }
}

.popover__content {
  width: 160px;
  padding: 4px;

  .popover__item {
    display: flex;
    align-items: center;

    &--icon {
      font-size: 16px;
      color: var(--IconBlack);
      margin-right: 8px;
    }
    &--name {
      color: var(--TextPrimary);
      font-size: 13px;
      line-height: 20px;
    }
  }
}

.modal {
  :global {
    .rock-scrollbar {
      width: calc(100% + 32px) !important;
      margin-left: -16px;
      margin-right: -16px;

      .rock-scrollbar-view {
        padding-left: 16px !important;
        padding-right: 16px !important;
        max-height: calc(100vh - 280px + 15px) !important;
      }
    }

    .rock-modal-content {
      overflow: hidden;
    }
  }
}

.FieldOverlay {
  width: 260px;
  padding-top: 16px;
  padding-bottom: 64px;
  position: relative;
}

.scroll {
  width: 100%;
  max-height: calc(80vh - 40px - 16px);
  padding: 0 16px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--aBlack8) transparent;
}

.title {
  color: var(--TextPrimary);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 16px;
  font-weight: bold;
}
.form {
  width: 100%;
}

.typeIcon {
  font-size: 16px;
  // color: var(--IconBlack);
  margin-right: 8px;
  vertical-align: middle;

  &.result {
    margin-right: 4px;
  }
}

.optionLabel {
  font-size: 13px;
  vertical-align: middle;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 16px;
  min-height: 64px;
  width: 100%;

  .btn {
    min-width: 68px;
  }
  .btn + .btn {
    margin-left: 10px;
  }
}
.container {
  position: absolute;
  top: 0;
  left: 0;

  &.mask {
    width: 100vw;
    height: 100vh;
    z-index: 3000;
  }
}

.okBtn {
  &.blink {
    animation: Blink linear 0.15s alternate;
    animation-direction: alternate;
    animation-iteration-count: 4;
  }
}

.tooltip__select {
  :global {
    .rock-select-selector:not(.rock-select-selector-disabled) {
      background: transparent;
    }
  }
}

@keyframes Blink {
  from {
    background-color: var(--aBrand12);
    color: var(--Brand600);
  }
  to {
    background-color: var(--Brand600);
    color: var(--absWhite);
  }
}
