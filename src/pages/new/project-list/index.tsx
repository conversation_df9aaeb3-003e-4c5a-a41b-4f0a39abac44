import { useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import PageErrorBoundary from '@/components/page-error-boundary';
import { Dispatch, RootState, store } from '@/models/store';
import { TableNoDataProp } from '@/types';
import {
  ProjectNoDataDark,
  ProjectNoDataLight,
  ProjectSearchNoDataDark,
  ProjectSearchNoDataLight,
} from '@/utils/const';
import I18N from '@/utils/I18N';
import { CorlorMode } from '@/utils/platform';

import Head from './head';
import s from './index.less';
import { ListTableNoData } from '@/components/table/components/no-data';
import ProjectTree from '../components/ProjectTree';

const Project: React.FC = () => {
  const { projectList, corlorMode, more, keyword, loading } = useSelector((state: RootState) => ({
    projectList: state.project.projectList,
    corlorMode: state.user.corlorMode,
    more: state.project.pagination.more,
    keyword: state.project.keyword,
    loading: state.project.listLoading,
  }));

  const dispatch = useDispatch<Dispatch>();

  // const handleConfirm = (e: any) => {
  //   e.stopPropagation();
  //   const input = e.target as InputElement;
  //   const name = input.value;
  //   if (name) {
  //     console.log('confirm', name);
  //     dispatch.project.createProjectGroup({
  //       name,
  //       isPin: false,
  //     });
  //   }
  // };

  // const memoColums = useMemo(() => {
  //   return [
  //     {
  //       title: <div className={s.headTaskName}>{I18N.auto.entryName}</div>,
  //       dataIndex: 'name',
  //       align: 'left',
  //       width: 'minmax(3.2rem, 1fr)',
  //       render: (text: string, item: ProjectInfo) => {
  //         const isGroup = item.isGroup;
  //         if (isGroup && item.status === ProjectItemStatus.editing) {
  //           return (
  //             <div className={`${s.columName} ${s.editing}`}>
  //               <Input
  //                 onPressEnter={handleConfirm}
  //                 onBlur={handleConfirm}
  //                 onClick={(e) => e.stopPropagation()}
  //                 className={s.creating__input}
  //                 maxLength={50}
  //                 defaultValue={text}
  //                 autoFocus
  //                 placeholder={I18N.auto.newGroup}
  //               />
  //             </div>
  //           );
  //         }

  //         return (
  //           <div className={s.columName}>
  //             {isGroup ? (
  //               <GroupIcon />
  //             ) : (
  //               <IconProject
  //                 className={s.projectIcon}
  //                 fontSize={24}
  //                 name={item.icon}
  //                 bgColor={item.iconColor}
  //                 active
  //               ></IconProject>
  //             )}
  //             <Tooltip title={item.name} onlyEllipsis>
  //               <span className={s.name}>{text}</span>
  //             </Tooltip>
  //             <Ping
  //               isPin={!!item.isPin}
  //               projectId={item.projectId!}
  //               onSuccess={(v) => {
  //                 dispatch.project.getPingProjectList();
  //                 dispatch.project.updateProjectList(v);
  //               }}
  //             ></Ping>
  //           </div>
  //         );
  //       },
  //     },
  //     {
  //       title: <div className={s.headTaskName}>{I18N.auto.state}</div>,
  //       dataIndex: 'state',
  //       align: 'left',
  //       width: '1.6rem',
  //       render: (v: string) => {
  //         return (
  //           <div className={'pl-6'}>
  //             <ProjectStatusTag status={v}></ProjectStatusTag>
  //           </div>
  //         );
  //       },
  //     },
  //     {
  //       title: <div className={s.headTaskName}>{I18N.auto.projectMembers}</div>,
  //       dataIndex: 'memberVos',
  //       align: 'left',
  //       width: '1.6rem',
  //       render: (_v, item: ProjectInfo) => {
  //         return (
  //           <div
  //             className={classNames(s.members)}
  //             onClick={(e) => {
  //               e.stopPropagation();
  //               dispatch.viewSetting.setTaskMenu(item.projectId!);
  //               history.push(`/new/project/${item.projectId}/member`);
  //             }}
  //           >
  //             <RenderPeoples
  //               showFinishedIcon={false}
  //               list={item.members || []}
  //               count={item.memberCount || item.members?.length}
  //               maxShowCount={3}
  //               avatarClassName="mr-4"
  //               canOpenUserProfile={false}
  //               // renderCountIcon={() => (
  //               //   <>
  //               //     <Icon name="icon-sys_more"></Icon>
  //               //   </>
  //               // )}
  //             ></RenderPeoples>
  //           </div>
  //         );
  //       },
  //     },
  //     {
  //       title: <div className={s.headTaskName}>{I18N.auto.creationTime}</div>,
  //       dataIndex: 'createTime',
  //       align: 'left',
  //       width: '1.8rem',
  //       render: (v: string) => {
  //         return (
  //           <div className={classNames('pl-8', s.time)}>
  //             {dayjs(v).format(
  //               getComFormat({
  //                 diffYear: false,
  //                 time: dayjs(v),
  //               })
  //             )}
  //           </div>
  //         );
  //       },
  //     },
  //     {
  //       title: <div className={s.headTaskName}>{I18N.auto.more}</div>,
  //       dataIndex: 'projectName',
  //       align: 'left',
  //       width: '0.8rem',
  //       render: (v, item) => {
  //         return (
  //           <div
  //             className={'pl-8'}
  //             onClick={(e) => {
  //               e.stopPropagation();
  //             }}
  //           >
  //             <ProjectMoreOption projectInfo={item}>
  //               <IconBtn iconName="icon-sys_more"></IconBtn>
  //             </ProjectMoreOption>
  //           </div>
  //         );
  //       },
  //     },
  //   ];
  // }, [dispatch]);

  const loadMore = () => {
    if (store.getState().project.pagination.more) {
      return dispatch.project.getProjectList({});
    }
    return Promise.resolve();
  };

  const handleDragEnd = (treeData, { preId, nextId, activeId, newParentId, oldParentId }) => {
    const params: any = {
      preId,
      tailId: nextId,
      newGroupId: newParentId,
      oldGroupId: oldParentId,
      currentId: activeId,
    };
    dispatch.project.projectSort(
      Object.keys(params).reduce((acc: any, key) => {
        if (typeof params[key] === 'number') {
          acc[key] = params[key];
        }
        return acc;
      }, {})
    );
  };

  useEffect(() => {
    dispatch.project.getProjectList({
      page: 1,
    });
  }, []);

  // useEffect(() => {
  //   // 计算左侧导航高度
  //   const handleResize = debounce(() => {
  //     if (listNodeRef.current && projectList?.length) {
  //       const height = listNodeRef.current.clientHeight - 37;
  //       console.log('height', height);
  //       setTreeHeight(height);
  //     }
  //   }, 320);
  //   handleResize();
  //   window.addEventListener('resize', handleResize);
  //   return () => {
  //     window.removeEventListener('resize', handleResize);
  //   };
  // }, [listNodeRef.current, projectList]);

  return (
    <PageErrorBoundary
      errorId="Page-Project-List-Render-Error"
      //@i18n-ignore
      errorMsg="全部项目列表界面异常"
    >
      <div className={s.projectList}>
        <Head className={s.head}></Head>
        {/* <div className={`${s.list} project__list scroll__container`} ref={listNodeRef}>
          <div className={s.list__header}>
            <div className={s.content}>
              <div className={s.head}>
                <span className={s.text}>{I18N.auto.entryName}</span>
              </div>
              <div className={s.head}>{I18N.auto.state}</div>
              <div className={s.head}>{I18N.auto.projectMembers}</div>
              <div className={s.head}>{I18N.auto.creationTime}</div>
              <div className={s.head}></div>
            </div>
          </div>

          <SortableTree<ProjectInfo>
            handlerRender={(item) => {
              return <OperateMove className="fs-16" />;
            }}
            collapsible
            draggable
            isPin={false}
            onDragEnd={handleDragEnd}
            canLoadMore={more}
            scrollContainer={listNodeRef.current}
            showCollapse={true}
            loadMore={loadMore}
            // indicator={true}
            itemRender={(item) => <ProjectItem projectInfo={item} />}
            treeData={projectList}
            overlayStyle={{
              backgroundColor: 'var(--aBlack6)',
              transform: `translateY(25px)`,
            }}
            estimateSize={(index, flattenedItems) => {
              return 51;
            }}
            rowOffset={37}
          /> */}
        <ProjectTree
          handleDragEnd={handleDragEnd}
          canLoadMore={more}
          loadMore={loadMore}
          treeData={projectList}
          keyword={keyword}
          loading={loading}
        />

        {/* {!!projectList?.length && (
            <ProjectList
              treeData={projectList}
              titleRender={(node) => {
                return <ProjectItem projectInfo={node} />;
              }}
              indicatorIndent={60}
              height={treeHeight}
              switcherIcon={(props: TreeNodeProps) => {
                const { isGroup, expanded } = props as unknown as ProjectInfo;
                let switchIcon = null;
                if (isGroup) {
                  switchIcon = (
                    <OperateArrowdown
                      className={classNames(s.switch__icon, 'fs-16', {
                        [s.arrow__right]: !expanded,
                        [s.hidden]: !isGroup,
                      })}
                    />
                  );
                }

                return switchIcon;
              }}
              loadMore={loadMore}
              className={`${s.project__tree} project__tree`}
            />
          )} */}
        {/* {!projectList?.length && keyword && (
          <ListTableNoData
            noSearchDataProps={memoNoSearchDataProps}
            dark={corlorMode === CorlorMode.dark}
            className={s.noData}
          />
        )}

        {!projectList?.length && !keyword && (
          <ListTableNoData
            noDataProps={memoNoDataProps}
            dark={corlorMode === CorlorMode.dark}
            className={s.noData}
          />
        )} */}

        {/* <TodoTable<ProjectInfo>
            columns={memoColums}
            data={projectList}
            atBottomStateChange={loadMore}
            drag={true}
            wholeRowDrag={false}
            dark={corlorMode === CorlorMode.dark}
            noDataProps={memoNoDataProps}
            noSearchDataProps={memoNoSearchDataProps}
            // onDragOver={handleDragOver}
            onDragEnd={({ active, over }) => {
              if (active.id !== over?.id) {
                let activeIndex = -1;
                let activeItem: ProjectInfo = {};
                let overIndex = -1;
                let overItemData: ProjectInfo = {};
                projectList.some((item, index) => {
                  if (item.id === active.id) {
                    activeIndex = index;
                    activeItem = item;
                  }
                  if (item.id === over?.id) {
                    overIndex = index;
                    overItemData = item;
                  }
                  return activeIndex !== -1 && overIndex !== -1;
                });

                if (overItemData?.isGroup) {
                  // overIndex = overIndex + 1;
                  overItemData.groupProjects = [...(overItemData.groupProjects || []), activeItem];
                  dispatch.project.setData({
                    pingProjectList: [...projectList],
                  });
                } else {
                  const _list = arrayMove(projectList, activeIndex, overIndex);
                  dispatch.project.setData({
                    projectList: _list,
                  });
                  const index = _list.findIndex((item) => item.projectId === active.id);
                  apiProjectResortPost({
                    currentProjectId: active.id as number,
                    preProjectId: _list[index - 1]?.projectId,
                    tailProjectId: _list[index + 1]?.projectId,
                  }).then(() => {
                    const activeItem = projectList[activeIndex];
                    if (activeItem.isPin) {
                      dispatch.project.getPingProjectList();
                    }
                  });
                }

                // apiProjectResortPost({
                //   currentProjectId: active.id as number,
                //   preProjectId: _list[index - 1]?.projectId,
                //   tailProjectId: _list[index + 1]?.projectId,
                // }).then(() => {
                //   const activeItem = projectList[activeIndex];
                //   if (activeItem.isPin) {
                //     dispatch.project.getPingProjectList();
                //   }
                // });
              }
            }}
            //@ts-ignore
            onRowClick={(v) => {
              dispatch.viewSetting.openNavigator({
                navigatorId: v.id as string,
              });
            }}
          ></TodoTable> */}
        {/* </div> */}
      </div>
    </PageErrorBoundary>
  );
};
export default Project;
