export default {
  associateProjectLimit: '关联项目超过上限',
  selectProjectGroup: 'Select a Group',
  addProjectGroup: 'Add to Group',
  ungroupedProject: 'Ungrouped Projects',
  addProject: 'Add Project',
  projectGroupGuideDesc: 'Organize and manage complex projects with ease',
  moveToGroup: 'Move to Group',
  canDragToGaoup: 'Drag and add projects to this group',
  afterProjectDelGroup: 'Its projects will remain in the workspace',
  confirmDelProjectGroup: 'Delete project group {val1}?',
  delGroup: 'Delete Group',
  newGroup: 'Create Group',
  newProjectGroup: 'Create Project Group',
  new: 'Create',
  members: 'Members {val1} ',
  setTaskNotify: 'Set notifications to track progress',
  clickJumpGTeam: 'Join the group chat to collaborate',
  afterUnbind: 'Group notifications will no longer be sent upon unlink',
  operateSuccess: 'Saved',
  confirmToUnbind: 'Confirm Unlink?',
  unbindTeam: 'Unlink Group Chat',
  bindTeam: 'Link Group',
  nitifyAlert: 'Link Group',
  membersAuth: 'Members & Permissions',
  haveBeenGranted: 'Role: {val1}',
  viewersHasNoPermission:
    'Viewer role cannot invite members or share projects. Contact admin to add members.',
  enterGroup: 'Enter Group Chat',
  past: 'Overdue',
  viewSubTask: 'View Subtasks',
  subTask: 'Subtasks',
  subTaskPlaceholder_2: 'Type subtask name ',
  subTaskPlaceholder: 'Type subtask name and press Enter to confirm',
  delSubTask: 'Delete Subtask',
  transferIndependent: 'Outdent subtask ',
  transferIndependentSuccess: 'Subtask outdented',
  AISplitSubtask: 'Smart Breakdown',
  deleteSubtasksConcurrent: 'Delete all subtasks',
  keepSubtasks: 'Preserve subtasks ',
  newSubtask: 'Add Subtask',
  smartSubtaskDesc:
    'Create subtasks within a task and leverage AI to break them down, streamlining complex workflows for maximum productivity.',
  smartSubtask: 'Smart Task Breakdown',
  subtaskLimit: 'Limit: {val1} subtasks per parent task',
  regenerate: 'Retry',
  splitSubtaskFail: 'Subtask breakdown failed. Please try again.',
  editingNotAllowed: 'Read only',
  backToParentTask: 'Back to Parent Task',
  addTitleEnter: 'Type subtask name and press Enter to confirm',
  unnamedProject: 'Untitled Project',
  project: 'Project',
  terminateTheTaskAnd: 'unlinks the task from the project [{val1}]',
  associateTasks: 'links the task to the project [{val1}] ',
  removePermissions: 'Remove User Permissions',
  exitTheProject: 'Exit Project',
  onlyViewingIsNotAllowed: 'View-only role. Editing of tasks and projects is restricted.',
  viewOnly: 'Viewer',
  modifyExceptProject: 'Edit and complete tasks',
  projectMembers: 'Member',
  canModifyAll:
    'Edit project name, project status, task management, public view, and custom fields',
  administrators: 'Admin',
  file: 'Archived',
  thereIsARiskInvolved: 'At Risk',
  suspend: 'On Hold',
  haveInHand: 'In Progress',
  afterTheChangeThereWillBe:
    'Change records will be generated and project members will be notified',
  confirmTheChangeItem: 'Confirm change project status to: {val1}',
  updatedTo: 'Updated from {val1} to {val2}',
  newProjectStatus: 'New project status: {val1}',
  searchForProjects: 'Search project',
  thereIsCurrentlyNoBelongingItem: 'Project not found',
  searchGroupChatOr: 'Search group chat or user',
  chooseGroupChatOr: 'Choose group chat or user',
  copySuccessful: 'Copied',
  sharingFailed: 'Sharing failed',
  sharingSuccess: 'Done',
  shareTo: 'Share to chats',
  copyLink: 'Copy link',
  shareToConversation: 'Share to chats',
  invitation: 'Confirm',
  inviteTheProjectToBecome: 'Invite team members',
  awardedForQueryOnly: 'View-only role',
  projectAwarded: 'Team member role ',
  managementHasBeenGranted: 'Admin role ',
  afterExitingYouWill:
    'Exiting will revoke your access to project tasks, though assigned tasks will be retained.',
  signOut: 'Exit',
  confirmDisbandItem: 'Confirm to close the project [{val1}] ?',
  confirmExitItem: 'Confirm to exit the project [{val1}] ?',
  preservation: 'Save',
  editProject: 'Edit project info',
  pleaseEnterTheProject: 'Enter project name',
  projectIconAnd: 'Project icon & name',
  theFollowingUsersWill: 'The users listed below will get your invitation',
  inviteMembersToAdd: 'Invite team members',
  taskAssistant: 'Task Helper',
  atProjectInitiation:
    'At the start of the project, you can create a project group chat to facilitate instant communication and collaboration among team members.  ',
  createANewProjectGroup: 'Create project group chat',
  clickHereToStand:
    'Click here to access the project group chat and kickstart a collaborative journey with your team!',
  theProjectGroupHasBeenCreated: 'Project group chat has been created',
  pleaseEnter: 'Please enter',
  chooseAColor: 'Choose color',
  selectAvatar: 'Choose icon',
  changeIcon: 'Change icon',
  selectIcon: 'Choose icon',
  easyToLearnItems: 'Getting Started',
  invitationSuccessful: 'Invitation successful',
  userFeedback: 'Feedback',
  userManual: 'User Guide',
  member: 'User(s)',
  operation: 'Action',
  preview: 'Preview',
  notificationScenario: 'Notification',
  event: 'Event',
  operationFailedPlease_3: 'Failed. Please try again',
  savedSuccessfully: 'Saved',
  subscriptionNotification: 'Subscribe',
  planSmartWith: 'Plan smart with start-to-finish timelines to visualize schedules',
  setTaskTimeframes: 'Set Task Timeframes',
  trackProgress: 'Track progress dynamically, predict delays proactively.',
  newTimelineView: 'New Timeline View',
  relatedGroupChat: 'Link to group chats',
  onlyAssociatedWithYou: 'Can only link to group chats where you are the owner or admin',
  successfullyAssociated: 'Linked to group chats',
  onlySupportsAssociation: 'Can only link to existing group chats',
  view: 'View',
  setSpecificTime: 'Set Specific Time',
  dueTomorrow: 'Due Tomorrow',
  dueToday: 'Due Today',
  setStartDueDate: 'Set Start & Due Date',
  viewPreview: 'Preview',
  groupNotification: 'Group Notices',
  associateGroupFirst: 'Please link to a group chat',
  triggerNotificationOverdue: 'Notify group members when task is overdue',
  taskOverdue: 'Overdue Task',
  triggerNotificationAfterExecuted: 'Notify group members when task is completed',
  triggerNotificationAfterFilling: 'Notify group members when task is created and assigned',
  assignTasks: 'New Task ',
  expandTasksOneClick: 'Click to show task list',
  collapseTasksOneClick: 'Click to hide task list',
  nextScreen: 'Next',
  previousScreen: 'Previous',
  addGroup: 'New Group',
  completionIcon: 'Completed Badge',
  cardSideColor: 'Card Edge Color',
  cardConfig: 'Card Settings',
  expandTaskList: 'Show Task List',
  collapseTaskList: 'Hide Task List',
  timeline: 'Timeline',
  associatedGroup: 'Group chat linked:',
  assignmentCompleted: 'Assignment completed',
  completedByEveryone: 'Completed collectively by all assignees',
  toDoCompleted_2: 'Confirmed the task completion',
  newNews: 'New',
  assigneeCancellation: '{val1} revoked their task completion confirmation',
  appointedPersonForAcceptance: 'Task completion confirmed',
  selectItem: '{val1} selected',
  everyDay_2: 'Every 3 days',
  nextWeek: 'Next week',
  laterToday: 'Later today',
  whenDeletingReminders: 'Remove alert',
  incompleteParticipants: 'Incomplete: {val1}',
  participantCompleted: 'Completed: {val1}',
  allParticipants: 'Assignees ({val1}, all completed)',
  assignedBy: 'Owner',
  addAComment: 'Mention people with @; press Enter to send your comment',
  valPersonNotYet: 'waiting for {val1} assignee(s) to do',
  valPersonFinished: '{val1} assignee(s) have completed the task',
  reminderTime: 'Alert',
  addParticipants: 'Add assignees(s)',
  start: 'From {val1}',
  openToDo: 'Task details',
  unsort: 'Default',
  sort: 'Sort',
  return: 'Back',
  toDoFor_2: 'Tasks for {val1} and me',
  toDoFor: 'Tasks for {val1}',
  successfulTransferRefersTo_2: 'Ownership assigned',
  successfulTransferRefersTo: 'Ownership assigned, You have quit the task',
  currentlyNotSupportedForAdding: 'Unable to assign the task owner as an assignee',
  theCurrentVersionDoesNot:
    'Capability unavailable. Update POPO to the latest version for full functionality',
  attachment: 'Attachments',
  addAttachments: 'Add attachments',
  deadlineSet: 'Tasks with deadlines will be displayed here. You have no scheduled tasks yet.',
  startTimeNotAvailable: 'Start time should be no later than deadline',
  deadlineNotAvailable: 'Deadline should be no earlier than start time',
  batchAdd: 'Batch add',
  ifAddingStarts: 'Removing the deadline will also remove the start time',
  currentListDefault_2: "You're the default assignee of all the listed tasks",
  currentListDefault: "You're the default owner of all the listed tasks",
  inASeparateWindow: 'Open in new window',
  closeIndependentWindows: 'Move to main window',
  nextFriday: 'Next Friday',
  nextMonday: 'Next Monday',
  thisFriday: 'This Friday',
  remove: 'Remove',
  thisIsAlreadyAToDoList: 'You are the task owner and cannot be an assignee',
  escCancel: 'Esc to cancel',
  enter: 'Enter to select',
  switch: '↑ ↓ to shift',
  recentContacts: 'Recent',
  send: 'Send',
  mention: 'Mention',
  expression: 'Emoji',
  nonToDoParticipation: 'Invite the mentioned user to the task',
  cannotSendEmpty: 'Cannot send an empty comment',
  everyone: 'All',
  mentionAllAchievements: 'Mention all group members',
  me: '@Me',
  okToDeleteThis: 'Confirm to delete this comment?',
  deleteComment: 'Delete',
  completionTime: 'Completion Time',
  characterCountExceeds: 'Character limit exceeded',
  thisTaskIsCurrentlyUnavailable: 'No assignees for this task yet',
  sortBy: 'Sort by {val1}',
  unfinishedToDoList: 'Assignees not completing the task',
  contacts: 'Contacts',
  participants: 'Assignees',
  notJoined: 'Guest',
  thisWeek: 'This week',
  theNextOneHasBeenGenerated:
    "A loop task has been generated, making the current task's details uneditable",
  confirmToCancelTheClosure_2: 'Confirm to unfollow the task?',
  confirmToRemoveFrom: 'Confirm to remove yourself from the task?',
  afterTheTransferYouWill: 'After the transfer, you will no longer be the owner of this task',
  atMostOnce: 'You can upload up to 5 files at a time',
  thereAreMoreThanM: 'There are files exceeding 200MB in the selected files',
  thereAreCurrentlyNoProjectsAvailable: 'No projects',
  state: 'Status',
  entryName: 'Project Name',
  allProjects: 'All Projects',
  membersCanCreate: 'Create project group chat concurrently',
  newSchedule: 'Create group event',
  enterGroupChat: 'Enter group chat',
  createANewGroupChat: 'Create group chat',
  createANewProjectGroup_2:
    'Failed to create the project group chat. Please retry in "More Settings".',
  reasonMustBeFilledIn: 'Reason for the request (required)',
  addTheFollowingMembers:
    'The following users must obtain supervisor approval to join the group. Please enter the reason for the request.',
  inviteToJoinTheGroupApplication: 'Request to join',
  waitingForPeopleToJoinTheGroup:
    '{val1}, the {val2} users have exceeded the group limit as specified and were unable to join this group',
  role: 'Role',
  joinTime: 'Joined on',
  fullName: 'Name',
  searchForExistingItems: 'Search project team members',
  memberManagement: 'Member Management',
  inviteMembers: 'Invite team members',
  changeProjectStatus: 'Change project status',
  more: 'More ',
  viewAllItems: 'View all projects',
  inviteTheTeamToBecome: 'Invite your team members to create, allocate, and track tasks together',
  newProject: 'New Project',
  cancel: 'Cancel',
  addComments: 'Add notes',
  priority: 'Priority',
  deadline: 'Deadline',
  reply: 'Replies',
  transferAssignor_2: 'set {val1} as the task owner',
  unassignPending: 'removed {val1} from the task assignees',
  assignToDoTo: 'assigned the task to {val1}',
  changeTheTitleTo: 'updated the title to {val1}',
  rebuildTheToDoList: 'reopened the task',
  toDoCompleted: 'completed the task',
  createdToDo: 'created the task',
  editRecord: 'History',
  whole: 'All',
  comment: 'Comments',
  source_1: 'Source',
  source: 'Source:',
  annually: 'every year',
  monthly: 'every month',
  biweekly: 'every two weeks',
  weekly: 'every week',
  everyDay: 'every day',
  everyWorkingDay: 'every weekday (Monday to Friday)',
  determine: 'Done',
  repeat: 'Recurrence',
  time: 'Time',
  hangInTheAir: 'Incomplete',
  completed: 'Completed',
  noSearchResults: 'No results found',
  delete: 'Delete',
  addTo: 'Add',
  low: 'Low',
  in: 'Medium',
  high: 'High',
  urgent: 'Urgent',
  search: 'Search',
  confirmToDeletePending: 'Delete the task:',
  dateAndTime: 'Customized time',
  expiredPending: 'View overdue tasks here. Fortunately,  you have no overdue tasks so far.',
  noPointsForTheTimeBeing: 'No tasks have been assigned to you.',
  notYet: 'There are no completed tasks. Go to  "All" to view all your tasks.',
  markAsUrgent: 'View "Urgent" and  "High" priority tasks here.',
  viewHere:
    'View all your tasks here. Fortunately,  you have no tasks at the moment, so seize the day and enjoy!',
  noDataAvailable: 'No data',
  assignToMe: 'Assign to me',
  distribution: 'Assignment',
  viewDetails: 'View',
  replyTo: 'replied {val1}',
  deletedToDoList: 'deleted the task',
  addedAComment: 'commented',
  deletedComment: 'deleted the comment',
  changeStartTime: 'updated the start time to {val1}',
  changeDeadline: 'updated the deadline to {val1}',
  deletedStartTime: 'deleted the start time {val1}',
  deletedDueDate: 'deleted the deadline {val1}',
  addedStartTime: 'set the start time as {val1}',
  addedDeadline: 'set the deadline as {val1}',
  year: 'year',
  youCurrentlyDoNotHave:
    'Fortunately, you have no tasks at the moment, so seize the day and enjoy!',
  createAnd: 'The tasks due or created for today are displayed here. ',
  focusOnToday: 'Stay on track today',
  remind: 'Notify',
  hasBeenSent: 'Sent',
  sendOrNot: 'Send notification?',
  searching: 'Searching...',
  unableToMarkAll: 'Failed to mark this task as completed',
  exitEsc: 'Exit (Esc)',
  commentFailedPlease: 'Failed to comment, please try again later',
  deleteDeadline: 'Remove deadline',
  deletePriority: 'Remove priority',
  addUpToPeople: 'Add up to {val1} user(s)',
  operationFailedPlease: 'An error occurred, please try again  later',
  afterDeletingOneself: 'Once decline this task, you won’t be able to view or edit it any more',
  deleteToDoItems: '{val1} task(s) will be deleted',
  canOnlyDeleteMe_2: 'Only the {val1} task(s) you created will be deleted',
  canOnlyDeleteMe: 'Only the creator can delete this task',
  allocationFrom: 'From',
  close: 'Close',
  selectedWaitingItems: 'Selected {val1} task(s). You can process up to {val2} tasks at a time',
  toDoCannotBeDone: 'Failed to mark {val1} task(s) as completed',
  repeat_2: 'Recurrence {val1}',
  end: 'Due {val1}',
  personCompleted: 'Completed by {val1} user(s)',
  addDuplicates: 'Recurrence',
  addPriority: 'Prioritize',
  addDeadline: 'Set deadline',
  noPermissions: 'Access denied',
  toDoHasBeenDeleted: 'This task has been deleted',
  networkRequestLoss: 'Network request failed',
  theresNoMeAtTheMoment:
    "View the tasks you assigned to others here. However, you haven't assigned any task to others yet.",
  toDoList: 'Tasks',
  personCompleted_2: '{val1} assignees',
  allCompleted: 'Completed by all {val1} assignees',
  iHavePublishedAReview: '{val1}, et al. {val2} users commented',
  commented: '{val1} commented',
  open: 'Open',
  assignee: 'Assignees',
  valReminder_2: 'From {val1}, remind me {val2}',
  valReminder: 'Alert me at {val1}',
  whenSettingReminders: 'Set alert',
  changeToResponsible:
    'Changing the follower to the assignee will automatically release the follower status',
  createdInAConversation: 'From:',
  otherTimes: 'Another day',
  startDateSelection: 'Start Date (optional)',
  specificTime: 'Start Time',
  startDate: 'Start Date',
  theTaskHasBeenRestarted: 'Task reopened',
  confirmRestart: 'Confirm',
  afterRestartingTheTask:
    'After reopening, the task will revert to an incomplete status for all assignees. Do you confirm to reopen?',
  restartTask: 'Reopen Task',
  confirmCompletion: 'Confirm',
  andThePersonInCharge:
    'The task has not been completed by all assignees. Do you confirm to mark the task as completed?',
  restartEverything: 'Reopen Task for All',
  restartMyTask: 'Reopen Task for Me',
  allCompleted_2: 'All Completed',
  onlyIHaveCompletedIt: "I've Completed",
  completeTheTask: 'Task Completion',
  taskCompleted: 'Completed',
  focusOnTasks: 'Follow the task',
  followedBy: 'Followed',
  dontPayAttentionAnymore: 'Unfollow',
  confirmToCancelTheClosure:
    'You will no longer be able to view this task. Are you sure you want to unfollow it?',
  peopleFollow: 'user(s) followed',
  nonTaskResponsible:
    'Invite the mentioned users to join the task if they are not the responsible persons',
  cancelSelection: 'Unselect',
  markingCompleted: 'Marked as Completed',
  markingIncomplete: 'Mark as Incomplete',
  end_2: 'Due {val1}',
  notCurrentlySupported:
    'Setting the task owner/responsible person as a follower is temporarily not supported.',
  addFollowers: 'Add Follower(s)',
  anyoneCanFinishIt: 'When anyone completes',
  everyoneNeedsTo: 'When everyone completes',
  notSet: 'Not set',
  other: 'and {val1} more',
  executor: '{val1} assignee(s)',
  attachments: '{val1} attachment(s)',
  theCurrentViewDoesNot: 'Sorting is disabled in Schedule View',
  plan: 'Schedule view',
  list: 'List view',
  unplannedAppointment: 'Unscheduled tasks',
  collapseUnplanned: 'Fold unscheduled tasks',
  unplannedDeployment: 'Show unscheduled tasks',
  fieldConfiguration: 'Field display',
  showWeekends: 'Show weekend',
  allocation: 'Settings',
  viewLayoutWord: 'Display settings',
  month: 'Month',
  week: 'Week',
  addTitle: 'Title',
  clickToCompleteTheTask: 'Click to complete task',
  clickToRebuildTheTask: 'Click to reopen task',
  atTheBeginningOfTheSetting: 'Start time',
  setDuplicateRules: 'Set recurrence',
  startTime: 'Start time',
  cancelAddingTo: 'Remove from calendar',
  addToCalendar_2: 'Add to calendar',
  cancelledAdding: 'Removed from calendar',
  successfullyAdded: 'Added to calendar',
  addToCalendar: 'Add to calendar',
  clickToSynchronize: 'Click to add to calendar',
  addedToDay: 'Added to calendar',
  clickToCancelTheSame: 'Click to remove from calendar',
  putAwayTheDayAndWait: 'Fold',
  expandTheDayOfWaiting: 'Show',
  assignedBy_2: '(Owner)',
  viewPersonalInformation: 'View profile',
  enterTitleBack: 'Type anything and press Enter to convert it into a Task',
  viewNotes: 'View notes',
  viewAttachments: 'View attachments',
  share: 'Share',
  forwardTo: 'Forward task to',
  transferAssignor: 'Assign ownership',
  networkException: 'Network error',
  noRemarks: 'No notes',
  withRemarks: 'Notes included',
  remarks: 'Notes',
  noPriority: 'Not ranked',
  withPriority: 'Ranked',
  noDeadline: 'No deadline',
  thereIsADeadline: 'Deadline set',
  inTheFutureAndToday: 'Future (starting tomorrow)',
  tomorrow: 'Tomorrow',
  deferred: 'Overdue',
  createdToday: 'Today',
  creationTime: 'Created',
  showCompleted: 'Show completed tasks',
  minute: 'minute(s)',
  hour: 'hour(s)',
  day: 'day(s)',
  today: 'Today',
  checkInTheCalendar: 'Open in Calendar',
  needToBeDealtWith: 'Task',
  notLoggedIn: 'Log in to continue',
  assignedTo: 'Assigned to',
  title: 'Title',
  cancelToDo: 'Cancel ',
  newSuccessfully: 'Created',
  throughKeywords: 'Enter keyword to search',
  empty: 'Clear',
  screen: 'Filter',
  thereIsCurrentlyNoScreeningThatMeetsTheCriteria:
    'No tasks match the filtering criteria. Please try adjusting the filtering options',
  noTasksAvailableAtTheMoment: 'No tasks found',
  deleteTask: 'Delete Task',
  completedTasks: 'Completed tasks',
  unfinishedTasks: 'Incomplete tasks',
  noDeadlineSet: 'No deadline set',
  deadlineNextMonth: 'Due next month',
  deadlineNextWeek_2: 'Due next week',
  thisWeeksDeadline_2: 'Due this week',
  deadlineTomorrow: 'Due tomorrow',
  deadlineToday_2: 'Due today',
  cutOffBeforeToday: 'Due before today',
  addAsFollow: 'Add as a follower',
  humanCompletion: '{val1} users completed',
  viewComments: 'View comments',
  atTheBeginningOfAdding: 'Add start time',
  addPriority_2: 'Add priority',
  helpCenter: 'Help Center',
  welcomeToParticipateInTheProduction: 'Product Feedback Survey',
  iGotIt: 'Got it!',
  taskManagementAgain:
    'Task management upgraded again, Kanban view brings you a brand new experience!',
  kanbanView: 'Kanban View',
  immediateMedicalExamination: 'Get Started',
  afterFollowingTheTask:
    'You will receive progress reminders through the task assistant bot after following the task',
  realTimeMonitoringOfAnyTask: 'Stay updated on task progress',
  iHaveCompletedIt: "I've Completed",
  enterComment: 'Enter your comment',
  confirmTheTransferOfResponsibilities: 'Do you want to change the task owner?',
  transferAndExit: 'Change the task owner and add yourself as assignee',
  transferAndTransferFrom: 'Change the task owner and add yourself as a assignee',
  humanExecution: '{val1} assignees',
  confirmDeletion: 'Delete',
  closingDate: 'Due date',
  allExecutors: 'All assignees have completed the task',
  noExecutorYet: 'No assignees have completed the task yet',
  people: 'people',
  itIsAlreadyATaskExecution: 'You are the assignee and cannot be added as a follower',
  itIsAlreadyATaskCreation: 'You are the task owner and cannot be added as a follower',
  noEditingPermission: 'No editing permission',
  noGrouping: 'Ungrouped',
  thisWeeksDeadline: 'Due this week',
  addDeadline_2: 'Set due date',
  clickToFollowRen: 'Click to follow the task',
  cancelFollow: 'Unfollow',
  peopleFollow_2: '0 follower(s)',
  updatedCompleted_3: 'updated the completion requirement to {val1}',
  updatedCompleted_2: 'updated the completion requirement to: required to be completed by all',
  updatedCompleted: 'updated the completion requirement to: can be completed by one of them',
  restartedTheEntireSystem: 'reopened the whole task',
  iRestartedMyself: 'reopened task, the whole task has been reopened.',
  willValBe: 'Change {val1} from a follower to an assignee',
  cancelVal: '{val1} unfollowed the task',
  valFollow: '{val1} followed the task',
  removedTask: 'Removed task follower {val1}',
  addVal: 'Add {val1} as a task follower',
  completedTheTask_2: 'completed the task, the whole task has been completed.',
  completedTheTask: 'completed the task',
  completedTheEntireTask: 'The whole task has been completed.',
  notArranged: 'Not Scheduled',
  inTheFuture: 'Later',
  futureDays: 'Next 7 days',
  customSorting: 'Custom sorting',
  nextMonth: 'Next month',
  beforeToday: 'Before today',
  deadlineNextWeek: 'Due next week',
  deadlineToday: 'Within today',
  completionStatus: 'Completion status',
  iCreatedIt: 'Created by me',
  whatIAmConcernedAbout: 'Followed',
  allTasks: 'All Tasks',
  belongingProject: 'Project',
  personLiable: 'Assignees',
  assignPerson: 'Assigner',
  clickOnAscendingOrder: 'Switch to ascending',
  clickOnDescendingOrder: 'Switch to descending',
  deadline_2: 'Deadline',
  followPeople: 'Followers',
  taskName: 'Task Title',
  nothing: 'None',
  allocatedTo: 'Assign to',
  tabOperation: '',
  rename: 'Rename',
  addNewTab: 'Add tab',
  bulletinBoard: 'Kanban',
  myTask: 'My Tasks',
  shortcuts: 'Quick Access',
  task: 'Tasks',
  sort_2: 'Sorted by:',
  positiveSequence: 'ASC ',
  reverseOrder: 'DESC',
  hide: 'Hide',
  newTask: 'Create',
  grouping_2: 'Group',
  grouping: 'Group: {val1}',
  allFilteringItems: 'All Filters',
  quickFilterBar: 'Quick Filters',
  pleaseSearchFirstAndThen: 'Please enter a word to search',
  addConditions: 'Add Filters',
  fieldHiding: 'Hide Fields',
  time_2: 'Date',
  thePrevious: 'Back',
  projectManager_2:
    'Project admins can edit project icons, names, statuses, invite members, and share projects',
  quickProjectDesign: 'Quick Project Setup',
  screenBasedOnDemands:
    'Filter and arrange tasks based on your preferences. Project admins can save views as "Public Views," while project members can save them as "Personal Views"',
  personalizedTasks: 'Personalized Task Views ',
  projectManager:
    'Project admins can tailor unique fields for tasks using text, single-choice, multi-choice, user, date, and number options, guaranteeing that every task meets the specific requirements of the project.',
  addCustom: 'Add custom field',
  next: 'Next',
  notFoundIncludes: 'We couldn\'t find any results containing "{val1}". Try another keyword',
  startImmediately: 'Start your first project now!',
  dissolveTheProject: 'Close project',
  notSupportedForModification: 'Modifying field types is unsupported',
  noSearchResultsFound: 'No results found',
  afterDisbandingTheProject:
    'Upon the conclusion of the project, the associated tasks will be preserved; however, any custom fields linked with those tasks will not be retained. Please be advised that once a project is closed, it cannot be recovered.',
  dissolution: 'Close',
  atLeastInTheProject: 'Please ensure there is at least one public view in project',
  operationFailedPlease_2: 'Failed. Please ensure there is at least one administrator.',
  projectMembersHave: 'Limits of project member have been reached',
  addToNavigation: 'Add to navigation bar',
  removeFromNavigation: 'Remove from navigation bar',
  newlyBuiltProject: '[Project]{val1} has been created successfully ',
  establish: 'Create',
  allWithinTheProject:
    "Sync all project members' data into the group chat automatically. Confirm to create a group chat with the same project name?",
  createAGroupChat: 'Create group chat',
  addTo_2: 'Add {val1}',
  more_2: 'More ({val1})',
  managementView_2: 'Manage view',
  newView: 'Create view',
  saveView: 'Save view',
  saveViewLost: 'Failed to save the view. Please retry.',
  cover: 'Save',
  afterSavingItWillBeMade:
    'It will serve as the default display scope for the current view, updated in real-time for project members.',
  saveAsDefault: 'Save as default view',
  notFilledIn: 'Unfilled',
  thisItemCannotBe: 'Required field',
  option_4: 'Option {val1}',
  option_3: 'Option 2',
  option_2: 'Option 1',
  format: 'Format',
  includingTimeDivision: 'Include hours and minutes',
  option: 'Option',
  fieldType: 'Field format',
  enterFieldLabels: 'Enter field title',
  newField: 'Create field',
  modifiedSuccessfully: 'Saved',
  percentageIsSmall: 'Percentage (2 decimal places)',
  percentage: 'Percentage',
  retainDecimalPlaces_2: '2 decimal places',
  retainDecimalPlaces: '1 decimal place',
  integer: 'Integer',
  number: 'Number',
  date: 'Date',
  personnel: 'User',
  multipleChoice: 'Multiple choice',
  singleChoice: 'Single choice',
  text: 'Text',
  pleaseChoose: 'Select',
  moveFromProject: 'Remove from project',
  taskAfterDeletion: 'Upon removal, the content in the filled fields will be cleared as well ',
  deleteField: 'Remove the field "{val1}"',
  editFields: 'Edit fields',
  updateStatus: 'Update status',
  projectManagementOnly: 'Only project admin can modify',
  addToProject:
    'Task collaborators are automatically included in the project by default when tasks are added',
  calendar: 'Calendar',
  saveAsNewVision_2: 'Save As New View',
  saveAsNewVision: 'Failed to save as a new view',
  publicView_3:
    'Limits of public views and personal views have been reached; additional views require removal of existing ones.',
  thisViewIsFor: 'This is a personal view, visible only to you',
  thisTypeHasReached:
    'Limits of this view have been reached; additional views require removal of existing ones.',
  viewRange: 'Visibility',
  viewMode: 'Display Settings',
  enterViewName: 'Enter view name',
  viewName: 'View Name',
  saveViewAs: 'View saved successfully',
  allMembersAre: 'Visible to all members',
  publicView_2: 'Public view',
  onlyYouCanDoItYourself: 'Visible to you only',
  personalView: 'Personal view',
  deleteFailed: 'Failed to remove',
  deleted: 'Removed',
  thisViewIsForPublic: 'This is a public view, and only administrators can edit',
  thisViewIsForTheSystem: 'This is a system view',
  theTaskWillNotBeAffected: 'The task will not be deleted; only this view will be removed.',
  deleteView: 'Remove view',
  confirmToDeleteThis: 'Confirm to remove this view?',
  addView: 'Add view',
  managementView: 'Manage view ({val1})',
  noPermissionToAdd: 'No permission to add views',
  numberOfPersonalViews:
    'Limits of personal views have been reached; additional views require removal of existing ones.',
  publicView:
    'Limits of public views and personal views have been reached; additional views require removal of existing ones.',
  selectDate: 'Select a date',
  noOptionsAvailable: 'None',
  addPersonnel: 'Add member',
  itCanAlsoBeBasedOn: 'You can also apply filters based on various criteria, such as {val1}.',
  useCustom: 'Narrow down data with custom filters!',
  taskStatusCutoff: 'task status, deadline, assignee',
  reloadingInProgress: 'Reloading',
  chooseToCompleteOr: 'Please choose a method to complete or reopen the task',
  simultaneouslyCreateItems: 'Create project group chat concurrently',
  transformToSubtask: 'Convert to Subtask',
  searchTask: 'Task Name',
  searchNoResult: 'No results found',
  recent: 'Recent',
  setParentTask: 'Set Parent Task',
};
