import { store } from '@/models/store'
import { getAddItem } from '@/models/utils'
import { PPTimeFormat, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const'
import { EnumField, FinishedStatus } from '@/utils/fields'
import dayjs, { Dayjs } from 'dayjs'
import { memoize } from 'lodash'
import { GroupConfigFactory } from '../GroupConfigFactory'
import autoExpandData from '../AutoExpandData'
import {
  GroupConfigMap,
  GroupConfig,
  GroupOptions,
  GroupedItem,
  IGanttTaskInfo,
  IGanttTaskGroupInfo,
  GroupByField,
} from '../types'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'
import { expandNodeSetMap } from './timelineExpandManage'

// 创建时间比较函数
export const createTimeCompareFunc = (baseTime: Dayjs, targetTime: Dayjs, comparison: string) => {
  const compareStrategies = {
    before: (dayA: Dayjs) => dayA.isBefore(targetTime, 'day') || dayA.isSame(targetTime, 'day'),
    same: (dayA: Dayjs) => dayA.isSame(targetTime, 'day'),
    between: (dayA: Dayjs) => dayA.isBetween(baseTime.add(2, 'day'), baseTime.add(7, 'day'), 'day', '[]'),
    after: (dayA: Dayjs) => dayA.isAfter(baseTime.add(7, 'day'), 'day'),
  }

  return (val?: number) => {
    if (!val) return false
    const dayA = dayjs(val)
    return compareStrategies[comparison]?.(dayA) || false
  }
}

// 查找分组ID
export const findGroupId = (configsMap: GroupConfigMap, value: any): string | number | null => {
  for (const [groupId, config] of Object.entries(configsMap)) {
    if (config.compareFunc?.(value)) {
      return groupId
    }
  }
  return null
}

export const TASK_BASE_INFO = {
  assignees: [],
  priority: 0,
  startTime: 0,
  deadline: 0,
  rrule: '',
  completeCondition: 'all',
  followers: [],
  alarm: { alarmCreateTime: 0, alarmTimestamp: 0, alarmRrule: '' },
  title: '',
  customFieldValues: {
    values: {},
  },
}

// 获取添加行
export const getAddLine = ({
  index,
  id,
  ...rest
}: {
  index: string | number
  id: string | number
  [k: string]: any
}) => {
  return getAddItem({
    _rowType: TaskTableRowType.addBtn,
    id: `${TaskTableRowTypeAdd}-${index}-${id}`,
    taskId: `${TaskTableRowTypeAdd}-${index}-${id}`,
    ...TASK_BASE_INFO,
    ...rest,
  })
}

// 获取添加行的默认参数
export const getAddLineDefaultParams = (groupData: {
  firstConfig: GroupConfig
  secondConfig?: GroupConfig
}): Record<string, any> => {
  const { firstConfig, secondConfig } = groupData
  const queryGroupBys = store.getState().viewSetting.currentViewTab?.queryGroupBys || []
  const defaultParams: Record<string, any> = {}

  // 统一处理分组参数
  ;[firstConfig, secondConfig].filter(Boolean).forEach((config, index) => {
    const groupBy = queryGroupBys[index]
    if (!groupBy || !config) return

    if (groupBy.customFieldId) {
      const customField = store
        .getState()
        .viewSetting.customFields?.find(field => field.fieldId === groupBy.customFieldId)

      if (!defaultParams.customFieldValues) {
        defaultParams.customFieldValues = { values: {} }
      }

      defaultParams.customFieldValues.values[groupBy.customFieldId] = {
        fieldId: groupBy.customFieldId,
        fieldVersion: customField?.fieldVersion,
        value: config.value,
      }
    } else {
      // 处理内置字段
      const fieldHandlers = {
        [EnumField.assignee]: () => {
          defaultParams.assigneeUids = [...(defaultParams.assigneeUids || []), ...(config.value || [])]
          defaultParams.assignees = [...(defaultParams.assignees || []), ...(config.users || [])]
        },
        [EnumField.priority]: () => {
          defaultParams.priority = config.value
        },
        [EnumField.finished]: () => {
          // 完成状态都为未完成
          defaultParams.finished = FinishedStatus.UnFinished
        },
        [EnumField.deadline]: () => {
          if (config.value) {
            defaultParams.deadline = config.value
            defaultParams.deadlineFormat = PPTimeFormat.olayDay
          }
        },
      }

      fieldHandlers[groupBy.fieldName]?.()
    }
  })

  return defaultParams
}

// 排序分组配置
export const sortGroupConfigs = (configsMap: GroupConfigMap): GroupConfig[] => {
  return Object.values(configsMap).sort((a, b) => {
    if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
      if (typeof a.sortOrder === 'number' && typeof b.sortOrder === 'number') {
        return a.sortOrder - b.sortOrder
      }
      return String(a.sortOrder).localeCompare(String(b.sortOrder), 'en', {
        sensitivity: 'base', // 忽略大小写和重音符号
        ignorePunctuation: true, // 忽略标点符号
        numeric: true, // 数字按数值排序而非字符串
      })
    }
    if (a.sortOrder !== undefined) return -1
    if (b.sortOrder !== undefined) return 1
    return String(a.groupId).localeCompare(String(b.groupId), 'en', {
      sensitivity: 'base', // 忽略大小写和重音符号
      ignorePunctuation: true, // 忽略标点符号
      numeric: true, // 数字按数值排序而非字符串
    })
  })
}

// 获取分组层次状态
export const getGroupHierarchyState = (group: GroupConfig) => {
  const currView = store.getState().viewSetting.currentViewTab
  const queryGroupBys = currView?.queryGroupBys || []
  const viewId = currView.id || currView.viewId
  if (queryGroupBys.length > 1 && group.groupLevel < 1) {
    group.hierarchyState = 'expand'
    return
  }
  let expandNodeSet = expandNodeSetMap.getExpandNodeSet(viewId)
  if (expandNodeSet?.has(group.groupId!)) {
    group.hierarchyState = 'expand'
  } else {
    group.hierarchyState = 'collapse'
  }
}

// 构建分组结构
export const buildGroupedStructure = (
  firstConfigs: GroupConfigMap,
  secondConfigs: GroupConfigMap | null,
  options: GroupOptions = {} as GroupOptions,
): GroupedItem[] => {
  const {
    fieldsName = {},
    groupMap,
    groupStats,
    groupDuration,
    keepFirstEmpty,
    keepSecondEmpty,
    canCreateTask,
  } = options
  const { children = 'subtask' } = fieldsName
  const sortedFirstConfigs = sortGroupConfigs(firstConfigs)

  // 配置需要保留空分组的字段

  autoExpandData.clearExpandData()

  return sortedFirstConfigs
    .map((firstConfig, index) => {
      getGroupHierarchyState(firstConfig)

      const group: GroupedItem = {
        ...firstConfig,
        groupLevel: 0,
        [children]: [],
        totalCount: 0,
        finishedCount: 0,
        isGroup: true,
      }

      if (secondConfigs) {
        // 有二级分组，一级分组默认展开
        autoExpandData.setExpandKey(`${firstConfig.groupId}`, true)
        autoExpandData.setExpandIndex(index, true)
        const sortedSecondConfigs = sortGroupConfigs(secondConfigs)
        let totalCount = 0
        let finishedCount = 0

        // 过滤有数据的二级分组
        const validSecondGroups = sortedSecondConfigs
          .map((secondConfig, i) => {
            getGroupHierarchyState(secondConfig)
            const index = `${firstConfig.groupId}-${secondConfig.groupId}`
            const subs = groupMap.get(index) || []
            const stats = groupStats.get(index) || { totalCount: 0, finishedCount: 0 }

            // 只有当存在数据或需要保留空分组时才包含此分组
            if (!keepFirstEmpty && !keepSecondEmpty && subs.length === 0 && stats.totalCount === 0) {
              return null
            }
            let subsLine = [...subs]
            if (canCreateTask) {
              const defaultParams = getAddLineDefaultParams({ firstConfig, secondConfig })
              const addLine = getAddLine({
                index,
                id: `${index}-${subs?.length || 0}`,
                groupedIds: [firstConfig.groupId, secondConfig.groupId],
                ...defaultParams,
              })

              // const subsWithAddLine = [...subs, addLine]
              subsLine.push(addLine)
            }
            totalCount += stats.totalCount
            finishedCount += stats.finishedCount

            const duration = groupDuration.get(index)
            return {
              ...secondConfig,
              $startTime: duration?.$startTime,
              $deadline: duration?.$deadline,
              groupLevel: 1,
              isGroup: true,
              [children]: subsLine,
              totalCount: stats.totalCount,
              finishedCount: stats.finishedCount,
            }
          })
          .filter(Boolean) // 过滤掉 null 值

        // 检查一级分组是否应该保留空分组

        // 如果二级分组全部为空且一级分组不需要保留空分组，则此一级分组也不显示
        if (!keepFirstEmpty && !keepSecondEmpty && validSecondGroups.length === 0) {
          return null
        }

        group[children] = validSecondGroups
        group.totalCount = totalCount
        group.finishedCount = finishedCount
        const duration = groupDuration.get(`${firstConfig.groupId}`)
        group.$startTime = duration?.$startTime
        group.$deadline = duration?.$deadline
      } else {
        const subs = groupMap.get(`${firstConfig.groupId}`) || []
        const stats = groupStats.get(`${firstConfig.groupId}`) || { totalCount: 0, finishedCount: 0 }

        // 只有当存在数据或需要保留空分组时才包含此分组
        if (subs.length === 0 && stats.totalCount === 0 && !keepFirstEmpty) {
          return null
        }
        if (canCreateTask) {
          const defaultParams = getAddLineDefaultParams({ firstConfig })
          const addLine = getAddLine({
            index: firstConfig.groupId,
            id: firstConfig.groupId,
            groupedIds: [firstConfig.groupId],
            ...defaultParams,
          })
          group[children] = [...subs, addLine]
        } else {
          group[children] = [...subs]
        }

        const duration = groupDuration.get(`${firstConfig.groupId}`)
        group.$startTime = duration?.$startTime
        group.$deadline = duration?.$deadline

        group.totalCount = stats.totalCount
        group.finishedCount = stats.finishedCount
      }

      return group
    })
    .filter(Boolean) // 过滤掉 null 值
}

/**
 * 判断是否应该保留空分组
 * @param config 分组配置
 * @param keepEmptyFields 需要保留空分组的字段集合
 * @returns 是否保留空分组
 */
export function shouldKeepEmptyGroup(config: GroupByField, keepEmptyFields: Set<string>): boolean {
  return keepEmptyFields.has(config?.customFieldId || config?.fieldName || '')
}

// 根据字段获取分组配置
export const getGroupConfigsByField = memoize((fieldName: string, groupLevel: number): GroupConfigMap => {
  const configCreators = {
    [EnumField.deadline]: () => GroupConfigFactory.createTimeConfigs(dayjs(), fieldName, groupLevel),
    [EnumField.priority]: () => GroupConfigFactory.createPriorityConfigs(fieldName, groupLevel),
    [EnumField.finished]: () => GroupConfigFactory.createFinishedConfigs(fieldName, groupLevel),
    [EnumField.assignee]: () => ({}),
  }

  return configCreators[fieldName]?.() || {}
})

// 标准化时间戳
export const normalizeTimestamp = (r: IGanttTaskInfo) => {
  if (r.startTime && r.deadline) {
    r.$startTime = r.startTime
    r.$deadline = r.deadline
  } else if (r.startTime && !r.deadline) {
    r.$startTime = r.startTime
    r.$deadline = r.startTime
  } else if (!r.startTime && r.deadline) {
    r.$startTime = r.deadline
    r.$deadline = r.deadline
  }
  if (r.isParent) {
    r.children = (r.subtask as IGanttTaskGroupInfo[]).map(s => normalizeTimestamp(s as IGanttTaskInfo))
  }
  return r
}
