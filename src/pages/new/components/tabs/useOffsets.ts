import { useMemo } from 'react';

import { Tab, TabOffsetMap, TabSizeMap } from './interface';

export default function useOffsets(tabs: Tab[], tabSizes: TabSizeMap) {
  return useMemo(() => {
    const map: TabOffsetMap = new Map();
    let _left = 0;

    for (let index = 0; index < tabs.length; index++) {
      const tabEl = tabSizes.get(tabs[index].id);
      if (tabEl) {
        map.set(tabs[index].id, {
          width: tabEl?.width,
          height: tabEl.height,
          left: _left,
          top: tabEl.top,
        });
        _left = _left + tabEl.left;
      }
    }
    return map;
  }, [tabs, tabSizes]);
}
