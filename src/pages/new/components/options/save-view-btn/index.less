.overlay__content {
  color: var(--TextPrimary);
  font-size: 13px;
  line-height: 20px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;

  &:hover {
    background-color: var(--aBlack6);
  }
}
.saveBtnWrapper {
  display: flex;
  align-items: center;
  cursor: pointer;

  &::before {
    content: '';
    width: 1px;
    height: 14px;
    background-color: var(--aBlack12);
    margin-right: 8px;
  }

  .save__btn {
    color: var(--Brand600);
    font-size: 13px;
    height: 28px;
    border-radius: 6px;
    overflow: hidden;
    white-space: nowrap;

    &--left,
    &--right {
      height: 100%;
      transition: background-color 0.2s ease;
      &:hover {
        background-color: var(--aBrand16) !important;
      }
      &:active {
        background-color: var(--aBrand20) !important;
      }
    }

    &--left {
      padding: 6px 2px 6px 8px;
      column-gap: 4px;
    }

    &--right {
      padding: 6px 8px 6px 2px;

      :global {
        .rock-dropdown-down-arrow-icon {
          transition: all 0.2s ease-in-out;
          margin-left: 0;
        }
      }

      &:global(.rock-dropdown-open .rock-dropdown-down-arrow-icon) {
        transform: rotate(180deg);
      }
    }

    &:hover {
      .save__btn--left,
      .save__btn--right {
        background-color: var(--aBrand8);
      }
    }
  }
}

.save__view--menu {
  background-color: var(--bgTop);
  border-radius: 8px !important;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.12);
  min-width: 110px !important;

  :global {
    .rock-dropdown-menu-item {
      color: var(--TextPrimary);
      font-size: 13px;
      line-height: 20px;
      padding: 8px;
      border-radius: 6px;

      &:hover {
        background-color: var(--aBlack6);
      }
    }
  }
}
