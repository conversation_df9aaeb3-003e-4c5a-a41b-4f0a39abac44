.commentBox {
  position: relative;
  margin: 3px 0;

  :global {
    .popo-qiull-editor > p {
      margin: 0;
      line-height: 18px;
      font-size: 14px;
    }
    .ql-popo-mention-myself {
      display: inline-flex;
      align-items: center;
      height: 18px;
      line-height: 18px;
      padding: 0 8px 0 7px;
      background-color: var(--Brand600);
      border-radius: 20px;
      color: var(--absWhite);
      font-weight: 500;
      font-size: 12px;
    }
    .popo-qiull-editor {
      .prosemirror-emoji {
        display: inline-block;
        width: 22px;
        height: 22px;
        line-height: 22px;
        vertical-align: bottom;
      }
      .prosemirror-mention {
        color: var(--Brand600);
      }
      .editor-popo-doc-span {
        display: inline;
        text-decoration-line: none;
        color: var(--Brand600);
        .editor-popo-doc-img {
          margin: 0 3px;
          width: 16px;
          height: 16px;
          vertical-align: sub;
        }
      }
      img {
        max-width: 390px;
        height: auto;
      }
      .prosemirror-mention-me {
        display: inline-flex;
        align-items: center;
        height: 18px;
        line-height: 18px;
        padding: 0 8px 0 7px;
        background-color: var(--Brand600);
        border-radius: 20px;
        color: var(--absWhite);
        font-weight: 500;
        font-size: 12px;
      }
      .prosemirror-popo-doc {
        cursor: pointer;
      }
    }
    .rock-editor-ol,
    .rock-editor-ul {
      margin: 0 auto;
      padding: 0;
      p {
        margin: 0 auto;
      }
      li {
        display: flex;
        list-style-type: none;
        line-height: 22px;
        ::marker {
          display: none;
          content: '';
        }
        .ProseMirror-li-placeholder {
          position: relative;
          display: inline-block;
          flex-shrink: 0;
          height: 22px;
          font-size: var(--r-editor-content-font-size);
          border-radius: 0.02rem;
          &::before {
            content: '\2022';
            padding: 0 5px;
          }
        }
      }
    }
    strong {
      font-weight: 600;
    }
  }
}
.commentHead {
  position: relative;
  display: flex;
  .commentHeadLeft {
    flex: 1;
  }
  .commentHeadRight {
    position: absolute;
    right: 0;
    top: -23px;
    display: flex;
    user-select: none;
    -webkit-user-select: none;
    // padding-top: 3px;
    & * {
      user-select: auto !important;
      -webkit-user-select: auto !important;
    }
    .icon {
      width: 22px;
      height: 22px;
      font-size: 16px;
      color: var(--IconPrimary);
      cursor: pointer;
    }
  }
}
.comment {
  // display: flex;
  font-size: 14px;
  // margin: 4px 0px;
  line-height: 22px;
  white-space: pre-line;
  color: var(--TextPrimary);
  word-break: break-all;
}
