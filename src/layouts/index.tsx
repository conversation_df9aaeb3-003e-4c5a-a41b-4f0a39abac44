import 'dayjs/locale/zh-cn'
import 'dayjs/locale/ja'

import { ConfigProvider, Message } from '@bedrock/components'
import { pp } from '@popo-bridge/web'
import { useLatest, useMemoizedFn } from 'ahooks'
import classNames from 'classnames'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'
import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Outlet } from 'umi'

import { apiEnvGet } from '@/api'
import { Dispatch, RootState, store } from '@/models/store'
import { getIsInSession, getPageIsProjectGroup, isProjectId } from '@/models/utils'
// import { getUrlParams } from '@/utils';
import { getUrlParams, setStorage, StorageType } from '@/utils'
import { onPageVisibleChange } from '@/utils'
import { checkAppVersion } from '@/utils/autoUpdate'
import { bridgeGetRecentContactsList } from '@/utils/bridge'
import Const, { FontSizeFactorMap, FontSizeFactorType, TaskNavigatorType, ViewType } from '@/utils/const'
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission'
import { CorlorMode, Platform } from '@/utils/platform'
import { EnumTrackeKey } from '@/utils/skyline'

import styles from './index.less'
import { useRegisterMouseEventEnableBridge } from '@/hooks/useRegisterMouseEventEnableBridge'
import I18N from '@/utils/I18N'

const timezone = require('dayjs/plugin/timezone')
dayjs.extend(timezone)
const calendar = require('dayjs/plugin/calendar')
dayjs.extend(calendar)
const updateLocale = require('dayjs/plugin/updateLocale')
dayjs.extend(updateLocale)
//@ts-ignore
dayjs.updateLocale('zh-cn', {
  //@i18n-ignore
  weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
})
const advancedFormat = require('dayjs/plugin/advancedFormat')
dayjs.extend(advancedFormat)
const localizedFormat = require('dayjs/plugin/localizedFormat')
dayjs.extend(localizedFormat)
const weekday = require('dayjs/plugin/weekday')
dayjs.extend(weekday)
const isBetween = require('dayjs/plugin/isBetween')
dayjs.extend(isBetween)

enum AppThemeEnum {
  light = 0,
  dark = 1,
}

const Layout: React.FC<any> = () => {
  const { navigatorId } = useSelector((state: RootState) => ({
    navigatorId: state.viewSetting.navigatorId,
  }))
  const dispatch = useDispatch<Dispatch>()
  const [hasLogin, setHasLogin] = useState<boolean>(false)
  const [locale, setLocale] = useState<string>('')
  const checkDetailRef = useRef<() => void>(() => {})
  const latestNavigatorId = useLatest(navigatorId)

  // 设置字体ZOOM比例
  // const setFontSizeFactor = (fontSizeFactor: FontSizeFactorType) => {
  //   //@ts-ignore
  //   const size = FontSizeFactorMap[fontSizeFactor] || 1;
  //   document.documentElement.style.fontSize = `${size * 100}px`;
  // };
  const init = () => {
    window.POPOTaskTrackTimeMap = {
      ...(window.POPOTaskTrackTimeMap || {}),
      layout: Date.now(), //进入第一个Script时间
    }

    const language = Platform.getLanguage() //getLanguage
    console.log('pp-getLanguage', language)
    dispatch.user.setLanguage(language)
    setLocale(language)
    pp.getAppTheme().then(data => {
      console.log('pp-getAppTheme', data)
      dispatch.user.setCorlorMode(data === AppThemeEnum.dark ? CorlorMode.dark : CorlorMode.light)
    })
    console.log('Initializing languages and themes')
    const fontSizeFactor = Platform.getAppFontSizeFactor() //fontSizeFactor
    console.log('Platform.getAppFontSizeFactor', fontSizeFactor)
    // setFontSizeFactor(fontSizeFactor);
    // pp.getPlatformInfo().then((res) => {
    //   if (Number(fontSizeFactor) !== Number(res.fontSizeFactor)) {
    //     setFontSizeFactor(res.fontSizeFactor);
    //   }
    // });
  }
  checkDetailRef.current = () => {
    const { taskId, visibleDetail } = store.getState().detail
    if (taskId && visibleDetail) {
      dispatch.detail.getTodoDetail(taskId).catch(() => {
        dispatch.detail.closeDetail({})
      })
    }
  }
  /**
   * 获取最近联系人
   */
  const getRecentContactsList = async () => {
    const list = await bridgeGetRecentContactsList({
      maxCount: 30,
    })
    dispatch.user.setData({
      recentContactsList: list,
    })
  }

  useRegisterMouseEventEnableBridge()

  useEffect(() => {
    //埋点: 进入任务的次数
    init()
    pp.onLanguageChange(value => {
      console.log('-----onLanguageChange-----', value)
      dispatch.user.setLanguage(value)
      setLocale(value)
      console.log('onLanguageChang--reload')
      window.location.reload()
    })
    //@ts-ignore
    pp.onAppThemeChange((value: CorlorMode) => {
      console.log('-----onAppThemeChange-----', value)
      dispatch.user.setCorlorMode(value)
    })

    // pp.onFontSizeFactorChange((value: { fontSizeFactor: FontSizeFactorType }) => {
    //   console.log('-----onFontSizeFactorChange-----', value.fontSizeFactor);
    //   setFontSizeFactor(value.fontSizeFactor);
    // });
    //注册通知 客户端要求必须先注册 才会发送
    pp.registerTodoNotification()
  }, [])
  const checkGrayscale = async () => {
    const data = await apiEnvGet()
    //约定 cookies grayscale_tag
    const grayscaleTag = Cookies.get('grayscale_tag')
    console.log('isGrayUser---', data.isGrayUser, grayscaleTag?.includes('grayscale'))
    if (data.isGrayUser && !grayscaleTag?.includes('grayscale')) {
      Cookies.set('grayscale_tag', 'grayscale')
      console.log('add Cookies')
      checkAppVersion(true)
    } else if (!data.isGrayUser && grayscaleTag?.includes('grayscale')) {
      Cookies.remove('grayscale_tag')
      console.log('remove Cookies')
      checkAppVersion(true)
    }
  }
  useEffect(() => {
    new Promise<void>(resolve => {
      window.POPOTaskTrackTimeMap = {
        ...(window.POPOTaskTrackTimeMap || {}),
        beforeLogin: Date.now(), //开始登录验证时间
      }
      dispatch.user.login().then(() => {
        dispatch.user.getUser().then(async () => {
          window.POPOTaskTrackTimeMap = {
            ...(window.POPOTaskTrackTimeMap || {}),
            loginSuccess: Date.now(), //获取用户信息时间
          }
          setHasLogin(true)
          getRecentContactsList()
          checkGrayscale()
          dispatch.user.tracking({ key: EnumTrackeKey.TaskApp })
        })
        resolve()
      })
    }).then(() => {
      // popo内切换tab的时候 显示当前tab的时候进行一次数据刷新,值刷左侧预制快捷标签和列表数据
      onPageVisibleChange(async pageShow => {
        if (pageShow) {
          console.log('-----onPageVisibleChange-----', pageShow)
          //埋点: 进入任务的次数
          setStorage(Const.RetryLoginTimes, 0)
          dispatch.user.login().then(async () => {
            dispatch.user.getUser().then(async () => {
              dispatch.user.tracking({ key: EnumTrackeKey.TaskApp })
              setHasLogin(true)
              console.log('-----onPageVisibleChange---checkGrayscale')
              await checkGrayscale()
              //更新最近联系人
              getRecentContactsList()
              // 不是会话中打开，刷新项目ping列表
              if (!getIsInSession()) {
                //刷新ping列表
                dispatch.project.getPingProjectList()
              }
              //获取当前是否有切换tab携带的数据
              let params
              if (Platform.isPOPO) {
                try {
                  const data = (await pp.getNativeTabPageParams()) as {
                    params: any
                  }
                  params = data.params
                  console.log('-----onPageVisibleChange---getNativeTabPageParams', data)
                } catch (error) {
                  console.log('无其他参数')
                }
              }
              if (params) {
                // 消息卡片解析参数, 与项目的消息卡片调整约定具体参数
                const { action } = params || {}
                if (action === 'view_project' && params?.data?.projectId) {
                  dispatch.viewSetting.openNavigator({
                    navigatorId: Number(params.data.projectId),
                    taskId: params?.data?.taskId,
                  })
                }
              } else {
                if (getPageIsProjectGroup()) {
                  return
                }
                // 如果当前是项目 判断是否还有权限
                if (isProjectId(latestNavigatorId.current)) {
                  const permissions = await dispatch.viewSetting.updatePermissions({})
                  const [CAN_VIEW] = validatesPermission({
                    permissions,
                    key: [ProjectPermissionEnum.CAN_VIEW],
                  }) as boolean[]
                  if (!CAN_VIEW) {
                    // 会话中若无权限，不经心跳转
                    if (getIsInSession()) {
                      Message.error(I18N.auto.noPermissions)
                    } else {
                      dispatch.viewSetting.openNavigator({
                        navigatorId: TaskNavigatorType.assignToMe,
                      })
                    }
                    return
                  }
                }
                //刷新全部任务数量
                dispatch.viewSetting.refreshDataByDataChange({
                  refreshCount: true,
                  refreshList: true, // TODO 暂时不刷新列表数据
                })
              }
            })
          })
        }
      })
    })
  }, [])

  const windowBlurListener = useMemoizedFn(e => {
    const target = e.target

    if (target === window) {
      const popoverOpenEls = document.querySelectorAll('.rock-popover-open')
      const dropdownOpenEls = document.querySelectorAll('.rock-dropdown-open')

      const tableWrapper = document.querySelector('#tanstack-table__wrapper_id')
      const calendarList = document.querySelector('.calendarList')
      const detailBody = document.querySelector('#detail_body')

      const safetyAreas = [detailBody, tableWrapper, calendarList]

      Array.from(popoverOpenEls).forEach((el: HTMLElement) => {
        if (safetyAreas.some(item => item?.contains(el))) {
          return
        }
        el?.click?.()
      })
      Array.from(dropdownOpenEls).forEach((el: HTMLElement) => {
        if (safetyAreas.some(item => item?.contains(el))) {
          return
        }
        el?.click?.()
      })
    }
  })

  useEffect(() => {
    const listener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        dispatch.batch.setBatchMode(false)
      }
      // 监听删除键，阻止默认事件: mac 上删除键会触发history back
      if (e.key === 'Backspace') {
        // 获取当前焦点元素
        const activeElement = document.activeElement as HTMLElement

        // 检查当前焦点元素是否是可编辑的表单元素
        const isFormElement =
          activeElement instanceof HTMLInputElement ||
          activeElement instanceof HTMLTextAreaElement ||
          activeElement instanceof HTMLSelectElement ||
          activeElement?.isContentEditable

        // 特殊检查：富文本编辑器通常有contentEditable属性
        const isEditable = activeElement?.hasAttribute('contenteditable') || activeElement?.isContentEditable

        // 如果不是表单元素或可编辑元素，则阻止默认行为
        if (!isFormElement && !isEditable) {
          e.preventDefault()
          // 可选：e.stopPropagation();
        }
      }
      // if (e.key === 'a' && (e.ctrlKey || e.metaKey)) {
      //   e.preventDefault();
      // }
    }
    document.addEventListener('keydown', listener)
    window.addEventListener('blur', windowBlurListener)
    return () => {
      document.removeEventListener('keydown', listener)
      window.removeEventListener('blur', windowBlurListener)
    }
  }, [])
  return (
    <ConfigProvider locale={locale}>
      <div key={locale} className={classNames(styles.tobedone, 'tobedone__root', 'tobedone-custom-light')}>
        {hasLogin ? <Outlet></Outlet> : null}
      </div>
    </ConfigProvider>
  )
}

export default Layout
