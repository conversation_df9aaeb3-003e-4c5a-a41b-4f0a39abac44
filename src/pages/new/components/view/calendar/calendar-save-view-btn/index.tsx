import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/models/store';
import { isTaskMenuId } from '@/models/utils';
import {
  ProjectPermissionEnum,
  validatesPermission,
  ViewPermissionEnum,
  ViewTypeEnum,
} from '@/utils/permission';

import SaveViewBtn from '../../../options/save-view-btn';
export const CalendarSaveViewBtn = () => {
  const { currentViewTab, navigatorId, permissions } = useSelector((state: RootState) => ({
    currentViewTab: state.viewSetting.currentViewTab,
    navigatorId: state.viewSetting.navigatorId,
    permissions: state.viewSetting.permissions,
  }));

  const isProjectTab = !isTaskMenuId(navigatorId as number);

  const [canEditView] = useMemo(() => {
    const isCommonView = currentViewTab.viewDistribution !== ViewTypeEnum.Personal;
    const canEditView = validatesPermission({
      permissions: currentViewTab.permissions,
      key: isCommonView
        ? ViewPermissionEnum.CAN_EDIT_COMMON_VIEW
        : ViewPermissionEnum.CAN_EDIT_PERSON_VIEW,
    });
    return [canEditView];
  }, [currentViewTab]);

  const [canCreateCommonView, canCreatePersonView] = validatesPermission({
    permissions,
    key: [
      ProjectPermissionEnum.CAN_CREATE_COMMON_VIEW,
      ProjectPermissionEnum.CAN_CREATE_PERSON_VIEW,
    ],
  }) as boolean[];

  const showSaveNewBtn = useMemo(() => {
    return currentViewTab.changeSearchParams && (canCreateCommonView || canCreatePersonView);
  }, [currentViewTab, canCreateCommonView, canCreatePersonView]);

  return (
    <SaveViewBtn
      showSaveNewBtn={showSaveNewBtn}
      isProjectTab={isProjectTab}
      canEditView={canEditView}
    />
  );
};
