import { useClickAway } from 'ahooks';
import React, { useContext, useMemo, useRef } from 'react';

import { Drawer } from '@/components/basic';
import Detail from '@/components/detail';
import { DrawerClickOrignKey } from '@/utils/const';

// import skyline from '@/utils/skyline';
import s from './index.less';
import { ConfigProvider } from '@bedrock/components';
export type Props = {
  className?: string;
  taskId?: number;
  visible: boolean;
  onClose: () => void;
  afterVisibleChange?: (visible: boolean) => void;
};

const DetailDrawer: React.FC<Props> = (props) => {
  const { className, taskId, visible, onClose, afterVisibleChange } = props;
  const ref = useRef<any>(null);
  // useEffect(() => {
  //   if (visible) {
  //     skyline.send('VIEW', { path: 'DETAIL' });
  //   }
  // }, []);

  // useEffect(() => {
  //   function onClick(e: any) {
  //     let clickOrign = (e as any)[DrawerClickOrignKey];
  //     // console.log(clickOrign);
  //     if (visible && !clickOrign) {
  //       //onClose();
  //     }
  //   }
  //   document.documentElement.addEventListener('click', onClick);
  //   return () => {
  //     document.documentElement.removeEventListener('click', onClick);
  //   };
  // }, [visible, onClose]);
  useClickAway(
    (e) => {
      if (visible) {
        // 在详情拖拽选中之后 点击详情意外 target的元素是body,其他点击事件不是
        if (e.target === document.body) {
          return;
        }
        if ((e as any)[DrawerClickOrignKey]) {
          return;
        }
        // 详情中的子任务删除，不关闭详情
        if (window.detailClosePending) {
          return;
        }
        onClose();
      }
    },
    ref,
    ['click']
  );
  const { getGlobalZIndex } = useContext(ConfigProvider.ConfigContext);

  const zIndex = useMemo(() => getGlobalZIndex(), [visible]);

  return (
    <div ref={ref}>
      <Drawer
        className={s.detailDrawer}
        visible={visible}
        width="4.8rem"
        //destroyOnClose
        // onClose={() => {}} showMask设置后失效了
        // zIndex={1000}
        keyboard={false}
        showMask={false}
        zIndex={Math.max(3000, zIndex)}
        afterVisibleChange={afterVisibleChange}
      >
        <Detail taskId={taskId} onClose={onClose}></Detail>
      </Drawer>
    </div>
  );
};

export default DetailDrawer;
