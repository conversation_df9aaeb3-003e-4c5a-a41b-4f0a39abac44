import { ChevronDown, ChevronUp } from '@bedrock/icons-react';
import classNames from 'classnames';
import React, { PropsWithChildren } from 'react';

import { Dropdown, Placeholder, Tooltip } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';
import { ProjectInfo } from '@/types';

import s from './index.less';
import Overlay from './overlay';
import ProjectTag from './tag-item';

interface Props {
  disabled?: boolean;
  className?: string;
  value?: ProjectInfo;
  onChange?: (v: ProjectInfo) => void;
  placeholder?: React.ReactNode;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  showArrow?: boolean;
  getPopupContainer?: () => HTMLElement;
}

const ProjectPicker: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    disabled,
    className,
    value,
    onChange,
    placeholder,
    visible,
    onVisible,
    showArrow = false,
    getPopupContainer,
  } = props;
  return (
    <Dropdown
      className={classNames(s.projectSelectDropdown, className)}
      title={
        value?.projectId ? (
          <Tooltip title={value.name} onlyEllipsis>
            <div className={`${s.project} ${showArrow ? s.showArrow : ''}`}>
              <ProjectTag item={value}></ProjectTag>
            </div>
          </Tooltip>
        ) : (
          <Placeholder text={placeholder}></Placeholder>
        )
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      disabled={disabled}
      open={visible}
      onOpenChange={(v) => {
        onVisible?.(v);
      }}
      destroyPopupOnHide={false}
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Overlay
          onChange={(v) => {
            onChange?.(v);
            onVisible?.(false);
          }}
        ></Overlay>
      }
    />
  );
};

export default ProjectPicker;
