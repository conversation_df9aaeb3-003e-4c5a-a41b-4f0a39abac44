import { PropsWithChildren, useState } from 'react';

import { Dropdown, IconProject, Tooltip } from '@/components/basic';
import I18N from '@/utils/I18N';

import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
  iconName?: string;
  iconBgColor?: string;
  onChangeIcon?: (v: string) => void;
  onChangeColor?: (v: string) => void;
  fontSize?: number;
  overlayClassName?: string;
}

const IconSelect: React.FC<PropsWithChildren<Props>> = (props) => {
  const {
    children,
    disabled,
    className,
    iconName,
    iconBgColor,
    onChangeIcon,
    onChangeColor,
    fontSize,
    overlayClassName,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  return (
    <Dropdown
      className={s.dropdown}
      overlayClassName={overlayClassName}
      title={
        <Tooltip title={disabled ? '' : I18N.auto.changeIcon}>
          <div>
            <IconProject
              fontSize={fontSize}
              name={iconName}
              bgColor={iconBgColor}
              active
              className={className}
            ></IconProject>
          </div>
        </Tooltip>
      }
      trigger="click"
      arrow={false}
      defaultOpen={visible}
      disabled={disabled}
      open={visible}
      onOpenChange={(v) => {
        setVisible(v);
      }}
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlay={
        <Overlay
          iconName={iconName}
          iconBgColor={iconBgColor}
          onChangeIcon={onChangeIcon}
          onChangeColor={onChangeColor}
        ></Overlay>
      }
    />
  );
};

export default IconSelect;
