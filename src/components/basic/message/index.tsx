import { Message as BasicMessage } from '@bedrock/components';
import { NoticeParam } from '@bedrock/components/es/Message/Message';

import s from './index.less';

type MessageType = 'success' | 'error' | 'warn' | 'info' | 'loading' | 'text';

function createToast(type: MessageType) {
  return function toast(
    config: NoticeParam,
    notificationOptions?: { zIndex: number; getContainer: () => HTMLElement }
  ) {
    let params: NoticeParam;
    if (typeof config === 'string') {
      params = {
        content: config,
        duration: 2,
      };
    } else {
      params = { duration: 2, ...config };
    }
    return BasicMessage[type](params, {
      maxCount: 1,
      className: s.message,
      ...notificationOptions,
    });
  };
}

export default {
  info: createToast('info'),
  loading: createToast('loading'),
  success: createToast('success'),
  error: createToast('error'),
  warn: createToast('warn'),
  text: createToast('text'),
};
