import React from 'react';
import { useDispatch } from 'react-redux';

import { apiTodoUpdateRemarkPost } from '@/api';
import ContentEditableEditor from '@/components/basic/content-editable-editor';
import { Dispatch } from '@/models/store';
import I18N from '@/utils/I18N';

import s from './index.less';
import { EditorContainer } from '@/components/editor';

export type Props = {
  className?: string;
  value?: string;
  placeholder?: string;
  disabled?: boolean;
  taskId: number;
  onEditStateChange: (v: boolean) => void;
  hasMultMsgContent?: boolean; // TODO 讲这个判断提到上一层组件
};

const RemarkItem: React.FC<Props> = (props) => {
  const { taskId, value = '', placeholder, disabled, hasMultMsgContent, onEditStateChange } = props;
  const dispatch = useDispatch<Dispatch>();

  return (
    <div className={`${s.remark__content} remark__content`}>
      {/* <ContentEditableEditor
        id="todo-remark-input-editor"
        contentClassName={s.remark}
        value={value}
        onChange={onChange}
        placeholder={disabled ? (hasMultMsgContent ? '' : I18N.auto.nothing) : placeholder}
        enterCombination={['ctrlKey', 'metaKey']}
        //maxLength={10}
        maxLength={2000}
        disabled={disabled}
        onEditStateChange={onEditStateChange}
        normalTextArea
        editable={!disabled}
      /> */}
      <EditorContainer
        type="remark"
        taskId={taskId}
        value={value}
        disabled={disabled}
        placeholder={disabled ? (hasMultMsgContent ? '' : I18N.auto.nothing) : placeholder}
      />
    </div>
  );
};

export default RemarkItem;
