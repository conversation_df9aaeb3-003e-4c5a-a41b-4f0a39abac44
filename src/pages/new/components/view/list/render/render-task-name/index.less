.operate {
  // margin-top: 4px;
  // padding-right: 6px !important;
  &:hover {
    .checkbox {
      display: block;
      // transition: all 0.3s;
    }
  }
}

.listTitleEditorWrapper {
  display: flex;
  //flex: 1;
  min-width: 0;
  &.fullWidth {
    flex: 1;
  }

  &.small {
    --TitleHeight: 24px;
    --TitleMargin: 0;
    --TitleLineHeight: 18px;
    --TitlePadding: 0 7px;
  }
}
.listTitleEditor {
  height: var(--TitleHeight, 36px);
  width: 100%;

  &.fullWidth {
    width: 100%;
  }

  &:hover {
    :global(#todo-title-input-editor) {
      background-color: var(--bgTop);
      border-radius: 4px;
      border: 1px solid var(--aBlack10) !important;
      &:hover {
        border-color: var(--aBlack24) !important;
      }
    }
  }
  :global {
    .com-placeholder {
      padding-left: 8px;
    }
    .rock-input-wrapper {
      &::before {
        opacity: 0;
      }
    }
    .contentEditableEditor-edit {
      padding: 6px 7px !important;
      border-radius: 0;
    }
    .editor-content-editable-wrap {
      height: fit-content;
      display: inline-flex;
      width: 100%;
      position: relative;
      //background-color: var(--bgTop);
    }
    .editor-content-editable-wrap-editding {
      z-index: 999 !important;
    }
  }
}

.listTitleContent {
  width: 100%;
  max-width: unset !important;
  padding: var(--TitlePadding, 2px 7px);
  margin-top: var(--TitleMargin, 5px);
  margin-bottom: var(--TitleMargin, 5px);
  margin-right: -1px !important;
  line-height: var(--TitleLineHeight, 20px);
  font-weight: 400;
  font-size: var(--TitleFontSize, 14px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  border: 1px solid transparent !important;
  border-radius: 0;
  color: var(--TextPrimary-strong);
  outline: none;
  user-select: none !important;
  -webkit-user-select: none !important;
  a[data-popo='tag'] {
    color: var(--LinkNormal);
  }

  &:hover {
    cursor: text;
  }

  :global {
    .rock-input-textarea {
      min-height: 20px !important;
      line-height: 20px !important;
      //color: var(--TextPrimary-strong) !important;
    }
  }
}

.focused {
  background-color: var(--bgTop) !important;
  border: 1px solid var(--Brand500) !important;
  border-radius: 4px;
  box-sizing: border-box !important;
  box-shadow: 0px 3px 4px 0px var(--absaBlack12) !important;
}

.titleWarp {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  &:hover {
    .titleWarpLeft:not(.disabled) {
      :global(#todo-title-input-editor) {
        background-color: var(--bgTop);
        border-radius: 4px;
        border: 1px solid var(--aBlack12) !important;
      }
    }

    .taskname__operatoin {
      width: auto;
    }
  }

  .titleWarpLeft {
    min-width: 0;
    flex: 1;
    display: flex;
    align-items: center;
    //margin-right: 28px;

    &.fullWidth {
      width: 100%;
      margin-right: 0;
      padding-right: 0;
    }
    :global(#todo-title-input-editor) {
      min-width: 0;
    }

    .commentCount,
    .participantCount {
      flex-shrink: 0;
      .iconBtn {
        padding: 2px 4px;
      }
      .icon {
        color: var(--TextTertiary);
        font-size: 12px;
      }
    }
  }
  .onlyLeft {
    width: 100%;
    max-width: 100%;
    flex: 1;
    margin-right: 0;
  }
  .disabled {
    pointer-events: none;
    cursor: default;
  }
  .finished {
    :global {
      .todo-content-editable {
        opacity: 0.4;
        text-decoration-line: line-through;
      }
    }
  }
  .titleWarpRight {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    padding: 3px 6px;
    border: 1px solid var(--aBlack12);
    border-radius: 6px;
    color: var(--TextSecondary);
    font-size: 12px;
    line-height: 14px;
    background-color: var(--bgTop);
    transition: background-color ease 0.25s;

    &.square {
      padding: 3px;
    }

    .openIcon {
      font-size: 14px;
      color: var(--IconPrimary);
      &:not(:last-child) {
        margin-right: 2px;
      }

      svg {
        pointer-events: none;
      }
    }
    &:hover {
      background-color: var(--aBlack4);
    }
  }
}

.taskname__extra {
  display: flex;
  align-items: center;
  column-gap: 2px;
  padding-left: 4px;
  color: var(--TextTertiary);
  :global {
    .taskname__extra--icon {
      color: var(--TextTertiary);
      font-size: 12px;
    }
    .taskname__extra--iconBtn {
      padding: 2px 4px;
    }
  }
}

.taskname__operatoin {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 4px;
  column-gap: 8px;
  width: 0;
  overflow: hidden;
}

.taskAdding {
  white-space: nowrap !important;
  overflow-x: scroll;
}

.cancel__btn {
  background-color: var(--bgTop);
  border: 1px solid var(--aBlack12) !important;
  &:hover {
    background-color: var(--aBlack4);
  }
}

.btnEdit {
  background-color: var(--primary-1) !important;
  color: var(--rock-btn-primary-color, var(--white-1)) !important;
}

.btnsWrapper {
  display: flex;
}
