@import './themes/dark.less';
@import './themes/light.less';
@import './themes/bedrock.less';

@import '../../src/assets/styles/index.less';
html,
body,
#root {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  color: var(--TextPrimary);
  overflow: hidden;
}

body {
  font-size: 14px;
  *::-webkit-scrollbar-thumb {
    border-radius: 4px;
    border: 8px solid transparent;
    //通过border减少宽度
    background-color: var(--aBlack12);
    cursor: pointer;
    &:hover {
      background-color: var(--aBlack24);
    }
  }
}
* {
  font-family: -apple-system, 'Microsoft Yahei', 'PingFang SC', BlinkMacSystemFont, 'Helvetica Neue',
    Tahoma, Arial, 'Hiragino Sans GB', sans-serif, 'Apple Color Emoji', Roboto, RobotoDraft;
  box-sizing: border-box;
  &:focus-visible {
    outline: none !important;
  }
}

*::-webkit-scrollbar {
  background-color: transparent;
  width: 4px;
  height: 6px;
}

.com-dropdown-select {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  background-color: var(--bgTop);
  box-shadow: var(--ComBoxShadow);
  border: 1px solid var(--aBlack12);
}

.com-btn-bg {
  &:hover {
    background-color: var(--aBlack6);
  }
  &:active {
    background-color: var(--aBlack4);
  }
}

.com-level-urgent {
  &.com-level-bg {
    background-color: var(--TasksBGIV);
  }
  .com-level-icon {
    color: var(--R700);
  }
  .com-level-label {
    color: var(--R700);
  }
}
.com-level-high {
  &.com-level-bg {
    background-color: var(--TasksBGIII);
  }
  .com-level-icon {
    color: var(--O700);
  }
  .com-level-label {
    color: var(--O700);
  }
}
.com-level-medium {
  &.com-level-bg {
    background-color: var(--TasksBGII);
  }
  .com-level-icon {
    color: var(--P700);
  }
  .com-level-label {
    color: var(--P700);
  }
}
.com-level-low {
  &.com-level-bg {
    background-color: var(--TasksBGI);
  }
  .com-level-icon {
    color: var(--G700);
  }
  .com-level-label {
    color: var(--G700);
  }
}

.com-level-unset {
  &.com-level-bg {
    background-color: var(--aBlack6);
  }
  .com-level-icon {
    color: var(--IconPrimary);
  }
  .com-level-label {
    color: var(--TextPrimary);
  }
}

.time-default {
  font-size: 13px;
}
.time-other {
  color: var(--TextPrimary) !important;
}
.time-today {
  color: var(--Brand600) !important;
}
.time-expired {
  color: var(--R600) !important;
}

body {
  --MenuWidth: 232px;
  --TableItemHeight: 36px;
  --ComBoxShadow: 0px 8px 20px 0px var(--absaBlack12);
  --ComBoxShadow6: 0px 8px 20px 0px var(--absaBlack6);
  --fc-highlight-color: var(--aBrand8); //日历拖拽的背景色
  --primary-1: var(--Brand600) !important;
  --status-danger-1: var(--R600);
  --border-thickness-focus: 1px !important;
  --bg-5: var(--bgTop) !important;
  --font-weight-medium: 400 !important;
}
.popo-qiull-editor.ql-container.ql-snow {
  border: none;
  .ql-editor {
    padding: 9px 12px;
  }
  .ql-editor > p {
    word-break: break-all;
    line-height: 18px;
    font-size: 14px;
  }
  .ql-editor.ql-blank:before {
    font-style: normal;
    color: var(--TextTertiary);
    font-size: 14px;
    //line-height: 22px;
  }
  & a {
    color: var(--Brand600);
  }
}

.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
  background: var(--aBlack12) !important;
  &:hover {
    background: var(--aBlack24) !important;
  }
}

.blink__animation {
  animation: Blink linear 0.15s alternate;
  animation-direction: alternate;
  animation-iteration-count: 1;
}

body .origin__primary__btn.origin__primary__btn {
  background-color: var(--Brand500);
  color: var(--absWhite);
}

@keyframes Blink {
  from {
    background-color: var(--aBrand12);
    color: var(--Brand600);
  }
  to {
    background-color: var(--Brand600);
    color: var(--absWhite);
  }
}
