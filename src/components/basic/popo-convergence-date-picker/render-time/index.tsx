import classNames from 'classnames';
import dayjs from 'dayjs';

import I18N from '@/utils/I18N';

import { getTimePickerFormat, PPTimeFormat } from '../utils';
export type Props = {
  startTime?: number;
  timeFormat?: number;
  deadline?: number;
  className?: string;
  isExpired?: boolean;
  isToday?: boolean;
  onlyShowStart?: boolean;
};

export default function RenderTime(props: Props) {
  const { startTime, timeFormat, deadline, isExpired, isToday, onlyShowStart } = props;

  // 改成方法, 跨天的时候可以更新
  const getTimeStr = () => {
    const hasHHmm = timeFormat === PPTimeFormat.dateAndTime;
    if (onlyShowStart) {
      return I18N.template(I18N.auto.start, {
        val1: getTimePickerFormat({
          time: dayjs(startTime),
          hasHHmm,
        }),
      });
    }
    if (!deadline) {
      return undefined;
    }
    if (startTime) {
      return `${getTimePickerFormat({
        time: dayjs(startTime),
        hasHHmm,
      })} - ${getTimePickerFormat({
        time: dayjs(deadline),
        hasHHmm,
      })}`;
    } else {
      return I18N.template(I18N.auto.end_2, {
        val1: getTimePickerFormat({
          time: dayjs(deadline),
          hasHHmm,
        }),
      });
    }
  };

  return (
    <span
      className={classNames('mr-6', 'time-default', {
        ['time-today']: isToday && !isExpired,
        ['time-expired']: isExpired,
      })}
    >
      {getTimeStr()}
    </span>
  );
}
