import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { useMemo } from 'react';

import { Divider } from '@/components/basic';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import type { TimeValue } from '../utils';
import s from './index.less';
import {
  Every_3_Day,
  Every_Day,
  Every_Weeks,
  ItemTimeOption,
  Later_Today,
  Next_Week,
  None,
  RemindTimeValue,
  Tomorrow,
} from './utils';

export type Props = {
  className?: string;
  onChange?: (time: RemindTimeValue) => void;
  onShowDateTimePanel?: () => void;
  showDelete?: boolean;
  deadlineTime?: TimeValue;
  createTime?: Dayjs;
};

export default function DatePickerOverlay(props: Props) {
  const { onChange, onShowDateTimePanel, showDelete, deadlineTime, createTime = dayjs() } = props;
  const onGetTime = (time: RemindTimeValue) => {
    const { time: _time, rrule, selectedOption } = time;
    onChange?.({
      time: dayjs(_time).valueOf(),
      timeFormat: PPTimeFormat.dateAndTime,
      rrule: rrule,
      selectedOption,
    });
  };
  const onDeleteTime = () => {
    onChange?.({
      time: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
      selectedOption: 'NONE',
    });
  };
  const options = useMemo(() => {
    return [None, Tomorrow, Next_Week, Every_Day, Every_3_Day, Every_Weeks];
  }, []);
  console.log('options', options);
  return (
    <div className={classNames('com-dropdown-select', s.panel)}>
      {options.map((item, index) => (
        <div
          key={index}
          className={s.item}
          onClick={() => {
            if (!item.getTime) {
              onDeleteTime();
              return;
            }
            //TODO 看下这个时间怎么获取 是当前时间还是传递进来的
            const time = createTime;
            onGetTime(item.getTime(time));
          }}
        >
          <div className={s.title}>{item.text}</div>
          {item.showDate ? <div className={s.desc}>{item.showDate(createTime)}</div> : null}
        </div>
      ))}
      {options.length ? <Divider type="horizontal" className={s.divider}></Divider> : null}
      <div className={s.item} onClick={onShowDateTimePanel}>
        <div className={s.title}>{I18N.auto.dateAndTime}</div>
      </div>
      {showDelete ? (
        <>
          <Divider type="horizontal" className={s.divider}></Divider>
          <div className={s.item} onClick={onDeleteTime}>
            <div className={s.title}>{I18N.auto.whenDeletingReminders}</div>
          </div>
        </>
      ) : null}
    </div>
  );
}
