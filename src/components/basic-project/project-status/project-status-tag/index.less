.tag {
  display: inline-flex;
  align-items: center;
  padding: 0 10px 0 6px;
  height: 24px;
  border-radius: 6px;
  font-size: 12px;
}

:global {
  //任务状态 进行中
  .com-project-status-ongoing {
    &.com-project-status-bg {
      background-color: var(--TasksProjectStatusI);
    }
    &.com-project-status-label {
      color: var(--Brand700);
    }
  }

  // 暂停
  .com-project-status-pause {
    &.com-project-status-bg {
      background-color: var(--TasksProjectStatusII);
    }
    &.com-project-status-label {
      color: var(--O700);
    }
  }
  // 有风险
  .com-project-status-risk {
    &.com-project-status-bg {
      background-color: var(--TasksProjectStatusIII);
    }
    &.com-project-status-label {
      color: var(--R700);
    }
  }

  // 已完成
  .com-project-status-done {
    &.com-project-status-bg {
      background-color: var(--G600);
    }
    &.com-project-status-label {
      color: var(--absWhite);
    }
  }

  // 归档
  .com-project-status-archive {
    &.com-project-status-bg {
      background-color: var(--N600);
    }
    &.com-project-status-label {
      color: var(--absWhite);
    }
  }
}
