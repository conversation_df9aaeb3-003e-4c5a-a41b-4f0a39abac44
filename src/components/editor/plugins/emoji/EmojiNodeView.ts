class EmojiNodeView {
  dom: HTMLElement | null = null;
  constructor(node, view, getPos) {
    // const { placeholder, value, id } = node.attrs;

    this.dom = document.createElement('span');
    this.dom.innerText = '表情';
    this.dom.className = 'emoji-node';
  }

  // 是否要阻止节点内事件冒泡， true即阻止
  stopEvent() {
    return true;
  }

  // 当 node view 更新自身的时候会调用该节点的此方法
  update(node) {
    if (node.type.name !== 'emoji') return false;
    if (this.dom) this.dom.innerText = node.attrs.type;

    return true;
  }

  destroy() {
    this.dom?.remove();
  }
}

export default EmojiNodeView;
