import { Dayjs } from 'dayjs';
import React, { useMemo } from 'react';

import { getTimePickerFormat } from '@/components/basic/popo-date-picker/utils';
import { PPTimeFormat } from '@/utils/const';
import I18N from '@/utils/I18N';

import { Tooltip } from '../..';
import s from './index.less';

type Props = {
  time: Dayjs;
  timeFormat?: PPTimeFormat;
  rrule?: string;
};

export const RemindStr: React.FC<Props> = (props) => {
  const { time, timeFormat, rrule } = props;

  const str = useMemo(() => {
    let hasHHmm = false;
    if (timeFormat === PPTimeFormat.dateAndTime) {
      hasHHmm = true;
    }
    const str = getTimePickerFormat({
      time: time,
      hasHHmm: hasHHmm,
      rrule: rrule,
    });
    return str;
  }, [time, timeFormat, rrule]);

  if (rrule) {
    const text = `${I18N.template(I18N.auto.valReminder, {
      val1: str,
    })} ${I18N.template(I18N.auto.start, { val1: time.format('M/D') })}`;
    return (
      <Tooltip title={text} onlyEllipsis>
        <div className={`${s.remindLabel} remind__label`}>{text}</div>
      </Tooltip>
    );
  }

  const text = I18N.template(I18N.auto.valReminder, {
    val1: str,
  });

  return (
    <Tooltip title={text} onlyEllipsis>
      <div className={`${s.remindLabel} remind__label`}>{text}</div>
    </Tooltip>
  );
};

export default RemindStr;
