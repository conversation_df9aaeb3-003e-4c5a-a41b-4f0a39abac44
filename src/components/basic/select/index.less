body {
  .rock-select {
    color: var(--TextPrimary) !important;
    font-size: 14px;
    --text-color-2: var(--TextPrimary);
    .rock-select-selector {
      padding-right: 26px; //右侧箭头占位
      padding-left: 8px;
      .rock-select-selection-item {
        line-height: 22px;
      }
      &.rock-select-selector-disabled {
        color: var(--TextPrimary);
      }
    }
    .rock-select-arrow {
      --text-color-3: var(--IconTertiary);
      right: 8px;
    }

    .rock-select-selection-placeholder {
      color: var(--TextTertiary);
      transform: translate3d(0, -50%, 0);
    }
    .rock-select-item.rock-select-item-selected.rock-select-item-has-checkbox {
      &:hover {
        background-color: var(--bgTop);
      }
    }
    .rock-select-label-wrapper {
      margin-left: -5px;
    }
  }

  .rock-select-dropdown {
    --bg-5: var(--bgTop);
    --primary-1: var(--Brand600);

    // background-color: var(--bgTop) !important;
    border-color: var(--aBlack12) !important;
    .rock-select-item-label {
      text-align: left;
      color: var(--TextPrimary) !important;
    }

    .rock-select-item {
      font-size: 13px;
    }
  }

  //check放到右边
  .rock-select-item.rock-select-item-has-select-icon {
    padding-right: 28px;
    padding-left: 8px;

    .rock-select-item-check-icon {
      left: auto;
      right: 8px;
    }
  }
}
