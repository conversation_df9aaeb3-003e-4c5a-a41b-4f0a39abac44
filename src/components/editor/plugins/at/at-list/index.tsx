import { <PERSON><PERSON><PERSON>, Spin } from '@bedrock/components';
import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { AvatarPeople } from '@/components/basic';
import { useOutsideClick } from '@/hooks';
import { UserInfo } from '@/types';
import I18N from '@/utils/I18N';
import s from './index.less';
import { MentionExtraData } from '@/components/basic-task/comment/editor/utils';
import { useLatest } from 'ahooks';
import { BedrockEditorContext } from '@/components/editor/bedrock-editor/context';


interface IAtListProps {
  onSelected: (v: UserInfo) => void;
  list: UserInfo[];
  search: boolean;
  participants: UserInfo[];
  style: React.CSSProperties;
}

const AtList: React.FC<IAtListProps> = (props) => {
  const { onSelected, list, search, participants, style } = props;
  const listRef = useLatest(list)
  const scrollRef = useRef<any>(null);
  const currentIndexRef = useRef(0);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const { showPopoList } = useContext(BedrockEditorContext)

  useEffect(() => {
    pp.setHotkeyFilterList({
      filters: [
        {
          modifiers: '',
          key: 'Key_Escape',
        },
      ],
    });

    const listener = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        selectIndex(-1)
      }
      if (e.key === 'ArrowDown') {
        selectIndex(1)
      }
      if (e.key === 'Escape') {
        // console.log('escape')
        //TODO 隐藏popplist
        showPopoList.current = false
      }
      if (e.key === 'Enter') {
        onSelected(listRef.current[currentIndexRef.current]);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
      pp.setHotkeyFilterList({
        filters: [],
      });
    };
  }, []);

  const selectIndex = (d: number) => {
    if (d === -1 && currentIndexRef.current === 0) {
      currentIndexRef.current = listRef.current.length + d;
    } else {
      const newIndex = (currentIndexRef.current + d) % listRef.current.length;
      currentIndexRef.current = Math.abs(newIndex);
    }
    setCurrentIndex(currentIndexRef.current);

    // 计算滚动条滚动距离
    //向下正向
    if (d === 1) {
      //超出一屏幕开始滚动
      if (currentIndexRef.current >= 5) {
        //扣除最近联系人的高度34
        const top = 34 + 36 * (currentIndexRef.current - 5);
        scrollRef.current.scrollTop(top);
      }
      //循环到头的时候置顶
      if (currentIndexRef.current === 0) {
        scrollRef.current.scrollTop(0);
      }
    } else {
      // 超出一屏滚动
      if (currentIndexRef.current >= 5) {
        const top = 34 + 36 * currentIndexRef.current;
        scrollRef.current.scrollTop(top);
      } else {
        //一屏幕数据内 置顶
        scrollRef.current.scrollTop(0);
      }
    }
  };

  return (
    <div
      style={style}
      className={s.mentionPanel}
      onClick={(e) => {
        // quill?.focus?.(); //失去焦点quill.keyboard.addBinding会失效
        e.stopPropagation();
      }}
    >
      <div className={s.box}>
        <Scrollbar ref={scrollRef}>
          <div className={s.list}>
            {list.map((item, index) => {
              let isParticipant = true;
              if (search && !participants.find((v) => v.uid === item.uid)) {
                isParticipant = false;
              }
              return (
                <div
                  className={classNames(s.item, { [s.active]: currentIndex === index })}
                  key={index}
                  onClick={(e) => {
                    setCurrentIndex(index);
                    currentIndexRef.current === index;
                    onSelected(item);
                    e.stopPropagation();
                  }}
                >
                  <div className={s.left}>
                    <AvatarPeople
                      className={classNames(s.avatar)}
                      avatarUrl={item.avatarUrl}
                    />
                    <div className={s.name}>{item.name}</div>
                    {item.desc ? <div className={s.desc}>{item.desc}</div> : null}
                  </div>
                  {search && !isParticipant ? (
                    <div className={s.tag}>{I18N.auto.notJoined}</div>
                  ) : null}
                </div>
              );
            })}
          </div>
        </Scrollbar>
      </div>
      <div className={s.tip}>
        <div>{I18N.auto.switch}</div>
        <div className={s.line}></div>
        <div>{I18N.auto.enter}</div>
        <div className={s.line}></div>
        <div>{I18N.auto.escCancel}</div>
      </div>
    </div>
  );
};

export default AtList;
