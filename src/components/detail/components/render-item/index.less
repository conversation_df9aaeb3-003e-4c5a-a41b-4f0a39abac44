.item {
  display: flex;
  justify-content: flex-start;
  //align-items: center;
  min-height: 32px;
  margin-bottom: 12px;
  &.disabled::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
  }
  // padding: 8px 0;

  .infoItem {
    display: flex;
    align-items: center;
    flex: 1;
    // padding-left: 14px;
    //padding: 8px 0 8px 14px;
    overflow: hidden;
    min-height: 32px;
    min-width: 0;
    // cursor: pointer;
  }
  :global {
    .todo-base-label {
      min-height: 32px;
    }
  }
}

.labelIcon {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 120px;
  height: 32px;
  color: var(--TextTertiary);
  margin-right: 8px;
  i {
    color: var(--IconTertiary);
    margin-right: 6px;
  }
}

.height0 {
  height: 0;
  width: 0;
}
.clear {
  // vertical-align: middle;
  display: flex;
  align-items: center;
  width: 16px;
  .clearIcon {
    color: var(--IconTertiary);
  }
}

.padding0 {
  padding-left: 0 !important;
}
