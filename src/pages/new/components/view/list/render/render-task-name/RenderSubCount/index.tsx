import { IconBtn } from '@/components/basic';
import classNames from 'classnames';
import s from './index.less';
import I18N from '@/utils/I18N';
import { QuickSub } from '@babylon/popo-icons';
import { TaskInfo } from '@/types';
import { FC, useMemo } from 'react';
import { Dispatch } from '@/models/store';
import { TaskTableRowType } from '@/utils/const';

interface IRenderSubCountProps {
  taskInfo?: TaskInfo;
  dispatch: Dispatch;
}
const RenderSubCount: FC<IRenderSubCountProps> = ({ taskInfo, dispatch }) => {
  let subLength = taskInfo?.subtask?.length || 0;

  if (subLength <= 0) {
    return null;
  }
  const last = taskInfo?.subtask?.[subLength - 1];
  if (last && last._rowType === TaskTableRowType.add) {
    subLength -= 1;
  }

  if (subLength <= 0) {
    return null;
  }

  const handleOpenDetail = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    // 打开详情
    dispatch.detail.openDetail(Number(taskInfo.taskId)).then(() => {
      const subtasksEl = document.querySelector('#subtasks');
      subtasksEl?.scrollIntoView({ behavior: 'smooth' });
    });
  };

  return (
    <span onClick={handleOpenDetail} className={classNames(s.participantCount, {})}>
      <IconBtn
        placement="top"
        iconClassName="taskname__extra--icon"
        className="taskname__extra--iconBtn"
        icon={<QuickSub className="fs-16" />}
      >
        <span className={'pl-3'}>
          {taskInfo?.subtaskFinishedCount || 0}/{subLength || 0}
        </span>
      </IconBtn>
    </span>
  );
};
export default RenderSubCount;
