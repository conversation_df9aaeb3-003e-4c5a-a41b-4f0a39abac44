import { TimeValue, TodoInfo } from '@/types';

export const getExtraParams = (params: Partial<TodoInfo>) => {
  return {
    deadline: params.deadline,
    rrule: params.rrule,
    assignees: params?.assignees?.map((v) => v.uid!) || [],
    startTime: params.startTime,
    deadlineFormat: params.deadlineFormat || (params as any).timeFormat,
    alarm: params.alarm && {
      alarmCreateTime: params.alarm.alarmCreateTime || 0,
      alarmTimestamp: (params.alarm as TimeValue)?.time || 0,
      alarmRrule: (params.alarm as TimeValue)?.rrule || '',
      selectedOption: params.alarm?.selectedOption,
    },
  };
};
