import { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import {
  apiTaskFollowerAddPost,
  apiTaskFollowerDeletePost,
  apiTaskFollowerDeleteSelfPost,
} from '@/api';
import { Icon, RenderPeoples, Tooltip, WatcherPicker } from '@/components/basic';
import { PeoplePickerContext } from '@/components/basic/people-picker';
import { Dispatch, RootState } from '@/models/store';
import { DetailTodoInfo, UserInfo } from '@/types';
import { onPageVisibleChange } from '@/utils';
import { PeoplePickerType } from '@/utils/const';
import I18N from '@/utils/I18N';
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission';

import s from './index.less';
import useGetPermissions from '@/hooks/useGetPermissions';

export type Props = {
  className?: string;
  value: UserInfo[];
  onChange?: (v: UserInfo[]) => void;
  showPercent?: boolean;
  maxShowCount?: number;
  hasArrow?: boolean;
  onActive?: (active: boolean) => void;
  resultGroup?: boolean;
  containerElement?: HTMLElement;
  taskInfo: DetailTodoInfo;
};

export const AddFollower = (props: Props) => {
  const { value, hasArrow, containerElement, taskInfo } = props;
  const { taskId, assigner, assigneeUids, followerUids } = taskInfo;
  const [visible, setVisible] = useState<boolean>(false);
  const dispatch = useDispatch<Dispatch>();
  const { userInfo } = useSelector((state: RootState) => ({
    userInfo: state.user.userInfo,
  }));

  const permissions = useGetPermissions({ taskId });

  useEffect(() => {
    onPageVisibleChange((pageShow) => {
      if (!pageShow) {
        setVisible(false);
      }
    });
  }, []);

  const change = (users: UserInfo[]) => {
    const uids: string[] = users.map((item) => item.uid!);
    const olduids = value.map((v) => v.uid);
    let reUids = olduids.filter((item) => !uids.includes(item!));
    let addedUids = uids.filter((item) => !olduids.includes(item));
    const requests = [];
    if (addedUids.length) {
      requests.push(apiTaskFollowerAddPost({ taskId: Number(taskId!), uids: addedUids }));
    }
    if (reUids.length) {
      if (reUids.includes(userInfo?.uid)) {
        requests.push(apiTaskFollowerDeleteSelfPost({ taskId: Number(taskId!) }));
        reUids = reUids.filter((item) => item !== userInfo?.uid);
      }
      if (reUids.length) {
        requests.push(apiTaskFollowerDeletePost({ taskId: Number(taskId!), uids: reUids }));
      }
    }
    Promise.all(requests).then(() => {
      dispatch.viewSetting.updateItemByDetail({ taskId });
    });
  };

  const titleStr = useMemo(() => {
    return value?.map((item) => item.name).join(', ');
  }, [value]);

  const memoValue = useMemo(() => {
    return [...(value || [])];
  }, [value]);

  const memoPermissions = useMemo(() => {
    const [CAN_EDIT, CAN_CANCEL_OBSERVER] = validatesPermission({
      permissions: permissions,
      key: [TaskPermissionEnum.CAN_EDIT, TaskPermissionEnum.CAN_CANCEL_OBSERVER],
    }) as boolean[];
    return {
      CAN_EDIT,
      CAN_CANCEL_OBSERVER,
    };
  }, [permissions]);
  return (
    <PeoplePickerContext.Provider
      value={{
        assignerUid: assigner?.assignerUid,
        assigneeUids: assigneeUids,
        followerUids: followerUids,
        pickerType: PeoplePickerType.watcher,
        currentUser: userInfo,
      }}
    >
      <WatcherPicker
        containerElement={containerElement}
        taskId={taskId}
        assignerUid={assigner?.assignerUid}
        search
        value={memoValue}
        onChange={change}
        hasMinimum
        visible={visible}
        onVisible={(v) => {
          setVisible(v);
        }}
        className={`${s.watcherPickerWrapper} ${value?.length && s.hasValueWatcherPickerWrapper}`}
        disabled={!memoPermissions.CAN_EDIT && !memoPermissions.CAN_CANCEL_OBSERVER}
        hasArrow={hasArrow}
        canDelete={false}
        canDeleteSelf={memoPermissions.CAN_CANCEL_OBSERVER && !memoPermissions.CAN_EDIT}
        canSelect={memoPermissions.CAN_EDIT}
        placeholder={
          <>
            {memoPermissions.CAN_EDIT ? (
              <span className={s.addFollowerWrapper}>
                <Icon name="icon-add" className={s.plus} fontSize={14}></Icon>
                {I18N.auto.addFollowers}
              </span>
            ) : (
              <span>{I18N.auto.peopleFollow_2}</span>
            )}
          </>
        }
      >
        {value?.length ? (
          <Tooltip title={titleStr}>
            <div style={{ display: 'flex' }}>
              <div className={s.listPeopleItem}></div>
              <RenderPeoples
                showFinishedIcon
                list={value || []}
                count={taskInfo.followers?.length || value.length}
                maxShowCount={6}
                showPercent={false}
                avatarClassName="mr-8"
              ></RenderPeoples>
              <span className={s.showFollowCount}>
                {value?.length}
                {I18N.auto.peopleFollow}
              </span>
            </div>
          </Tooltip>
        ) : null}
      </WatcherPicker>
    </PeoplePickerContext.Provider>
  );
};
