const getSelectionLength = (node: Node, nodeOffset: number) => {
  const childNodes = node?.parentNode?.childNodes;
  let lenght = 0;
  if (childNodes) {
    for (let index = 0; index < childNodes.length; index++) {
      const element = childNodes[index];
      if (element === node) {
        if (node.nodeName === 'DIV') {
          //@ts-ignore
          lenght += node.innerText.length;
        } else {
          lenght += nodeOffset;
        }

        break;
      } else {
        if (element.nodeName === '#text') {
          //@ts-ignore
          lenght += element.length;
        }
        if (element.nodeName === 'A') {
          //@ts-ignore
          lenght += element.innerText.length;
        }
      }
    }
  }
  return lenght;
};

const getSelectionRange = (sel: Selection) => {
  const { focusOffset = 0, focusNode, anchorNode, anchorOffset = 0 } = sel;
  let start = 0;
  if (anchorNode) {
    start = getSelectionLength(anchorNode, anchorOffset);
  }
  let end = 0;
  if (focusNode) {
    end = getSelectionLength(focusNode, focusOffset);
  }
  if (end < start) {
    const temp = start;
    start = end;
    end = temp;
  }
  return {
    start,
    end,
  };
};

export { getSelectionRange };
