import React from 'react';

import { getStatusChangeName, getStatusTimeStr, ProjectRecord } from '../use-project-status';
import s from './index.less';

export type Props = {
  className?: string;
  value?: ProjectRecord;
};

const ProjectStatusRecord: React.FC<Props> = (props) => {
  const { className, value } = props;
  return (
    <div className={s.record}>
      <div className={s.recordTop}>
        <div className={s.name}>{value?.userName}&nbsp;</div>
        <div className={s.time}>{getStatusTimeStr(value?.updateTime)}</div>
      </div>
      <div className={s.recordBottom}>{getStatusChangeName(value?.state, value?.preState)}</div>
    </div>
  );
};

export default ProjectStatusRecord;
