import classNames from 'classnames'
import { Dayjs } from 'dayjs'
import { useMemo } from 'react'
import { useSelector } from 'react-redux'

import { Icon, Tooltip } from '@/components/basic'
import LevelTag from '@/components/basic/level-picker/level-tag'
import TodoStatus from '@/components/basic-task/todo-status'
import { RootState } from '@/models/store'
import { TodoInfo } from '@/types'
import { CalendarModeType, DrawerClickOrignKey, DrawerOrigin, PPTimeFormat, TableIndex } from '@/utils/const'
import { TaskPermissionEnum, validatesPermission } from '@/utils/permission'

import s from './index.less'
import useGetPermissions from '@/hooks/useGetPermissions'
export enum CardType {
  onlyShow = 'onlyShow',
  add = 'add',
}
interface Props {
  taskId: string
  title: string
  level: number
  time?: Dayjs
  done: boolean
  mode: CalendarModeType
  isFinished?: boolean
  isExpired?: boolean
  isToday?: boolean
  timeFormat?: PPTimeFormat
  //type: CardType;
  data: TodoInfo
  laneTime?: Date | null
}

const Card: React.FC<Props> = props => {
  const { title, level, time, mode, isExpired, isToday, timeFormat, data, laneTime } = props

  const { displayFields } = useSelector((state: RootState) => ({
    displayFields: state.viewSetting.currentViewTab.displays || [],
  }))

  const rowNum = useMemo(() => {
    // const num = displayFields?.filter((item) => item.visible).length + 1;
    // if (num > 3) {
    //   return num;
    // }
    //displayFields
    return 4
  }, [])
  const memoRow = useMemo(() => {
    if (mode === CalendarModeType.month) {
      return 1
    }
    return rowNum
  }, [rowNum, mode])

  const ellipsisNum = useMemo(() => {
    let _ellipsisNum = rowNum
    if (mode === CalendarModeType.month) {
      return 1
    }
    if (level && displayFields.find(item => item.fieldName === TableIndex.priority && !!item.visible)) {
      _ellipsisNum -= 1
    }
    if (
      time &&
      timeFormat === PPTimeFormat.dateAndTime &&
      displayFields.find(item => item.fieldName === TableIndex.deadline && !!item.visible)
    ) {
      _ellipsisNum -= 1
    }
    return _ellipsisNum
  }, [level, time, timeFormat, rowNum, mode, displayFields])
  const isfinished = useMemo(() => {
    return data?.finished || data?.selfFinished
  }, [data])

  const permissions = useGetPermissions({ taskId: data?.taskId })

  const disabledComplete = useMemo(() => {
    if (permissions) {
      const [CAN_COMPLETE] = validatesPermission({
        permissions: permissions,
        key: [TaskPermissionEnum.CAN_COMPLETE],
      }) as boolean[]
      return !CAN_COMPLETE
    }
    return true
  }, [permissions])

  return (
    <div
      className={classNames(s.item, 'calendar-task-item', s[`height_${memoRow}`], {
        [s.completed]: isfinished,
      })}
      // @ts-ignore
      id={data?.taskId}
      data-deadline={(laneTime?.getTime() || 0) - 1000}
      onClick={e => {
        ;(e.nativeEvent as any)[DrawerClickOrignKey] = DrawerOrigin.list
      }}
    >
      <div className={s.left}>
        {!!data?.taskId && (
          <TodoStatus
            completeCondition={data.completeCondition}
            assigneeUids={data.assigneeUids || []}
            selfFinished={data.selfFinished}
            finished={data.finished}
            taskId={data.taskId!}
            className={classNames(s.status)}
            //TODO disabled
            disabled={disabledComplete}
            finishedParticipantCount={data.finishedParticipantCount}
            participantCount={data.participantCount}
            taskInfo={data}
          ></TodoStatus>
        )}
      </div>
      <div className={s.right}>
        <Tooltip title={title} placement="topLeft">
          <div
            className={classNames(s.title, { [s.completedTitle]: isfinished })}
            style={{ WebkitLineClamp: ellipsisNum }}
          >
            {title}
          </div>
        </Tooltip>
        {mode === CalendarModeType.week &&
          displayFields.map((item, index) => {
            if (item.visible) {
              if (item.fieldName === TableIndex.priority) {
                return <LevelTag key={index} className={s.level} value={level}></LevelTag>
              }
              if (item.fieldName === TableIndex.deadline && time && timeFormat === PPTimeFormat.dateAndTime) {
                return (
                  <div
                    key={index}
                    className={classNames(s.time, { ['time-today']: isToday }, { ['time-expired']: isExpired })}
                  >
                    <Icon name="icon-details_data_calendar" fontSize={12}></Icon>
                    <span>{time.format('HH:mm')}</span>
                  </div>
                )
              }
            }
            return null
          })}
      </div>
    </div>
  )
}
export default Card
