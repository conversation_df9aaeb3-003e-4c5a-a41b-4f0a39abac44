import { createModel } from '@rematch/core';
import dayjs from 'dayjs';

import {
  apiTaskFlowQueryP2pPost,
  ApiTaskFlowQueryP2pPostResponse,
  apiTaskFlowQueryTeamPost,
  ApiTaskFlowQueryTeamPostResponse,
  apiV3TaskCountSessionGet,
  ApiV3TaskCountSessionGetRequest,
  apiV3TaskFlowQueryP2pAllPost,
  apiV3TaskFlowQueryP2pPost,
  apiV3TaskFlowQueryTeamPost,
} from '@/api';
import { getPermissionsByTaskList } from '@/api-common';
import { PermissionsTasksMap, TaskInfo, TaskPermission, TodoInfo } from '@/types';
import { ServerSourceTypeMapKey, TaskTableRowType, TaskTableRowTypeAdd } from '@/utils/const';

import type { RootModel } from '.';
import {
  createTask,
  getAddItem,
  getExpandedIndexMap,
  getIsInSession,
  getIsSessionTasks,
  getTaskByIndexes,
  handleSeverData,
  scrollToIndex,
  setExpandedIndexMap,
  setNewAddItem,
  updateTaskListByInfo,
} from './utils';
import { Row } from '@tanstack/react-table';
import { invoke, pp } from '@popo-bridge/web';

interface Pagination {
  page?: number;
  size?: number;
  more?: boolean;
}

const DefaultPaginatio = {
  size: 50,
  more: false,
};

interface ImTodoListState {
  sessionId?: string;
  sessionType?: ServerSourceTypeMapKey;
  dataList: TaskInfo[];
  pagination?: Pagination;
  loading?: boolean;
  permissionsMap: PermissionsTasksMap;
  expandedKeysMap: Record<number, boolean>;
  totalTaskCount: number;
}

const InitState: ImTodoListState = {
  sessionId: '', //当前 群或P2P 的id
  sessionType: undefined, //群:3，P2P：1
  dataList: [],
  pagination: Object.assign({}, DefaultPaginatio),
  loading: false,
  permissionsMap: {} as Record<string, { permissions: TaskPermission[] }>,
  expandedKeysMap: {},
  totalTaskCount: 0,
};

export const SESSION_TASKS_VIEW_ID = 'session-tasks';

let IS_ADDING_TASK_REF = {
  isAdding: false,
};

export const imTodoList = createModel<RootModel>()({
  state: InitState,
  reducers: {
    setData(state, payload: Partial<ImTodoListState>) {
      return {
        ...state,
        ...payload,
      };
    },
    setSessionId(state, payload: string | undefined) {
      return {
        ...state,
        sessionId: payload,
      };
    },
    setSessionType(state, payload: ServerSourceTypeMapKey | undefined) {
      return {
        ...state,
        sessionType: payload,
      };
    },
    setTodoList(state, payload: TodoInfo[]) {
      return {
        ...state,
        dataList: payload,
      };
    },
    updateTodoList(state, payload: TodoInfo) {
      const { taskId } = payload;
      const { dataList = [] } = state;
      const list = dataList.map((item) => {
        if (item.taskId === taskId) {
          return {
            ...item,
            ...payload,
          };
        } else {
          return item;
        }
      });
      return {
        ...state,
        dataList: [...list],
      };
    },
    setLoading(state, payload: boolean) {
      return {
        ...state,
        loading: payload,
      };
    },
  },
  effects: (dispatch) => ({
    async getListCount({}, state) {
      const { sessionId, sessionType } = state.imTodoList;
      const p2pFrom = state.user.userInfo?.uid;

      let params: ApiV3TaskCountSessionGetRequest = {};
      if (sessionType === 1) {
        params = {
          // @ts-ignore
          sessionType: 1,
          p2pFrom,
          p2pTo: sessionId,
        };
      } else {
        params = {
          // 此处sessionType和其他地方的sessionType不一样，群组要传2
          // @ts-ignore
          sessionType: 2,
          teamId: sessionId,
        };
      }
      const res = await apiV3TaskCountSessionGet(params);
      dispatch.imTodoList.setData({
        totalTaskCount: res.count || 0,
      });

      try {
        // 通过桥更新客户端展示数量
        invoke({
          moduleName: '',
          methodName: 'setSessionTaskCount',
          params: {
            count: res.count,
          },
        });
      } catch (error) {
        console.log('更新客户端展示数量 err', error);
      }
      return res;
    },

    /**
     * P2P会话待办列表
     * @param options
     * @param state
     */
    async getTodoList(
      options: { page?: number; size?: number },
      state
    ): Promise<ApiTaskFlowQueryTeamPostResponse | ApiTaskFlowQueryP2pPostResponse> {
      const { pagination, dataList = [], sessionId, sessionType } = state.imTodoList;
      const { userInfo } = state.user;
      const { page, size = DefaultPaginatio.size } = options;
      let scrollId: number | undefined = undefined;
      if (dataList?.length && page !== 1) {
        scrollId = dataList[dataList.length - 1].taskId!;
      }
      let params: Record<string, any> = {
        size: size < DefaultPaginatio.size ? DefaultPaginatio.size : size,
        scrollId: scrollId ? String(scrollId) : undefined,
        //@ts-ignore
        timezone: dayjs.tz?.guess?.(),
      };

      dispatch.imTodoList.setLoading(true);

      const isInSessionTasks = getIsSessionTasks();

      if (isInSessionTasks && (!page || page === 1)) {
        dispatch.imTodoList.getListCount({});
      }

      let requestAPI;
      if (sessionType === 1) {
        requestAPI = isInSessionTasks ? apiV3TaskFlowQueryP2pAllPost : apiV3TaskFlowQueryP2pPost;
        // requestAPI = apiV3TaskFlowQueryP2pPost;
        params = {
          ...params,
          orderBy: 'createTime',
          order: 'desc',
          p2pFrom: userInfo?.uid,
          p2pTo: sessionId,
        };
      } else {
        requestAPI = apiV3TaskFlowQueryTeamPost;
        params = {
          ...params,
          orderBy: 'createTime',
          order: 'desc',
          teamId: sessionId,
        };
      }

      return requestAPI(params, {}).then((ret) => {
        let { list = [] as TaskInfo[], hasMore = false, totalTaskCount } = ret;
        //@ts-ignore
        list = list.map((item) => handleSeverData(item, userInfo!));
        list = page === 1 ? list : dataList.concat(list);

        const _list = [...list];

        const setDataParams: any = {
          dataList: _list,
          loading: false,
          pagination: {
            ...pagination,
            more: hasMore,
          },
          expandedKeysMap: getExpandedIndexMap(SESSION_TASKS_VIEW_ID),
        };

        // 老的半屏容器，数量直接使用列表返回总数
        if (!isInSessionTasks) {
          setDataParams.totalTaskCount = totalTaskCount;
        }

        dispatch.imTodoList.setData(setDataParams);

        if (hasMore) {
          dispatch.imTodoList.getTodoList({
            page: (page || 1) + 1,
          });
        } else {
          // const addItem = getAddItem({
          //   _rowType: TaskTableRowType.addBtn,
          //   id: `${TaskTableRowTypeAdd}-1`,
          // });
          // const newList = setNewAddItem({
          //   dataList: [..._list],
          //   isHeader: false,
          //   item: addItem,
          // });
          dispatch.imTodoList.setData({
            dataList: [..._list],
          });
        }
        return ret;
      });
    },
    updatePermissionsList(list: TaskInfo[], state) {
      // const { dataList, permissionsMap } = state.imTodoList;
      // const { userInfo } = state.user;
      // const noPermissionsList = list.filter((item) => !permissionsMap[item.taskId!]);
      // getPermissionsByTaskList(noPermissionsList, userInfo).then((mapPermissions) => {
      //   const newPermissionsMap = {
      //     ...permissionsMap,
      //     ...mapPermissions,
      //   } as PermissionsTasksMap;
      //   const _dataList = dataList.map((item) => {
      //     if (!item.permissions && newPermissionsMap?.[item.taskId!]?.permissions) {
      //       return {
      //         ...item,
      //         permissions: newPermissionsMap?.[item.taskId!]?.permissions,
      //       };
      //     }
      //     return item;
      //   });
      //   dispatch.imTodoList.setData({
      //     dataList: _dataList,
      //     permissionsMap: newPermissionsMap,
      //   });
      // });
    },
    async tableAddTaskItem(
      opt: {
        taskId?: number;
        groupId?: string;
        isHeader: boolean;
        tableRowData?: Row<TodoInfo>;
        isSubtask?: boolean;
      },
      state
    ) {
      let { groupId, isHeader, tableRowData } = opt;
      let dataList = state.imTodoList.dataList;
      // 先判端下数据是否已处在新建数据
      const hasaddIndex = dataList.findIndex(
        (item: TodoInfo) => item._rowType === TaskTableRowType.add
      );
      if (hasaddIndex > -1) {
        dataList = dispatch.imTodoList.tableRemoveTaskItemBase({
          taskId: dataList[hasaddIndex].taskId,
          groupId,
          tableRowData: tableRowData || { id: '0' },
        });
      }

      const addOption = {
        _rowType: TaskTableRowType.add,
        id: `${TaskTableRowTypeAdd}-2-${TaskTableRowType.add}-${Date.now()}`,
        assigner: state.user.userInfo,
        groupId: groupId,
      };

      const addItem = getAddItem(addOption);
      addItem.isHeader = isHeader;
      const list = setNewAddItem({
        dataList: dataList,
        isHeader: isHeader,
        //@ts-ignore
        item: addItem,
      });
      dispatch.imTodoList.setData({
        dataList: list,
      });

      setTimeout(() => {
        scrollToIndex({
          taskId: addItem.taskId || addItem.id,
          callback: () => {
            setTimeout(() => {
              const scrollContainer = document.querySelector('#tanstack-table__wrapper_id');
              if (scrollContainer) {
                // 滚动到底部
                scrollContainer.scrollTop = scrollContainer.scrollHeight;
              }
            }, 16);
          },
        });
      }, 16);
    },
    addSubLine(opt, state) {
      const { taskInfo, tableRowData, addItemData = {}, isSubTask } = opt;
      const { id } = tableRowData;
      if (!id) {
        return;
      }
      const indexes = id.split('.');
      const addTarget = getTaskByIndexes({
        indexes: indexes,
        list: state.imTodoList.dataList,
        id,
        secondLast: indexes.length >= 2 && !isSubTask,
      });
      if (!addTarget) {
        return;
      }
      let prevSubRows = addTarget.subtask || [];

      const now = Date.now();
      const addItem = getAddItem({
        ...addItemData,
        _rowType: TaskTableRowType.add,
        id: `${TaskTableRowTypeAdd}-${id}-${TaskTableRowType.add}-${now}`,
        taskId: `${TaskTableRowTypeAdd}-${id}-${TaskTableRowType.add}-${now}`,
        defaultParams: addTarget.defaultParams,
        assigner: state.user.userInfo,
        groupId: addTarget.groupId,
        // 父任务相关
        parentId: typeof addTarget.taskId === 'number' ? addTarget.taskId : undefined,
        title: '',
      }) as TodoInfo;

      const lastItem = prevSubRows[prevSubRows.length - 1] as TodoInfo;

      if (lastItem?._rowType === TaskTableRowType.add) {
        prevSubRows.splice(prevSubRows.length - 1, 1, addItem);
      } else if (lastItem?._rowType === TaskTableRowType.addBtn) {
        prevSubRows.splice(prevSubRows.length - 1, 0, addItem);
      } else {
        prevSubRows.push(addItem);
      }
      addTarget.subtask = prevSubRows;

      const newExpandedKeysMap = {
        ...getExpandedIndexMap(SESSION_TASKS_VIEW_ID),
        [tableRowData.id]: true,
      };

      const updateData: any = {
        dataList: [...state.imTodoList.dataList],
      };

      if (isSubTask) {
        setExpandedIndexMap(newExpandedKeysMap, SESSION_TASKS_VIEW_ID);
        updateData.expandedKeysMap = newExpandedKeysMap;
      }

      dispatch.task.setData(updateData);

      setTimeout(() => {
        const id = addItem.taskId || addItem.id;

        scrollToIndex({
          taskId: id,
        });

        setTimeout(() => {
          const activeItem = document.querySelector(`tr[id="${id}"]`);
          if (activeItem) {
            activeItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }, 160);
      }, 160);
    },
    updateTaskInfo(opt: any, state) {
      if (opt.actionType === 'delete') {
        dispatch.imTodoList.getListCount({});
      }
      const newList = updateTaskListByInfo({ ...opt, dataList: state.imTodoList.dataList });
      dispatch.imTodoList.setData({
        dataList: newList,
      });
    },
    async addTask(opt, state) {
      return createTask(opt)
        .then((res) => {
          dispatch.imTodoList.getListCount({});
          return res;
        })
        .catch((err) => {
          dispatch.imTodoList.getTodoList({
            page: 1,
            size: state.imTodoList.dataList?.length,
          });
          return err;
        });
    },
    tableRemoveTaskItemBase: (opt: any, state) => {
      const { taskId, tableRowData } = opt;

      const tableData = state.imTodoList.dataList as TodoInfo[];
      let _dataList = tableData;
      const indexes = tableRowData?.id?.split?.('.');
      if (indexes?.length > 1) {
        const item = getTaskByIndexes({
          indexes: indexes,
          id: tableRowData?.id,
          list: _dataList || [],
          secondLast: true,
        });
        if (item) {
          item.subtask?.splice(Number(indexes[indexes.length - 1]), 1);
          item.subtask = [...(item.subtask || [])];
          dispatch.imTodoList.setData({
            dataList: [..._dataList],
          });
        }
      } else {
        _dataList = _dataList.filter((v) => v.id !== taskId);
        dispatch.imTodoList.setData({
          dataList: [..._dataList],
        });
      }
      return _dataList;
    },
    tableRemoveTaskItem: (opt) => {
      dispatch.imTodoList.tableRemoveTaskItemBase(opt);
    },
  }),
});
