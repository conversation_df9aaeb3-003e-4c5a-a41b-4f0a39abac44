import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

import { Button, DatePicker } from '@/components/basic';
import I18N from '@/utils/I18N';

import OptionItem from '../option-item';
import Repeat from '../repeat';
import Time from '../time';
import { getDayEnd, TimeValue } from '../utils';
import {
  FORMAT_DD,
  FORMAT_DD_mm,
  getDayStart,
  getRepeatTypeByRuleStr,
  getRuleStrByRepeatType,
  getTimePeriodMilliseconds,
  PPTimeFormat,
} from '../utils';
import s from './index.less';

export type Props = {
  className?: string;
  value?: TimeValue;
  onChange?: (time: TimeValue) => void;
  hasTime?: boolean;
  hasRRule?: boolean;
  initTime?: Dayjs;
  isGetEnd?: boolean;
  minTime?: Dayjs | Date | number;
  minTimeError?: string;
  maxTime?: Dayjs | Date | number;
  maxTimeError?: string;
  defaultDate?: Dayjs | Date | number;
};
export default function DatePickerOverlay(props: Props) {
  const {
    value,
    onChange,
    hasTime = true,
    hasRRule,
    initTime,
    isGetEnd = true,
    minTime,
    minTimeError,
    maxTime,
    maxTimeError,
    defaultDate,
  } = props;
  const [date, setDate] = useState<Dayjs>(() => {
    if (defaultDate) {
      return dayjs(defaultDate);
    }
    return dayjs(new Date()).add(1, 'day');
  });
  const [repeatType, setRepeatType] = useState<number | undefined>();
  const [time, setTime] = useState<number | undefined>(() => {
    if (defaultDate && hasTime) {
      return dayjs(defaultDate).valueOf() - dayjs(defaultDate).startOf('days').valueOf();
    }
    return;
  });
  useEffect(() => {
    if (value) {
      const { time: _time, timeFormat, rrule } = value;
      if ((timeFormat === PPTimeFormat.dateAndTime && _time) || initTime) {
        if (initTime && !_time) {
          const milliseconds = getTimePeriodMilliseconds(initTime);
          setTime(milliseconds);
        } else {
          const milliseconds = getTimePeriodMilliseconds(_time);
          setTime(milliseconds);
          setDate(dayjs(_time));
        }
      }
      if (timeFormat === PPTimeFormat.olayDay) {
        setDate(dayjs(_time));
      }
      if (rrule) {
        const type = getRepeatTypeByRuleStr(rrule);
        setRepeatType(type);
      }
    }
  }, [value, initTime]);
  /**
   * 操作数据之后都会统一走这个方法想上层组件抛出数据
   * @param _date 日期
   * @param _time 时间
   * @param _repeatType 重复类型
   */
  const mergeTimeData = () => {
    let timeFormat = PPTimeFormat.olayDay;
    let rrule = undefined;
    if (time !== undefined) {
      timeFormat = PPTimeFormat.dateAndTime;
    }
    let dateTime = getDayStart(date).add(time || 0, 'millisecond');
    if (time === undefined && isGetEnd) {
      dateTime = getDayEnd(date);
    }
    if (repeatType) {
      rrule = getRuleStrByRepeatType(repeatType, dateTime);
    }
    //
    const retData = {
      timeFormat: timeFormat,
      rrule,
      time: dateTime.valueOf(),
      selectedOption: 'CUSTOM',
    };
    onChange?.(retData);
  };

  const changeRepeat = (v?: number) => {
    setRepeatType(v);
  };
  const changeTime = (v?: number) => {
    setTime(v);
  };
  const changeDate = (v: Dayjs) => {
    setDate(v.endOf('date'));
  };

  const timeStr = useMemo(() => {
    if (date && !time) {
      return getDayStart(date).format(FORMAT_DD);
    }
    if (date && time) {
      return getDayStart(date).add(time, 'millisecond').format(FORMAT_DD_mm);
    }
  }, [date, time]);

  const showError = useMemo(() => {
    let _time = time;
    // time为undefined 表示没有选具体的 时分, 这个时候取一下当前的默认时间距离当天0时的时间差 如果time已经存在 说明在操作时间 不需要取默认值
    if (value?.time && value.timeFormat === PPTimeFormat.olayDay && time === undefined) {
      _time = value?.time - getDayStart(dayjs(value?.time)).valueOf();
    }

    // 有最小值的时候 取最大的time
    if (minTime && value?.timeFormat === PPTimeFormat.dateAndTime && time === undefined) {
      _time = getDayEnd(dayjs(value?.time)).valueOf() - getDayStart(dayjs(value?.time)).valueOf();
    }

    const dateTime = getDayStart(date).add(_time || 0, 'millisecond');

    if (minTime && minTimeError && !dayjs(minTime).isBefore(dayjs(dateTime))) {
      //截止不能早于开始
      return minTimeError;
    }
    if (maxTime && maxTimeError && !dayjs(maxTime).isAfter(dayjs(dateTime))) {
      //开始不能晚于截止
      return maxTimeError;
    }
    return '';
  }, [minTime, maxTime, minTimeError, maxTimeError, date, time, value]);
  return (
    <div className={classNames('com-dropdown-select')}>
      <DatePicker.Panel picker="date" value={date} onChange={changeDate} />
      {hasTime ? (
        <OptionItem
          title={I18N.auto.time}
          onClose={() => {
            changeTime();
          }}
          showSelect={time !== undefined}
        >
          <Time value={time} onChange={changeTime} className={s.selectWrap}></Time>
        </OptionItem>
      ) : null}
      {hasRRule ? (
        <OptionItem
          title={I18N.auto.repeat}
          onClose={() => {
            changeRepeat();
          }}
          showSelect={repeatType !== undefined}
        >
          <Repeat value={repeatType} onChange={changeRepeat} className={s.selectWrap}></Repeat>
        </OptionItem>
      ) : null}
      {showError ? <div className={s.error}>{showError}</div> : null}
      <div className={s.footer}>
        <div>{timeStr}</div>
        <Button type="primary" size="small" onClick={mergeTimeData} disabled={!!showError}>
          {I18N.auto.determine}
        </Button>
      </div>
    </div>
  );
}
