import React from 'react';

import { RenderTime } from '@/components/basic';
import { TodoInfo } from '@/types';
import { FORMAT_DD_mm, TaskTableRowType } from '@/utils/const';

import AddRowBox from '../row-box';
import s from './index.less';

interface Props {
  taskInfo: TodoInfo;
}

const RenderCreateTime: React.FC<Props> = (props) => {
  const { taskInfo } = props;
  const { taskCreateTime, _rowType: rowType } = taskInfo;
  //新增
  if (rowType === TaskTableRowType.add) {
    return null;
  }
  // const disabled = useMemo(() => {
  //   const CAN_EDIT = validatesPermission({
  //     permissions: taskInfo?.permissions,
  //     key: TaskPermissionEnum.CAN_EDIT,
  //   });
  //   return !CAN_EDIT;
  // }, [taskInfo?.permissions]);
  return (
    <AddRowBox focused={false} disabled={true} taskId={taskInfo.taskId}>
      <div className={s.createTimeBox}>
        <RenderTime time={taskCreateTime} format={FORMAT_DD_mm}></RenderTime>
      </div>
    </AddRowBox>
  );
};

export default RenderCreateTime;
