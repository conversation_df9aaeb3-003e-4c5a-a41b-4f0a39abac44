.box {
  display: flex;
  flex-direction: column;
  padding: 0 0 0px 0px;
  height: 100%;
  width: 100%;
  //width: 480px;
  overflow: hidden;
  background-color: var(--bgMiddleLight);
}
.scrollbar {
  overflow-x: hidden;
}
.body {
  position: relative;
  //padding-right: 10px;
  //margin-right: -10px;
  flex: 1;
  // height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 0px 0px 0px;
  min-height: calc(100% - 108px);

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    border: 8px solid transparent;
    //通过border减少宽度
    background: rgba(128, 128, 128, 0.48);
  }

  &::-webkit-scrollbar {
    background-color: transparent;
    width: 4px;
    height: 4px;
  }

  .detail {
    user-select: none;
    padding-left: 24px;
    padding-right: 24px;
    .icon {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      font-size: 16px;
      color: var(--IconTertiary);
    }

    .renderItemIitle {
      position: relative;
      align-items: flex-start;
      height: auto;
      margin-bottom: 6px;
      .icon {
        margin-top: 5px;
      }
    }

    .renderItemIitleNoSource {
      margin-bottom: 16px;
    }

    .autoHeight {
      height: auto;
    }
    :global {
      .rock-dropdown-trigger-default {
        display: flex;
        justify-content: flex-start;
        min-width: 60px;
        padding: 0;
        & > span {
          width: 100%;
        }
        .picker-label-content {
          width: 100%;
        }
      }
      .todo-base-label,
      .todo-level-label {
        color: var(--TextPrimary);
      }
    }
  }
}
.footer {
  position: sticky;
  bottom: 0;
  z-index: 1;
  background-color: var(--bgMiddleLight);
  flex-shrink: 0;
  padding: 6px 20px 0;
  padding-bottom: 20px;
}

.replyWrap {
  display: inline-block;
  // width: 100%;
}
.reply {
  display: flex;
  align-items: center;
  margin: 8px 0 0 6px;
  height: 24px;
  padding: 0 2px 0 8px;
  border-radius: 2px;
  font-size: 13px;
  background-color: var(--aBlack6);
  color: var(--TextTertiary);
}
.deleteReply {
  margin-left: 10px;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  &:hover {
    border-radius: 3px;
  }
}
.iconClass {
  border-radius: 3px;
  color: var(--IconPrimary);
}

.attachmentWrapper {
  .labelIcon {
    height: 32px;
  }
  :global {
    .todo-detail-infoItem {
      margin-left: 4px;
    }
  }
}
.noAttachmentWrapper {
  :global {
    .todo-detail-infoItem {
      margin-left: 0px;
    }
  }
}
.itemFlexStart {
  align-items: flex-start;
  height: auto;
  min-height: 32px;
  .remarkIcon {
    margin-top: 2px;
  }
  :global {
    .todo-detail-label-icon {
      // margin-top: 3px;
    }
    .todo-detail-infoItem {
      display: block;
    }
    .icon-Remark {
      margin-top: 2px;
    }
    .icon-attachments_1 {
      margin-top: 2px;
    }
    .detail-attachment-placeholder {
      line-height: 18px;
      height: 28px;
      padding: 5px 4px;
      span {
        color: var(--TextTertiary);
        font-size: 13px;
        font-weight: 400;
      }
      .com-placeholder {
        // padding-left: 4px !important;
      }
    }
    .people-picker-executor-item {
      padding-left: 4px;
      height: 32px;
      display: flex;
      & > div {
        width: 100%;
      }
      &:hover {
        background-color: var(--aBlack6);
        border-radius: 4px;
      }
    }
    .people-picker-executor-item-visible {
      background-color: var(--aBlack6);
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.labelText {
  line-height: 20px;
}

.disabledParticipantWrapper {
  :global {
    .people-picker-executor-item {
      &:hover {
        background-color: transparent !important;
      }
    }
    .picker-label-content {
      .com-placeholder {
        font-size: 14px !important;
      }
    }
  }
}

.attachments {
  height: auto;
  min-height: 36px;
}

.hidden {
  display: none;
}

.remark {
  :global {
    .todo-detail-infoItem {
      overflow: unset;
      // background-color: var(--aBlack4);
      border-radius: 6px;
      display: flex;
      // padding: 8px 8px 6px 8px;
      margin-left: 4px;
    }
  }
}

.remarkEditing {
  :global {
    .todo-detail-infoItem {
      background-color: transparent;
    }
  }
}

.disabledRemark {
  :global {
    .todo-detail-infoItem {
      background-color: transparent !important;
      padding-left: 4px;
      display: flex;
      margin-left: 0px;
    }
  }
}

.noRemark {
  :global {
    .todo-detail-infoItem {
      display: flex;
      align-items: center;
      padding-left: 4px;
      margin-left: 0px;
      background-color: transparent !important;
      &:hover {
        background-color: var(--aBlack6);
        border-radius: 4px;
      }
    }
  }
}
.noRemark {
  .infoItem {
    .editor {
      .placeholder {
        margin-left: 4px;
        line-height: 32px;
      }
    }
  }
}
