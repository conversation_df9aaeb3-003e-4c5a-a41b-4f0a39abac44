import { IconBtn, IconProject, Input, RenderPeoples, Tooltip } from '@/components/basic';
import GroupIcon from '../GroupIcon';
import s from './index.less';
import { InputElement } from '@bedrock/components/lib/Input/InputBase';
import { useDispatch } from 'react-redux';
import { Dispatch } from '@/models/store';
import I18N from '@/utils/I18N';
import Ping from '../../ping';
import { ProjectInfo, ProjectItemStatus } from '@/types';
import { ProjectStatusTag } from '@/components/basic-project';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { getComFormat } from '@/utils/date-format';
import ProjectMoreOption from '../../more-option';
import { history } from 'umi';
import { CompositionInput } from '@/components/basic/input';

interface Props {
  projectInfo: ProjectInfo;
}

const ProjectItem = ({ projectInfo }: Props) => {
  const dispatch = useDispatch<Dispatch>();
  return (
    <div
      className={s.item__container}
      onClick={(e) => {
        if (projectInfo.isGroup) {
          dispatch.viewSetting.openNavigator({
            navigatorId: projectInfo?.projectId!,
            isGroup: true,
          });
          return;
        }
        e.stopPropagation();
        dispatch.viewSetting.openNavigator({
          navigatorId: projectInfo?.projectId!,
        });
      }}
    >
      <NameNode projectInfo={projectInfo} />
      <StatusTag projectInfo={projectInfo} />
      <ProjectMembers projectInfo={projectInfo} />
      <ProjectCreateTime projectInfo={projectInfo} />
      <MoreOption projectInfo={projectInfo} />
    </div>
  );
};

export default ProjectItem;

const NameNode = ({ projectInfo }: Props) => {
  const dispatch = useDispatch<Dispatch>();

  const isGroup = projectInfo.isGroup;
  const handleConfirm = (e: any) => {
    e.stopPropagation();
    const input = e.target as InputElement;
    const name = input.value.trim();
    if (name) {
      if (projectInfo.groupId) {
        dispatch.project.updateGroupName({
          projectId: projectInfo.projectId,
          name,
          isPin: false,
        });
      } else {
        dispatch.project.createProjectGroup({
          name,
          isPin: false,
        });
      }
    } else {
      if (projectInfo.groupId) {
        dispatch.project.updateGroupName({
          projectId: projectInfo.projectId,
          name: projectInfo.name,
          isPin: false,
        });
      } else {
        dispatch.project.removeItem({ projectInfo, isPin: false });
      }
    }
  };

  if (projectInfo.status === ProjectItemStatus.placeholder) {
    return (
      <div className={`${s.item__placeholder} flex-y-center`}>
        <span className={`${s.name} pl-10`}>{projectInfo.name}</span>
      </div>
    );
  }

  if (isGroup && projectInfo.status === ProjectItemStatus.editing) {
    return (
      <div className={`${s.columName} ${s.editing}`}>
        <GroupIcon />
        <CompositionInput
          onPressEnter={handleConfirm}
          onBlur={handleConfirm}
          onClick={(e) => e.stopPropagation()}
          className={s.creating__input}
          maxLength={50}
          defaultValue={projectInfo.name}
          autoFocus
          placeholder={I18N.auto.newGroup}
        />
      </div>
    );
  }

  return (
    <div className={s.columName}>
      {/* <DragHandler /> */}
      {isGroup ? (
        <GroupIcon />
      ) : (
        <IconProject
          className={s.projectIcon}
          fontSize={24}
          name={projectInfo.icon}
          bgColor={projectInfo.iconColor}
          active
        ></IconProject>
      )}
      <Tooltip title={projectInfo.name} onlyEllipsis>
        <span className={s.name}>{projectInfo.name}</span>
      </Tooltip>
      <Ping
        isPin={!!projectInfo.isPin}
        projectId={projectInfo.projectId!}
        groupId={projectInfo.groupId}
        isGroup={projectInfo.isGroup}
        onSuccess={(v) => {
          dispatch.project.getPingProjectList();
          dispatch.project.updateProjectList({ ...projectInfo, ...v });
        }}
      />
    </div>
  );
};

const StatusTag = ({ projectInfo }: Props) => {
  return (
    <div className={'pl-6'}>
      <ProjectStatusTag status={projectInfo.state}></ProjectStatusTag>
    </div>
  );
};

const ProjectMembers = ({ projectInfo }: Props) => {
  const dispatch = useDispatch<Dispatch>();
  return (
    <div
      className={classNames(s.members)}
      onClick={(e) => {
        if (projectInfo.members?.length) {
          e.stopPropagation();
          dispatch.viewSetting.setTaskMenu(projectInfo.projectId!);
          history.push(`/new/project/${projectInfo.projectId}/member`);
        }
      }}
    >
      <RenderPeoples
        showFinishedIcon={false}
        list={projectInfo.members || []}
        count={projectInfo.memberCount || projectInfo.members?.length}
        maxShowCount={3}
        avatarClassName="mr-4"
        canOpenUserProfile={false}
        // renderCountIcon={() => (
        //   <>
        //     <Icon name="icon-sys_more"></Icon>
        //   </>
        // )}
      />
    </div>
  );
};

const ProjectCreateTime = ({ projectInfo }: Props) => {
  return (
    <div className={classNames('pl-6', s.time)}>
      {projectInfo.status === ProjectItemStatus.placeholder || !projectInfo.createTime
        ? ''
        : dayjs(projectInfo.createTime).format(
            getComFormat({
              diffYear: false,
              time: dayjs(projectInfo.createTime),
            })
          )}
    </div>
  );
};

const MoreOption = ({ projectInfo }: Props) => {
  if (projectInfo.status === ProjectItemStatus.placeholder) {
    return null;
  }
  return (
    <div
      className={'pl-8'}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <ProjectMoreOption projectInfo={projectInfo}>
        <IconBtn iconName="icon-sys_more"></IconBtn>
      </ProjectMoreOption>
    </div>
  );
};
