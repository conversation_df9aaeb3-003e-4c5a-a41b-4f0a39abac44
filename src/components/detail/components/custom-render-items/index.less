.customFieldsWrapper {
  :global {
    .rock-input-wrapper.rock-input-wrapper-inline .rock-input-textarea {
      min-height: 20px;
    }
  }
}
.customField {
  display: flex;
  .wrapper {
    width: 100%;
  }
  .customFieldLeft {
    display: flex;
    align-items: center;
    line-height: 20px;
    .name {
      margin-left: 6px;
      max-width: 94px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    :global {
      .babylon-popo-icon {
        font-size: 16px;
        i {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
      i {
        margin-right: 0px !important;
      }
    }
  }
  .customFieldRight {
    flex: 1;
    width: 100%;
    :global {
      .rock-select {
        width: 100%;
      }
    }
  }
}

.customSelectDropdown {
  padding: 4px;
  width: 160px !important;
  :global {
    .rock-select-item-wrapper {
      height: 36px;
      .rock-select-item {
        padding: 6px 8px;
        height: 36px;
        .rock-select-item-label {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}

.customSelect {
  :global {
    .rock-select-selector {
      padding-left: 4px;
      padding-right: 4px;
    }
    .rock-select-selection-placeholder {
      transform: translate3d(-5px, -50%, 0) !important;
    }
  }
}
.multipleCustomSelect {
  :global {
    .rock-select-selection-placeholder {
      transform: translate3d(-5px, -50%, 0) !important;
    }
  }
}

.customNumber {
  height: 32px;
  width: 100%;
  :global {
    .rock-input {
      padding: 6px 4px !important;
      height: 32px;
    }
    // input {
    //   &::placeholder {
    //   }
    // }
  }
}

.customTimeWrapper {
  display: flex;
  align-items: center;
  .customTime {
    display: inline-flex;
    height: 32px !important;
    width: auto !important;

    &:hover {
      background-color: var(--aBlack6) !important;
    }
    &.disabled {
      &:hover {
        background-color: transparent !important;
      }
    }
    :global {
      .picker-label-content {
        padding: 6px 0px;
        height: 32px !important;
        display: inline-flex;
        align-items: center;
      }
    }
  }

  :global {
    .rock-dropdown-trigger-default > span {
      width: auto !important;
    }
  }
  .noTimeValue {
    width: 100% !important;
  }
  .timeDeleteIcon {
    color: var(--IconTertiary);
  }
}

.customPeople {
  padding: 4px !important;
  height: 32px !important;
  &:hover {
    background-color: var(--aBlack6) !important;
  }
  &.disabled {
    &:hover {
      background-color: transparent !important;
    }
  }
}

.closeIcon {
  width: 24px;
  height: 24px;
  padding: 4px;
  margin-left: 8px;
  &:hover {
    background-color: var(--aBlack6);
    border-radius: 4px;
  }
}

.customText {
  padding: 5px 4px;
  //height: 32px;
  min-height: 20px;
  &:hover {
    background-color: var(--aBlack6);
    border-radius: 6px;
  }
  &.disabled {
    &:hover {
      background-color: transparent;
    }
  }
  :global {
    .com-placeholder {
      height: 22px !important;
      line-height: 22px !important;
    }
    .editor-content-editable-wrap {
      line-height: 22px;
      font-size: 14px;
    }
    .rock-input::placeholder {
      font-size: 14px !important;
    }
  }
}
