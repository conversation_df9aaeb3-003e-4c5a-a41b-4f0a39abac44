import { pp } from '@popo-bridge/web';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { apiProjectRequestJoinTeamPost } from '@/api';
import { AvatarPeople, Modal, TextArea } from '@/components/basic';
import { Dispatch, RootState } from '@/models/store';
import { ProjectInfo, UserInfo } from '@/types';
import I18N from '@/utils/I18N';

import s from './index.less';

interface Props {
  className?: string;
  projectInfo?: ProjectInfo;
}

const InvitationGroup: React.FC<Props> = (props) => {
  const { className, projectInfo } = props;
  const { createTeamInfo } = useSelector((state: RootState) => ({
    createTeamInfo: state.project.createTeamInfo,
  }));
  const [visible, setVisible] = useState<boolean>(false);
  const [reason, setReason] = useState<string>('');
  const [list, setList] = useState<UserInfo[]>([]);
  const dispatch = useDispatch<Dispatch>();

  const claerCreateTeamInfo = () => {
    dispatch.project.setData({
      createTeamInfo: {},
    });
  };

  useEffect(() => {
    if (createTeamInfo?.needConfirm?.length) {
      setList(
        createTeamInfo?.needConfirm.map((item) => ({
          uid: item.uid,
          name: item.name,
          avatarUrl: item.headPic,
        }))
      );

      setVisible(true);
    }
  }, [createTeamInfo?.needConfirm]);
  useEffect(() => {
    if (createTeamInfo?.extUsers?.length) {
      pp.showToast({
        title: I18N.template(I18N.auto.waitingForPeopleToJoinTheGroup, {
          val1: createTeamInfo?.extUsers[0].name,
          val2: createTeamInfo?.extUsers?.length,
        }),
      });
      dispatch.project.setData({
        createTeamInfo: {
          ...createTeamInfo,
          extUsers: [],
        },
      });
    }
  }, [createTeamInfo?.extUsers]);

  const onSubmit = () => {
    if (reason.trim() && projectInfo?.projectId) {
      apiProjectRequestJoinTeamPost({
        projectId: projectInfo?.projectId,
        tid: createTeamInfo?.tid,
        reason: reason.trim(),
        toUids: createTeamInfo?.needConfirm?.map((item) => item.uid!),
      }).then(() => {
        setVisible(false);
        claerCreateTeamInfo();
      });
    }
  };
  const memoOkButtonProps = useMemo(() => {
    return {
      disabled: !reason,
    };
  }, [reason]);
  return (
    <Modal
      scrolled={false}
      wrapClassName={s.modal}
      title={I18N.auto.inviteToJoinTheGroupApplication}
      centered
      closable
      visible={visible}
      className={classNames(s.head, className)}
      onOk={() => {
        onSubmit();
      }}
      okButtonProps={memoOkButtonProps}
      onCancel={() => {
        setVisible(false);
        claerCreateTeamInfo();
      }}
    >
      <div className={s.box}>
        <div className={s.desc}>{I18N.auto.addTheFollowingMembers}</div>
        <div className={s.peoples}>
          {list.map((item, index) => (
            <AvatarPeople
              key={index}
              className={s.avatar}
              avatarUrl={item.avatarUrl}
              onClick={() => {
                pp.openUserProfile({ uid: item.uid! });
              }}
            ></AvatarPeople>
          ))}
        </div>
        <TextArea
          className={s.input}
          rows={4}
          placeholder={I18N.auto.reasonMustBeFilledIn}
          allowResize={false}
          value={reason}
          onChange={(e) => {
            setReason(e.target.value);
          }}
        ></TextArea>
      </div>
    </Modal>
  );
};
export default InvitationGroup;
