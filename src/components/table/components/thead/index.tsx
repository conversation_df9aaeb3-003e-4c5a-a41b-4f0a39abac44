import classNames from 'classnames';
import React, { useContext } from 'react';

import { TaskColumnsType } from '@/types';
import { Order } from '@/utils/const';

import { TableContext } from '../../context';
import HeadCell from '../head-cell';
import Row from '../row';
import { TableAllCheckbox } from '../table-checkbox';

export interface Props<RecordType = any> {
  className?: string;
  data: RecordType[];
  columns: TaskColumnsType<RecordType>;
  onAdd?: (id: number) => void;
  sortOrder?: { orderBy?: string; order?: Order };
}

const TableHead = React.forwardRef((props: Props, refs) => {
  const { data, sortOrder } = props;
  const { onSelect, onSortChange, columns } = useContext(TableContext);
  return (
    <div className="todo-thead" id="TodoThead" ref={refs}>
      <Row>
        {onSelect ? (
          <HeadCell>
            <TableAllCheckbox data={data}></TableAllCheckbox>
          </HeadCell>
        ) : null}
        {columns.map((item, key) => {
          let _className = '';
          if (item.fixed === 'left') {
            _className = 'todo-td-fixed-left';
          }
          if (item.fixed === 'right') {
            _className = 'todo-td-fixed-right';
          }
          return (
            <HeadCell
              className={classNames(
                'todo-th',
                _className,
                {
                  ['todo-td-right-no-border']: key === columns.length - 1,
                },
                item.className
              )}
              key={key}
              style={{ textAlign: item.align }}
              sortable={item.sortable}
              active={sortOrder?.orderBy == item.dataIndex}
              order={sortOrder?.orderBy == item.dataIndex ? sortOrder?.order : undefined}
              onSortChange={(sort) => {
                onSortChange?.(item.dataIndex as string, sort);
              }}
              prefix={item.prefix}
              suffix={item.suffix}
              tooltip={'tooltip' in item ? item.tooltip : item.title}
            >
              {item.title}
            </HeadCell>
          );
        })}
      </Row>
    </div>
  );
});

export default TableHead;
