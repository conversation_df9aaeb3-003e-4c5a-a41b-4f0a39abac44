import classNames from 'classnames';
import React, { PropsWithChildren, useCallback, useEffect, useRef, useState } from 'react';

import { apiProjectCheckRolePost } from '@/api';
import { Dropdown } from '@/components/basic';
import { TaskGuide } from '@/components/guide/useGuide';
import { Permission, ProjectInfo } from '@/types';
import { getStorage, removeStorage, StorageType } from '@/utils';
import Const, { GuideType } from '@/utils/const';
import { ProjectPermissionEnum, validatesPermission } from '@/utils/permission';

import GuideCreateTeam from './guide-create-team';
import GuideCreateTeamSuccess from './guide-create-team-success';
import s from './index.less';
import Overlay from './overlay';

interface Props {
  disabled?: boolean;
  className?: string;
  projectInfo?: ProjectInfo;
  permissions: Permission[];
}

enum ChildrenType {
  help,
  errorTooltip,
  normal,
}

const MoreOption: React.FC<PropsWithChildren<Props>> = (props) => {
  const { children, disabled, className, projectInfo, permissions } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [permissionList, setPermissionList] = useState<Permission[]>(permissions);
  const ref = useRef<ChildrenType>();
  const [title, setTitle] = useState<React.ReactNode>(<span></span>);

  const onOk = () => {
    ref.current = ChildrenType.normal;
  };

  const getPermissionList = async () => {
    //参数 约定的操作类型 数组 如
    const keys = [
      ProjectPermissionEnum.CAN_CREATE_IM_TEAM,
      ProjectPermissionEnum.CAN_ENTER_IM_TEAM,
      ProjectPermissionEnum.CAN_CREATE_SCHEDULE,
      ProjectPermissionEnum.CAN_ADD_MANAGER,
      ProjectPermissionEnum.CAN_ADD_EDITOR,
      ProjectPermissionEnum.CAN_ADD_VIEWER,
      ProjectPermissionEnum.CAN_EXIT,
      ProjectPermissionEnum.CAN_DELETE,
    ];

    const list = (await apiProjectCheckRolePost({
      roles: keys,
      projectId: projectInfo?.projectId,
    })) as Permission[];
    setPermissionList(list);
  };

  // useEffect(() => {
  //   //初始化执行一次
  //   if (projectInfo?.projectId) {
  //     //Const.ProjectNewAdd 判断进入此界面是不是从创建页面而来
  //     const isAdd = !!getStorage(Const.ProjectNewAdd, StorageType.session);
  //     if (isAdd) {
  //       removeStorage(Const.ProjectNewAdd);
  //     }
  //     if (!ref.current) {
  //       if (projectInfo.createTeamSuccess === false && isAdd) {
  //         ref.current = ChildrenType.normal;
  //       } else if (isAdd) {
  //         ref.current = ChildrenType.help;
  //       } else {
  //         ref.current = ChildrenType.normal;
  //       }
  //       setTimeout(() => {
  //         TaskGuide.start(GuideType.project);
  //       }, 10);
  //     }
  //     genTitle();
  //   }
  // }, [projectInfo?.projectId]);

  // const genTitle = useCallback(() => {
  //   let _title: React.ReactNode = null;
  //   //项目引导 分2步 群引导和 项目引导属于不同的引导
  //   if (permissionList.length) {
  //     if (ref.current === ChildrenType.help) {
  //       const [CAN_CREATE_IM_TEAM, CAN_ENTER_IM_TEAM] = validatesPermission({
  //         permissions: permissionList,
  //         key: [ProjectPermissionEnum.CAN_CREATE_IM_TEAM, ProjectPermissionEnum.CAN_ENTER_IM_TEAM],
  //       }) as boolean[];
  //       if (!!projectInfo?.tid && CAN_ENTER_IM_TEAM) {
  //         //设置引导数量
  //         TaskGuide.setList([GuideType.createTeamSuccess, GuideType.project]);
  //         _title = <GuideCreateTeamSuccess onOk={onOk}>{children}</GuideCreateTeamSuccess>;
  //       } else if (!projectInfo?.tid && CAN_CREATE_IM_TEAM) {
  //         //设置引导数量
  //         TaskGuide.setList([GuideType.canCreateTeam, GuideType.project]);
  //         _title = <GuideCreateTeam onOk={onOk}>{children}</GuideCreateTeam>;
  //       } else {
  //         TaskGuide.setList([GuideType.project]);
  //         //只有项目引导
  //         _title = children;
  //       }
  //     } else if (ref.current === ChildrenType.normal) {
  //       //只有项目引导
  //       TaskGuide.setList([GuideType.project]);
  //       _title = <div>{children}</div>;
  //     } else {
  //       _title = <div>{children}</div>;
  //     }
  //     setTitle(_title);
  //   }
  // }, [children, permissionList, projectInfo?.tid]);

  return (
    <div className={classNames(s.iconSelect, className)}>
      <Dropdown
        className={s.dropdown}
        title={<div>{children}</div>}
        trigger="click"
        arrow={false}
        defaultOpen={visible}
        disabled={disabled}
        open={visible}
        onOpenChange={async (v) => {
          if (v && projectInfo?.projectId) {
            await getPermissionList();
          }
          setVisible(v);
        }}
        destroyPopupOnHide={false}
        minOverlayWidthMatchTrigger={false}
        overlay={<Overlay projectInfo={projectInfo} permissions={permissionList}></Overlay>}
      />
    </div>
  );
};

export default MoreOption;
