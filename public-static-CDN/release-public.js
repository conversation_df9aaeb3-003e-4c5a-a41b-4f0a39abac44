const path = require('path');
const { onekeypubFileOrFolderListToPath, gdlcSpaw, setConfigPath } = require('@popoDrive/gdlc');
const minimist = require('minimist');
const { toRootPath } = require('./tools/tools');
const argv = minimist(process.argv.slice(2));

const rootDir = path.join(__dirname, '../');

function resolvePath(str) {
  return path.join(rootDir, str);
}

const configPath = resolvePath(path.join('public-static-CDN', 'gdlc_config'));

setConfigPath(configPath);

let envtag = argv.envtag || 'prod'; // test, staging, production

// 初始化cdn发布状态
async function publishCDN(versionTag) {
  // 更新gdlc，设置gdlc_config路径
  await gdlcSpaw(['update']);
  return onekeypubFileOrFolderListToPath({
    filePathList: ['public'].map((n) => toRootPath(n)),
    targetPath: `popo/todo/static/${envtag}`,
    versionName: `popo-todo-static-${envtag}-${versionTag}`,
    publishConfig: {
      no: true,
      env: 'commercial',
      channel: 'gsf',
    },
  }).then((data) => {
    console.log('发布完成', data);
  });
}

publishCDN(new Date().getTime());
