import { OperateClose } from '@babylon/popo-icons';
import classNames from 'classnames';
import { Dayjs } from 'dayjs';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';

import { Dropdown, Placeholder, RenderTime } from '@/components/basic';
import DropdownIcon from '@/components/basic/dropdown-icon';

import DatePickerOverlay from './date-picker-overlay';
import s from './index.less';
import QuickOverlay from './quick-overlay';
import QuickRemindOverlay from './quick-remind-overlay';
import type { TimeValue } from './utils';
import { FORMAT_DD, FORMAT_DD_mm, PPTimeFormat } from './utils';

export { TimeValue };

export type Props = {
  className?: string;
  value?: TimeValue;
  onChange?: (time: TimeValue) => void;
  hasTime?: boolean;
  hasRRule?: boolean;
  placeholder?: ReactNode;
  showLabelRRuleIcon?: boolean;
  hasArrow?: boolean;
  renderLabel?: (time: Dayjs, rrule?: string) => ReactNode;
  quickType?: QuickType;
  deadlineTime?: TimeValue;
  children?: ReactNode;
  showDelete?: boolean;
  createTime?: Dayjs; //TODO 相对时间  在创建的时候用的是创建时间  在编辑的时候用的是当前编辑的时间
  labelClassName?: string;
  disabled?: boolean;
  visible?: boolean;
  onVisible?: (v: boolean) => void;
  hasQuick?: boolean;
  isGetEnd?: boolean;
  minTime?: Dayjs | Date | number;
  minTimeError?: string;
  maxTime?: Dayjs | Date | number;
  maxTimeError?: string;
  defaultDate?: Dayjs | Date | number;
};
export enum Overlay {
  datePanel = 1,
  quick = 2,
  visible = 3,
  quick2datePanel = 4,
}

export enum QuickType {
  deadline,
  remind,
}

export default function POPODatePicker(props: Props) {
  const {
    value,
    onChange,
    hasTime,
    hasRRule,
    placeholder,
    showLabelRRuleIcon,
    renderLabel,
    quickType = QuickType.deadline,
    deadlineTime,
    children,
    showDelete,
    className,
    createTime,
    labelClassName,
    disabled,
    visible = false,
    onVisible,
    hasQuick = true,
    isGetEnd,
    minTime,
    minTimeError,
    maxTime,
    maxTimeError,
    defaultDate,
  } = props;
  const [showDateTimePanel, setShowDateTimePanel] = useState<Overlay>(Overlay.quick);
  // 默认展示下拉组件选中的值
  const onRemove = useCallback(() => {
    onChange?.({
      time: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
      selectedOption: 'NONE',
    });
  }, [onChange]);

  const showTimeLabel = () => {
    const { time, timeFormat, rrule } = value || {};
    if (time && timeFormat) {
      let FORMAT = FORMAT_DD;
      if (timeFormat === PPTimeFormat.olayDay) {
        FORMAT = FORMAT_DD;
      }
      if (timeFormat === PPTimeFormat.dateAndTime) {
        FORMAT = FORMAT_DD_mm;
      }
      return (
        <RenderTime
          className={classNames(s.renderTime, labelClassName)}
          time={time}
          format={FORMAT}
          rrule={rrule}
          showLabelRRuleIcon={showLabelRRuleIcon}
          onRemove={() => {
            onVisible?.(false);
            onRemove();
          }}
          renderLabel={
            renderLabel
              ? (time) => {
                  return renderLabel(time, rrule);
                }
              : undefined
          }
        />
      );
    }
    return <Placeholder className={s.placeholder} text={placeholder}></Placeholder>;
  };

  const change = (v) => {
    setShowDateTimePanel(Overlay.visible);
    onChange?.(v);
    onVisible?.(false);
  };

  const showDatePicker = useMemo(() => {
    return (
      showDateTimePanel === Overlay.datePanel ||
      showDateTimePanel === Overlay.quick2datePanel ||
      !hasQuick ||
      (value?.time && showDateTimePanel !== Overlay.visible)
    );
  }, [showDateTimePanel, hasQuick, value?.time]);

  const showQuick = useMemo(() => {
    return showDateTimePanel === Overlay.quick && hasQuick && !value?.time;
  }, [showDateTimePanel, hasQuick, value?.time]);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange?.({
      time: 0,
      timeFormat: PPTimeFormat.noDate,
      rrule: '',
    });
  };

  useEffect(() => {
    const listener = () => {
      onVisible?.(false);
    };
    if (visible) {
      document.querySelector('.rock-table-body')?.addEventListener('scroll', listener);
      return () => {
        document.querySelector('.rock-table-body')?.removeEventListener('scroll', listener);
      };
    }
  }, [visible]);

  return (
    <Dropdown
      className={classNames(s.timepickerDropdown, className)}
      title={children ? children : showTimeLabel()}
      trigger="click"
      defaultOpen={false}
      open={visible}
      onOpenChange={(open) => {
        onVisible?.(open);
        if (open) {
          if (value?.time) {
            setShowDateTimePanel(Overlay.datePanel);
          } else {
            setShowDateTimePanel(Overlay.quick);
          }
        }
      }}
      disabled={disabled}
      arrow={
        value?.time || value?.rrule ? (
          <OperateClose
            className={`${s.clear__icon} fs-16`}
            style={{ transition: 'unset' }}
            onClick={handleClear}
          />
        ) : (
          true
        )
      }
      //@ts-ignore
      destroyPopupOnHide
      minOverlayWidthMatchTrigger={false}
      overlayClassName={classNames(
        s.picker,
        { [s.datePicker]: showDatePicker },
        { [s.quick]: showQuick }
      )}
      getPopupContainer={(dom) => dom.parentNode}
      overlay={
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {showDatePicker ? (
            <DatePickerOverlay
              value={value}
              onChange={change}
              hasRRule={hasRRule}
              hasTime={hasTime}
              initTime={createTime}
              isGetEnd={isGetEnd}
              minTime={minTime}
              minTimeError={minTimeError}
              maxTime={maxTime}
              maxTimeError={maxTimeError}
              defaultDate={defaultDate}
            ></DatePickerOverlay>
          ) : null}
          {showQuick && quickType === QuickType.deadline ? (
            <QuickOverlay
              onShowDateTimePanel={() => {
                setShowDateTimePanel(Overlay.quick2datePanel);
              }}
              onChange={change}
              showDelete={showDelete}
              isGetEnd={isGetEnd}
            ></QuickOverlay>
          ) : null}
          {showQuick && quickType === QuickType.remind ? (
            <QuickRemindOverlay
              onShowDateTimePanel={() => {
                setShowDateTimePanel(Overlay.quick2datePanel);
              }}
              onChange={change}
              deadlineTime={deadlineTime}
              showDelete={showDelete}
              createTime={createTime}
            ></QuickRemindOverlay>
          ) : null}
        </div>
      }
    />
  );
}
