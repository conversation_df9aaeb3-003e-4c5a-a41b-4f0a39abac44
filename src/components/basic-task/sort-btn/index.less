.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 36px;
  padding: 0 8px;
  border-radius: 6px;
  cursor: pointer;
  &:hover {
    background-color: var(--aBlack6);
    border-radius: 4px;
    :global {
      .im-order-btn {
        display: flex;
      }
    }
    .order {
      visibility: visible;
    }
  }
  .activeCheck {
    color: var(--Brand500);
  }
  .label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    color: var(--TextPrimary);
    &.active {
      color: var(--Brand500);
    }
  }
}
.orderBtn {
  display: flex;
  flex-shrink: 0;
  justify-content: space-between;
  align-items: center;
  height: 24px;
  padding: 2px 1px;
  border-radius: 6px;
  &.show {
    display: flex;
  }
  &.hide {
    display: none !important;
  }

  .order {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-left: 4px;
    color: var(--IconPrimary);
    border-radius: 4px;
    font-size: 13px;
    visibility: hidden;
    &.active {
      color: var(--Brand500);
      visibility: visible;
    }
    &:hover {
      background-color: var(--aBlack6);
    }
  }

  .orderBtnAsce {
    transform: rotate(180deg);
  }
}
