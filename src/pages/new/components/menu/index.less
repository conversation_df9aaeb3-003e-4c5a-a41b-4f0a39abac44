.navigator {
  padding-top: 16px;
  background-color: var(--bgApplication);
  height: 100%;
  display: flex;
  flex-direction: column;
}
.head {
  height: 28px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding-left: 18px;
  padding-right: 12px;
  margin-bottom: 12px;
  .toggleShowMenuIcon {
    color: var(--IconSecondary);
  }
  .title {
    flex: 1;
    margin: 0 2px;
    color: var(--TextPrimary);
    font-weight: 600;
    font-size: 18px;
    white-space: nowrap;
  }
}
.navigatorBox {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.line {
  margin-top: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
}
.menu {
  .desc {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 32px;
    margin-bottom: 2px;
    padding-left: 8px;
    color: var(--TextPrimary);
    font-size: 13px;
  }
  .menuItem {
    position: relative;
    display: flex;
    align-items: center;
    padding: 5px 8px;
    font-size: 14px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    .menuName {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-left: 8px;
      color: var(--TextPrimary);
      height: 24px;
      line-height: 24px;
      .count {
        color: var(--TextTertiary);
        font-size: 11px;
      }
      .hidden {
        display: none;
      }
    }
    &:hover {
      background-color: var(--aBlack6);
    }
    &.active {
      background-color: var(--aBrand8) !important;
      &.menuName {
        color: var(--TextPrimary-strong);
      }
    }
  }

  :global {
    .sortable-tree-item {
      border-radius: 6px;
      &:not(.sortable-tree-item__disabled):hover {
        background-color: var(--aBlack6);
      }
    }

    .sortable-tree-item__selected {
      background-color: var(--aBrand8) !important;
    }

    .sortable-tree-item__above {
      &::after {
        content: '';
        border-radius: 6px;
      }
    }
  }
}
.taskMenu {
  padding: 0 12px;
  flex-shrink: 0;
  :global {
    .iconfont {
      color: var(--IconPrimary);
    }
  }
}

.projectMenu {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 12px;
  overflow: hidden; //当前不可删除, menu的列表需要滚动
  .projectHead {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 34px;
    padding: 5px 8px;
    flex-shrink: 0;

    :global {
      .rock-btn {
        width: 24px;
      }
    }
  }
  .auto {
    //flex: 1;
    overflow-y: auto;
    height: 100%;
    position: relative;
  }
  .project {
    font-weight: 400;
    font-size: 13px;
    color: var(--TextPrimary);
  }
  .addIcon {
    margin-right: 0;
    color: var(--IconSecondary);
  }
}

.projectMenu {
  .menuItem {
    position: relative;
    display: flex;
    align-items: center;
    padding: 5px 8px;
    font-size: 14px;
    border-radius: 8px;

    cursor: pointer;
    .menuName {
      display: inline;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      align-items: center;
      margin-left: 8px;
      color: var(--TextPrimary);
      height: 24px;
      line-height: 24px;
    }

    .left {
      position: relative;
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      .menuName {
        flex: 1;
      }
    }
    .right {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-left: 4px;
      .editIcon {
        color: var(--IconPrimary);
        font-size: 16px;
      }
    }
    &:hover {
      background-color: var(--aBlack6);
    }
    .projectIcon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      border-radius: 4px;
    }
    &.active {
      background-color: var(--aBrand8) !important;
      &.menuName {
        color: var(--TextPrimary-strong);
      }
    }
  }
}

.all {
  font-size: 13px !important;
  height: 34px;
}

.add {
  margin: 10px 0 0 0;
  padding: 14px 14px 14px 14px;
  border-radius: 8px;
  border: 1px solid var(--aBrand8);
  background: rgba(0, 0, 46, 0.01);
  cursor: pointer;
  .addDesc {
    padding-top: 16px;
    padding-bottom: 12px;
    font-size: 12px;
    line-height: 20px;
    color: var(--TextTertiary);
    text-align: center;
  }
  .addBg {
    width: 100%;
    height: 100px;
    background: url('https://popo.res.netease.com/popo-assets/todo/project/addpro-pic-light.png')
      center center no-repeat;
    background-size: contain;
  }
  .addBtn {
    width: 100%;
    background-color: var(--Brand500);
    color: var(--absWhite);
    font-size: 13px;
    &:hover {
      background-color: var(--Brand600);
    }
  }
}

body[data-theme='dark'] {
  .addBg {
    background-image: url('https://popo.res.netease.com/popo-assets/todo/project/addpro-pic-dark.png');
  }
}

.pin__list {
  :global {
    .rc-tree-switcher {
      display: none !important;
    }

    .rc-tree-title {
      width: 100%;
    }

    .rc-tree-node-content-wrapper {
      background-color: transparent !important;
      min-width: 0 !important;
    }

    .rc-tree-treenode {
      padding: 0;
      border-radius: 0.06rem;
      max-height: 34px;

      &:hover,
      &:has(.project__item--active) {
        background-color: var(--aBrand12);
      }
    }

    .rc-tree-indent-unit {
      width: 5px;
    }
  }
}
