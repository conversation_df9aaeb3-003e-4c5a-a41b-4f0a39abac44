import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { apiTodoBatchUpdatePriorityPost } from '@/api';
import BatchsLevelPicker from '@/components/basic/level-picker/batch';
import { Dispatch, RootState } from '@/models/store';
import I18N from '@/utils/I18N';

import BatchItem from '../batch-item';
import s from './index.less';

export type Props = {
  className?: string;
  disabled?: boolean;
};
const BatchLevel: React.FC<Props> = (props) => {
  const { disabled } = props;
  const { batchList } = useSelector((state: RootState) => ({
    batchList: state.batch.batchList,
  }));
  const dispatch = useDispatch<Dispatch>();
  const onChange = (v: number) => {
    const taskIds = batchList.map((item) => item.taskId!);
    // 请求批量操作
    apiTodoBatchUpdatePriorityPost({ priority: v, taskIds: taskIds }).then(() => {
      //刷新左侧标签列表、标签数量、当前列表数据
      dispatch.viewSetting.refreshDataByDataChange({
        refreshList: true,
        refreshCount: false,
      });
      dispatch.batch.setBatchList([]);
      dispatch.batch.setBatchMode(false);
    });
  };
  const showDelete = useMemo(() => {
    return batchList.some((item) => item.priority);
  }, [batchList]);
  return disabled ? (
    <BatchItem
      disabled={disabled}
      className={s.batchStatus}
      name={I18N.auto.priority}
      iconName="icon-pc_details_rank"
    ></BatchItem>
  ) : (
    <BatchsLevelPicker
      showDelete={showDelete}
      onChange={(v) => {
        if (!disabled) {
          onChange(v!);
        }
      }}
    >
      <BatchItem
        className={s.batchStatus}
        name={I18N.auto.priority}
        iconName="icon-pc_details_rank"
      ></BatchItem>
    </BatchsLevelPicker>
  );
};

export default BatchLevel;
