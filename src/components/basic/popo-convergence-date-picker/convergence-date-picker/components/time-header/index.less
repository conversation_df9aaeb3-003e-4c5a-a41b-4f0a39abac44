.newDateSelectWrapper {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  padding: 6px 8px;
  height: 32px;
  border: 1px solid var(--aBlack16);
  //margin-bottom: 10px;
  color: var(--TextPrimary);
  i {
    color: var(--IconPrimary);
    margin-right: 8px;
  }
  .dateTip {
    flex: 1;
  }
  .verticalSplitter {
    height: 10px;
    margin: 0 8px;
    width: 1px;
    background-color: var(--aBlack12);
  }

  &:hover {
    .clear__icon {
      opacity: 1;
      pointer-events: all;
    }
  }
}
.currentDateSelectWrapper {
  border-color: var(--Brand600);
}

.prefix {
  font-size: 16px;
  color: var(--IconTertiary);
  margin-right: 8px;
}

.input {
  border: none;
  padding: 0;
  min-width: 0;
  background-color: transparent;
  font-size: 13px;

  &::placeholder {
    color: var(--TextTertiary);
  }
}

.clear__icon {
  position: absolute;
  right: 8px;
  color: var(--IconTertiary);
  font-size: 14px;
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
}
