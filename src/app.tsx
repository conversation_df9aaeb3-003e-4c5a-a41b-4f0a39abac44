/* eslint-disable simple-import-sort/imports */
// TODO 启用pont的时候放开
// import './api';
import '@/styles/index.less';
import '@bedrock/components/base.css';
import '@bedrock/components/dark-base-switch.css';
import { store } from '@/models/store';
import React, { ReactNode } from 'react';
import { Provider } from 'react-redux';
import Const from '@/utils/const';
import autoUpdate from '@/utils/autoUpdate';
import scout from '@/utils/scout';

import 'overlayscrollbars/css/OverlayScrollbars.css';

//@ts-ignore
OverlayScrollbars.extension(
  'virtuosoExtension',
  //@ts-ignore
  function (defaultOptions, framework) {
    //@ts-ignore
    const osInstance = this;
    const extension = {};
    //@ts-ignore
    extension.added = ({ extraProps }) => {
      const osViewport = framework(osInstance.getElements().viewport);
      Object.entries(extraProps).forEach(([key, value]) => {
        if (key.startsWith('data-')) {
          osViewport.attr(key, value);
        }
      });
    };

    return extension;
  },
  {}
);

if (process.env.NODE_ENV === Const.PRODUCTION) {
  autoUpdate();
  scout.initScout();
}
autoUpdate();

try {
  caches
    .keys()
    .then((keys) =>
      Promise.all(
        keys.map(async (key) => console.log('caches.delete', key, await caches.delete(key)))
      )
    );
} catch (error) {
  console.warn('caches.delete error');
}

// if ('serviceWorker' in navigator) {
//   const serviceWorker = navigator.serviceWorker;
//   if (serviceWorker) {
//     //TODO埋点
//     serviceWorker.getRegistrations
//       ? serviceWorker.getRegistrations().then(function (sws) {
//           sws.forEach(function (sw) {
//             sw.unregister();
//             console.log('sw unregister 1');
//           });
//         })
//       : serviceWorker.getRegistration &&
//         serviceWorker.getRegistration().then(function (sw) {
//           sw && sw.unregister();
//           console.log('sw unregister 2');
//         });
//   }
// }

export const rootContainer = (container: ReactNode) => (
  <Provider store={store}>{container}</Provider>
);
