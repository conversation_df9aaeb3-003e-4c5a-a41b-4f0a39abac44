import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { useCallback } from 'react';

const CustomScrollbar = React.forwardRef(({ children, className, style, ...extraProps }, ref) => {
  // Default scrollbar refSetter (for any component AND Virtuoso)
  const refSetter = useCallback(
    (scrollbarsRef) => {
      if (ref && scrollbarsRef) {
        const osInstance = scrollbarsRef.osInstance();

        // For virtuoso, set viewport as ref and add our custom extension
        ref.current = osInstance.getElements().viewport;
        osInstance.addExt('virtuosoExtension', { extraProps });
      }
    },
    [ref, extraProps]
  );

  return (
    <OverlayScrollbarsComponent
      ref={refSetter}
      className={className}
      style={style}
      options={{ scrollbars: { autoHide: 'scroll' } }}
      {...extraProps}
    >
      {children}
    </OverlayScrollbarsComponent>
  );
});

export default CustomScrollbar;
