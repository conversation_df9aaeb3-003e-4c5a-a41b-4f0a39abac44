import { CustomField } from '@/types/custom-field'
import { PPTimeFormat } from '@/utils/const'
import { EnumDeadlineGroup, EnumFieldNONE, FinishedStatus, Priority } from '@/utils/fields'
import I18N from '@/utils/I18N'
import dayjs, { Dayjs } from 'dayjs'
import { GroupConfig, GroupConfigMap } from './types'
import { createTimeCompareFunc } from './utils'

// 分组配置工厂
export class GroupConfigFactory {
  static createBaseConfig({
    groupId,
    title,
    groupBy,
    groupLevel,
    sortOrder,
    defaultParams,
    compareFunc,
    value,
  }: {
    groupId: string | number
    title: string
    groupBy: string
    groupLevel: number
    sortOrder: string | number
    defaultParams: Record<string, any>
    compareFunc?: (val?: any) => boolean
    value?: any
  }): GroupConfig {
    return {
      groupId,
      title,
      groupBy,
      groupLevel,
      sortOrder,
      defaultParams,
      compareFunc,
      totalCount: 0,
      finishedCount: 0,
      value,
    }
  }

  static createPriorityConfigs(groupBy: string, groupLevel: number): GroupConfigMap {
    const priorityMappings = [
      { id: Priority.Urgent, title: I18N.auto.urgent, order: 1 },
      { id: Priority.High, title: I18N.auto.high, order: 2 },
      { id: Priority.Medium, title: I18N.auto.in, order: 3 },
      { id: Priority.Low, title: I18N.auto.low, order: 4 },
      { id: Priority.Unset, title: I18N.auto.nothing, order: 999 },
    ]

    return priorityMappings.reduce((configs, { id, title, order }) => {
      configs[id] = this.createBaseConfig({
        groupId: id,
        title: title,
        groupBy: groupBy,
        groupLevel: groupLevel,
        sortOrder: order,
        defaultParams: { priority: id },
        compareFunc: (val: number) => val === id,
        value: id,
      })
      return configs
    }, {} as GroupConfigMap)
  }

  static createFinishedConfigs(groupBy: string, groupLevel: number): GroupConfigMap {
    const finishedMappings = [
      { id: FinishedStatus.UnFinished, title: I18N.auto.hangInTheAir, order: 1 },
      { id: FinishedStatus.Finished, title: I18N.auto.completed, order: 2 },
    ]

    return finishedMappings.reduce((configs, { id, title, order }) => {
      configs[id] = this.createBaseConfig({
        groupId: id,
        title: title,
        groupBy: groupBy,
        groupLevel: groupLevel,
        sortOrder: order,
        defaultParams: { finished: id },
        compareFunc: val => val === id,
        value: id,
      })
      return configs
    }, {} as GroupConfigMap)
  }

  static createTimeConfigs(time: Dayjs, groupBy: string, groupLevel: number): GroupConfigMap {
    const timeConfigs = [
      {
        offset: -1,
        title: I18N.auto.past,
        groupId: EnumDeadlineGroup.EXPIRED,
        comparison: 'before',
        order: 1,
      },
      {
        offset: 0,
        title: I18N.auto.today,
        groupId: EnumDeadlineGroup.TODAY,
        comparison: 'same',
        order: 2,
      },
      {
        offset: 1,
        title: I18N.auto.tomorrow,
        groupId: EnumDeadlineGroup.TOMORROW,
        comparison: 'same',
        order: 3,
      },
      {
        offset: 7,
        title: I18N.auto.futureDays,
        groupId: EnumDeadlineGroup.FUTURE7DAYS,
        comparison: 'between',
        order: 4,
      },
      {
        offset: 8,
        title: I18N.auto.inTheFuture,
        groupId: EnumDeadlineGroup.FUTURE,
        comparison: 'after',
        order: 5,
      },
    ]

    const configs = timeConfigs.reduce((acc, { offset, title, groupId, comparison, order }) => {
      const targetTime = time.add(offset, 'day')
      acc[groupId] = this.createBaseConfig({
        groupId: groupId,
        title: title,
        groupBy: groupBy,
        groupLevel: groupLevel,
        sortOrder: order,
        defaultParams: { time: targetTime.valueOf(), format: PPTimeFormat.olayDay },
        compareFunc: createTimeCompareFunc(time, targetTime, comparison),
        value: targetTime.valueOf(),
      })
      return acc
    }, {} as GroupConfigMap)

    // 添加未安排分组
    configs[EnumDeadlineGroup.NONE] = this.createBaseConfig({
      groupId: EnumDeadlineGroup.NONE,
      title: I18N.auto.notArranged,
      groupBy: groupBy,
      groupLevel: groupLevel,
      sortOrder: 999,
      defaultParams: { time: 0, format: PPTimeFormat.noDate },
      compareFunc: (val?: number) => !val,
      value: null,
    })

    return configs
  }

  static createOptionConfigs(customField: CustomField, groupLevel: number): GroupConfigMap {
    const configs: GroupConfigMap = {}

    if (customField.options?.length) {
      customField.options.forEach(option => {
        configs[option.value] = this.createBaseConfig({
          groupId: option.value,
          title: option.name,
          groupBy: customField.fieldId,
          groupLevel: groupLevel,
          sortOrder: option.sortOrder || 0,
          defaultParams: { [customField.fieldId]: option.value },
          compareFunc: (val: any) => val === option.value,
          value: option.value,
        })
      })
    }

    // 添加默认空分组
    configs[EnumFieldNONE.NONE] = this.createBaseConfig({
      groupId: EnumFieldNONE.NONE,
      title: I18N.auto.nothing,
      groupBy: customField.fieldId,
      groupLevel: groupLevel,
      sortOrder: 999,
      defaultParams: { [customField.fieldId]: EnumFieldNONE.NONE },
      compareFunc: (val: any) => !val,
      value: null,
    })

    return configs
  }
}
