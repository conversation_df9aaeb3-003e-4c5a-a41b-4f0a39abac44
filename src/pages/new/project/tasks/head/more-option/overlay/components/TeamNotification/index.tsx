import { FC, PropsWithChildren, ReactNode, useMemo, useState } from 'react';
import s from './index.less';
import { Button, Divider, Dropdown, Icon, Message, Popover, Switch } from '@/components/basic';
import { useRequest } from 'ahooks';
import {
  apiProjectNotifySettingListGet,
  ApiProjectNotifySettingListGetResponse,
  apiProjectNotifySettingPut,
} from '@/api';
import { ArrayItem, ProjectInfo } from '@/types';
import { NOTIFICATION_TYPE } from '@/utils/const';
import { createTeamByProject, linkTeam, openSession } from '../..';
import { CardFp, CardYq } from '@babylon/popo-icons';
import I18N from '@/utils/I18N';

const nitificationTypeMap = {
  [NOTIFICATION_TYPE.TASK_ASSIGN]: {
    name: I18N.auto.assignTasks,
    desc: I18N.auto.triggerNotificationAfterFilling,
  },
  [NOTIFICATION_TYPE.TASK_COMPLETED]: {
    name: I18N.auto.taskCompleted,
    desc: I18N.auto.triggerNotificationAfterExecuted,
  },
  [NOTIFICATION_TYPE.TASK_EXPIRED]: {
    name: I18N.auto.taskOverdue,
    desc: I18N.auto.triggerNotificationOverdue,
  },
};

const notiIconsMap: Record<string, ReactNode> = {
  [NOTIFICATION_TYPE.TASK_ASSIGN]: <CardFp size={13}></CardFp>,
  [NOTIFICATION_TYPE.TASK_COMPLETED]: <Icon fontSize={13} name="icon-taskstate_down"></Icon>,
  [NOTIFICATION_TYPE.TASK_EXPIRED]: <CardYq size={13}></CardYq>,
};

interface ITeamNotifitycationProps {
  dispatch: any;
  projectInfo: ProjectInfo;
  language?: string;
  colorMode?: string;
}

const TeamNotifitycation: FC<ITeamNotifitycationProps> = ({
  projectInfo,
  language,
  dispatch,
  colorMode = 'light',
}) => {
  const [projectData, setProjectData] = useState<ProjectInfo>(projectInfo);
  const { tid, id } = projectData || {};

  const { data = {}, run: getNotifyList } = useRequest(() =>
    apiProjectNotifySettingListGet({ projectId: id })
  );

  const { settings = [] } = data;

  const handleCardPreChange = (checked: boolean) => {
    if (checked) {
      if (tid) {
        return Promise.resolve();
      }
      Message.error(I18N.auto.associateGroupFirst);
      return Promise.reject();
    }
    return Promise.resolve();
  };

  const handleChangeSuccess = () => {
    getNotifyList();
  };

  const handleCreateTeam = () => {
    createTeamByProject({ projectId: id, dispatch }).then((teamId) => {
      setProjectData({ ...projectData, tid: teamId });
      getNotifyList();
    });
  };

  const handleLinkTeam = () => {
    linkTeam({ projectId: id, dispatch }).then((teamId) => {
      setProjectData({ ...projectData, tid: teamId });
      getNotifyList();
    });
  };

  const handleOpenSession = () => {
    openSession({ teamId: tid });
  };

  const headerText = useMemo(
    () => ({
      'zh-CN': (
        <span>
          {/* @i18n-ignore */}
          请先
          <span className={s.header__highlight} onClick={handleCreateTeam}>
            {/* @i18n-ignore */}
            新建项目群
          </span>
          {/* @i18n-ignore */}
          <span>或</span>
          <span className={s.header__highlight} onClick={handleLinkTeam}>
            {/* @i18n-ignore */}
            关联已有群
          </span>
        </span>
      ),
      'en-US': (
        <span>
          {/* @i18n-ignore */}
          Please
          <span className={s.header__highlight} onClick={handleCreateTeam}>
            {/* @i18n-ignore */}
            create a project group chat
          </span>
          {/* @i18n-ignore */}
          or
          <span className={s.header__highlight} onClick={handleLinkTeam}>
            {/* @i18n-ignore */}
            link to existing group chats
          </span>
        </span>
      ),
      'ja-JP': (
        //@i18n-ignore
        <span>
          <span className={s.header__highlight} onClick={handleCreateTeam}>
            {/* @i18n-ignore */}
            プロジェクト専用グループを作成する
          </span>
          {/* @i18n-ignore */}
          か、
          <span className={s.header__highlight} onClick={handleLinkTeam}>
            {/* @i18n-ignore */}
            既存のグループとの関連付けを完了
          </span>
          {/* @i18n-ignore */}
          してください
        </span>
      ),
    }),
    [language]
  );

  return (
    <div className={s.team__noti}>
      <div className={s['team__noti--header']}>
        {!tid ? (
          headerText[language]
        ) : (
          <span>
            {I18N.auto.associatedGroup}
            <span className={s.header__highlight} onClick={handleOpenSession}>
              {tid}
            </span>
          </span>
        )}
      </div>
      <div className={s.cards}>
        {settings.map((item, index) => {
          return (
            <Card
              key={index}
              language={language}
              notiData={item}
              onPrevChange={handleCardPreChange}
              onChangeSuccess={handleChangeSuccess}
              colorMode={colorMode}
            />
          );
        })}
      </div>
    </div>
  );
};

interface IconProps {
  notiData: ArrayItem<ApiProjectNotifySettingListGetResponse['settings']>;
  language?: string;
  onPrevChange: (checked: boolean) => void;
  onChangeSuccess?: () => void;
  colorMode?: string;
}

const Card: FC<IconProps> = ({ notiData, onPrevChange, onChangeSuccess, language, colorMode }) => {
  const { status, type, sortNo } = notiData;

  const { run: updateNotify } = useRequest((params) => apiProjectNotifySettingPut(params), {
    manual: true,
    onSuccess: () => {
      Message.success(I18N.auto.savedSuccessfully);
      onChangeSuccess?.();
    },
    onError: () => {
      Message.error(I18N.auto.operationFailedPlease_3);
    },
  });

  const handleSwitch = async (checked: boolean) => {
    try {
      await onPrevChange?.(checked);
      updateNotify({
        ...notiData,
        status: checked ? 1 : 0,
      });
    } catch (error) {}
  };

  const icon = notiIconsMap[type];
  const imgUrl = `https://popo.gsf.netease.com/popo/todo/static/prod/public/imgs/notify/${type}-${colorMode}-${language}.png?t=${Date.now()}`;

  const { name, desc } = nitificationTypeMap[type];

  return (
    <div className={s.card}>
      <div className={s.card__header}>
        <NotiIcon>{icon}</NotiIcon>
        <Switch checked={!!status} onChange={handleSwitch} />
      </div>
      <div className={s.card__content}>
        <div className={s.card__title}>{name}</div>
        <div className={s.card__desc}>{desc}</div>
      </div>

      <Divider type="horizontal" className={s.divider} />

      <div className={s.card__footer}>
        <Dropdown
          trigger="click"
          placement="topLeft"
          overlay={
            <div className={s.preview}>
              <img src={imgUrl || ''} alt="" />
            </div>
          }
        >
          <Button className={s.button} type="link">
            {I18N.auto.viewPreview}
          </Button>
        </Dropdown>
      </div>
    </div>
  );
};

const NotiIcon: FC<PropsWithChildren> = ({ children }) => {
  return <div className={s.noti__icon}>{children}</div>;
};

export default TeamNotifitycation;
