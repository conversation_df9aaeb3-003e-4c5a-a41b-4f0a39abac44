/**
 * requestIdleCallback 的参数接口
 */
export interface IdleRequestOptions {
  timeout?: number
}

/**
 * IdleDeadline 接口定义
 */
export interface IdleDeadline {
  didTimeout: boolean
  timeRemaining: () => number
}

/**
 * 实现 requestIdleCallback 的 polyfill
 * 优先使用原生 requestIdleCallback，不支持时使用 requestAnimationFrame 模拟
 */
export function rIC(callback?: (deadline: IdleDeadline) => void, options?: IdleRequestOptions): number {
  if (!callback) {
    return 0
  }
  // 检查浏览器是否原生支持
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    return (window as any).requestIdleCallback(callback, options)
  }

  const timeout = options?.timeout || 50
  const start = Date.now()

  return requestAnimationFrame(() => {
    const deadline: IdleDeadline = {
      didTimeout: Date.now() - start >= timeout,
      timeRemaining: () => Math.max(0, 50 - (Date.now() - start)),
    }
    callback(deadline)
  })
}

/**
 * 取消 requestIdleCallback 请求
 */
export function cIC(id: number): void {
  if (typeof window !== 'undefined' && 'cancelIdleCallback' in window) {
    ;(window as any).cancelIdleCallback(id)
    return
  }

  // 使用 cancelAnimationFrame 取消
  cancelAnimationFrame(id)
}
